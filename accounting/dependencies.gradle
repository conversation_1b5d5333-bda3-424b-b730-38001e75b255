dependencies {
  api project(':base')

  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:$kotlinx_coroutines_version"
  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:$kotlinx_coroutines_version"
  implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"
  implementation "org.jetbrains.kotlinx:kotlinx-serialization-json:1.0.1"

  implementation "io.ktor:ktor-server-servlet:$ktor_version"
  implementation "io.ktor:ktor-auth:$ktor_version"
  implementation "io.ktor:ktor-server-netty:$ktor_version"
  implementation "io.ktor:ktor-serialization:$ktor_version"
  implementation "io.ktor:ktor-client-auth:$ktor_version"

  implementation "javax.servlet:javax.servlet-api:4.0.1"

  implementation "org.jetbrains.exposed:exposed-core:$exposed_version"
  implementation "org.jetbrains.exposed:exposed-jdbc:$exposed_version"
  implementation "org.jetbrains.exposed:exposed-java-time:$exposed_version"
  implementation "com.google.cloud.sql:mysql-socket-factory-connector-j-8:$mysql_socket_factory_version"
  implementation "mysql:mysql-connector-java:$mysql_connector_version"

  implementation "com.zaxxer:HikariCP:$hikari_version"

  implementation "ch.qos.logback:logback-classic:$logback_version"

  implementation "com.paypal:paypalhttp:2.0.0"
  implementation("com.paypal.sdk:payouts-sdk:1.1.0") {
    exclude group: "com.paypal", module: "paypalhttp"
  }
  implementation "com.paypal.sdk:rest-api-sdk:1.14.0"

  implementation "com.squareup.okhttp3:okhttp:4.8.0"
  implementation "joda-time:joda-time:2.10.6"

  implementation "com.google.cloud:google-cloud-storage:2.28.0"

  implementation 'org.apache.commons:commons-lang3:3.11'

  implementation "org.apache.commons:commons-csv:1.8"
  implementation "org.apache.pdfbox:pdfbox:2.0.29"
  implementation "org.apache.pdfbox:preflight:2.0.29"
  implementation "com.github.vandeseer:easytable:0.8.5"

  testImplementation(testFixtures(project(":base")))
  testImplementation "net.bytebuddy:byte-buddy:1.10.18"
  testImplementation "net.bytebuddy:byte-buddy-agent:1.10.18"
  testImplementation 'org.mockito.kotlin:mockito-kotlin:5.2.0'
  testImplementation "org.junit.jupiter:junit-jupiter:$junit_jupiter_version"
  testImplementation "org.junit.jupiter:junit-jupiter-params:$junit_jupiter_version"
  testImplementation "com.willowtreeapps.assertk:assertk-jvm:0.22"
  testImplementation "org.mockito:mockito-inline:5.2.0"
  testImplementation "org.mockito:mockito-junit-jupiter:5.12.0"
  testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$kotlinx_coroutines_version"
  testImplementation "org.jetbrains.kotlin:kotlin-test-junit5:$kotlin_version"
  testImplementation "io.ktor:ktor-client-mock:$ktor_version"
  testImplementation "io.ktor:ktor-server-test-host:$ktor_version"
  testImplementation "com.codeborne:pdf-test:1.7.0"
  testImplementation "org.testcontainers:junit-jupiter:$testcontainers_version"
  testImplementation "org.liquibase:liquibase-core:$liquibase_version"
}
