package com.justplayapps.service.accounting

import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.justplayapps.proxybase.PROXY_BASIC_AUTH_REALM
import com.justplayapps.proxybase.PROXY_BASIC_AUTH_USER
import com.justplayapps.proxybase.paypal.PayPalApiProxyFacade
import com.justplayapps.service.accounting.tango.TangoCardCredentialsService
import com.justplayapps.service.accounting.util.getDefaultJsonConverter
import com.moregames.base.app.BaseModule
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.DataSourceProvider
import com.moregames.base.app.DefaultDataSourceProvider
import com.moregames.base.ktor.HttpClientTracing
import com.moregames.base.secret.Secret
import com.moregames.base.secret.SecretService
import io.ktor.application.*
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.features.*
import io.ktor.client.features.auth.*
import io.ktor.client.features.auth.providers.*
import io.ktor.client.features.json.*
import io.ktor.client.features.json.serializer.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.Json

class CoreModule(
  application: Application,
  private val buildVariant: BuildVariant,
  dataSourceProvider: DataSourceProvider = DefaultDataSourceProvider(),
) : BaseModule(application, buildVariant, dataSourceProvider) {

  companion object {
    const val PROXY_SERVICE_HTTP_CLIENT = "proxyServiceHttpClient"
  }

  @Provides
  fun tangoCredentials() =
    if (buildVariant == BuildVariant.PRODUCTION) {
      TangoCardCredentialsService.PRODUCTION
    } else {
      TangoCardCredentialsService.SANDBOX
    }

  @Provides
  fun tangoCardClient(secretService: SecretService, tangoCardCredentialsService: TangoCardCredentialsService) =
    runBlocking { tangoCardCredentialsService.client(secretService) }


  @Provides
  @Singleton
  fun database(secretService: SecretService) = connectToDatabase(DbConfigs.ACCOUNTING, secretService)

  @Provides
  @Singleton
  fun getJsonConverter() = getDefaultJsonConverter()

  @Provides
  @Singleton
  @Named(PROXY_SERVICE_HTTP_CLIENT)
  fun proxyServiceHttpClient(json: Json, secretService: SecretService) = HttpClient(CIO) {
    expectSuccess = true
    install(JsonFeature) {
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "proxyService"
    }
    install(Auth) {
      basic {
        sendWithoutRequest { true }
        credentials {
          BasicAuthCredentials(
            username = PROXY_BASIC_AUTH_USER,
            password = secretService.secretValue(Secrets.BASIC_AUTH_PROXY)
          )
        }
        realm = PROXY_BASIC_AUTH_REALM
      }
    }
  }

  @Provides
  @Singleton
  fun paypalApiProxyFacade(@Named(PROXY_SERVICE_HTTP_CLIENT) httpClient: HttpClient) = PayPalApiProxyFacade(httpClient)

  private enum class DbConfigs(override val username: String, override val password: Secret, override val maximumPoolSize: Int) : DbConfig {
    ACCOUNTING("accounting", Secrets.DATABASE_USER_ACCOUNTING, 50)
  }
}