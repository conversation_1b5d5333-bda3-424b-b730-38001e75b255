package com.justplayapps.service.accounting

import com.google.inject.Inject
import com.justplayapps.service.accounting.cron.CronController
import com.justplayapps.service.accounting.reports.ReportsController
import com.justplayapps.service.accounting.util.getDefaultJsonConverter
import com.moregames.base.app.BuildVariant
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.ktor.cronInterceptor
import com.moregames.base.secret.BaseSecrets
import com.moregames.base.secret.SecretService
import com.moregames.base.util.basicAuth
import com.moregames.base.util.cronLogger
import com.moregames.base.util.installLogging
import com.moregames.base.util.logger
import io.ktor.application.*
import io.ktor.auth.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import io.ktor.serialization.*
import org.slf4j.Logger

private const val REPORTING_AUTH_CONFIG_NAME = "reporting-api"
private const val REPORTING_BASIC_AUTH_REALM = "reporting server"
private const val REPORTING_BASIC_AUTH_USER = "reporting"

class ApiManager @Inject constructor(
  private val cronController: CronController,
  private val reportsController: ReportsController,
  private val buildVariant: BuildVariant,
  private val secretService: SecretService,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val application: Application,
) {

  suspend fun initApi() {
    application.installLogging(buildVariant, featureFlagsFacade)
    initAuthentication(application)
    application.install(ContentNegotiation) {
      json(
        json = getDefaultJsonConverter()
      )
    }
    application.routing {
      lifecycle()
      initCronRoute()
      initReportsRoute()
    }
  }

  private suspend fun initAuthentication(application: Application) {
    val reportingPassword = secretService.secretValue(BaseSecrets.BASIC_AUTH_REPORTING)
    application.authentication {
      basicAuth(REPORTING_AUTH_CONFIG_NAME, REPORTING_BASIC_AUTH_REALM, REPORTING_BASIC_AUTH_USER, reportingPassword)
    }
  }

  private fun Routing.lifecycle() {
    get("/_ah/warmup") {
      call.respond(HttpStatusCode.OK)
    }
  }

  private fun Route.initCronRoute() {
    route("/cron") {
      install(StatusPages, statusPageConfig(cronLogger()))
      cronInterceptor(buildVariant)
      cronController.startRouting(this)
    }
  }

  private fun Route.initReportsRoute() {
    authenticate(REPORTING_AUTH_CONFIG_NAME) {
      route("/reports") {
        install(StatusPages, statusPageConfig())
        reportsController.startRouting(this)
      }
    }
  }

  private fun statusPageConfig(logger: Logger = logger()): StatusPages.Configuration.() -> Unit = {
    exception<Throwable> { e ->
      logger.error("Error", e)
      call.respondText(e.message ?: "Unknown server error", ContentType.Text.Plain, HttpStatusCode.InternalServerError)
    }
  }
}
