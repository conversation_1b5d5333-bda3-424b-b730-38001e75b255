package com.justplayapps.service.router

import com.google.inject.Inject
import com.justplayapps.service.router.user.CreateUserController
import com.justplayapps.service.router.util.Secrets
import com.justplayapps.service.router.util.getDefaultJsonConverter
import com.moregames.base.app.BuildVariant
import com.moregames.base.exceptions.ApiError
import com.moregames.base.exceptions.BaseException
import com.moregames.base.exceptions.ErrorType
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.ktor.cors
import com.moregames.base.secret.SecretService
import com.moregames.base.util.basicAuth
import com.moregames.base.util.installLogging
import com.moregames.base.util.logger
import io.ktor.application.*
import io.ktor.auth.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import io.ktor.serialization.*
import org.slf4j.Logger
import javax.inject.Singleton

private const val PLAYTIME_AUTH_CONFIG_NAME = "playtime-api"
private const val PLAYTIME_BASIC_AUTH_REALM = "playtime server"
private const val PLAYTIME_BASIC_AUTH_USER = "playtime"

@Singleton
class ApiManager @Inject constructor(
  private val createUserController: CreateUserController,
  private val buildVariant: BuildVariant,
  private val secretService: SecretService,
  private val featureFlagsFacade: FeatureFlagsFacade,
) {

  suspend fun initApi(application: Application) {
    application.installLogging(buildVariant, featureFlagsFacade)
    application.install(XForwardedHeaderSupport) // MUST BE BEFORE CORS!!!
    application.install(CORS, cors(buildVariant))
    application.install(ContentNegotiation) {
      json(
        json = getDefaultJsonConverter()
      )
    }
    initAuthentication(application)

    initRouting(application)
  }

  private fun initRouting(application: Application) {
    application.routing {
      initPlaytimeRoute()
      initWarmup()
    }
  }

  private fun Routing.initWarmup() {
    get("/_ah/warmup") {
      call.respond(HttpStatusCode.OK)
    }
  }

  private fun Routing.initPlaytimeRoute() {
    authenticate(PLAYTIME_AUTH_CONFIG_NAME) {
      route("/playtime/") {
        install(StatusPages, statusPageConfig())
        createUserController.startRouting(this)
      }
    }
  }

  private suspend fun initAuthentication(application: Application) {
    val playtimePassword = secretService.secretValue(Secrets.BASIC_AUTH_PLAYTIME)
    application.authentication {
      basicAuth(PLAYTIME_AUTH_CONFIG_NAME, PLAYTIME_BASIC_AUTH_REALM, PLAYTIME_BASIC_AUTH_USER, playtimePassword)
    }
  }

  private fun statusPageConfig(logger: Logger = logger()): StatusPages.Configuration.() -> Unit = {
    exception<BaseException> { e ->
      if (e.errorType == ErrorType.SERVER_ERROR)
        logger.error(e.internalMessage, e)
      else
        logger.warn(e.internalMessage)

      val apiError = ApiError(e.errorCode.toCode(), if (buildVariant == BuildVariant.PRODUCTION) e.externalMessage else e.internalMessage)
      call.respond(e.errorType.toHttpStatusCode(), apiError)
    }
    exception<Throwable> { e ->
      logger.error("Unknown server error", e)
      call.respondText(e.buildVariantMessageOrDefault("Unknown server error"), ContentType.Text.Plain, HttpStatusCode.InternalServerError)
    }
  }

  private fun Throwable.buildVariantMessageOrDefault(default: String = "") =
    if (buildVariant == BuildVariant.PRODUCTION) default else message ?: default

  private fun ErrorType.toHttpStatusCode() =
    when (this) {
      ErrorType.SERVER_ERROR -> HttpStatusCode.InternalServerError
      ErrorType.INPUT_ERROR -> HttpStatusCode.BadRequest
      ErrorType.AUTHENTICATION_ERROR -> HttpStatusCode.Unauthorized
      ErrorType.RESOURCE_NOT_FOUND -> HttpStatusCode.NotFound
      ErrorType.TOO_EARLY -> HttpStatusCode(429, "Too Early")
    }
}
