package com.justplayapps.service.router

import com.google.inject.Injector
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.justplayapps.service.router.util.Secrets
import com.justplayapps.service.router.util.getDefaultJsonConverter
import com.moregames.base.app.BaseModule
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.DataSourceProvider
import com.moregames.base.app.DefaultDataSourceProvider
import com.moregames.base.ktor.HttpClientTracing
import com.moregames.base.messaging.auth.AuthService
import com.moregames.base.messaging.auth.HttpClientAuth
import com.moregames.base.secret.SecretService
import io.ktor.application.*
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.features.*
import io.ktor.client.features.json.*
import io.ktor.client.features.json.serializer.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.util.*
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.Database

class CoreModule(
  application: Application,
  buildVariant: BuildVariant,
  dataSourceProvider: DataSourceProvider = DefaultDataSourceProvider(),
) : BaseModule(application, buildVariant, dataSourceProvider) {

  companion object {
    const val CROSS_SERVICE_HTTP_CLIENT = "crossServiceHttpClient"
    const val COUNTRY_DETECTOR_HTTP_CLIENT = "countryDetectorHttpClient"
  }

  @Provides
  @Singleton
  fun database(secretService: SecretService) = connectToDatabase(DbConfigs.ROUTER, secretService)

  @Provides
  @Singleton
  fun getJsonConverter() = getDefaultJsonConverter()

  @Provides
  @Singleton
  @Named(CROSS_SERVICE_HTTP_CLIENT)
  fun crossServiceHttpClient(json: Json, authService: AuthService) = HttpClient(CIO) {
    expectSuccess = true
    install(JsonFeature) {
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "createUser"
    }
    install(HttpClientAuth) {
      this.authService = authService
    }
  }

  @Provides
  @Singleton
  @Named(COUNTRY_DETECTOR_HTTP_CLIENT)
  fun countryDetectorHttpClient() = HttpClient(CIO) {
    install(JsonFeature) {
      val json = getDefaultJsonConverter()
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "ipRegistry"
    }
  }

  @Provides
  @Singleton
  fun databases(injector: Injector) =
    injector.allBindings.keys.filter { Database::class.java.isAssignableFrom(it.typeLiteral.rawType) }.map { injector.getInstance(it) as Database }.toSet()

  private enum class DbConfigs(override val username: String, override val password: Secrets, override val maximumPoolSize: Int) : DbConfig {
    ROUTER("router", Secrets.DATABASE_USER_ROUTER, 40)
  }
}

sealed class ApiResponse<out T, out E> {
  data class Success<T>(
    val data: T
  ) : ApiResponse<T, Nothing>()

  sealed class Error<E> : ApiResponse<Nothing, E>() {
    data class HttpError<E>(
      val apiError: E,
      val httpStatus: HttpStatusCode,
    ) : Error<E>()

    data class GenericError(
      val exception: Exception
    ) : Error<Nothing>()
  }
}

suspend inline fun <reified T, reified E> HttpClient.request(
  urlString: String,
  block: HttpRequestBuilder.() -> Unit,
): ApiResponse<T, E> =
  try {
    val response = post<T>(urlString) { block() }
    ApiResponse.Success(response)
  } catch (exception: ClientRequestException) {
    try {
      ApiResponse.Error.HttpError(
        apiError = getDefaultJsonConverter().decodeFromString(String(exception.response.content.toByteArray())),
        httpStatus = exception.response.status
      )
    } catch (e: Exception) {
      ApiResponse.Error.GenericError(exception)
    }

  } catch (e: Exception) {
    ApiResponse.Error.GenericError(e)
  }