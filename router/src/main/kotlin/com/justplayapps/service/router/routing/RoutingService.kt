package com.justplayapps.service.router.routing

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.app.BuildVariant
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.exceptions.CountryIsNotAllowedException
import com.moregames.base.util.*
import com.moregames.base.util.Constants.GKE_INTERNAL_URL
import kotlinx.coroutines.async
import javax.inject.Singleton

@Singleton
class RoutingService @Inject constructor(
  private val defaultRoutesPersistenceService: DefaultRoutesPersistenceService,
  private val customRoutesPersistenceService: CustomRoutesPersistenceService,
  private val randomGenerator: RandomGenerator,
  private val buildVariantProvider: Provider<BuildVariant>,
  private val coroutineScope: Provider<IoCoroutineScope>,
) {

  companion object {
    const val CACHE_KEY_STUB = "stub"
  }

  private val defaultRoutesCache = buildCache(buildVariantProvider.get(), expireAfter = 1L) { _: String ->
    coroutineScope.get().async {
      defaultRoutesPersistenceService.loadDefaultRoutes()
    }
  }
  private val customRoutesCache = buildCache(buildVariantProvider.get(), expireAfter = 1L) { _: String ->
    coroutineScope.get().async {
      customRoutesPersistenceService.loadCustomRoutes()
    }
  }

  suspend fun getRoute(appVersion: AppVersionDto, country: String): RoutingResult {
    val customRoutes = getCustomRoutes().asSequence()
      .filter { it.allowedCountries.contains(country) }
      .filter { it.platform == appVersion.platform }
      .filter { appVersion.version >= it.minimalAppVersion }
      .sortedByDescending { it.minimalAppVersion }
      .toList()

    if (customRoutes.isNotEmpty()) {
      val dice = randomGenerator.nextInt(100)
      customRoutes
        .groupBy { it.minimalAppVersion }
        .forEach { (_, rules) ->
          var minPercentage = 0
          rules.forEach { routeRule ->
            if (routeRule.trafficPercentage + minPercentage > dice) {
              return routeRule.toRoute()
            }
            minPercentage += routeRule.trafficPercentage
          }
        }
    }
    // fallback to default routes
    getDefaultRoutes().asSequence()
      .filter { it.allowedCountries.contains(country) }
      .filter { it.platform == appVersion.platform }
      .firstOrNull()
      ?.let {
        return it.toRoute()
      }

    return RoutingResult.RoutingException(CountryIsNotAllowedException())
  }

  private fun Routing.toRoute(): RoutingResult {
    val gkeEnvPrefix = "https://${if (buildVariantProvider.get() == BuildVariant.TEST) "test" else "production"}"
    return RoutingResult.Route(
      redirectUrl = "${gkeEnvPrefix}.${this.market.shortName}.$GKE_INTERNAL_URL${this.url}",
      market = this.market
    )
  }

  private suspend fun getDefaultRoutes(): List<DefaultRoute> =
    defaultRoutesCache.get(CACHE_KEY_STUB).await().also { routes ->
      routes
        .groupBy { it.platform }
        .forEach { (platform, routes) ->
          val countries = routes.flatMap { it.allowedCountries }
          if (countries.size != countries.toSet().size) {
            val duplicates = countries
              .groupBy { it }
              .filter { it.value.size != 1 }
              .flatMap { it.value }
              .toSet()
            logger().alert("Countries $duplicates exist in several default routes for platform $platform")
          }
        }
    }

  private suspend fun getCustomRoutes(): List<CustomRoute> =
    customRoutesCache.get(CACHE_KEY_STUB).await()
}

sealed class Routing {
  abstract val market: Market
  abstract val url: String
}

data class CustomRoute(
  val platform: AppPlatform,
  val allowedCountries: Set<String>,
  val minimalAppVersion: Int,
  override val market: Market,
  override val url: String,
  val trafficPercentage: Int,
) : Routing()

data class DefaultRoute(
  val platform: AppPlatform,
  val allowedCountries: Set<String>,
  override val market: Market,
  override val url: String,
) : Routing()

sealed class RoutingResult {
  data class Route(
    val market: Market,
    val redirectUrl: String
  ) : RoutingResult()

  data class RoutingException(
    val exception: Exception
  ) : RoutingResult()
}

enum class Market(val shortName: String) {
  US("us"),
  US_STAGING("us-staging"),
  AU("au"),
  GB("gb"),
  ASIA("asia"),
  LATAM("latam"),
  IOS_US("ios-us"),
  UNKNOWN("");

  companion object {
    fun valueOfOrUnknown(value: String): Market =
      Market.values().firstOrNull { it.name == value }
        ?: UNKNOWN.also { logger().alert("Unexpected value '$value' for Market") }
  }
}