package com.justplayapps.service.router.routing

import org.jetbrains.exposed.dao.id.IntIdTable

object CustomRoutesTable : IntIdTable("router.custom_routes") {
  val platform = varchar("platform", 12)
  val allowedCountries = text("allowed_countries")
  val market = varchar("market", 50)
  val url = varchar("url", 128)
  val trafficPercentage = integer("traffic_percentage")
  val minimalAppVersion = integer("minimal_app_version")
}
