package com.moregames.base.messaging.dto

import com.moregames.base.config.ServicesRegistry
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.time.Instant

@Serializable
data class NotifyReachCoinGoalTaskDto(
  val userId: String,
  @Contextual val periodStart: Instant,
  @Contextual val periodEnd: Instant,
  val minutesSinceLastCoins: Long,
) : TaskDto {
  override fun taskQueueName(): String = TASK_QUEUE_NAME
  override fun taskUri(): String = TASK_URI
  override fun destinationService(): ServicesRegistry = ServicesRegistry.PLAYTIME

  companion object {
    const val TASK_QUEUE_NAME = "notify-reach-coin-goal-tf"
    const val TASK_URI = "notify-reach-coin-goal"
  }
}