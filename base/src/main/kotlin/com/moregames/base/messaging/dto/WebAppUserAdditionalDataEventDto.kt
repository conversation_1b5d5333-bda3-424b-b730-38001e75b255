package com.moregames.base.messaging.dto

import kotlinx.serialization.Serializable

@Serializable
data class WebAppUserAdditionalDataEventDto(
  val projectName: String,
  val userId: String,
  val idfv: String?,
  val idfa: String?,
  val deviceToken: String?,
  val firebaseAppInstanceId: String?,
  val notificationEnabled: Boolean?,
  val adjustId: String?
) : CrossProjectMessageDto {

  override fun defaultPubsubTopicName(): String = TOPIC_NAME
  override fun defaultProjectName(): String = projectName

  companion object {
    const val TOPIC_NAME = "webapp-additional-user-data"
  }
}