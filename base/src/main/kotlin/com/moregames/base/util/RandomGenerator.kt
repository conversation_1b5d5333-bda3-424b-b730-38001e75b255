package com.moregames.base.util

import com.fasterxml.uuid.Generators
import com.google.inject.Inject
import java.util.*
import javax.inject.Singleton
import kotlin.random.Random

@Singleton
class RandomGenerator @Inject constructor() {
  private val uuidV7Generator = Generators.timeBasedEpochRandomGenerator() // uuid v7

  fun nextUUIDV7(): UUID = uuidV7Generator.generate()
  fun nextUUID(): String = UUID.randomUUID().toString()
  fun intRangeRandom(range: IntRange) = range.random()
  fun nextDouble(): Double = Random.nextDouble()
  fun nextDouble(from: Double, until: Double): Double = Random.nextDouble(from, until)
  fun nextInt(until: Int): Int = Random.nextInt(until)
  fun getRandom(): Random = Random
}