package com.moregames.base.util

import com.google.common.reflect.ClassPath
import io.ktor.application.*
import kotlinx.serialization.Contextual
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.modules.SerializersModuleBuilder
import kotlinx.serialization.serializer
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.*
import kotlin.reflect.KClass
import kotlin.reflect.full.createType
import kotlin.reflect.full.findAnnotation

typealias InstantAsString = @Contextual Instant
typealias BigDecimalAsString = @Contextual BigDecimal
typealias LocalDateAsString = @Contextual LocalDate
typealias LocaleAsString = @Contextual Locale

fun serializationReflectionProvider(vararg basePackages: String) = SerializationReflectionProvider(basePackages)

class SerializationReflectionProvider(private val basePackage: Array<out String>) {
  val ignored = mutableSetOf<Class<*>>()
  val sealedClasses = mutableSetOf<KClass<*>>()

  val allClasses: List<Class<*>> by lazy {
    val classPath = ClassPath.from(Application::class.java.classLoader)
    basePackage
      .flatMap { classPath.getTopLevelClassesRecursive(it) }
      .filterNot { it.name.endsWith("Test") || it.name.endsWith("TestBase") } // TODO actually these are needed only because we use static initialization in persistence test base, which is not cool. refactor.
      .filterNot { it.packageName.contains("com.moregames.base.db.extensions") }
      .mapNotNull { runCatching { it.load() }.getOrNull() } + sealedClasses.flatMap { it.sealedSubclasses }.map { it.java }
  }

  /**
   * Registers the given class as ignored, so it won't be registered as a subclass of any other class.
   * This is useful for classes that will be registered manually.
   */
  inline fun <reified T : Any> ignore() = ignored.add(T::class.java)

  /**
   * Includes sealed class subclasses into the reflection provider. Also, excludes the sealed class itself, because it's not serializable. This is useful for sealed classes substructures, e.g.
   * ```kotlin
   * @Serializable // still needs this for correct deserialization
   * sealed class TestPolymorphicMessage : MessageDto { // implements MessageDto which is polymorphic
   *   @Serializable
   *   data class TestPolymorphicMessage1(val field: String) : TestPolymorphicMessage()
   *   @Serializable
   *   data class TestPolymorphicMessage2(val field: String) : TestPolymorphicMessage()
   *   ...
   * }
   * ```
   */
  inline fun <reified T : Any> includeSealedClassSubclasses() {
    if (!T::class.isSealed) {
      throw IllegalStateException("Using registerSealedClassSubclasses for non sealed class...")
    }
    sealedClasses.add(T::class)
    ignore<T>()
  }

  /**
   * Registers all classes in the given packages that are annotated with [Serializable] as subclasses of [T].
   */
  inline fun <reified T : Any> SerializersModuleBuilder.registerSubclasses() {
    allClasses
      .filter { !ignored.contains(it) && T::class.java.isAssignableFrom(it) && T::class.java != it }
      .filter { it.kotlin.findAnnotation<Serializable>() != null }
      .map { it.kotlin as KClass<T> to serializer(it.kotlin.createType()) as KSerializer<T> }
      .forEach {
        polymorphic(T::class, it.first, it.second)
      }
  }
}
