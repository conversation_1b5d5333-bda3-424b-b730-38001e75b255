package com.moregames.base.util

import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.*

object ClientVersionsSupport {

  // region iOS
  private const val IOS_USER_CREATION_MIN_APP_VERSION = 108
  private const val IOS_HARD_UPDATE_MIN_APP_VERSION = 105
  private const val IOS_DESIRED_APP_VERSION = 115

  const val IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE = 133
  const val IOS_BALANCE_UPDATE_EXPERIENCE_MIN_APP_VERSION = 133
  const val IOS_LEGACY_NEWS_API_MIN_APP_VERSION = 134
  const val IOS_PAYMENT_PERSONAL_DATA_REMOVED_MIN_APP_VERSION = 135
  const val IOS_BIPA_NON_RESTRICTED_MIN_APP_VERSION = 135
  const val IOS_CASHOUT_BONUS_STATUS_UNUSED_APP_VERSION = 136
  const val IOS_BOOSTER_GOAL_UNUSED_APP_VERSION = 137
  const val IOS_SHOW_GOAL_MARKERS_UNUSED_VERSION = 137
  const val IOS_SHOW_COIN_GOAL_UNUSED_VERSION = 137
  // endregion

  // region Android
  private const val ANDROID_USER_CREATION_MIN_APP_VERSION = 55
  private const val ANDROID_HARD_UPDATE_MIN_APP_VERSION = 55
  private const val ANDROID_DESIRED_APP_VERSION = 67

  const val ANDROID_USE_REWARDS_UNUSED_APP_VERSION = 38
  const val ANDROID_REDEEM_INSTRUCTIONS_UNUSED_APP_VERSION = 39
  const val ANDROID_CREATE_USER_SHOW_POINTING_ARROW_UNUSED_APP_VERSION = 61
  const val ANDROID_OFFERWALL_MULTI_CONFIG_APP_VERSION = 62
  const val ANDROID_CASHOUT_FORM_TYPE_UNUSED_APP_VERSION = 62
  const val ANDROID_COINS_AMOUNT_SEPARATOR_APP_VERSION = 63
  const val ANDROID_PLAY_INTEGRITY_BLOCKING_APP_VERSION = 63
  const val ANDROID_ONLY_IMAGES_APP_VERSION = 63
  const val ANDROID_GET_USER_COIN_GOAL_STRING_UNUSED_APP_VERSION = 63
  const val ANDROID_CASHOUT_BONUS_STATUS_UNUSED_APP_VERSION = 63
  const val ANDROID_SHOW_AD_AFTER_CASHOUT_APP_VERSION = 64
  const val ANDROID_PLAYERS_ONLINE_UNUSED_APP_VERSION = 63
  const val ANDROID_MARK_VIEWED_GAMES_UNUSED_APP_VERSION = 65
  const val ANDROID_CREATE_USER_SHOW_TOP_RUNNING_BAR_UNUSED_APP_VERSION = 66

  // https://app.asana.com/0/1155692811605665/1208681781063678/f
  const val ANDROID_NUMBER_SEPARATOR_APP_VERSION = 67
  const val ANDROID_ONE_CLICK_CASHOUT_UNUSED_APP_VERSION = 65
  const val ANDROID_SMALL_OFFERS_SUBTEXT_UNUSED_APP_VERSION = 68
  const val ANDROID_CREATE_USER_USE_ANIMATION_TIMER_UNUSED_APP_VERSION = 68
  const val ANDROID_PLAY_STORE_NOTIFICATION_UNUSED_APP_VERSION = 68
  const val ANDROID_CREATE_USER_USE_GDPR_UNUSED_APP_VERSION = 68
  const val ANDROID_THREE_DOT_OPT_OUT_APP_VERSION = 68
  const val ANDROID_EVENT_MANAGER_TOP_UNUSED_MIN_APP_VERSION = 70
  const val ANDROID_CASHOUT_FACE_PRE_SCAN_BIPA_FULLSCREEN_UNUSED_APP_VERSION = 70
  // endregion

  // region games
  // endregion

  //region webapp
  const val WEB_APP_USER_CREATION_MIN_APP_VERSION = 1
  //

  fun getUserCreationMinAppVersion(appPlatform: AppPlatform) = when (appPlatform) {
    ANDROID -> ANDROID_USER_CREATION_MIN_APP_VERSION
    IOS -> IOS_USER_CREATION_MIN_APP_VERSION
    IOS_WEB -> WEB_APP_USER_CREATION_MIN_APP_VERSION
  }

  fun getHardUpdateVersion(appPlatform: AppPlatform) = when (appPlatform) {
    ANDROID -> ANDROID_HARD_UPDATE_MIN_APP_VERSION
    IOS -> IOS_HARD_UPDATE_MIN_APP_VERSION
    IOS_WEB -> WEB_APP_USER_CREATION_MIN_APP_VERSION
  }

  fun getDesiredAppVersion(appPlatform: AppPlatform) = when (appPlatform) {
    ANDROID -> ANDROID_DESIRED_APP_VERSION
    IOS -> IOS_DESIRED_APP_VERSION
    IOS_WEB -> WEB_APP_USER_CREATION_MIN_APP_VERSION
  }
}