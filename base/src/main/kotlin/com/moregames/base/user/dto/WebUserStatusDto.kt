package com.moregames.base.user.dto

import kotlinx.serialization.Serializable

@Serializable
data class WebUserStatusDto(
  val requiredDataFields: List<WebUserAdditionalData>,
  val verifications: List<WebUsersInGameVerification>,
)

enum class WebUsersInGameVerification {
  DEVICE_EXAMINATION,
  JAIL_BREAK,
}

enum class WebUserAdditionalData {
  IDFV,
  IDFA,
  FIREBASE_DEVICE_TOKEN,
  FIREBASE_APP_INSTANCE_ID,
  NOTIFICATION_STATE,
  ADJUST_ID
}