package com.moregames.base.secret

import com.google.cloud.secretmanager.v1.SecretManagerServiceClient
import com.google.cloud.secretmanager.v1.SecretVersionName
import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import com.moregames.base.util.Constants.GOOGLE_CLOUD_PROJECT_ID
import com.moregames.base.util.IoCoroutineScope
import io.ktor.util.*
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.nio.file.Files
import java.nio.file.Path
import java.util.concurrent.TimeUnit
import kotlin.io.path.inputStream
import kotlin.io.path.isRegularFile
import kotlin.io.path.nameWithoutExtension

interface SecretService {
  suspend fun secretValue(secret: Secret): String
}

class GcpApiSecretService : SecretService {

  private val secretManagerServiceClient = SecretManagerServiceClient.create()
  private val coroutineScope = IoCoroutineScope()

  private val cache: LoadingCache<Secret, Deferred<String>> = CacheBuilder
    .newBuilder()
    .expireAfterWrite(10, TimeUnit.MINUTES)
    .build(CacheLoader.from { secret ->
      coroutineScope.async { loadSecretValue(secret!!) }
    })

  override suspend fun secretValue(secret: Secret) = cache[secret].await()

  private suspend fun loadSecretValue(secret: Secret): String =
    withContext(coroutineScope.coroutineContext) {
      secretManagerServiceClient
        .accessSecretVersion(SecretVersionName.of(GOOGLE_CLOUD_PROJECT_ID, secret.key, "latest"))
    }.payload.data.toStringUtf8()
}

class K8sSecretService : SecretService {

  private val secrets: Map<String, String> = Files.walk(Path.of("/var/secrets")).filter { it.isRegularFile() && it.extension == "txt" }.map {
    it.fileName.nameWithoutExtension to it.inputStream().readAllBytes().toString(Charsets.UTF_8)
  }.toList().toMap()

  override suspend fun secretValue(secret: Secret): String {
    return secrets[secret.key] ?: throw IllegalStateException("Can't find secret ${secret.key}. Probably it's not mounted. Please refer to justplay-helm")
  }
}