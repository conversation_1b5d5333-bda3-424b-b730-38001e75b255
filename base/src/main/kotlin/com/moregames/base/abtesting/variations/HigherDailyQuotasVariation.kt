package com.moregames.base.abtesting.variations

import com.moregames.base.abtesting.TypedVariation
import com.moregames.base.abtesting.TypedVariationBase
import com.moregames.base.abtesting.base

sealed class HigherDailyQuotasVariation(private val key: String) : TypedVariation {
  data object Adjusted : HigherDailyQuotasVariation("adjusted")
  data object FiftyMore : HigherDailyQuotasVariation("50more")
  data object OneHundredMore : HigherDailyQuotasVariation("100more")
  data object TwoHundredMore : HigherDailyQuotasVariation("200more")
  data object FourHundredMore : HigherDailyQuotasVariation("400more")

  override fun getKey(): String = key

  companion object : TypedVariationBase<HigherDailyQuotasVariation> by base()
}