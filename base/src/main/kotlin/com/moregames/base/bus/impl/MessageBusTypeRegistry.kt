package com.moregames.base.bus.impl

import com.google.common.reflect.ClassPath
import com.moregames.base.bus.*
import com.moregames.base.bus.config.BusModuleConfiguration
import io.ktor.application.*
import kotlin.reflect.KClass
import kotlin.reflect.KFunction
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.jvm.kotlinFunction

class MessageBusTypeRegistry(busModuleConfiguration: BusModuleConfiguration) {
  val handlerMethods: Set<KFunction<*>> = findHandlers(busModuleConfiguration.rootPackages)

  private fun findHandlers(basePackages: List<String>): Set<KFunction<*>> {
    val classes = basePackages.flatMap { basePackage ->
      ClassPath.from(Application::class.java.classLoader)
        .getTopLevelClassesRecursive(basePackage)
        .filterNot { it.packageName.contains("com.moregames.base.db.extensions") }
        .mapNotNull { runCatching { it.load() }.getOrNull() }
    }

    val annotatedMethods = classes
      .filterNot { it.isInterface }
      .flatMap {
        it.methods.filter {
          it.isAnnotationPresent(EffectHandler::class.java)
            || it.isAnnotationPresent(MessageHandler::class.java)
        }.toList()
      }

    val incorrectMethods = annotatedMethods.filter {
      it.kotlinFunction == null
        || it.parameters.isEmpty()
        || !hasCorrectArgument(it.parameters[0].type)
    }
    if (incorrectMethods.isNotEmpty()) {
      throw IncorrectHandlerMethodsException(incorrectMethods.toSet())
    }
    return annotatedMethods.map { it.kotlinFunction!! }.toSet()
  }

  private fun hasCorrectArgument(type: Class<*>): Boolean =
    (type.isKotlinClass() && (type.kotlin.isSubclassOfAny(
      Message::class,
      Effect::class,
      AsyncEffect::class
    ))) || com.google.protobuf.Message::class.java.isAssignableFrom(type)

  private fun KClass<*>.isSubclassOfAny(vararg classes: KClass<*>): Boolean {
    return classes.any { this.isSubclassOf(it) }
  }

  private fun Class<*>.isKotlinClass() = this.isAnnotationPresent(Metadata::class.java)
}
