package com.moregames.base.bus.impl

import java.lang.reflect.Method

class IncorrectHandlerMethodsException(val incorrectMethods: Set<Method>) : Exception() {
  override val message: String
    get() = """
      |Some methods are defined incorrectly. They must have kotlin functions and should have exactly one argument of type Message or it's accessors.
      |Incorrect methods:
      ${incorrectMethods.joinToString(separator = "\n") { "|* $it" }}
    """.trimMargin()
}