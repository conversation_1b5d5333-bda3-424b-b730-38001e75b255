package com.moregames.base.grpc.client

import com.moregames.base.util.logger
import io.grpc.NameResolver
import io.grpc.NameResolverProvider
import io.grpc.internal.GrpcUtil
import java.net.URI

class KubernetesNameResolverProvider : NameResolverProvider() {
  override fun newNameResolver(targetUri: URI, args: NameResolver.Args): NameResolver? {
    if (targetUri.scheme != defaultScheme) return null

    val targetPath = targetUri.path
    val parts = targetPath.split("/")
    if (parts.size != 3) {
      throw IllegalArgumentException("Invalid target: $targetUri. Should be kubernetes://{namespace}/{service}/{port}")
    }
    logger().debug("Creating KubernetesNameResolver for target: {}", targetUri)
    return KubernetesNameResolver(targetUri.host, parts[1], parts[2].toInt(), GrpcUtil.TIMER_SERVICE)
  }

  override fun getDefaultScheme(): String = "kubernetes"
  override fun isAvailable(): Boolean = true
  override fun priority(): Int = 5 // default value from docs
}