package com.moregames.base.table

import com.fasterxml.uuid.Generators
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IdTable
import org.jetbrains.exposed.sql.Column
import java.util.*

private val uuidGenerator = Generators.timeBasedEpochRandomGenerator()

open class UuidV7PkTable(name: String, columnName: String = "id") : IdTable<UUID>(name) {
  override val id: Column<EntityID<UUID>> = uuid(columnName).clientDefault { uuidGenerator.generate() }.entityId()
}