package com.moregames.base.table

import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.insertAndGetId
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import kotlin.test.assertEquals

@ExtendWith(DatabaseExtension::class)
class UuidV7PkTableTest(
  private val database: Database,
) {

  @BeforeEach
  fun setUp() {
    transaction(database) {
      SchemaUtils.create(TestUuidV7Table)
    }
  }

  @Test
  fun `SHOULD save and find correctly`() {
    val id = transaction(database) {
      TestUuidV7Table.insertAndGetId {
        it[name] = "123"
      }.value
    }

    transaction(database) {
      TestUuidV7Table.select { TestUuidV7Table.id eq id }.single().also {
        assertEquals("123", it[TestUuidV7Table.name])
      }
    }
  }
}

object TestUuidV7Table : UuidV7PkTable("playtime.test_uuid_v7_table") {
  val name = varchar("name", 30)
}