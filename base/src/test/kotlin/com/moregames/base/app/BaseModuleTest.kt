package com.moregames.base.app

import assertk.assertThat
import assertk.assertions.isNotNull
import com.google.inject.Guice
import com.moregames.base.abtesting.AbTestingExperimentHook
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.ExperimentBase
import com.moregames.base.table.TestDataSourceProvider
import io.ktor.application.*
import io.ktor.server.testing.*
import org.junit.jupiter.api.Test
import javax.inject.Inject
import kotlin.reflect.KClass

class BaseModuleTest {
  val application: Application = withTestApplication {
    application
  }

  val testModule = object : BaseModule(application, BuildVariant.LOCAL, TestDataSourceProvider()) {
    override fun provideAbTestingHooks(): Map<ExperimentBase, KClass<out AbTestingExperimentHook>> {
      return mapOf(ClientExperiment.EARNINGS_MODEL_V2 to TestAbTestingExperimentHook::class)
    }
  }

  class TestAbTestingExperimentHook @Inject constructor() : AbTestingExperimentHook

  @Test
  fun `run module`() {
    val injector = Guice.createInjector(testModule)
    val testHookRunner = injector.getInstance(TestHookRunner::class.java)
    testHookRunner.assertEm2Hook()
  }

  class TestHookRunner @Inject constructor(
    private val hooks: Map<ExperimentBase, AbTestingExperimentHook>,
  ) {
    fun assertEm2Hook() {
      hooks[ClientExperiment.EARNINGS_MODEL_V2].also {
        assertThat(it).isNotNull()
      }
    }
  }
}