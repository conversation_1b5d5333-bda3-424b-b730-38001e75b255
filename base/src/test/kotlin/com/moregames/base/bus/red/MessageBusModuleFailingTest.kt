package com.moregames.base.bus.red

import assertk.all
import assertk.assertThat
import assertk.assertions.any
import assertk.assertions.extracting
import assertk.assertions.isEqualTo
import assertk.assertions.isInstanceOf
import com.google.inject.Guice
import com.moregames.base.bus.EffectHandler
import com.moregames.base.bus.MessageBusModule
import com.moregames.base.bus.impl.IncorrectHandlerMethodsException
import org.junit.jupiter.api.Test
import javax.inject.Inject
import kotlin.test.assertFailsWith

class TestHandler @Inject constructor() {
  @EffectHandler
  fun emptyParameterMethod() {

  }

  @EffectHandler
  fun wrongParameterTypeMethod(effect: String) {
  }
}

class MessageBusModuleFailingTest {

  @Test
  fun `SHOULD fail on initialization`() {
    val e = assertFailsWith<IncorrectHandlerMethodsException> {
      Guice.createInjector(MessageBusModule {
        rootPackages("com.moregames.base.bus.red")
      })
    }
    assertThat(e).isInstanceOf(IncorrectHandlerMethodsException::class)
      .transform { it.incorrectMethods }
      .extracting { it.name }
      .all {
        any { it.isEqualTo("emptyParameterMethod") }
        any { it.isEqualTo("wrongParameterTypeMethod") }
      }
  }
}