package com.moregames.payment.payment

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.justplayapps.proxybase.PaymentAccounts
import com.justplayapps.service.payment.proto.cashoutRejectedEvent
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.app.PaymentProviderType.*
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.Payment.PaymentStatus
import com.moregames.base.messaging.DelayedMessagePublisher
import com.moregames.base.messaging.GenericMessagePublisher
import com.moregames.base.messaging.dto.FinalizePaymentProcessTaskDto
import com.moregames.base.messaging.dto.PaymentOrderedEventDto
import com.moregames.base.messaging.dto.PaymentRejectedEventDto
import com.moregames.base.messaging.dto.PaymentSentEventDto
import com.moregames.base.paymentStub
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.payment.payment.AsyncPaymentProviderService.PaymentStatusCheckResult
import com.moregames.payment.payment.currency.CurrencyExchangeService
import com.moregames.payment.payment.donation.DonationAsyncPaymentService
import com.moregames.payment.payment.paypal.PayPalAsyncPaymentService
import com.moregames.payment.payment.tangocard.TangoCardAsyncPaymentService
import com.moregames.payment.payment.tremendous.TremendousAsyncPaymentService
import com.paypal.payouts.Currency
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoMoreInteractions
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.Instant
import java.util.Currency.getInstance

class AsyncPaymentServiceTest {
  private val paymentProcessingService: PaymentProcessingService = mock()
  private val payPalAsyncPaymentService: PayPalAsyncPaymentService = mock()
  private val tangoCardAsyncPaymentService: TangoCardAsyncPaymentService = mock()
  private val tremendousAsyncPaymentService: TremendousAsyncPaymentService = mock()
  private val donationAsyncPaymentService: DonationAsyncPaymentService = mock()
  private val genericMessagePublisher: GenericMessagePublisher = mock()
  private val delayedMessagePublisher: DelayedMessagePublisher = mock()
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()
  private val timeService: TimeService = mock()
  private val currencyExchangeService: CurrencyExchangeService = mock()
  private val messageBus: MessageBus = mock()

  private val service = AsyncPaymentService(
    paymentProcessingService = paymentProcessingService,
    payPalAsyncPaymentService = payPalAsyncPaymentService,
    tangoCardAsyncPaymentService = tangoCardAsyncPaymentService,
    tremendousAsyncPaymentService = tremendousAsyncPaymentService,
    donationAsyncPaymentService = donationAsyncPaymentService,
    genericMessagePublisher = genericMessagePublisher,
    delayedMessagePublisher = delayedMessagePublisher,
    bigQueryEventPublisher = bigQueryEventPublisher,
    timeService = timeService,
    currencyExchangeService = currencyExchangeService,
    messageBus = messageBus,
  )

  private companion object {
    val now: Instant = Instant.now()
  }

  @BeforeEach
  fun before() {
    payPalAsyncPaymentService.mock({ providerName() }, "PayPal")
    whenever(timeService.now()).thenReturn(now)
  }

  @Test
  fun `SHOULD create payment, send it to provider and mark as PENDING ON orderPayment`() {
    payPalAsyncPaymentService.mock({ orderPayment(paymentStub) }, PaymentAccounts.PAYPAL_GMC)
    runBlocking {
      service.orderPayment(paymentStub)
    }

    verifyBlocking(paymentProcessingService) { createPayment(paymentStub) }
    verifyBlocking(payPalAsyncPaymentService) { orderPayment(paymentStub) }
    verifyBlocking(paymentProcessingService) {
      markAsPending(
        paymentStub.cashoutTransactionId,
        PaymentStatus.PENDING,
        null,
        account = PaymentAccounts.PAYPAL_GMC
      )
    }
    verifyBlocking(delayedMessagePublisher) {
      publish(
        FinalizePaymentProcessTaskDto(paymentStub.cashoutTransactionId),
        timeService.now().plusSeconds(5)
      )
    }
    verifyBlocking(bigQueryEventPublisher) { publish(PaymentOrderedEventDto(paymentStub.cashoutTransactionId, now)) }
  }

  @Test
  fun `SHOULD create payment, send it to provider and mark as PENDING ON orderPayment WHEN payment is already in INITIATED state`() {
    paymentProcessingService.mock({ getPaymentStatus(paymentStub.cashoutTransactionId) }, PaymentStatus.INITIATED)

    runBlocking {
      service.orderPayment(paymentStub)
    }

    verifyBlocking(paymentProcessingService) { createPayment(paymentStub) }
    verifyBlocking(payPalAsyncPaymentService) { orderPayment(paymentStub) }
    verifyBlocking(paymentProcessingService) { markAsPending(paymentStub.cashoutTransactionId, PaymentStatus.PENDING, null, account = null) }
    verifyBlocking(delayedMessagePublisher) {
      publish(
        FinalizePaymentProcessTaskDto(paymentStub.cashoutTransactionId),
        timeService.now().plusSeconds(5)
      )
    }
    verifyBlocking(bigQueryEventPublisher) { publish(PaymentOrderedEventDto(paymentStub.cashoutTransactionId, now)) }
  }

  @ParameterizedTest
  @EnumSource(PaymentStatus::class, names = ["PENDING", "UNCLAIMED", "ON_HOLD", "COMPLETED", "REJECTED"])
  fun `SHOULD do nothing ON orderPayment WHEN transaction is already in NOT INITIATED status`(status: PaymentStatus) {
    paymentProcessingService.mock({ getPaymentStatus(paymentStub.cashoutTransactionId) }, status)

    runBlocking {
      service.orderPayment(paymentStub)
    }

    verifyBlocking(paymentProcessingService) { getPaymentStatus(paymentStub.cashoutTransactionId) }
    verifyNoMoreInteractions(paymentProcessingService)
    verifyNoInteractions(payPalAsyncPaymentService)
    verifyNoInteractions(delayedMessagePublisher)
  }

  @ParameterizedTest
  @EnumSource(PaymentStatus::class, names = ["PENDING", "UNCLAIMED", "ON_HOLD"])
  fun `SHOUD keep payment as PENDING ON checkPaymentStatus WHEN status check returns PENDING status`(status: PaymentStatus) {
    payPalAsyncPaymentService.mock(
      { checkPaymentStatus(paymentStub) },
      PaymentStatusCheckResult(status)
    )

    val actual = runBlocking {
      service.checkPaymentStatus(paymentStub)
    }

    assertThat(actual).isEqualTo(status)
    verifyBlocking(paymentProcessingService) { markAsPending(paymentStub.cashoutTransactionId, status, null, account = null) }
  }

  @ParameterizedTest
  @ValueSource(strings = ["USD", "CAD", "null"])
  fun `SHOUD change payment status, create invoice and publish event ON checkPaymentStatus WHEN status check returns COMPLETED status`(currency: String) {
    if (currency != "null") {
      currencyExchangeService.mock({
        convertAmountToUsd(BigDecimal("0.05"), getInstance(currency))
      }, BigDecimal("1.00"))
    }
    val feeUsd: BigDecimal? = when (currency) {
      "USD" -> BigDecimal("0.05")
      "CAD" -> BigDecimal("1.00")
      else -> BigDecimal.ZERO
    }
    val paymentExternalTransactionId = "paymentExternalTransactionId"
    val fee: Currency? = if (currency != "null") {
      Currency().apply {
        currency(currency)
        value("0.05")
      }
    } else {
      null
    }
    val status = PaymentStatusCheckResult(
      PaymentStatus.COMPLETED,
      fee = fee,
      paymentExternalTransactionId = paymentExternalTransactionId,
      claim = "claim",
      redeemInstructions = "redeemInstructions",
      claimLabel = "claim label",
      account = PaymentAccounts.PAYPAL_PS
    )
    payPalAsyncPaymentService.mock({ checkPaymentStatus(paymentStub) }, status)

    val actual = runBlocking {
      service.checkPaymentStatus(paymentStub)
    }

    val feeValue = if (currency == "null") BigDecimal("0.00") else BigDecimal("0.05")
    assertThat(actual).isEqualTo(PaymentStatus.COMPLETED)
    verifyBlocking(paymentProcessingService) {
      completePayment(
        cashoutTransactionId = paymentStub.cashoutTransactionId,
        paymentExternalTransactionId = paymentExternalTransactionId,
        claim = "claim",
        redeemInstructions = "redeemInstructions",
        fee = feeValue,
        claimLabel = "claim label",
        account = PaymentAccounts.PAYPAL_PS
      )
    }
    verifyBlocking(genericMessagePublisher) {
      publish(
        PaymentSentEventDto(
          paymentStub.cashoutTransactionId, feeValue, feeUsd = feeUsd, paymentStub.operationalWithholdAmount, now,
          account = PaymentAccounts.PAYPAL_PS.name, feeCurrencyCode = if (currency == "null") null else currency
        )
      )
    }
  }

  @Test
  fun `SHOULD change payment status and publish event ON checkPaymentStatus WHEN status check returns REJECTED status`() {
    val fee = Currency().currency("USD").value("0.05")
    val status = PaymentStatusCheckResult(
      PaymentStatus.REJECTED,
      fee = fee
    )
    payPalAsyncPaymentService.mock(
      { checkPaymentStatus(paymentStub) }, status
    )

    val actual = runBlocking {
      service.checkPaymentStatus(paymentStub)
    }

    assertThat(actual).isEqualTo(PaymentStatus.REJECTED)
    verifyBlocking(paymentProcessingService) { rejectPayment(paymentStub.cashoutTransactionId, null) }
    verifyBlocking(genericMessagePublisher) {
      publish(PaymentRejectedEventDto(paymentStub.cashoutTransactionId, BigDecimal("0.05"), feeUsd = BigDecimal("0.05"), BigDecimal("0.00"), now))
    }
    verifyBlocking(messageBus) {
      publish(cashoutRejectedEvent {
        this.userId = paymentStub.userId
        this.cashoutTransactionId = paymentStub.cashoutTransactionId
      })
    }
  }

  @Test
  fun `SHOULD add payment account and publish event ON checkPaymentStatus WHEN status check returns REJECTED status AND payment provider is PayPal`() {
    val fee = Currency().currency("USD").value("0.05")
    val status = PaymentStatusCheckResult(
      PaymentStatus.REJECTED,
      fee = fee,
      account = PaymentAccounts.PAYPAL_PS
    )
    payPalAsyncPaymentService.mock(
      { checkPaymentStatus(paymentStub.copy(account = PaymentAccounts.PAYPAL_PS)) }, status
    )

    val actual = runBlocking {
      service.checkPaymentStatus(paymentStub.copy(account = PaymentAccounts.PAYPAL_PS))
    }

    assertThat(actual).isEqualTo(PaymentStatus.REJECTED)
    verifyBlocking(paymentProcessingService) { rejectPayment(paymentStub.cashoutTransactionId, PaymentAccounts.PAYPAL_PS) }
    verifyBlocking(genericMessagePublisher) {
      publish(
        PaymentRejectedEventDto(
          paymentStub.cashoutTransactionId,
          BigDecimal("0.05"),
          feeUsd = BigDecimal("0.05"),
          BigDecimal("0.00"),
          now,
          PaymentAccounts.PAYPAL_PS.name
        )
      )
    }
    verifyBlocking(messageBus) {
      publish(cashoutRejectedEvent {
        this.userId = paymentStub.userId
        this.cashoutTransactionId = paymentStub.cashoutTransactionId
      })
    }
  }

  @Test
  fun `SHOULD not add payment account and publish event ON checkPaymentStatus WHEN status check returns REJECTED status AND payment provider is not PayPal`() {
    val fee = Currency().currency("USD").value("0.05")
    val status = PaymentStatusCheckResult(
      PaymentStatus.REJECTED,
      fee = fee
    )
    tangoCardAsyncPaymentService.mock(
      { checkPaymentStatus(paymentStub.copy(provider = AMAZON, account = PaymentAccounts.TANGO_JP)) }, status
    )

    val actual = runBlocking {
      service.checkPaymentStatus(paymentStub.copy(provider = AMAZON, account = PaymentAccounts.TANGO_JP))
    }

    assertThat(actual).isEqualTo(PaymentStatus.REJECTED)
    verifyBlocking(paymentProcessingService) { rejectPayment(paymentStub.cashoutTransactionId, null) }
    verifyBlocking(genericMessagePublisher) {
      publish(PaymentRejectedEventDto(paymentStub.cashoutTransactionId, BigDecimal("0.05"), feeUsd = BigDecimal("0.05"), BigDecimal("0.00"), now, null))
    }
    verifyBlocking(messageBus) {
      publish(cashoutRejectedEvent {
        this.userId = paymentStub.userId
        this.cashoutTransactionId = paymentStub.cashoutTransactionId
      })
    }
  }

  @Test
  fun `SHOULD add payment account and publish event ON checkPaymentStatus WHEN status check returns COMPLETED status AND payment provider is PayPal`() {
    val fee = Currency().currency("USD").value("0.05")
    val paymentExternalTransactionId = "paymentExternalTransactionId"
    val status = PaymentStatusCheckResult(
      PaymentStatus.COMPLETED,
      fee = fee,
      paymentExternalTransactionId = paymentExternalTransactionId,
      claim = "claim",
      redeemInstructions = "redeemInstructions",
      claimLabel = "claim label",
      account = PaymentAccounts.PAYPAL_JP
    )
    payPalAsyncPaymentService.mock({ checkPaymentStatus(paymentStub.copy(account = PaymentAccounts.PAYPAL_JP)) }, status)

    val actual = runBlocking {
      service.checkPaymentStatus(paymentStub.copy(account = PaymentAccounts.PAYPAL_JP))
    }

    val feeValue = BigDecimal(fee.value())
    assertThat(actual).isEqualTo(PaymentStatus.COMPLETED)
    verifyBlocking(paymentProcessingService) {
      completePayment(
        cashoutTransactionId = paymentStub.cashoutTransactionId,
        paymentExternalTransactionId = paymentExternalTransactionId,
        claim = "claim",
        redeemInstructions = "redeemInstructions",
        fee = feeValue,
        claimLabel = "claim label",
        account = PaymentAccounts.PAYPAL_JP,
      )
    }
    verifyBlocking(genericMessagePublisher) {
      publish(
        PaymentSentEventDto(
          paymentStub.cashoutTransactionId, feeValue, feeUsd = BigDecimal("0.05"), paymentStub.operationalWithholdAmount, now,
          account = PaymentAccounts.PAYPAL_JP.name, feeCurrencyCode = "USD"
        )
      )
    }
  }

  @Test
  fun `SHOULD add payment account and publish event ON checkPaymentStatus WHEN status check returns COMPLETED status AND payment provider is Venmo`() {
    val fee = Currency().currency("USD").value("0.05")
    val paymentExternalTransactionId = "paymentExternalTransactionId"
    val status = PaymentStatusCheckResult(
      PaymentStatus.COMPLETED,
      fee = fee,
      paymentExternalTransactionId = paymentExternalTransactionId,
      claim = "claim",
      redeemInstructions = "redeemInstructions",
      claimLabel = "claim label",
      account = PaymentAccounts.PAYPAL_JP
    )
    payPalAsyncPaymentService.mock({ checkPaymentStatus(paymentStub.copy(provider = VENMO, account = PaymentAccounts.PAYPAL_JP)) }, status)

    val actual = runBlocking {
      service.checkPaymentStatus(paymentStub.copy(provider = VENMO, account = PaymentAccounts.PAYPAL_JP))
    }

    val feeValue = BigDecimal(fee.value())
    assertThat(actual).isEqualTo(PaymentStatus.COMPLETED)
    verifyBlocking(paymentProcessingService) {
      completePayment(
        cashoutTransactionId = paymentStub.cashoutTransactionId,
        paymentExternalTransactionId = paymentExternalTransactionId,
        claim = "claim",
        redeemInstructions = "redeemInstructions",
        fee = feeValue,
        claimLabel = "claim label",
        account = PaymentAccounts.PAYPAL_JP
      )
    }
    verifyBlocking(genericMessagePublisher) {
      publish(
        PaymentSentEventDto(
          paymentStub.cashoutTransactionId, feeValue, feeUsd = BigDecimal("0.05"), paymentStub.operationalWithholdAmount, now,
          account = PaymentAccounts.PAYPAL_JP.name, feeCurrencyCode = "USD"
        )
      )
    }
  }

  @Test
  fun `SHOULD not add payment account and publish event ON checkPaymentStatus WHEN status check returns COMPLETED status AND payment provider is not PayPal`() {
    val fee = Currency().currency("USD").value("0.05")
    val paymentExternalTransactionId = "paymentExternalTransactionId"
    val status = PaymentStatusCheckResult(
      PaymentStatus.COMPLETED,
      fee = fee,
      paymentExternalTransactionId = paymentExternalTransactionId,
      claim = "claim",
      redeemInstructions = "redeemInstructions",
      claimLabel = "claim label",
    )
    tremendousAsyncPaymentService.mock(
      { checkPaymentStatus(paymentStub.copy(provider = TREMENDOUS_MONETARY, account = PaymentAccounts.TREMENDOUS_JP)) },
      status
    )

    val actual = runBlocking {
      service.checkPaymentStatus(paymentStub.copy(provider = TREMENDOUS_MONETARY, account = PaymentAccounts.TREMENDOUS_JP))
    }

    val feeValue = BigDecimal(fee.value())
    assertThat(actual).isEqualTo(PaymentStatus.COMPLETED)
    verifyBlocking(paymentProcessingService) {
      completePayment(
        cashoutTransactionId = paymentStub.cashoutTransactionId,
        paymentExternalTransactionId = paymentExternalTransactionId,
        claim = "claim",
        redeemInstructions = "redeemInstructions",
        fee = feeValue,
        claimLabel = "claim label",
        account = null
      )
    }
    verifyBlocking(genericMessagePublisher) {
      publish(
        PaymentSentEventDto(
          paymentStub.cashoutTransactionId,
          feeValue,
          feeUsd = BigDecimal("0.05"),
          paymentStub.operationalWithholdAmount,
          now,
          feeCurrencyCode = "USD"
        )
      )
    }
  }

  @ParameterizedTest
  @EnumSource(PaymentProviderType::class)
  fun `SHOULD return valid service ON getPaymentProviderService`(provider: PaymentProviderType) {
    val actual = service.getPaymentProviderService(provider)

    when (provider) {
      PAYPAL, VENMO -> assertThat(actual).isEqualTo(payPalAsyncPaymentService)
      in PaymentProviderType.tangoProviders() -> assertThat(actual).isEqualTo(tangoCardAsyncPaymentService)
      in PaymentProviderType.donationProviders() -> assertThat(actual).isEqualTo(donationAsyncPaymentService)
      in PaymentProviderType.tremendousProviders() -> assertThat(actual).isEqualTo(tremendousAsyncPaymentService)
      else -> throw AssertionError("unexpected payment provider type!")
    }
  }
}