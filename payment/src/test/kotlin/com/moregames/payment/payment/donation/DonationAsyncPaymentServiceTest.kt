package com.moregames.payment.payment.donation

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.dto.Payment
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.mail.MailService
import com.moregames.base.mail.MailTemplates
import com.moregames.base.paymentStub
import com.moregames.base.util.mock
import com.moregames.payment.payment.AsyncPaymentProviderService.PaymentStatusCheckResult
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking

class DonationAsyncPaymentServiceTest {

  private val mailService: MailService = mock()
  private val encryptionService: EncryptionService = mock()

  private val service = DonationAsyncPaymentService(
    mailService = mailService,
    encryptionService = encryptionService
  )

  @BeforeEach
  fun before() {
    encryptionService.mock({ decryptOrEmpty(paymentStub.encryptedRecipientName) }, "encrypted name")
    encryptionService.mock({ decryptOrEmpty(paymentStub.encryptedRecipientEmail) }, "encrypted email")
  }

  @Test
  fun `SHOULD save transaction data and send email ON orderPayment`() {
    runBlocking { service.orderPayment(paymentStub.copy(provider = PaymentProviderType.DOCTORS_WITHOUT_BORDERS)) }

    verifyBlocking(mailService) {
      sendTemplateMail(
        senderMail = "<EMAIL>",
        senderName = "JustPlay Donation",
        receiverMail = "encrypted email",
        receiverName = "encrypted name",
        template = MailTemplates.DONATION_CONFIRMATION,
        substitutions = mapOf(
          "full_name" to "encrypted name",
          "amount" to "$${paymentStub.amount}",
          "cause_name" to "Doctors Without Borders",
          "cause_description" to "Doctors without Borders provides medical assistance to people affected by conflict, epidemics, disasters, or exclusion from healthcare. They bring medical care to millions of people caught in crises around the world. With your donation you support their tens of thousands of health professionals, logistic and administrative staff and you will help pay for millions of consultations, surgeries, treatments and vaccinations every year.",
          "cause_link" to "https://www.doctorswithoutborders.org/"
        ),
        appPlatform = null,
      )
    }
  }

  @Test
  fun `SHOULD save transaction data and send email ON orderPayment WHEN currency is CAD`() {
    runBlocking { service.orderPayment(paymentStub.copy(currencyCode = "CAD", provider = PaymentProviderType.DOCTORS_WITHOUT_BORDERS)) }

    verifyBlocking(mailService) {
      sendTemplateMail(
        senderMail = "<EMAIL>",
        senderName = "JustPlay Donation",
        receiverMail = "encrypted email",
        receiverName = "encrypted name",
        template = MailTemplates.DONATION_CONFIRMATION,
        substitutions = mapOf(
          "full_name" to "encrypted name",
          "amount" to "$${paymentStub.amount}",
          "cause_name" to "Doctors Without Borders",
          "cause_description" to "Doctors without Borders provides medical assistance to people affected by conflict, epidemics, disasters, or exclusion from healthcare. They bring medical care to millions of people caught in crises around the world. With your donation you support their tens of thousands of health professionals, logistic and administrative staff and you will help pay for millions of consultations, surgeries, treatments and vaccinations every year.",
          "cause_link" to "https://www.doctorswithoutborders.org/"
        ),
        appPlatform = null,
      )
    }
  }

  @Test
  fun `SHOULD return COMPLETED ON checkPaymentStatus`() {
    val actual = runBlocking { service.checkPaymentStatus(paymentStub) }

    assertThat(actual).isEqualTo(PaymentStatusCheckResult(Payment.PaymentStatus.COMPLETED))
  }

  @Test
  fun `SHOULD not send email ON orderPayment WHEN email is empty`() {
    encryptionService.mock({ decryptOrEmpty(paymentStub.encryptedRecipientEmail) }, "")

    runBlocking { service.orderPayment(paymentStub.copy(currencyCode = "CAD", provider = PaymentProviderType.DOCTORS_WITHOUT_BORDERS)) }

    verifyNoInteractions(mailService)
  }
}