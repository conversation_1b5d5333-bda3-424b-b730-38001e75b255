package com.moregames.payment.payment

import assertk.assertThat
import com.google.cloud.tasks.v2.CloudTasksClient
import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.util.Modules
import com.moregames.base.GuiceExtension
import com.moregames.base.app.BuildVariant
import com.moregames.base.bus.MessageBusModule
import com.moregames.base.messaging.auth.AuthService
import com.moregames.base.secret.SecretService
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.table.TestDataSourceProvider
import com.moregames.payment.app.ApiManager
import com.moregames.payment.app.CoreModule
import io.ktor.application.*
import io.ktor.server.testing.*
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.RegisterExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock

@ExtendWith(DatabaseExtension::class)
class ComponentTest {

  @Test
  fun `context starts up`() {
    val injector = guiceExtension.getInjector()
    println("Context starts up")
    val apiManager = injector.getInstance(ApiManager::class.java)
    assertThat { apiManager != null }
  }

  companion object {

    val application: Application = withTestApplication { application }

    @JvmField
    @RegisterExtension
    val guiceExtension = GuiceExtension(
      Modules.override(
        CoreModule(
          application = application,
          buildVariant = BuildVariant.LOCAL,
          dataSourceProvider = TestDataSourceProvider("/database-local-newInstance.properties")
        ),
        MessageBusModule {
          rootPackages("com.moregames.payment")
        }
      )
        .with(TestModule())
    ) {
      runBlocking { getInstance(ApiManager::class.java).initApi(application) }
    }
  }

  class TestModule : AbstractModule() {
    @Provides
    @Singleton
    fun secretService() = mock<SecretService> {
      onBlocking { secretValue(any()) } doReturn "**********"
    }

    @Provides
    @Singleton
    fun cloudTasksClient() = mock<CloudTasksClient>()

    @Provides
    @Singleton
    fun authService() = mock<AuthService>()
  }
}
