package com.moregames.payment.app

import com.google.inject.Inject
import com.moregames.base.app.BuildVariant
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.ktor.cronInterceptor
import com.moregames.base.messaging.auth.HttpClientAuthInterceptor
import com.moregames.base.secret.BaseSecrets
import com.moregames.base.secret.SecretService
import com.moregames.base.util.basicAuth
import com.moregames.base.util.installLogging
import com.moregames.base.util.logger
import com.moregames.payment.cron.CronController
import com.moregames.payment.payment.PaymentServiceApiController
import com.moregames.payment.payment.PaymentServiceController
import com.moregames.payment.payment.TaskController
import io.ktor.application.*
import io.ktor.auth.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*

private const val REPORTING_AUTH_CONFIG_NAME = "reporting-api"
private const val REPORTING_BASIC_AUTH_REALM = "reporting server"
private const val REPORTING_BASIC_AUTH_USER = "reporting"

private const val PAYMENT_AUTH_CONFIG_NAME = "payment-api"
private const val PAYMENT_BASIC_AUTH_REALM = "payment server"
private const val PAYMENT_BASIC_AUTH_USER = "paymentUser"

class ApiManager @Inject constructor(
  private val buildVariant: BuildVariant,
  private val paymentServiceController: PaymentServiceController,
  private val paymentServiceApiController: PaymentServiceApiController,
  private val taskController: TaskController,
  private val secretService: SecretService,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val cronController: CronController,
  private val httpClientAuthInterceptor: HttpClientAuthInterceptor,
) {

  suspend fun initApi(application: Application) {
    application.installLogging(buildVariant, featureFlagsFacade)
    initAuthentication(application)
    initRouting(application)
  }

  private suspend fun initAuthentication(application: Application) {
    val reportingPassword = secretService.secretValue(BaseSecrets.BASIC_AUTH_REPORTING)
    val paymentUserPassword = secretService.secretValue(BaseSecrets.BASIC_AUTH_PAYMENT)
    application.authentication {
      basicAuth(REPORTING_AUTH_CONFIG_NAME, REPORTING_BASIC_AUTH_REALM, REPORTING_BASIC_AUTH_USER, reportingPassword)
      basicAuth(PAYMENT_AUTH_CONFIG_NAME, PAYMENT_BASIC_AUTH_REALM, PAYMENT_BASIC_AUTH_USER, paymentUserPassword)
    }
  }


  private fun initRouting(application: Application) {
    application.routing {
      install(StatusPages, statusPageConfig())
      initServiceRoute()
      initServiceApiRoute()
      initCronRoute()
      taskController.initRouting(this)
      initLifecycle()
    }
  }

  private fun Routing.initLifecycle() {
    get("/_ah/start") {
      // TODO: health check
      call.respond(HttpStatusCode.OK)
    }
    get("/_ah/stop") {
      // TODO: trigger shutdown
      call.respond(HttpStatusCode.OK)
    }
    get("/_ah/warmup") {
      call.respond(HttpStatusCode.OK)
    }
  }

  private fun Route.initServiceRoute() {
    route("/service") {
      with(httpClientAuthInterceptor) { installInterceptor() }
      paymentServiceController.initRouting(this)
    }
  }

  private fun Route.initServiceApiRoute() {
    authenticate(PAYMENT_AUTH_CONFIG_NAME) {
      route("/service-api") {
        paymentServiceApiController.initApiRouting(this)
      }
    }
  }

  private fun Routing.initCronRoute() {
    route("/cron/") {
      cronInterceptor(buildVariant)
      cronController.startRouting(this)
    }
  }

  private fun statusPageConfig(): StatusPages.Configuration.() -> Unit = {
    exception<Throwable> { e ->
      logger().error("Error", e)
      call.respondText(e.buildVariantMessageOrDefault("Unknown server error"), ContentType.Text.Plain, HttpStatusCode.InternalServerError)
    }
  }

  private fun Throwable.buildVariantMessageOrDefault(default: String = "") =
    if (buildVariant == BuildVariant.PRODUCTION) default else message ?: default
}
