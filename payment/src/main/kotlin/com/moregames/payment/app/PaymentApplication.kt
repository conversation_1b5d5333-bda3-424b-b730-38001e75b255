package com.moregames.payment.app

import com.google.inject.Guice
import com.justplayapps.service.payment.proto.PaymentEvents
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.ModulesContainer
import com.moregames.base.app.ModulesInitializer
import com.moregames.base.bus.MessageBusModule
import com.moregames.base.util.logger
import com.moregames.payment.payment.CashoutRequestCreatedMessageSubscriber
import com.moregames.payment.util.getDefaultJsonConverter
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.serialization.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.ExperimentalSerializationApi

// Entry Point of the application as defined in resources/application.conf.
// @see https://ktor.io/servers/configuration.html#hocon-file
fun Application.main() {
// This adds Date and Server headers to each response, and allows custom additional headers
  install(DefaultHeaders)

  val buildVariant = BuildVariant.byKey(System.getenv("NODE_ENV"))

  val paymentApplication = PaymentApplication(ModulesContainer(this, buildVariant, paymentModules))
  paymentApplication.onStart()

  environment.monitor.subscribe(ApplicationStopped) {
    paymentApplication.onShutdown()
  }
}

val paymentModules: ModulesInitializer = {
  +CoreModule(application, buildVariant)
  +MessageBusModule {
    rootPackages("com.moregames.payment")

    val cashoutRejectedEventRoute by publishEndpoint("cashout-rejected")
    routing {
      route<PaymentEvents.CashoutRejectedEvent>() to cashoutRejectedEventRoute
    }
  }
}

class PaymentApplication(val modulesContainer: ModulesContainer) {
  private val injector = Guice.createInjector(modulesContainer)

  private lateinit var cashoutRequestCreatedMessageSubscriber: CashoutRequestCreatedMessageSubscriber


  @OptIn(ExperimentalSerializationApi::class)
  fun onStart() {
    logger().info("Starting application with build variant ${injector.getInstance(BuildVariant::class.java)}")

    modulesContainer.application.install(ContentNegotiation) {
      json(
        json = getDefaultJsonConverter()
      )
    }

    runBlocking { injector.getInstance(ApiManager::class.java).initApi(modulesContainer.application) }
    cashoutRequestCreatedMessageSubscriber = injector.getInstance(CashoutRequestCreatedMessageSubscriber::class.java)
    cashoutRequestCreatedMessageSubscriber.startup()
  }

  fun onShutdown() {
    cashoutRequestCreatedMessageSubscriber.shutdown()
  }
}
