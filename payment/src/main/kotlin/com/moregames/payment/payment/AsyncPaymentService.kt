package com.moregames.payment.payment

import com.google.inject.Inject
import com.justplayapps.service.payment.proto.cashoutRejectedEvent
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.app.PaymentProviderType.*
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.Payment
import com.moregames.base.dto.Payment.Companion.PENDING_STATUSES
import com.moregames.base.messaging.DelayedMessagePublisher
import com.moregames.base.messaging.GenericMessagePublisher
import com.moregames.base.messaging.dto.FinalizePaymentProcessTaskDto
import com.moregames.base.messaging.dto.PaymentOrderedEventDto
import com.moregames.base.messaging.dto.PaymentRejectedEventDto
import com.moregames.base.messaging.dto.PaymentSentEventDto
import com.moregames.base.util.TimeService
import com.moregames.payment.payment.currency.CurrencyExchangeService
import com.moregames.payment.payment.donation.DonationAsyncPaymentService
import com.moregames.payment.payment.paypal.PayPalAsyncPaymentService
import com.moregames.payment.payment.tangocard.TangoCardAsyncPaymentService
import com.moregames.payment.payment.tremendous.TremendousAsyncPaymentService
import java.math.BigDecimal
import java.util.Currency.getInstance

// See: https://docs.google.com/drawings/d/1C08B4j4d0qjhhw-jTHYjkqCEnpP3JKStRs5CHklXC-Q/edit?usp=sharing
class AsyncPaymentService @Inject constructor(
  private val paymentProcessingService: PaymentProcessingService,
  private val payPalAsyncPaymentService: PayPalAsyncPaymentService,
  private val tangoCardAsyncPaymentService: TangoCardAsyncPaymentService,
  private val tremendousAsyncPaymentService: TremendousAsyncPaymentService,
  private val donationAsyncPaymentService: DonationAsyncPaymentService,
  private val genericMessagePublisher: GenericMessagePublisher,
  private val delayedMessagePublisher: DelayedMessagePublisher,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val timeService: TimeService,
  private val currencyExchangeService: CurrencyExchangeService,
  private val messageBus: MessageBus,
) {

  suspend fun orderPayment(payment: Payment) {
    val paymentStatus = paymentProcessingService.getPaymentStatus(payment.cashoutTransactionId)
    if (paymentStatus != null && paymentStatus != Payment.PaymentStatus.INITIATED) {
      return // avoid processing if payment was already ordered.
    }
    paymentProcessingService.createPayment(payment)
    val account = getPaymentProviderService(payment.provider).orderPayment(payment)
    delayedMessagePublisher.publish(
      FinalizePaymentProcessTaskDto(payment.cashoutTransactionId),
      timeService.now().plusSeconds(5)
    )
    bigQueryEventPublisher.publish(
      PaymentOrderedEventDto(payment.cashoutTransactionId, timeService.now())
    )
    paymentProcessingService.markAsPending(
      payment.cashoutTransactionId,
      Payment.PaymentStatus.PENDING,
      null,
      account
    ) // Note: this line should be the last line of this method
  }

  suspend fun checkPaymentStatus(payment: Payment): Payment.PaymentStatus {
    val status = getPaymentProviderService(payment.provider).checkPaymentStatus(payment)
    val fee = BigDecimal(status.fee?.value() ?: "0.00")
    val account = if (payment.provider == PAYPAL || payment.provider == VENMO) {
      status.account
    } else null
    val feeUsd = status.fee?.let {
      if (it.currency() == "USD") fee
      else currencyExchangeService.convertAmountToUsd(fee, getInstance(it.currency()))
    } ?: BigDecimal.ZERO
    when (status.paymentStatus) {
      in PENDING_STATUSES ->
        paymentProcessingService.markAsPending(
          cashoutTransactionId = payment.cashoutTransactionId,
          status = status.paymentStatus,
          paymentExternalTransactionId = status.paymentExternalTransactionId,
          account = account
        )

      Payment.PaymentStatus.COMPLETED -> {
        paymentProcessingService.completePayment(
          cashoutTransactionId = payment.cashoutTransactionId,
          paymentExternalTransactionId = status.paymentExternalTransactionId,
          claim = status.claim,
          redeemInstructions = status.redeemInstructions,
          fee = fee,
          claimLabel = status.claimLabel,
          account = account
        )
        genericMessagePublisher.publish(
          PaymentSentEventDto(
            payment.cashoutTransactionId,
            fee,
            feeUsd,
            payment.operationalWithholdAmount,
            timeService.now(),
            account = account?.name,
            feeCurrencyCode = status.fee?.currency(),
          )
        )
      }

      Payment.PaymentStatus.REJECTED -> {
        paymentProcessingService.rejectPayment(payment.cashoutTransactionId, account = account)
        genericMessagePublisher.publish(
          PaymentRejectedEventDto(
            payment.cashoutTransactionId,
            fee,
            feeUsd,
            payment.operationalWithholdAmount,
            timeService.now(),
            account = account?.name
          )
        )
        messageBus.publish(
          cashoutRejectedEvent {
            this.userId = payment.userId
            this.cashoutTransactionId = payment.cashoutTransactionId
          }
        )
      }

      else -> throw IllegalStateException("Unexpected status '${status.paymentStatus}' of payment '$payment'")
    }
    return status.paymentStatus
  }

  fun getPaymentProviderService(provider: PaymentProviderType): AsyncPaymentProviderService =
    when (provider) {
      PAYPAL, VENMO -> payPalAsyncPaymentService

      AMAZON, BEST_BUY, BURGER_KING, GOOGLE_PLAY, TARGET, WALMART, REWARD_LINK -> tangoCardAsyncPaymentService

      DOCTORS_WITHOUT_BORDERS, CLEAN_AIR_TASK_FORCE, THE_HUNGER_PROJECT, UKRAINE -> donationAsyncPaymentService

      TREMENDOUS_MONETARY, TREMENDOUS_GIFT_CARD, TREMENDOUS_PAYPAL,
      TREMENDOUS_VENMO, TREMENDOUS_ACH, TREMENDOUS_AMAZON, TREMENDOUS_GOOGLE_PLAY,
      TREMENDOUS_WALMART, TREMENDOUS_TARGET, TREMENDOUS_BURGER_KING, TREMENDOUS_UBER -> tremendousAsyncPaymentService
    }
}