package com.justplayapps.service.notifications

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isFalse
import assertk.assertions.isNull
import assertk.assertions.isTrue
import com.justplayapps.service.notifications.table.DeviceTokensTable
import com.justplayapps.service.notifications.table.DeviceTokensTable.appPlatform
import com.justplayapps.service.notifications.table.DeviceTokensTable.deviceToken
import com.justplayapps.service.notifications.table.DeviceTokensTable.isDisabled
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.PlatformDeviceTokenDto
import com.moregames.base.table.DatabaseExtension
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.util.*

@ExtendWith(DatabaseExtension::class)
class DeviceTokenPersistenceServiceTest(
  private val database: Database
) {

  private lateinit var service: DeviceTokenPersistenceService

  @BeforeEach
  fun before() {
    service = DeviceTokenPersistenceService(database)
  }

  @Test
  fun `SHOULD store device token ON storeToken`() {
    val userId = UUID.randomUUID().toString()

    runBlocking {
      service.storeToken(userId, PlatformDeviceTokenDto("deviceToken", AppPlatform.ANDROID))
    }

    transaction(database) {
      DeviceTokensTable.select { DeviceTokensTable.userId eq userId }.first()
        .let {
          assertThat(it[deviceToken]).isEqualTo("deviceToken")
          assertThat(it[appPlatform]).isEqualTo(AppPlatform.ANDROID.name)
          assertThat(it[isDisabled]).isFalse()
        }
    }
  }

  @Test
  fun `SHOULD store new device token AND reset disable flag ON storeToken`() {
    val userId = UUID.randomUUID().toString()
    transaction(database) {
      DeviceTokensTable.insert {
        it[DeviceTokensTable.userId] = userId
        it[deviceToken] = "deviceToken1"
        it[isDisabled] = true
        it[appPlatform] = AppPlatform.ANDROID.name
      }
    }

    runBlocking {
      service.storeToken(userId, PlatformDeviceTokenDto("deviceToken2", AppPlatform.IOS))
    }

    transaction(database) {
      DeviceTokensTable.select { DeviceTokensTable.userId eq userId }.first()
        .let {
          assertThat(it[deviceToken]).isEqualTo("deviceToken2")
          assertThat(it[appPlatform]).isEqualTo(AppPlatform.IOS.name)
          assertThat(it[isDisabled]).isFalse()
        }
    }
  }

  @Test
  fun `SHOULD return non-disabled device token ON getToken`() {
    val userId1 = UUID.randomUUID().toString()
    val userId2 = UUID.randomUUID().toString() // disabled
    val userId3 = UUID.randomUUID().toString() // no token
    val userId4 = UUID.randomUUID().toString() // isOS token

    transaction(database) {
      DeviceTokensTable.insert {
        it[userId] = userId1
        it[deviceToken] = "deviceToken1"
        it[isDisabled] = false
      }
      DeviceTokensTable.insert {
        it[userId] = userId2
        it[deviceToken] = "deviceToken2"
        it[isDisabled] = true
      }
      DeviceTokensTable.insert {
        it[userId] = userId4
        it[deviceToken] = "deviceToken4"
        it[isDisabled] = false
        it[appPlatform] = AppPlatform.IOS.name
      }
    }

    runBlocking {
      assertThat(service.getToken(userId1)).isEqualTo(PlatformDeviceTokenDto("deviceToken1", AppPlatform.ANDROID))
      assertThat(service.getToken(userId2)).isNull()
      assertThat(service.getToken(userId3)).isNull()
      assertThat(service.getToken(userId4)).isEqualTo(PlatformDeviceTokenDto("deviceToken4", AppPlatform.IOS))
    }
  }

  @Test
  fun `SHOULD disable device token ON disableToken`() {
    val userId = UUID.randomUUID().toString()
    val deviceToken = UUID.randomUUID().toString()

    transaction(database) {
      DeviceTokensTable.insert {
        it[DeviceTokensTable.userId] = userId
        it[DeviceTokensTable.deviceToken] = deviceToken
        it[isDisabled] = false
      }
    }

    runBlocking {
      service.disableToken(userId, deviceToken)
    }

    transaction(database) {
      DeviceTokensTable.select { DeviceTokensTable.userId eq userId }.first()
        .let {
          assertThat(it[isDisabled]).isTrue()
        }
    }
  }
}