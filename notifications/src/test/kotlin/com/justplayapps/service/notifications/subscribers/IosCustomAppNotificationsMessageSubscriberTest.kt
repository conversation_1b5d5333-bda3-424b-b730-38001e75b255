package com.justplayapps.service.notifications.subscribers

import com.justplayapps.service.notifications.FirebaseMessagingService
import com.moregames.base.messaging.IosCustomAppNotificationApiDto
import com.moregames.base.messaging.customnotification.CustomNotificationDto
import com.moregames.base.messaging.customnotification.CustomNotificationSize.LARGE
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking

class IosCustomAppNotificationsMessageSubscriberTest {

  private val firebaseMessagingService: FirebaseMessagingService = mock()

  private val service = IosCustomAppNotificationsMessageSubscriber(firebaseMessagingService)

  @Test
  fun `SHOULD send message to firebaseMessagingService ON ios-custom-app-notification subscriber call`() {
    val customNotificationDto = CustomNotificationDto(
      backgroundColor = "#A090B0",
      notificationId = "22a44fc9-a9bf-4817-bbcf-4e51ee9ea590",
      size = LARGE,
      title = "Notification title",
      label = "label"
    )
    val event = IosCustomAppNotificationApiDto(
      userId = "e599838c-42de-4d7f-a52f-6bf0541f754e",
      notification = customNotificationDto,
      collapseId = "collapseId",
      collapseKey = "collapseKey",
      userVisible = false
    )

    runBlocking {
      service.handle(event)
    }

    verifyBlocking(firebaseMessagingService) { send(event) }
  }
}