package com.justplayapps.service.notifications

import com.google.inject.Inject
import com.justplayapps.service.notifications.util.getDefaultJsonConverter
import com.moregames.base.app.BuildVariant
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.secret.BaseSecrets
import com.moregames.base.secret.SecretService
import com.moregames.base.util.basicAuth
import com.moregames.base.util.installLogging
import io.ktor.application.*
import io.ktor.auth.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import io.ktor.serialization.*

private const val NOTIFICATIONS_AUTH_CONFIG_NAME = "notifications-api"
private const val NOTIFICATIONS_BASIC_AUTH_REALM = "notifications server"
private const val NOTIFICATIONS_BASIC_AUTH_USER = "notifications"

class ApiManager @Inject constructor(
  private val buildVariant: BuildVariant,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val secretService: SecretService,
) {
  suspend fun initApi(application: Application) {
    application.installLogging(buildVariant, featureFlagsFacade)

    application.install(ContentNegotiation) {
      json(
        json = getDefaultJsonConverter()
      )
    }
    initAuthentication(application)
    application.routing {
      lifecycle()
    }
  }

  private fun Routing.lifecycle() {
    get("/_ah/warmup") {
      call.respond(HttpStatusCode.OK)
    }
  }

  private suspend fun initAuthentication(application: Application) {
    val notificationsPassword = secretService.secretValue(BaseSecrets.BASIC_AUTH_NOTIFICATIONS)
    application.authentication {
      basicAuth(NOTIFICATIONS_AUTH_CONFIG_NAME, NOTIFICATIONS_BASIC_AUTH_REALM, NOTIFICATIONS_BASIC_AUTH_USER, notificationsPassword)
    }
  }
}