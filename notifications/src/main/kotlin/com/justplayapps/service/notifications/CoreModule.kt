package com.justplayapps.service.notifications

import com.google.auth.oauth2.GoogleCredentials
import com.google.firebase.FirebaseApp
import com.google.firebase.FirebaseOptions
import com.google.firebase.messaging.FirebaseMessaging
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.justplayapps.service.notifications.util.getDefaultJsonConverter
import com.moregames.base.app.BaseModule
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.DataSourceProvider
import com.moregames.base.app.DefaultDataSourceProvider
import com.moregames.base.secret.Secret
import com.moregames.base.secret.SecretService
import com.moregames.base.util.Constants.ANDROID_FIREBASE_ACCOUNT
import com.moregames.base.util.Constants.IOS_FIREBASE_ACCOUNT
import io.ktor.application.*

class CoreModule(
  application: Application,
  buildVariant: BuildVariant,
  dataSourceProvider: DataSourceProvider = DefaultDataSourceProvider(),
) : BaseModule(application, buildVariant, dataSourceProvider) {
  companion object {
    const val ANDROID_FIREBASE_APP_ID = "playspot-server-dev"
    const val IOS_FIREBASE_APP_ID = "justplay-ios"
  }

  @Provides
  @Singleton
  fun database(secretService: SecretService) = connectToDatabase(DbConfigs.NOTIFICATIONS, secretService)

  @Provides
  fun jsonConverter() = getDefaultJsonConverter()

  @Provides
  @Singleton
  @Named(ANDROID_FIREBASE_APP_ID)
  fun firebaseMessaging(): FirebaseMessaging =
    FirebaseApp.initializeApp(
      FirebaseOptions.builder()
        .setProjectId(ANDROID_FIREBASE_APP_ID)
        .setServiceAccountId(ANDROID_FIREBASE_ACCOUNT)
        .setCredentials(GoogleCredentials.getApplicationDefault())
        .build(),
      ANDROID_FIREBASE_APP_ID
    ).let {
      FirebaseMessaging.getInstance(it)
    }

  @Provides
  @Singleton
  @Named(IOS_FIREBASE_APP_ID)
  fun iosFirebaseMessaging(): FirebaseMessaging =
    FirebaseApp.initializeApp(
      FirebaseOptions.builder()
        .setProjectId(IOS_FIREBASE_APP_ID)
        .setServiceAccountId(IOS_FIREBASE_ACCOUNT)
        .setCredentials(GoogleCredentials.getApplicationDefault())
        .build(),
      IOS_FIREBASE_APP_ID
    ).let {
      FirebaseMessaging.getInstance(it)
    }

  private enum class DbConfigs(override val username: String, override val password: Secret, override val maximumPoolSize: Int) : DbConfig {
    NOTIFICATIONS("notifications", NotificationsSecrets.DATABASE_USER_NOTIFICATIONS, 10)
  }

  private enum class NotificationsSecrets(override val key: String) : Secret {
    DATABASE_USER_NOTIFICATIONS("database-user-notifications")
  }
}