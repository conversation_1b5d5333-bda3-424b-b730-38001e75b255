package com.justplayapps.service.rewarding

import com.google.inject.Injector
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.justplayapps.service.rewarding.utils.defaultJsonConverter
import com.moregames.base.app.BaseModule
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.DataSourceProvider
import com.moregames.base.app.DefaultDataSourceProvider
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.secret.SecretService
import io.ktor.application.*
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.Database
import redis.clients.jedis.JedisPool
import redis.clients.jedis.JedisPoolConfig

class CoreModule(
  application: Application,
  buildVariant: BuildVariant,
  dataSourceProvider: DataSourceProvider = DefaultDataSourceProvider(),
) : BaseModule(application, buildVariant, dataSourceProvider) {

  companion object {

  }

  @Provides
  @Singleton
  fun jsonConverter(): Json = defaultJsonConverter

  @Provides
  @Singleton
  @Named("playtime")
  fun playtimeDatabase(secretService: SecretService): Database = connectToDatabase(DbConfigs.PLAYTIME, secretService, "playtime")

  @Provides
  @Singleton
  @Named("rewarding")
  fun rewardingDatabase(secretService: SecretService): Database = connectToDatabase(DbConfigs.REWARDING, secretService, "rewarding")

  @Provides
  @Singleton
  fun databases(injector: Injector) =
    injector.allBindings.keys.filter { Database::class.java.isAssignableFrom(it.typeLiteral.rawType) }.map { injector.getInstance(it) as Database }.toSet()

  @Provides
  @Singleton
  fun jedisPool(applicationConfig: ApplicationConfig): JedisPool {
    val jedisPoolConfig = JedisPoolConfig()
    jedisPoolConfig.maxTotal = 200
    return JedisPool(jedisPoolConfig, applicationConfig.redisHost, applicationConfig.redisPort)
  }

  private enum class DbConfigs(override val username: String, override val password: RewardingServiceSecrets, override val maximumPoolSize: Int) : DbConfig {
    PLAYTIME("app_engine", RewardingServiceSecrets.DATABASE_USER_PLAYTIME, 40),
    REWARDING("rewarding", RewardingServiceSecrets.DATABASE_USER_REWARDING, 40)
  }
}
