dependencies {
  implementation project(':base')
  implementation project(':rewarding-service:proto')

  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:$kotlinx_coroutines_version"
  implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"
  implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
  implementation "org.jetbrains.kotlinx:kotlinx-serialization-json:1.0.1"

  implementation "io.ktor:ktor-server-servlet:$ktor_version"
  implementation "io.ktor:ktor-html-builder:$ktor_version"
  implementation "io.ktor:ktor-auth:$ktor_version"
  implementation "io.ktor:ktor-serialization:$ktor_version"
  implementation "io.ktor:ktor-client-apache:$ktor_version"
  implementation "io.ktor:ktor-client-cio:$ktor_version"
  implementation "io.ktor:ktor-client-logging:$ktor_version"
  implementation "io.ktor:ktor-client-serialization:$ktor_version"
  implementation "io.ktor:ktor-server-netty:$ktor_version"

  implementation "javax.servlet:javax.servlet-api:4.0.1"

  implementation "org.jetbrains.exposed:exposed-core:$exposed_version"
  implementation "org.jetbrains.exposed:exposed-jdbc:$exposed_version"
  implementation "org.jetbrains.exposed:exposed-java-time:$exposed_version"
  implementation "com.google.cloud.sql:mysql-socket-factory-connector-j-8:$mysql_socket_factory_version"
  implementation "mysql:mysql-connector-java:$mysql_connector_version"
  implementation "org.liquibase:liquibase-core:$liquibase_version"

  implementation "com.zaxxer:HikariCP:$hikari_version"

  implementation "ch.qos.logback:logback-classic:$logback_version"

  implementation "org.apache.commons:commons-csv:1.8"
  implementation "org.apache.httpcomponents:httpclient:4.5.12"

  implementation "com.google.api:gax-grpc:1.57.0"
  implementation "com.google.apis:google-api-services-appengine:v1-rev20201201-1.31.0"
  implementation "com.google.apis:google-api-services-playintegrity:v1-rev20240101-2.0.0"
  implementation "com.google.auth:google-auth-library-oauth2-http:0.22.2"
  implementation("com.github.papsign:Ktor-OpenAPI-Generator:0.3-beta.3") {
    exclude group: 'org.webjars', module: 'swagger-ui'
  }
  implementation "org.webjars:swagger-ui:5.17.14"
  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:$kotlinx_coroutines_version"
  implementation "redis.clients:jedis:3.7.0"
  implementation "com.ibm.icu:icu4j:73.2"
  implementation "io.github.resilience4j:resilience4j-kotlin:1.7.0"
  implementation "io.github.resilience4j:resilience4j-circuitbreaker:1.7.0"

  // ios device attestations
  implementation "ch.veehait.devicecheck:devicecheck-appattest:0.9.6"
  implementation "org.bouncycastle:bcpkix-jdk18on:1.73"
  implementation "joda-time:joda-time:2.10.6"

  //uid2
  implementation("com.uid2:uid2-client:4.6.0")

  testImplementation(testFixtures(project(":base")))
  testImplementation "net.bytebuddy:byte-buddy:1.10.18"
  testImplementation "net.bytebuddy:byte-buddy-agent:1.10.18"
  testImplementation "org.mockito:mockito-inline:5.2.0"
  testImplementation "org.mockito:mockito-junit-jupiter:5.12.0"
  testImplementation 'org.mockito.kotlin:mockito-kotlin:5.2.0'
  testImplementation "org.junit.jupiter:junit-jupiter:5.8.2"
  testImplementation "com.willowtreeapps.assertk:assertk-jvm:0.28.1"
  testImplementation "org.jetbrains.kotlin:kotlin-test-junit5:$kotlin_version"
  testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$kotlinx_coroutines_version"
  testImplementation "io.ktor:ktor-server-test-host:$ktor_version"
  testImplementation "io.ktor:ktor-client-mock:$ktor_version"
  testImplementation "org.awaitility:awaitility-kotlin:4.0.3"
  testImplementation "com.madgag.spongycastle:bcpkix-jdk15on:********"
  testImplementation "com.redis:testcontainers-redis:2.0.1"
  testImplementation "org.testcontainers:junit-jupiter:$testcontainers_version"
  testImplementation "org.liquibase:liquibase-core:$liquibase_version"
}
