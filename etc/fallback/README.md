Fallback files are placed as static files at AWS S3: 

For Android: https://us-east-2.console.aws.amazon.com/s3/buckets/fallback.justplayapi.com?region=us-east-2&bucketType=general&tab=objects

For IOS: https://us-east-2.console.aws.amazon.com/s3/buckets/jp-fallback?region=us-east-2&bucketType=general&tab=objects

/etc/fallback/fallback-android.json - contains all of android games we should to show when our API does not work (all markets).     
/etc/fallback/fallback-ios.json - contains all of ios games we should to show when our API does not work (all markets).

to get actual list of games use two new endpoints on QaDashboard:

/qa/qa/fallback/android - android games     
/qa/qa/fallback/ios - ios games

We can use it on US market because there are no specific game list for each market. We will show the same games we show for US without 'word' games

after endpoints invoked - just put result to the json file and upload to the AWS

To check that files were refreshed - just go to the link:       
Android - https://s3.us-east-2.amazonaws.com/fallback.justplayapi.com/fallback.json     
IOS - https://jp-fallback.s3.us-east-2.amazonaws.com/fallback.json      

If they are not refreshed - just invalidate cache:
CloudFront->Distributions->select distribution(create if it is not exist)->Invalidations Tab
-> Create invalidation button -> Object paths(use /*)->push Create invalidation     
direct link: https://us-east-1.console.aws.amazon.com/cloudfront/v4/home?region=us-east-2#/distributions/EDOQ9F89F5PN8/invalidations

Then check files again, they will be refreshed

login: <EMAIL>
password: *****

Also, there is AWS Route 53 to manage fallback.justplayapi.com subdomain
(and there are NS records for "fallback" subdomain that target to AWS in GoDaddy DNS records)


TODO: move images from GCloud to AWS and add translations (https://app.asana.com/0/1155692811605665/1203999803432329/f)