package com.justplayapps.liquibase.runner

import com.moregames.base.secret.GcpApiSecretService
import com.moregames.base.secret.Secret
import kotlinx.coroutines.runBlocking
import liquibase.Contexts
import liquibase.Liquibase
import liquibase.database.DatabaseFactory
import liquibase.database.jvm.JdbcConnection
import liquibase.resource.FileSystemResourceAccessor
import java.nio.file.Paths
import java.sql.DriverManager
import kotlin.system.exitProcess

fun main(): Unit = runBlocking {
  try {
    Config // checking configuration init before further initialization
  } catch (e: Throwable) {
    e.printStackTrace()
    exitProcess(1)
  }

  val mysqlConnection = "*******************************************************************************************************************************=${Config.instanceName}&socketFactory=com.google.cloud.sql.mysql.SocketFactory"
  val postgresqlConnection = "**************************************************=${Config.instanceName}&socketFactory=com.google.cloud.sql.postgres.SocketFactory"

  val url = if (Config.isPostgresql) postgresqlConnection else mysqlConnection

  val password = Config.password ?: GcpApiSecretService().secretValue(SimpleSecret(Config.passwordSecretName))

  DriverManager.getConnection(
    url,
    Config.username,
    password,
  ).use { conn ->
    val database = DatabaseFactory.getInstance().findCorrectDatabaseImplementation(JdbcConnection(conn))
    database.defaultSchemaName = Config.schema

    Liquibase(Config.changelogFile, FileSystemResourceAccessor(Paths.get(Config.changelogBaseDir).toFile()), database).use {
      it.update(Contexts(Config.contexts))
    }
  }
}

class SimpleSecret(override val key: String) : Secret