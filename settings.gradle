pluginManagement {
  plugins {
    id "com.github.ben-manes.versions" version '0.41.0'
    id 'org.jetbrains.kotlin.jvm' version "${kotlin_version}"
    id 'org.jetbrains.kotlin.kapt' version "${kotlin_version}"
    id 'com.google.cloud.tools.appengine' version "${appengine_version}"
    id 'com.github.johnrengelman.shadow' version '7.0.0'
    id 'com.google.cloud.tools.jib' version '3.4.4'
    id 'com.bmuschko.docker-remote-api' version '9.4.0'
  }

  repositories {
    gradlePluginPortal()
    mavenCentral()
  }

  resolutionStrategy {
    eachPlugin {
      if (requested.id.id.startsWith('com.google.cloud.tools.appengine')) {
        //workaround as plugins in subproject shouldn't have defined versions, so take use version defined in plugins block above
        useModule("com.google.cloud.tools:appengine-gradle-plugin:${requested.version ?: appengine_version}")
      }
    }
  }
}
plugins {
  id 'org.gradle.toolchains.foojay-resolver-convention' version '0.5.0'
}

rootProject.name = 'Playtime_server'
include 'base'
include 'base-proxy'
include 'playtime_app'
include 'game_progress'
include 'payment'
include 'reporting'
include 'notifications'
include 'applovin'
include 'orchestrator'
include 'accounting'
include 'router'
include 'proxy_service'
include 'commands'
include 'liquibase-runner'
include 'playtime_app:proto'
findProject(':playtime_app:proto')?.name = 'proto'
include 'base:proto'
findProject(':base:proto')?.name = 'proto'
include 'rewarding-service'
include 'rewarding-service:proto'
findProject(':rewarding-service:proto')?.name = 'proto'
include 'orchestrator:proto'
findProject(':orchestrator:proto')?.name = 'proto'
include 'payment:proto'
findProject(':payment:proto')?.name = 'proto'