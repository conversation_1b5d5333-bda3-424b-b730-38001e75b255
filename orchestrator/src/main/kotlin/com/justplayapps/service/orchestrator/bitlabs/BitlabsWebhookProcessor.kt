package com.justplayapps.service.orchestrator.bitlabs

import com.justplayapps.service.orchestrator.dto.BitlabsReceiptRevenueWebhookDto
import com.justplayapps.service.orchestrator.dto.BitlabsSurveyRevenueWebhookDto
import com.justplayapps.service.orchestrator.dto.UserSearchResult
import com.justplayapps.service.orchestrator.tracking.TrackingService
import com.moregames.base.config.ServicesRegistry
import com.moregames.base.messaging.WebhookPublisher
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import io.ktor.http.*
import javax.inject.Inject

class BitlabsWebhookProcessor @Inject constructor(
  private val webhookPublisher: WebhookPublisher,
  private val trackingService: TrackingService,
  private val bitlabsWebhookValidator: BitlabsWebhookValidator,
) {

  suspend fun processSurveyWebhook(fullUrl: Url, user: UserSearchResult) {
    if (!bitlabsWebhookValidator.isHashValid(fullUrl)) {
      logger().alert("Invalid hash in Bitlabs survey revenue webhook! Full URL: $fullUrl")
      return
    }

    val queryString = fullUrl.parameters.formUrlEncode()
    webhookPublisher.publish(
      BitlabsSurveyRevenueWebhookDto(
        originalQueryString = queryString,
        marketServiceAddress = trackingService.getServiceAddressByMarket(user.market, ServicesRegistry.PLAYTIME),
      )
    )
  }

  suspend fun processReceiptWebhook(fullUrl: Url, user: UserSearchResult) {
    if (!bitlabsWebhookValidator.isHashValid(fullUrl)) {
      logger().alert("Invalid hash in Bitlabs survey revenue webhook! Full URL: $fullUrl")
      return
    }

    val queryString = fullUrl.parameters.formUrlEncode()
    webhookPublisher.publish(
      BitlabsReceiptRevenueWebhookDto(
        originalQueryString = queryString,
        marketServiceAddress = trackingService.getServiceAddressByMarket(user.market, ServicesRegistry.PLAYTIME),
      )
    )
  }
}