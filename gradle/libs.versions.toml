[versions]
protobuf = "3.25.8"
grpc-java = "1.72.0"
grpc-kotlin = "1.4.3"
jackson = "2.19.0"

[plugins]
protobuf = { id = "com.google.protobuf", version = "0.9.5" }
artifactregistry = { id = "com.google.cloud.artifactregistry.gradle-plugin", version = "2.2.0" }

[libraries]
protobuf-protoc = { group = "com.google.protobuf", name = "protoc", version.ref = "protobuf" }
protobuf-java = { group = "com.google.protobuf", name = "protobuf-java", version.ref = "protobuf" }
protobuf-kotlin = { group = "com.google.protobuf", name = "protobuf-kotlin", version.ref = "protobuf" }
grpc-gen-java = { group = "io.grpc", name = "protoc-gen-grpc-java", version.ref = "grpc-java" }
grpc-gen-kotlin = { group = "io.grpc", name = "protoc-gen-grpc-kotlin", version.ref = "grpc-kotlin" }
grpc-stub = { group = "io.grpc", name = "grpc-stub", version.ref = "grpc-java" }
grpc-stub-kotlin = { group = "io.grpc", name = "grpc-kotlin-stub", version.ref = "grpc-kotlin" }
grpc-protobuf = { group = "io.grpc", name = "grpc-protobuf", version.ref = "grpc-java" }
grpc-xds = { group = "io.grpc", name = "grpc-xds", version.ref = "grpc-java" }
grpc-inprocess = { group = "io.grpc", name = "grpc-inprocess", version.ref = "grpc-java" }
jackson-databind = { group = "com.fasterxml.jackson.core", name = "jackson-databind", version.ref = "jackson" }
jackson-module-kotlin = { group = "com.fasterxml.jackson.module", name = "jackson-module-kotlin", version.ref = "jackson" }

games-api = { group = "com.justplayapps.games", name = "api", version = "1.1.7" }