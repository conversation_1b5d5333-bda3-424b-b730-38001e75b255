package main

import (
	"flag"
	"fmt"
	"github.com/pkg/errors"
	"gopkg.in/yaml.v2"
	"io"
	"os"
	"path/filepath"
	"text/template"
)

var (
	openFile            = os.Open
	createFile          = os.Create
	ioReadAll           = io.ReadAll
	yamlUnmarshal       = yaml.Unmarshal
	executeTemplateFile = func(templateFile *template.Template, wr io.Writer, data any) error {
		return templateFile.Execute(wr, data)
	}
	funcMap = template.FuncMap{
		"takeOrDefault": takeOrDefault,
	}
)

func takeOrDefault(value any, defaultValue string) string {
	if value != nil {
		return value.(string)
	} else {
		return defaultValue
	}
}

func valuesFromYamlFile(dataFile string, values *map[string]interface{}) error {
	data, err := openFile(dataFile)
	if err != nil {
		return errors.Wrap(err, "opening data file")
	}
	defer data.Close()
	s, err := ioReadAll(data)
	if err != nil {
		return errors.Wrap(err, "reading data file")
	}
	err = yamlUnmarshal(s, values)
	if err != nil {
		return errors.Wrap(err, "unmarshalling yaml file")
	}
	return nil
}

func Parse(templateFile []string, outputFile string, values *map[string]interface{}) error {
	tmpl, err := template.New(templateFile[0]).Funcs(funcMap).ParseFiles(templateFile...)
	if err != nil {
		return errors.Wrap(err, "parsing template file")
	}
	fmt.Println(filepath.Abs(outputFile))
	output, err := createFile(outputFile)
	if err != nil {
		return errors.Wrap(err, "creating output file")
	}
	defer output.Close()
	err = executeTemplateFile(tmpl, output, values)
	if err != nil {
		return errors.Wrap(err, "executing template file")
	}
	return nil
}

func run(market, env, service string, force bool) error {
	templateFiles := []string{"template.yaml", "scaling.yaml", "entrypoint.yaml", "additional_env_vars.yaml"}
	outputFile := "../" + market + "/appengine/" + env + "/" + service + "/app.yaml"
	if _, err := os.Stat(fmt.Sprintf("../%s/appengine/%s/%s/app.yaml", market, env, service)); err == nil && !force {
		fmt.Println("app.yaml file already exists.")
		os.Exit(0)
	}
	var values map[string]interface{}
	valuesFromYamlFile("../values.yaml", &values)
	valuesFromYamlFile("../"+market+"/appengine/values.yaml", &values)
	valuesFromYamlFile("../"+market+"/appengine/"+env+"/values.yaml", &values)
	valuesFromYamlFile("../"+market+"/appengine/"+env+"/"+service+"/values.yaml", &values)

	if err := Parse(templateFiles, outputFile, &values); err != nil {
		return err
	}
	fmt.Printf("file %s was generated.\n", outputFile)
	return nil
}

func main() {
	var force = flag.Bool("force", false, "true")
	flag.Parse()
	args := flag.Args()
	market := args[0]
	env := args[1]
	service := args[2]

	if err := run(market, env, service, *force); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
