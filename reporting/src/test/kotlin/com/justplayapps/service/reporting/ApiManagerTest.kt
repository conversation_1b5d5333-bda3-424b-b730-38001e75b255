package com.justplayapps.service.reporting

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.justplayapps.service.reporting.import.adjust.AdjustReportsService
import com.justplayapps.service.reporting.ipqs.IpqsService
import com.justplayapps.service.reporting.revenue.applovin.RevenueImportManager
import com.moregames.base.app.BuildVariant
import com.moregames.base.featureflags.FeatureFlagsFacade
import io.ktor.http.*
import io.ktor.server.testing.*
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal
import java.time.LocalDate

class ApiManagerTest {
  private val ipqsService: IpqsService = mock()
  private val adjustReportsService: AdjustReportsService = mock()
  private val featureFlagsFacade: FeatureFlagsFacade = mock()
  private val applovinRevenueService: RevenueImportManager = mock()

  private val apiManger = ApiManager(
    BuildVariant.PRODUCTION,
    ipqsService,
    featureFlagsFacade,
    adjustReportsService,
    applovinRevenueService = applovinRevenueService
  )

  @Test
  fun `SHOULD trigger extraction and check queuing of suspicious ips ON orderIpqsCheckForSuspiciousIps call`() =
    withTestApplication({ apiManger.initApi(this) }) {
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/cron/order-ipqs-check-for-suspicious-ips?revenueThresholdUsd=8.0"
      ) {
        addCronHeader()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      verifyBlocking(ipqsService) { orderIpqsCheckForSuspiciousIps(revenueThresholdUsd = BigDecimal("8.0")) }
    }

  @Test
  fun `SHOULD trigger adjust report loading ON load-adjust-spend-report-by-channel-by-date-by-applovin-mode`() =
    withTestApplication({ apiManger.initApi(this) }) {
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/cron/load-adjust-spend-report-by-channel-by-date-by-applovin-mode?date=2020-01-01&applovinMode=regular"
      ) {
        addCronHeader()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      verifyBlocking(adjustReportsService) { adjustReportsService.loadSpendReportByChannel(LocalDate.parse("2020-01-01"), "regular") }
    }

  @Test
  fun `SHOULD trigger adjust report loading ON load-adjust-spend-report-by-channel-by-applovin-mode-by-os-filter`() =
    withTestApplication({ apiManger.initApi(this) }) {
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/cron/load-adjust-spend-report-by-channel-by-applovin-mode-by-os-filter?applovinMode=regular&osFilterOn=true"
      ) {
        addCronHeader()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      verifyBlocking(adjustReportsService) { adjustReportsService.loadSpendReportByChannel(LocalDate.now(), applovinMode = "regular", osFilterOn = true) }
    }

  @Test
  fun `SHOULD trigger adjust report loading ON load-adjust-spend-report-by-channel-by-os-filter`() = withTestApplication({ apiManger.initApi(this) }) {
    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/cron/load-adjust-spend-report-by-channel-by-os-filter?osFilterOn=true"
    ) {
      addCronHeader()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(adjustReportsService) { adjustReportsService.loadSpendReportByChannel(LocalDate.now(), osFilterOn = true) }

  }

  @Test
  fun `SHOULD trigger applovin csv revenue import ON import-applovin-csv-revenue`() = withTestApplication({ apiManger.initApi(this) }) {
    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/cron/import-applovin-csv-revenue"
    ) {
      addCronHeader()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(applovinRevenueService) { importAndPushCsvRevenueToBQ(100) }
  }

  @Test
  fun `SHOULD trigger applovin csv revenue import ON import-applovin-csv-revenue with batchSize WHEN present`() =
    withTestApplication({ apiManger.initApi(this) }) {
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/cron/import-applovin-csv-revenue?batchSize=1000"
      ) {
        addCronHeader()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      verifyBlocking(applovinRevenueService) { importAndPushCsvRevenueToBQ(1000) }
    }

  private fun TestApplicationRequest.addCronHeader() {
    addHeader("X-Appengine-Cron", "true")
  }
}