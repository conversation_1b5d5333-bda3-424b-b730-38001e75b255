package com.justplayapps.service.reporting

import com.google.inject.Inject
import com.justplayapps.service.reporting.import.adjust.AdjustReportsService
import com.justplayapps.service.reporting.ipqs.IpqsService
import com.justplayapps.service.reporting.revenue.applovin.RevenueImportManager
import com.moregames.base.app.BuildVariant
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.ktor.cronInterceptor
import com.moregames.base.util.cronLogger
import com.moregames.base.util.installLogging
import com.moregames.base.util.logger
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import org.slf4j.Logger
import java.math.BigDecimal
import java.time.LocalDate

class ApiManager @Inject constructor(
  private val buildVariant: BuildVariant,
  private val ipqsService: IpqsService,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val adjustReportsService: AdjustReportsService,
  private val applovinRevenueService: RevenueImportManager
) {
  fun initApi(application: Application) {
    application.installLogging(buildVariant, featureFlagsFacade)

    application.routing {
      lifecycle()
      cron()
    }
  }

  private fun Routing.lifecycle() {
    get("/_ah/start") {
      // TODO: health check
      call.respond(HttpStatusCode.OK)
    }
    get("/_ah/stop") {
      // TODO: trigger shutdown
      call.respond(HttpStatusCode.OK)
    }
    get("/_ah/warmup") {
      call.respond(HttpStatusCode.OK)
    }
  }

  private fun Routing.cron() {
    route("/cron") {
      install(StatusPages, statusPageConfig(cronLogger()))
      cronInterceptor(buildVariant)
      get("/order-ipqs-check-for-suspicious-ips") {
        cronLogger().info("Order IPQS check for suspicious IPs. Start")
        val revenueThresholdUsd = BigDecimal(call.parameters["revenueThresholdUsd"])
        ipqsService.orderIpqsCheckForSuspiciousIps(revenueThresholdUsd)
        cronLogger().info("Order IPQS check for suspicious IPs. Finish")
        call.respond(HttpStatusCode.OK)
      }
      get("/load-adjust-spend-report-by-channel") {
        cronLogger().info("Starting 'load-adjust-spend-report-by-channel'")
        val daysToSubtract = call.parameters["daysToSubtract"]?.toLong() ?: 0
        adjustReportsService.loadSpendReportByChannel(LocalDate.now().minusDays(daysToSubtract))
        call.respond(HttpStatusCode.OK)
        cronLogger().info("Finished 'load-adjust-spend-report-by-channel'")
      }
      get("load-adjust-spend-report-by-channel-by-date-by-applovin-mode") {
        cronLogger().info("Starting 'load-adjust-spend-report-by-channel-by-date-by-applovin-mode'")
        val date = LocalDate.parse(call.parameters["date"])
        val applovinMode = call.parameters["applovinMode"]
        adjustReportsService.loadSpendReportByChannel(date, applovinMode)
        call.respond(HttpStatusCode.OK)
        cronLogger().info("Finished 'load-adjust-spend-report-by-channel-by-date-by-applovin-mode'")
      }
      get("load-adjust-spend-report-by-channel-by-applovin-mode-by-os-filter") {
        cronLogger().info("Starting 'load-adjust-spend-report-by-channel-by-applovin-mode-by-os-filter'")
        val daysToSubtract = call.parameters["daysToSubtract"]?.toLong() ?: 0
        val osFilter = call.parameters["osFilterOn"]?.toBoolean() == true
        val applovinMode = call.parameters["applovinMode"]
        adjustReportsService.loadSpendReportByChannel(LocalDate.now().minusDays(daysToSubtract), applovinMode = applovinMode, osFilterOn = osFilter)
        call.respond(HttpStatusCode.OK)
        cronLogger().info("Finished 'load-adjust-spend-report-by-channel-by-applovin-mode-by-os-filter'")
      }
      get("load-adjust-spend-report-by-channel-by-os-filter") {
        cronLogger().info("Starting 'load-adjust-spend-report-by-channel-by-os-filter'")
        val daysToSubtract = call.parameters["daysToSubtract"]?.toLong() ?: 0
        val osFilterOn = call.parameters["osFilterOn"]?.toBoolean() == true
        adjustReportsService.loadSpendReportByChannel(LocalDate.now().minusDays(daysToSubtract), osFilterOn = osFilterOn)
        call.respond(HttpStatusCode.OK)
        cronLogger().info("Finished 'load-adjust-spend-report-by-channel-by-os-filter'")
      }
      get("import-applovin-csv-revenue") {
        cronLogger().info("Starting 'import-applovin-csv-revenue'")
        val batchSize = call.parameters["batchSize"]?.toInt() ?: 100
        applovinRevenueService.importAndPushCsvRevenueToBQ(batchSize)
        call.respond(HttpStatusCode.OK)
        cronLogger().info("Finished 'import-applovin-csv-revenue'")
      }
    }
  }

  private fun statusPageConfig(logger: Logger = logger()): StatusPages.Configuration.() -> Unit = {
    exception<Throwable> { e ->
      logger.error("Unknown server error", e)
      call.respondText(e.message ?: "Unknown server error", ContentType.Text.Plain, HttpStatusCode.InternalServerError)
    }
  }
}