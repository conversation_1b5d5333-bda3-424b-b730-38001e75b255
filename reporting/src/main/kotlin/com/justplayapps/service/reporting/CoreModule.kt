package com.justplayapps.service.reporting

import com.google.cloud.bigquery.BigQueryOptions
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.justplayapps.service.reporting.utils.getDefaultJsonConverter
import com.moregames.base.app.BaseModule
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.DataSourceProvider
import com.moregames.base.app.DefaultDataSourceProvider
import com.moregames.base.messaging.auth.AuthService
import com.moregames.base.messaging.auth.HttpClientAuth
import com.moregames.base.secret.Secret
import com.moregames.base.secret.SecretService
import io.ktor.application.*
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.features.*
import io.ktor.client.features.json.*
import io.ktor.client.features.json.serializer.*
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json

class CoreModule(
  application: Application,
  buildVariant: BuildVariant,
  dataSourceProvider: DataSourceProvider = DefaultDataSourceProvider(),
) : BaseModule(application, buildVariant, dataSourceProvider) {

  companion object {
    const val ADJUST_HTTP_CLIENT = "adjustHttpClient"
    const val APPLOVIN_HTTP_CLIENT = "applovinHttpClient"
    const val CROSS_SERVICE_HTTP_CLIENT = "crossServiceHttpClient"
  }

  @Provides
  @Singleton
  fun database(secretService: SecretService) = connectToDatabase(DbConfigs.REPORTING, secretService)

  @Provides
  @Singleton
  @Named("replica")
  fun databaseReplica(secretService: SecretService) = connectToDatabase(DbConfigs.REPORTING, secretService, "replica")

  @Provides
  @Singleton
  @Named("applovin")
  fun databaseApplovin(secretService: SecretService) = connectToDatabase(DbConfigs.APPLOVIN, secretService, "applovin")

  @ExperimentalSerializationApi
  @Provides
  fun jsonConverter() = getDefaultJsonConverter()

  @Provides
  @Named(ADJUST_HTTP_CLIENT)
  fun adjustHttpClient() = HttpClient {
    install(HttpTimeout) {
      requestTimeoutMillis = 10 * 60 * 1000 // 10 mins
    }
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
      serializer = KotlinxSerializer(json)
    }
  }

  @Provides
  @Singleton
  @Named(APPLOVIN_HTTP_CLIENT)
  fun applovinHttpClient() = HttpClient(CIO) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json {
        ignoreUnknownKeys = true
      }
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    expectSuccess = false
  }

  @Provides
  @Named(CROSS_SERVICE_HTTP_CLIENT)
  fun crossServiceHttpClient(json: Json, authService: AuthService) = HttpClient(CIO) {
    install(JsonFeature) {
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientAuth) {
      this.authService = authService
    }
  }

  @Provides
  @Singleton
  fun bigQuery() = BigQueryOptions.getDefaultInstance().service!!

  private enum class DbConfigs(override val username: String, override val password: Secret, override val maximumPoolSize: Int) : DbConfig {
    REPORTING("reporting", ReportingSecrets.DATABASE_USER_REPORTING, 10),
    APPLOVIN("applovin", ReportingSecrets.DATABASE_USER_APPLOVIN, 10)
  }

  private enum class ReportingSecrets(override val key: String) : Secret {
    DATABASE_USER_REPORTING("database-user-reporting"),
    DATABASE_USER_APPLOVIN("database-user-applovin")
  }
}