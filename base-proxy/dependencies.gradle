dependencies {
  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:$kotlinx_coroutines_version"
  implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"
  implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

  api "io.ktor:ktor-client-serialization:$ktor_version"

  api "com.paypal:paypalhttp:2.0.0"
  api("com.paypal.sdk:payouts-sdk:1.1.0") {
    exclude group: "com.paypal", module: "paypalhttp"
  }
  api "com.paypal.sdk:rest-api-sdk:1.14.0"
  implementation 'com.google.code.gson:gson:2.11.0'

  testImplementation "org.mockito:mockito-inline:5.2.0"
  testImplementation "org.mockito:mockito-junit-jupiter:5.12.0"
  testImplementation 'org.mockito.kotlin:mockito-kotlin:5.2.0'
  testImplementation "com.willowtreeapps.assertk:assertk-jvm:0.26"
  testImplementation "org.jetbrains.kotlin:kotlin-test-junit5:$kotlin_version"
  testImplementation "org.junit.jupiter:junit-jupiter:$junit_jupiter_version"
  testImplementation "org.junit.jupiter:junit-jupiter-api:$junit_jupiter_version"
  testImplementation "org.junit.jupiter:junit-jupiter-params:$junit_jupiter_version"
  testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$junit_jupiter_version"
  testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$kotlinx_coroutines_version"
  testImplementation "io.ktor:ktor-client-mock:$ktor_version"
  testImplementation ("io.ktor:ktor-server-test-host:$ktor_version") {
    exclude(group: 'ch.qos.logback', module: 'logback-classic')
  }
}
