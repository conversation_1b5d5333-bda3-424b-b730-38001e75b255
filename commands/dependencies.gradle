dependencies {
  implementation project(":base")

  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:$kotlinx_coroutines_version"
  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:$kotlinx_coroutines_version"
  implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"
  implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
  implementation "io.ktor:ktor-serialization:$ktor_version"

  implementation "io.ktor:ktor-server-servlet:$ktor_version"
  implementation "io.ktor:ktor-client-apache:$ktor_version"
  implementation "io.ktor:ktor-server-netty:$ktor_version"

  implementation "ch.qos.logback:logback-classic:$logback_version"

  // fix for inconsistent grpc version https://github.com/grpc/grpc-java/issues/7002
  implementation "com.google.api:gax-grpc:1.57.0"

  implementation ("io.lettuce:lettuce-core:6.3.2.RELEASE") {
    exclude(group: 'io.netty', module: '*')
  }
  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-reactive:$kotlinx_coroutines_version"

  testImplementation(testFixtures(project(":base")))
  testImplementation "com.nhaarman.mockitokotlin2:mockito-kotlin:2.2.0"
  testImplementation "org.junit.jupiter:junit-jupiter:5.6.1"
  testImplementation "com.willowtreeapps.assertk:assertk-jvm:0.22"
  testImplementation "org.mockito:mockito-junit-jupiter:3.3.3"
  testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$kotlinx_coroutines_version"
  testImplementation "org.jetbrains.kotlin:kotlin-test-junit5:$kotlin_version"
  testImplementation "io.ktor:ktor-server-test-host:$ktor_version"
  testImplementation "com.redis:testcontainers-redis:2.0.1"
  testImplementation "org.testcontainers:junit-jupiter:$testcontainers_version"
}
