syntax = "proto3";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "base/common.proto";

package com.justplayapps.service.rewarding.earnings.proto;

message RevenueSavedEventProto {
  string user_id = 1;
  google.protobuf.Int32Value game_id = 2;
  google.protobuf.StringValue platform = 3;
  RevenueSourceProto source = 4;
  google.protobuf.StringValue package_name = 5;
  google.protobuf.StringValue ad_unit_format = 6;
  google.protobuf.StringValue ad_unit_id = 7;
  com.justplayapps.base.DecimalValue amount = 8;

  google.protobuf.StringValue event_id = 9;
  google.protobuf.Timestamp timestamp = 10;
  google.protobuf.StringValue ad_source = 11;
  com.justplayapps.base.DecimalValue applovin_revenue = 12;
}

enum RevenueSourceProto {
  REVENUE_SOURCE_UNDEFINED = 0;
  APPLOVIN = 1;
  IRON_SOURCE = 2;
  FYBER = 3;
  TAPJOY = 4;
  ADJOE = 5;
  BITLABS = 6;
}

// EarningsCalculationResult
enum EarningsCalculationType {
  EARNINGS_CALCULATION_TYPE_UNDEFINED = 0;
  SIMPLE = 1;
  EM2 = 2;
}

message UserCurrentCoinsBalanceProto {
  com.justplayapps.base.DecimalValue game_coins = 1;
  com.justplayapps.base.DecimalValue offer_coins = 2;
  com.justplayapps.base.DecimalValue bonus_coins = 3;
}

message UserEarningsQuotasProto {
  string user_id = 1;
  repeated string quotas = 2;
  google.protobuf.Timestamp period_end = 3;
  repeated com.justplayapps.base.DecimalValue values = 4;
}

message UsedQuotaProto {
  com.justplayapps.base.DecimalValue sum = 1;
  com.justplayapps.base.DecimalValue sum_for_offer_wall = 2;
}

message EarningsProto {
  string user_id = 1;
  com.justplayapps.base.DecimalValue earnings_sum = 2;
  UserEarningsQuotasProto quotas = 3;
  UsedQuotaProto used_quota = 4;
}

message Simple {
  google.protobuf.Int32Value metaId = 1;
  com.justplayapps.base.DecimalValue amount = 2;
  com.justplayapps.base.DecimalValue amountNoRounding = 3;
}

message Em2 {
  Simple simple_calculation_result = 1;
  bool no_earnings = 2;
  com.justplayapps.base.DecimalValue real_revenue = 3;
  com.justplayapps.base.DecimalValue real_game_revenue = 4;
  UserCurrentCoinsBalanceProto em2_coins_balance = 5;
  com.justplayapps.base.DecimalValue coins_for_one_dollar = 6;
  com.justplayapps.base.DecimalValue em2_game_revenue = 7;
  EarningsProto earnings = 8;
  com.justplayapps.base.DecimalValue em2_revenue = 9;
}

message EarningsCalculationResultProto {
  EarningsCalculationType type = 1;

  oneof result {
    Simple simple = 2;
    Em2 em2 = 3;
  }
}

message ConvertRevenueToEarningsMessage {
  string user_id = 1;
  google.protobuf.Timestamp period_start = 2;
  google.protobuf.Timestamp period_end = 3;
  int32 coins_goal = 4;
}

message RevenueToEarningsConvertedEvent {
  string user_id = 1;
  EarningsCalculationResultProto result = 2;
}

message CreateUserRevenueMessagesMessage {
  string user_id = 1;
  com.justplayapps.base.DecimalValue user_revenue = 2;
  com.justplayapps.base.DecimalValue games_revenue = 3;
  com.justplayapps.base.DecimalValue user_revenue_without_earnings = 4;
}

message AddUserCoinsMessage {
  string user_id = 1;
  com.justplayapps.base.AppPlatformProto platform = 2;
  oneof data {
    AddGameCoinsMessage game_coins = 3;
    AddOfferWallCoinsMessage offer_wall_coins = 4;
    AddAdditionalOfferCoinsMessage additional_offer_coins = 5;
  }

  message AddGameCoinsMessage {
    com.justplayapps.base.DecimalValue coins_earned = 1;
    bool force_command_notification = 2;
    google.protobuf.StringValue command_id = 3;
    int32 game_id = 4;
  }

  message AddOfferWallCoinsMessage {
    com.justplayapps.base.DecimalValue coins_earned = 1;
    google.protobuf.Int32Value non_inflated_coins_earned = 2;
  }

  message AddAdditionalOfferCoinsMessage {
    com.justplayapps.base.DecimalValue coins_earned = 1;
    google.protobuf.Int32Value non_inflated_coins_earned = 2;
    int32 offer_id = 3;
  }
}

message UserCoinsAddedEvent {
  string user_id = 1;
  int64 coins = 2;
  int64 coins_added = 8;

  oneof data {
    UserGameCoins game_coins_data = 3;
    AdditionalOfferCoins additional_offer_coins_data = 4;
    OfferWallCoins offer_wall_coins_data = 5;
    BonusCoins bonus_coins_data = 6;
    Em3UserGameCoins em3_game_coins_data = 7;
  }

  message UserGameCoins {
    bool force_command_notification = 1;
    google.protobuf.StringValue command_id = 2;
    int32 game_id = 3;
  }

  message AdditionalOfferCoins {
    int32 offer_id = 1;
  }

  message Em3UserGameCoins {}

  message OfferWallCoins {}

  message BonusCoins {}
}

message FirstTaskCompletedEvent {
  string user_id = 1;
  string task_id = 2;
  com.justplayapps.base.AppPlatformProto platform = 3;
}

message EngagementCarouselClaimCommand {
  string user_id = 1;
  string task_id = 2;
  string command_id = 3;
  com.justplayapps.base.AppPlatformProto platform = 4;
}

message RewardClaimedEvent {
  string user_id = 1;
  RewardType type = 2;
  com.justplayapps.base.DecimalValue amount = 3;
  string command_id = 4;

  enum RewardType {
    ENGAGEMENT_CAROUSEL_REWARD_TYPE_UNDEFINED = 0;
    EM2_COINS = 1;
    EARNINGS = 2;
    BOOSTER = 3;
    DIAMOND = 4;
    DIAMONDS_REWARD = 5;
  }
}

