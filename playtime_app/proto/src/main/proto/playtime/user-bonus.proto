syntax = "proto3";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "base/common.proto";

package com.justplayapps.service.rewarding.bonus.proto;

service UserBonusBalanceApi {
  rpc HasBonusFor(HasBonusForRequest) returns (HasBonusForResponse);
  rpc FindLastWelcomeCoinsDate(FindLastWelcomeCoinsDateRequest) returns (FindLastWelcomeCoinsDateResponse);
  rpc GetBonusBankEarnings(GetBonusBankEarningsRequest) returns (GetBonusBankEarningsResponse);

  // That endpoint is a huge exception. We will never do it again!!!
  rpc AddBonusCoins(AddBonusCoinsRequest) returns (google.protobuf.Empty);
}

message HasBonusForRequest {
  string user_id = 1;
  com.justplayapps.base.UserBonusBalanceTypeProto bonus_type = 2;
  google.protobuf.StringValue unique_bonus_key = 3;
}
message HasBonusForResponse {
  bool has_bonus = 1;
}

message FindLastWelcomeCoinsDateRequest {
  string user_id = 1;
}
message FindLastWelcomeCoinsDateResponse {
  google.protobuf.Timestamp timestamp = 1;
}

message AddBonusCoinsRequest {
  string user_id = 1;
  com.justplayapps.base.AppPlatformProto platform = 2;
  int32 coins_amount = 3;
  com.justplayapps.base.UserBonusBalanceTypeProto bonus_balance_type = 4;
  google.protobuf.StringValue unique_bonus_key = 5;
}

message GetBonusBankEarningsRequest {
  string user_id = 1;
}
message GetBonusBankEarningsResponse {
  com.justplayapps.base.DecimalValue earnings = 1;
}