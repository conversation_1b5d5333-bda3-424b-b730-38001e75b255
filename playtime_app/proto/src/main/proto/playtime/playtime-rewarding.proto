syntax = "proto3";
import "google/protobuf/empty.proto";
import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";
import "base/common.proto";

package com.justplayapps.playtime.rewarding.proto;

service PlaytimeRewardingApi  {
  rpc GetUserData(GetUserDataRequest) returns (GetUserDataResponse);
  rpc GetConvertedUsdToUserCurrency(GetConvertedUsdToUserCurrencyRequest) returns (GetConvertedUsdToUserCurrencyResponse);
  rpc GetRevenueWithChallengesCut(GetRevenueWithChallengesCutRequest) returns (GetRevenueWithChallengesCutResponse);
  rpc GetOfferwallCoinsToUsdConversionRatio(google.protobuf.Empty) returns (GetOfferwallCoinsToUsdConversionRatioResponse);
  rpc GetCurrentTaskForGame(GetCurrentTaskForGameRequest) returns (GetCurrentTaskForGameResponse);
}

message GetUserDataRequest {
  string user_id = 1;
}
message GetUserDataResponse {
  bool is_user_blocked = 1;
  double fraud_score = 2;
  bool is_user_restricted = 3;
  bool is_user_whitelisted = 4;
  com.justplayapps.base.AppPlatformProto platform = 5;
  string user_country_code = 6;
  CountryTierSettingsProto country_tier_settings = 7;
  com.justplayapps.base.DecimalValue user_max_earnings_amount = 8;
  google.protobuf.BoolValue cts_profile_match_status = 9;
  int32 ios_earnings_increase_percentage = 10;
  bool is_highly_trusted_user = 11;
  HigherQuotasRndRange higher_quotas_rnd_range = 12;
  repeated CashStreakRewardProto current_boosters = 13;

  com.justplayapps.base.DecimalValue offer_wall_coins_to_usd_conversion_ratio = 14;
  string user_currency = 16;
  CashoutPeriodsConfigProto cashout_periods_config = 18;
  bool is_welcome_coins_offer_completed = 19;
  bool is_eu_country = 20;
  google.protobuf.Int32Value cashout_period_counter = 22;
  google.protobuf.StringValue exp_earnings_user_filter = 23;
  com.justplayapps.base.DecimalValue user_quality = 24;
  com.justplayapps.base.DecimalValue current_to_first_ecpm_ratio = 25;
  int32 coin_goal = 26;
  repeated int32 coin_goal_milestones = 27;
  bool is_deleted = 28;
}

message CountryTierSettingsProto {
  com.justplayapps.base.DecimalValue max_cashout_amount_multiplier = 1;
  repeated com.justplayapps.base.DecimalValue daily_earnings_quotas = 2;
}
message HigherQuotasRndRange {
  double lower_multiplier = 1;
  double upper_multiplier = 2;
}
message CashStreakRewardProto {
  int32 achievement_day = 1;
  CashStreakRewardTypeProto type = 2;
  com.justplayapps.base.DecimalValue value = 3;
  bool big_reward = 4;
}
enum CashStreakRewardTypeProto {
  CASH_STREAK_REWARD_TYPE_UNDEFINED = 0;
  COINS = 1;
  EARNING_POWER = 2;
}
message CashoutPeriodsConfigProto {
  int64 first_cashout_period_minutes = 1;
  int64 second_cashout_period_minutes = 2;
  int64 default_cashout_period_minutes = 3;
  google.protobuf.Int32Value first_cashout_period_video_offer_reward = 4;
}

message GetConvertedUsdToUserCurrencyRequest {
  com.justplayapps.base.DecimalValue amount_usd = 1;
  string user_currency = 2;
}
message GetConvertedUsdToUserCurrencyResponse {
  com.justplayapps.base.DecimalValue usd_amount = 1;
  string user_currency = 2;
  com.justplayapps.base.DecimalValue amount = 3;
  com.justplayapps.base.DecimalValue amount_no_rounding = 4;
}

message GetRevenueWithChallengesCutRequest {
  string user_id = 1;
  repeated GenericRevenueProto revenues = 2;
}
message GetRevenueWithChallengesCutResponse {
  com.justplayapps.base.DecimalValue revenue = 1;
}
enum RevenueSourceProto {
  REVENUE_SOURCE_UNDEFINED = 0;
  APPLOVIN = 1;
  IRON_SOURCE = 2;
  FYBER = 3;
  TAPJOY = 4;
  ADJOE = 5;
  BITLABS = 6;
}
message GenericRevenueProto {
  string id = 1;
  string user_id = 2;
  RevenueSourceProto source = 3;
  google.protobuf.Timestamp timestamp = 4;
  com.justplayapps.base.DecimalValue amount = 5;
  com.justplayapps.base.DecimalValue amount_extra = 6;
  google.protobuf.Int32Value game_id = 7;
}
message GetOfferwallCoinsToUsdConversionRatioResponse {
  com.justplayapps.base.DecimalValue ratio = 1;
}

message GetCurrentTaskForGameRequest {
  string user_id = 1;
  int32 game_id = 2;
}

message GetCurrentTaskForGameResponse {
  google.protobuf.StringValue taskId = 1;
}