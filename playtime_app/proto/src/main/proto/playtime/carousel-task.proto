syntax = "proto3";

package com.justplayapps.playtime.carousel;

service Carousel {
  rpc GetTaskStatus (GetTaskStatusRequest) returns (GetTaskStatusResponse);
}

message GetTaskStatusRequest {
  string user_id = 1;
  int32 game_id = 2;
}

message GetTaskStatusResponse {
  oneof status {
    TaskInactive inactive = 1;
    TaskActive active = 2;
  }
}

message TaskInactive {}
message TaskActive {
  string task_id = 1;
}

message UserClaimedCarouselEvent {
  string task_id = 1;
  string command_id = 2;
}

message FirstTaskCompletedEvent {
  string task_id = 1;
}