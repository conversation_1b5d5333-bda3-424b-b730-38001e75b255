syntax = "proto3";

package com.justplayapps.playtime.rewarding.bonusbank;

import "google/protobuf/empty.proto";
import "base/common.proto";

service BonusBankApi {
  rpc GetBonusCashBarState (GetBonusCashBarStateRequest) returns (GetBonusCashBarStateResponse);
}

message GetBonusCashBarStateRequest {
  string user_id = 1;
  com.justplayapps.base.AppPlatformProto platform = 2;
}

message GetBonusCashBarStateResponse {
  string user_id = 1;

  oneof status {
    BonusCashBarState enabled = 2;
    google.protobuf.Empty disabled = 3;
  }

  message BonusCashBarState {
    com.justplayapps.base.DecimalValue valueToReach = 1;
    com.justplayapps.base.DecimalValue currentValue = 2;
    com.justplayapps.base.DecimalValue reward = 3;
    bool readyToClaim = 4; // final claim ability. important, as, regardless of current Bar progress, there may be completed (but unclaimed) Bars
    repeated BonusCashBarStateMilestone milestone = 5;

    message BonusCashBarStateMilestone {
      com.justplayapps.base.DecimalValue valueToReach = 1;
      com.justplayapps.base.DecimalValue reward = 2;
      BonusCashBarStateMilestoneStatus status = 3;

      enum BonusCashBarStateMilestoneStatus {
        MILESTONE_STATUS_UNDEFINED = 0;
        IN_PROGRESS = 1;
        READY_TO_CLAIM = 2;
        CLAIMED = 3;
      }
    }
  }
}

message BonusBankClaim {
  string user_id = 1;
  string command_id = 2;
  com.justplayapps.base.AppPlatformProto platform = 3;
}

