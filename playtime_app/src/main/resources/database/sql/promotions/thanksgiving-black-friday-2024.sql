INSERT INTO playtime.cfg_offerwall_rule
(name, description, button_text, image_filename, icon_filename, order_key, offerwall_type, date_from, date_until)
SELECT
    'Black Friday BONANZA! Get 150% MORE PAYOUTS Today!' AS name,
    'Don\'t miss out! Offer runs for a limited time!' AS description,
    '$_ofw_cfg_btn_text' AS button_text,
    'offerwalls/offerwall_2024_black_friday.gif' AS image_filename,
    'offerwalls/offerwall_2024_black_friday.gif' AS icon_filename,
    1 AS order_key,
    ofw_types.ofw_type AS offerwall_type,
    timestamp '2024-12-01 00:00:00' AS date_from,
    timestamp '2024-12-03 23:55:00' AS date_until
FROM (SELECT 'TAPJOY' as ofw_type UNION ALL SELECT 'FYBER') ofw_types;
--
INSERT INTO playtime.scheduled_generic_notification
(title, text, send_after)
SELECT
    '🦃🛍️ Thanksgiving & Black Friday: 150% MORE PAYOUTS Feast! 💸🍂' AS title,
    'Gobble up 150% MORE COINS during our Thanksgiving & Black Friday blowout! Hurry before this deal is gone! 🦃💰' AS text,
    time_set.send_after AS send_after
FROM (SELECT timestamp '2024-12-01 13:00:00' as send_after UNION ALL SELECT timestamp '2024-12-03 13:00:00') time_set;
--
INSERT INTO `playtime`.`promotions_push_notification`
(promotion_id, scheduled_notification_id)
SELECT
    cfg_offerwall_rule.id AS promotion_id,
	  scheduled_generic_notification.id AS scheduled_notification_id
FROM playtime.cfg_offerwall_rule
JOIN playtime.scheduled_generic_notification
	  ON title = '🦃🛍️ Thanksgiving & Black Friday: 150% MORE PAYOUTS Feast! 💸🍂' AND
	  send_after IN (timestamp '2024-12-01 13:00:00', timestamp '2024-12-03 13:00:00')
WHERE date_from = timestamp '2024-12-01 00:00:00' AND name = 'Black Friday BONANZA! Get 150% MORE PAYOUTS Today!';