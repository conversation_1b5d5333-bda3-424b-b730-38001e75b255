UPDATE playtime.games_loyalty_exp
SET description = 'Defeat monsters for loyalty coins'
WHERE variation_key = 'loyaltyCoinsStrings'
  AND game_id = (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.treasuremaster' AND platform = 'ANDROID');

UPDATE playtime.games_loyalty_exp
SET description = 'Defeat monsters for loyalty points'
WHERE variation_key = 'loyaltyPointsStrings'
  AND game_id = (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.treasuremaster' AND platform = 'ANDROID');

INSERT INTO playtime.games_loyalty_exp (variation_key, game_id, description)
SELECT variation_key, game_id, description
FROM (
            SELECT 'loyaltyCoinsStringsV2' AS variation_key, (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.treasuremaster' AND platform = 'ANDROID') AS game_id, 'Defeat monsters for loyalty coins and earn even more loyalty coins for defeating bosses and getting their treasure' AS description
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.madsmash' AND platform = 'ANDROID'), 'Smash through level after level for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.mixblox' AND platform = 'ANDROID'), 'Make number matches for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.ballbounce' AND platform = 'ANDROID'), 'Destroy the blocks and collect pick ups for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.wordseeker' AND platform = 'ANDROID'), 'Solve word puzzles for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.solitaireverse' AND platform = 'ANDROID'), 'The more games you win the more loyalty coins you get'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.hexapuzzlefun' AND platform = 'ANDROID'), 'Reach 2048 to get more loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.puzzlepopblaster' AND platform = 'ANDROID'), 'Match colored blocks to make loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.idlemergefun' AND platform = 'ANDROID'), 'Merge your way to improved production and loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.triviamadness' AND platform = 'ANDROID'), 'Put your general knowledge to the test and earn loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.mergeblast' AND platform = 'ANDROID'), 'Have a blast and merge blocks to survive as long as you can for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.emojiclickers' AND platform = 'ANDROID'), 'Tap and bounce your way to more loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.hexmatch' AND platform = 'ANDROID'), 'Solve hexagonal puzzles for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.zentiles' AND platform = 'ANDROID'), 'Enter a state of bliss and earn loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.sugarmatch' AND platform = 'ANDROID'), 'Match 3 to make sweet treats for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.carsmerge' AND platform = 'ANDROID'), 'Merge and build your team to defeat your enemies for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.blockholeclash' AND platform = 'ANDROID'), 'Remove the correct blocks for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.bubblepop' AND platform = 'ANDROID'), 'Solve puzzles and rescue the animals for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.blockslider' AND platform = 'ANDROID'), 'Survive the wall by making complete rows and earn loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.marblemadness' AND platform = 'ANDROID'), 'Survive Aztec trials for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.sudoku' AND platform = 'ANDROID'), 'Earn loyalty coins and relax with a classic'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.brickdoku' AND platform = 'ANDROID'), 'Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins'
  UNION ALL SELECT 'loyaltyCoinsStringsV2', (SELECT id FROM playtime.games WHERE application_id = 'com.gimica.wordkitchen' AND platform = 'ANDROID'), 'Take on the heat of the Word Kitchen and discover words for loyalty coins'
) t
WHERE game_id IS NOT NULL;