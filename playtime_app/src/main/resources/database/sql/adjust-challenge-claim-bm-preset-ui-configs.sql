update playtime.boosted_mode_preset set ui_config =
'
{
  "mainScreenHintTranslation": "<big><font color=\'#F2C94C\'>Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>",
  "topLeftCoinsReplacementTranslation": "Boosted Coins",
  "earnPlayingGamesReplacementTranslation": "Boosted Games:",
  "balanceUpdate": {
    "titleTranslation": "$_balance_update_notification_title",
    "descriptionTranslation": "You now have %s Boosted Coins!",
    "descriptionNoCoinsTranslation": "Keep collecting your Boosted Coins!"
  },
  "readyToCashout": {
    "titleTranslation": "Your Boosted Payout is READY!",
    "descriptionTranslation": "Open JustPlay to claim your rewards"
  }
}
'
where id = 'challenge_claim_2x_offer_1';

update playtime.boosted_mode_preset set ui_config =
'
{
  "mainScreenHintTranslation": "<big><font color=\'#F2C94C\'>Double payouts!</font></big><br><br><big>Get 2x payouts for every coin you collect!</big>",
  "topLeftCoinsReplacementTranslation": "Boosted Coins",
  "earnPlayingGamesReplacementTranslation": "Boosted Games:",
  "balanceUpdate": {
    "titleTranslation": "$_balance_update_notification_title",
    "descriptionTranslation": "You now have %s Boosted Coins!",
    "descriptionNoCoinsTranslation": "Keep collecting your Boosted Coins!"
  },
  "readyToCashout": {
    "titleTranslation": "Your Boosted Payout is READY!",
    "descriptionTranslation": "Open JustPlay to claim your rewards"
  }
}
'
where id = 'challenge_claim_2x_offer_1_20';