-- translation resources
insert into playtime.string_resource (resource_name)
values ('$_one_line_pattern_puzzle_description'),
       ('$_one_line_pattern_puzzle_text_install_top'),
       ('$_one_line_pattern_puzzle_text_install_bottom');

-- translations en stubs
INSERT INTO playtime.string_resource_translation (resource_name, language, translation)
VALUES ('$_one_line_pattern_puzzle_description', 'en', 'one_line_pattern_puzzle_description'),
       ('$_one_line_pattern_puzzle_text_install_top', 'en', 'one_line_pattern_puzzle_text_install_top'),
       ('$_one_line_pattern_puzzle_text_install_bottom', 'en', 'one_line_pattern_puzzle_text_install_bottom')
ON DUPLICATE KEY UPDATE translation = VALUES(translation);

-- fill other languages
INSERT INTO playtime.string_resource_translation (resource_name, language, translation)
    (WITH res AS (SELECT *
                  FROM playtime.string_resource_translation
                  WHERE resource_name like '$\_one_line_pattern_puzzle%'
                    AND language = 'en'),
          lang AS (SELECT DISTINCT language FROM playtime.string_resource_translation WHERE language != 'en')
     SELECT res.resource_name,
            lang.language,
            res.translation
     FROM res
              LEFT JOIN lang ON 1 = 1)
ON DUPLICATE KEY UPDATE translation = VALUES(translation);

-- Hidden game stub for enable rewarding mechanics
INSERT INTO playtime.games (id, application_id, name, description, icon_filename, image_filename, order_key,
                            applovin_api_key, activity_name, install_image_filename, info_text_install_top,
                            info_text_install_bottom, publisher_id, platform, do_not_show)
SELECT 200083                                                                                                          AS id,
       'com.gimica.oneline'                                                                                            AS application_id,
       'One Line: Pattern Puzzle'                                                                                      AS name,
       '$_one_line_pattern_puzzle_description'                                                                         AS description,
       'one_line_pattern_puzzle.jpg'                                                                                   AS icon_filename,
       'one_line_pattern_puzzle_preview.jpg'                                                                           AS image_filename,
       (SELECT order_key FROM playtime.games WHERE platform = 'ANDROID' AND application_id = 'com.gimica.dontpop') + 1 AS order_key,
       'gimica-api-key'                                                                                                AS applovin_api_key,
       'com.unity3d.player.UnityPlayerActivity'                                                                        AS activity_name,
       'install_image_20230504.jpg'                                                                                    AS install_image_filename,
       '$_one_line_pattern_puzzle_text_install_top'                                                                    AS info_text_install_top,
       '$_one_line_pattern_puzzle_text_install_bottom'                                                                 AS info_text_install_bottom,
       4                                                                                                               AS publisher_id,
       'ANDROID'                                                                                                       AS platform,
       1                                                                                                               as do_not_show;

-- AdUnitIds
INSERT INTO playtime.game_ad_unit_ids
    (package_id, ad_type, ad_unit_id, is_highly_trusted_user, app_platform)
VALUES ('com.gimica.oneline', 'BANNER', '4d419705798ed9f6', 1, 'ANDROID'),
       ('com.gimica.oneline', 'INTERSTITIAL', '7805b4e702a1e7fd', 1, 'ANDROID'),
       ('com.gimica.oneline', 'REWARDED', '3c1f0a6ad162811d', 1, 'ANDROID'),
       ('com.gimica.oneline', 'BANNER', 'b9e38c3440568043', 0, 'ANDROID'),
       ('com.gimica.oneline', 'INTERSTITIAL', 'ef50416a5112fb33', 0, 'ANDROID'),
       ('com.gimica.oneline', 'REWARDED', 'ccb5dc8718117530', 0, 'ANDROID');
