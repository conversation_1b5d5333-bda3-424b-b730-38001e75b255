swagger: "2.0"
info:
  title: "JustPlay Games API"
  description: "JustPlay Games API interactions (not public - not used in prod, only as documentation)"
  version: "1.0.0"
schemes:
  - "https"
securityDefinitions:
  BasicAuth:
    type: basic
paths:
  /commands/{commandId}:
    get:
      summary: "Get command status"
      operationId: "getCommandStatus"
      parameters:
        - name: commandId
          in: path
          type: string
          required: true
      responses:
        200:
          description: "Returns 200 when command is completed. Body has payload structure specific for each command"
          schema:
            type: object
            properties:
              someKey1:
                type: string
              someKey2:
                type: string
        425:
          description: "Returns 425 when command is still in progress"
        404:
          description: "Returns 404 when command is not found"
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  # Webhook section
  /games/progress:
    get:
      description: "Game progress tracking (coins earned)"
      operationId: "webhookGamesProgress"
      deprecated: true
      parameters:
        - $ref: '#/parameters/USER_ID_UNDERSCORE_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
        - $ref: '#/parameters/GAME_ID_REQUIRED'
        - $ref: '#/parameters/GAME_VERSION_OPTIONAL'
        - $ref: '#/parameters/SCORE_OPTIONAL'
        - $ref: '#/parameters/AMOUNT_OPTIONAL'
        - $ref: '#/parameters/IS_BOSS_OPTIONAL'
        - $ref: '#/parameters/MILESTONE_OPTIONAL'
        - $ref: '#/parameters/API_LEVEL_OPTIONAL'
        - $ref: '#/parameters/IS_NEW_RECORD_OPTIONAL'
      responses:
        200:
          description: "Ok"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /game-score:
    get:
      summary: "Game progress tracking (coins earned). Signed request"
      operationId: "webhookGameScore"
      parameters:
        - $ref: '#/parameters/USER_ID_UNDERSCORE_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
        - $ref: '#/parameters/GAME_ID_REQUIRED'
        - $ref: '#/parameters/GAME_VERSION_OPTIONAL'
        - $ref: '#/parameters/SCORE_OPTIONAL'
        - $ref: '#/parameters/AMOUNT_OPTIONAL'
        - $ref: '#/parameters/IS_BOSS_OPTIONAL'
        - $ref: '#/parameters/MILESTONE_OPTIONAL'
        - $ref: '#/parameters/API_LEVEL_OPTIONAL'
        - $ref: '#/parameters/IS_NEW_RECORD_OPTIONAL'
        - $ref: '#/parameters/SIGNATURE_REQUIRED'
      responses:
        200:
          description: "Ok"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /game-score/async:
    get:
      summary: "Game progress tracking - async (coins earned). Signed request"
      operationId: "webhookGameScoreAsync"
      parameters:
        - $ref: '#/parameters/USER_ID_UNDERSCORE_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
        - $ref: '#/parameters/GAME_ID_REQUIRED'
        - $ref: '#/parameters/GAME_VERSION_OPTIONAL'
        - $ref: '#/parameters/SCORE_OPTIONAL'
        - $ref: '#/parameters/AMOUNT_OPTIONAL'
        - $ref: '#/parameters/IS_BOSS_OPTIONAL'
        - $ref: '#/parameters/MILESTONE_OPTIONAL'
        - $ref: '#/parameters/API_LEVEL_OPTIONAL'
        - $ref: '#/parameters/IS_NEW_RECORD_OPTIONAL'
        - $ref: '#/parameters/SIGNATURE_REQUIRED'
      responses:
        200:
          description: "[COMMAND] This response is returned via async command API"
          schema:
            type: object
            properties:
              balance:
                type: string
                description: "User's balance after update"
        202:
          $ref: '#/responses/COMMAND_ID'
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /webglgame-score:
    get:
      summary: "Game progress tracking (coins earned). WebGL games endpoint"
      operationId: "webglGameScore"
      parameters:
        - $ref: '#/parameters/USER_ID_UNDERSCORE_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
        - $ref: '#/parameters/GAME_ID_REQUIRED'
        - $ref: '#/parameters/GAME_VERSION_OPTIONAL'
        - $ref: '#/parameters/SCORE_OPTIONAL'
        - $ref: '#/parameters/AMOUNT_OPTIONAL'
        - $ref: '#/parameters/IS_BOSS_OPTIONAL'
        - $ref: '#/parameters/MILESTONE_OPTIONAL'
        - $ref: '#/parameters/API_LEVEL_OPTIONAL'
        - $ref: '#/parameters/IS_NEW_RECORD_OPTIONAL'
      responses:
        200:
          description: "Ok"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /webglgame-score/async:
    get:
      summary: "Game progress tracking - async (coins earned). WebGL games endpoint"
      operationId: "webglGameScoreAsync"
      parameters:
        - $ref: '#/parameters/USER_ID_UNDERSCORE_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
        - $ref: '#/parameters/GAME_ID_REQUIRED'
        - $ref: '#/parameters/GAME_VERSION_OPTIONAL'
        - $ref: '#/parameters/SCORE_OPTIONAL'
        - $ref: '#/parameters/AMOUNT_OPTIONAL'
        - $ref: '#/parameters/IS_BOSS_OPTIONAL'
        - $ref: '#/parameters/MILESTONE_OPTIONAL'
        - $ref: '#/parameters/API_LEVEL_OPTIONAL'
        - $ref: '#/parameters/IS_NEW_RECORD_OPTIONAL'
      responses:
        200:
          description: "[COMMAND] This response is returned via async command API"
          schema:
            type: object
            properties:
              balance:
                type: string
                description: "User's balance after update"
        202:
          $ref: '#/responses/COMMAND_ID'
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  # Cross-platform section
  /get/sanityCheck:
    get:
      description: "User sanity check"
      operationId: "getSanityCheck"
      parameters:
        - $ref: '#/parameters/IP_OPTIONAL'
        - $ref: '#/parameters/COUNTRY_OPTIONAL'
        - $ref: '#/parameters/VD_OPTIONAL'
        - $ref: '#/parameters/SKIP_CACHE_OPTIONAL'
        - $ref: '#/parameters/PACKAGE_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
        - $ref: '#/parameters/USER_ID_UNDERSCORE_OPTIONAL'
      responses:
        200:
          description: "Sanity check result"
          schema:
            type: object
            required:
              - isValidUser
              - isFromAllowedCountry
              - isUsingVpn
            properties:
              isValidUser:
                type: boolean
                description: "Is user fraudster or not"
              isFromAllowedCountry:
                type: boolean
                description: "Is user from allowed country"
              isUsingVpn:
                type: boolean
                description: "Is user using VPN"
              fraudScore:
                type: integer
                description: "User's FS count"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /get/adUnitIdsByGoogleAdId:
    get:
      description: "Get adUnitIds by googleAdId"
      operationId: "getAdUnitIdsByGoogleAdId"
      parameters:
        - $ref: '#/parameters/GOOGLE_AD_ID_REQUIRED'
        - $ref: '#/parameters/PACKAGE_ID_REQUIRED'
      responses:
        200:
          description: "Ad unit Ids"
          schema:
            $ref: '#/definitions/AD_UNIT_IDS'
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /get/adUnitIds:
    get:
      description: "Get adUnitIds"
      operationId: "getAdUnitIds"
      parameters:
        - $ref: '#/parameters/PACKAGE_ID_REQUIRED'
        - $ref: '#/parameters/GOOGLE_AD_ID_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
      responses:
        200:
          description: "Ad unit Ids"
          schema:
            $ref: '#/definitions/AD_UNIT_IDS'
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /get/shouldShowEarlyInterstitial:
    get:
      description: "Should the game show early Interstitial"
      operationId: "getShouldShowEarlyInterstitial"
      deprecated: true
      parameters:
        - $ref: '#/parameters/GOOGLE_AD_ID_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
      responses:
        200:
          description: "ShowEarlyInterstitial info"
          schema:
            type: object
            required:
              - show
            properties:
              show:
                type: boolean
                description: "Should the game show early Interstitial"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /get/gdprState:
    get:
      description: "Should GDPR rules be applied"
      operationId: "getGdprState"
      parameters:
        - $ref: '#/parameters/GOOGLE_AD_ID_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
      responses:
        200:
          description: "GdprState info"
          schema:
            type: object
            required:
              - useGdpr
            properties:
              useGdpr:
                type: boolean
                description: "Should GDPR rules be applied"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /post/game-key:
    post:
      description: "Propagate signature public key"
      operationId: "postGameKey"
      parameters:
        - $ref: '#/parameters/APPLICATION_ID_REQUIRED'
        - $ref: '#/parameters/PUBLIC_KEY_REQUIRED'
        - $ref: '#/parameters/GOOGLE_AD_ID_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
      responses:
        200:
          description: "Ok"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /games/install:
    post:
      description: Game installation event
      operationId: gameInstallationEvent
      parameters:
        - name: userId
          in: header
          type: string
          required: false
        - name: idfa
          in: header
          type: string
          required: false
        - name: idfv
          in: header
          type: string
          required: false
        - name: request
          in: body
          schema:
            required:
              - applicationId
              - gameVersion
              - platform
            type: object
            properties:
              applicationId:
                description: "Application's package"
                type: string
              gameVersion:
                description: "Version of a game"
              gameVersionApi:
                description: "Version of a game as INT (1, 2, 3 ...) for more convenient comparing"
                type: integer
              platform:
                description: "Platform"
                type: string
                enum: [ IOS, ANDROID ]
            example:
              gameVersion: "1.0.79"
              gameVersionApi: 35
              applicationId: "com.gimica.treasuremaster"
              platform: "ANDROID"
      responses:
        200:
          description: "Ok"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'

  # Android section
  /android/status:
    get:
      description: "Get user's status for game"
      operationId: "getAndroidUserGameStatus"
      parameters:
        - $ref: '#/parameters/PACKAGE_ID_REQUIRED'
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/GOOGLE_AD_ID_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
      responses:
        200:
          description: "Status of User for a Game"
          schema:
            type: object
            required:
              - userId
              - examinationRequired
            properties:
              userId:
                type: string
                description: "userId calculated for given input ID-s"
              examinationRequired:
                type: boolean
                description: "if examination required for a game and not yet completed"
              configuration:
                type: object
                properties:
                  adVariant:
                    type: string
                    description: "variation for adVariant experiment"
              lft:
                type: string
                description: "LFT"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /android/examination:
    get:
      description: "Get nonce"
      operationId: "getAndroidExamination"
      parameters:
        - $ref: '#/parameters/PACKAGE_ID_REQUIRED'
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/GOOGLE_AD_ID_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
      responses:
        200:
          description: "Nonce"
          schema:
            type: object
            required:
              - nonce
            properties:
              nonce:
                type: string
                description: "Nonce"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
    post:
      description: "Examination request"
      operationId: "postAndroidExamination"
      parameters:
        - name: request
          in: body
          description: "Device Attestation Statement"
          schema:
            type: object
            required:
              - packageId
              - attestationStatement
            properties:
              userId:
                type: string
              idfv:
                type: string
              idfa:
                type: string
              packageId:
                type: string
              attestationStatement:
                type: string
      responses:
        200:
          description: "Ok"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /android/coins-booster-configuration/:
    get:
      description: "Get coins booster configuration"
      operationId: "androidCoinsBoosterConfiguration"
      parameters:
        - $ref: '#/parameters/PACKAGE_ID_REQUIRED'
        - $ref: '#/parameters/GAME_VERSION_REQUIRED'
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/GOOGLE_AD_ID_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
      responses:
        200:
          description: "Coins booster configuration"
          schema:
            type: object
            required:
              - userId
            properties:
              userId:
                type: string
              configurationId:
                type: string
                enum:
                  - coinsBoosterOn
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /android/celebration-configuration/:
    get:
      description: "Get games celebration configuration"
      operationId: "androidGamesCelebration"
      parameters:
        - $ref: '#/parameters/PACKAGE_ID_REQUIRED'
        - $ref: '#/parameters/GAME_VERSION_REQUIRED'
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/GOOGLE_AD_ID_OPTIONAL'
        - $ref: '#/parameters/GAID_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
      responses:
        200:
          description: "Games celebration configuration"
          schema:
            type: object
            required:
              - userId
            properties:
              parameters:
                type: string
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'

  # iOS section
  /ios/status:
    get:
      description: "Get user's status for game"
      operationId: "getIosUserGameStatus"
      parameters:
        - $ref: '#/parameters/PACKAGE_ID_REQUIRED'
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/IDFV_REQUIRED'
        - $ref: '#/parameters/IDFA_OPTIONAL'
      responses:
        200:
          description: "Status of User for a Game"
          schema:
            type: object
            properties:
              userId:
                type: string
                description: "User ID"
              configuration:
                type: object
                description: "User configuration"
                properties:
                  adVariant:
                    type: string
                    description: "variation for adVariant experiment"
              lft:
                type: string
                description: "LFT"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /ios/att-consent-configuration/:
    get:
      description: "Get ATT consent configuration"
      operationId: "iosAttConsentConfiguration"
      parameters:
        - $ref: '#/parameters/PACKAGE_ID_REQUIRED'
        - $ref: '#/parameters/IDFV_REQUIRED'
        - $ref: '#/parameters/GAME_VERSION_REQUIRED'
      responses:
        200:
          description: "Att consent configuration"
          schema:
            type: object
            required:
              - userId
            properties:
              userId:
                type: string
              configurationId:
                type: string
                enum:
                  - consentVariant1
                  - simple
                  - aggressive
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /ios/track-consent/:
    post:
      description: "ATT consent result tracking"
      operationId: "postTrackConsent"
      parameters:
        - $ref: '#/parameters/PACKAGE_ID_REQUIRED'
        - $ref: '#/parameters/IDFV_REQUIRED'
        - $ref: '#/parameters/GAME_VERSION_REQUIRED'
        - name: request
          in: body
          description: "Device Attestation Statement"
          schema:
            type: object
            required:
              - status
            properties:
              status:
                type: string
                enum:
                  - AUTHORIZED
                  - DENIED
                  - NOT_DETERMINED
                  - RESTRICTED
      responses:
        200:
          description: "Ok"
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /ios/coins-booster-configuration/:
    get:
      description: "Get coins booster configuration"
      operationId: "iosCoinsBoosterConfiguration"
      parameters:
        - $ref: '#/parameters/PACKAGE_ID_REQUIRED'
        - $ref: '#/parameters/IDFV_REQUIRED'
        - $ref: '#/parameters/GAME_VERSION_REQUIRED'
      responses:
        200:
          description: "Coins booster configuration"
          schema:
            type: object
            required:
              - userId
            properties:
              userId:
                type: string
              configurationId:
                type: string
                enum:
                  - coinsBoosterOn
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'
  /ios/celebration-configuration/:
    get:
      description: "Get games celebration configuration"
      operationId: "iosGamesCelebration"
      parameters:
        - $ref: '#/parameters/PACKAGE_ID_REQUIRED'
        - $ref: '#/parameters/IDFV_REQUIRED'
        - $ref: '#/parameters/GAME_VERSION_REQUIRED'
      responses:
        200:
          description: "Games celebration configuration"
          schema:
            type: object
            required:
              - userId
            properties:
              parameters:
                type: string
        400:
          $ref: '#/responses/4XX_REQUEST_ERROR'
        500:
          $ref: '#/responses/5XX_SERVER_ERROR'

  # unified id section
  /unifiedid/status:
    get:
      description: Get user's  eligibility status for unified id
      operationId: 'unifiedIdStatus'
      parameters:
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
      responses:
        200:
          description: 'Ok'
          schema:
            type: object
            properties:
              shouldUseUnifiedId:
                type: boolean
                description: True if mobile app should proceed with unified id usage
        404:
          description: 'User not found in database'
  /unifiedid/token:
    get:
      description: Generate unified id token for user
      operationId: 'unifiedIdToken'
      parameters:
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
      responses:
        200:
          description: 'Ok'
          schema:
            $ref: '#/definitions/UNIFIED_ID_RESPONSE'
        404:
          description: 'User not found in database'
        412:
          description: 'User is opted out, unretryable'
  /unifiedid/refresh:
    get:
      description: Generate unified id token for user
      operationId: 'unifiedIdRefresh'
      parameters:
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
      responses:
        200:
          description: 'Ok'
          schema:
            $ref: '#/definitions/UNIFIED_ID_RESPONSE'
        404:
          description: 'User not found in database'
        412:
          description: 'User is opted out, unretryable'
  /rampid/status:
    get:
      description: Get user's  eligibility status for ramp id
      operationId: 'rampIdStatus'
      parameters:
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
      responses:
        200:
          description: 'Ok'
          schema:
            type: object
            properties:
              shouldUseUnifiedId:
                type: boolean
                description: True if mobile app should proceed with ramp id usage
        404:
          description: 'User not found in database'
  /rampid/emailHash:
    get:
      description: Generate email hashes sha256, sha1, md5 for user
      operationId: 'rampIdEmailHash'
      parameters:
        - $ref: '#/parameters/USER_ID_OPTIONAL'
        - $ref: '#/parameters/IDFV_OPTIONAL'
        - $ref: '#/parameters/IDFA_OPTIONAL'
      responses:
        200:
          description: 'Ok'
          schema:
            $ref: '#/definitions/RAMP_ID_RESPONSE'
        404:
          description: 'User not found in database'

# Definitions section
responses:
  4XX_REQUEST_ERROR:
    description: "Request error. It can be either validation error or user state that prevents the operation"
  5XX_SERVER_ERROR:
    description: "Server error. Request can be retried"
  COMMAND_ID:
    description: "Command ID to poll. This is direct method's response"
    schema:
      type: object
      properties:
        commandId:
          type: string
          description: "Command ID to check the status"
parameters:
  PACKAGE_ID_REQUIRED:
    name: packageId
    in: query
    description: "Game's application id (package id)"
    type: string
    required: true
  USER_ID_OPTIONAL:
    name: userId
    in: query
    description: "Main App's userId"
    type: string
    required: false
  USER_ID_UNDERSCORE_OPTIONAL:
    name: user_id
    in: query
    description: "Main App's userId"
    type: string
    required: false
  IDFV_OPTIONAL:
    name: idfv
    in: query
    description: "Android: AppSetId. iOS: IDFV."
    type: string
    required: false
  IDFV_REQUIRED:
    name: idfv
    in: query
    description: "Android: AppSetId. iOS: IDFV."
    type: string
    required: true
  GOOGLE_AD_ID_REQUIRED:
    name: googleAdId
    in: query
    description: "User's Google Ad Id. Deprecated"
    type: string
    required: true
  GOOGLE_AD_ID_OPTIONAL:
    name: googleAdId
    in: query
    description: "User's Google Ad Id. Deprecated"
    type: string
    required: false
  GAID_OPTIONAL:
    name: gaid
    in: query
    description: "User's Google Ad Id. Deprecated"
    type: string
    required: false
  IDFA_OPTIONAL:
    name: idfa
    in: query
    description: "Android: Google Ad Id. iOS: IDFA."
    type: string
    required: false
  GAME_ID_REQUIRED:
    name: game_id
    in: query
    description: "Application Id"
    type: string
    required: true
  GAME_VERSION_REQUIRED:
    name: gameVersion
    in: query
    description: "Game version"
    type: string
    required: true
  GAME_VERSION_OPTIONAL:
    name: gameVersion
    in: query
    description: "Game version"
    type: string
    required: false
  SCORE_OPTIONAL:
    name: score
    in: query
    description: "Score"
    type: string
    required: false
  AMOUNT_OPTIONAL:
    name: amount
    in: query
    description: "Amount"
    type: string
    required: false
  IS_BOSS_OPTIONAL:
    name: is_boss
    in: query
    description: "Is boss"
    type: string
    required: false
  MILESTONE_OPTIONAL:
    name: milestone
    in: query
    description: "Milestone"
    type: string
    required: false
  API_LEVEL_OPTIONAL:
    name: apiLevel
    in: query
    description: "Api level"
    type: string
    required: false
  IS_NEW_RECORD_OPTIONAL:
    name: is_new_record
    in: query
    description: "Is new record"
    type: string
    required: false
  SIGNATURE_REQUIRED:
    name: signature
    in: query
    description: "Request signature"
    type: string
    required: true
  IP_OPTIONAL:
    name: ip
    in: query
    description: "IP address"
    type: string
    required: false
  COUNTRY_OPTIONAL:
    name: country
    in: query
    description: "Country"
    type: string
    required: false
  VD_OPTIONAL:
    name: vd
    in: header
    description: "Is VPN usage detected on the game side"
    type: string
    required: false
  SKIP_CACHE_OPTIONAL:
    name: skipCache
    in: query
    description: "Skip IP addresses cache"
    type: string
    required: false
  PACKAGE_OPTIONAL:
    name: package
    in: query
    description: "Game package id"
    type: string
    required: false
  APPLICATION_ID_REQUIRED:
    name: applicationId
    in: query
    description: "Game application id"
    type: string
    required: true
  PUBLIC_KEY_REQUIRED:
    name: publicKey
    in: query
    description: "Signature public key"
    type: string
    required: true
definitions:
  AD_UNIT_IDS:
    description: "Ad unit Ids"
    type: object
    required:
      - banner_ad_unit_id
      - rewarded_ad_unit_id
      - interstitial_ad_unit_id
    properties:
      banner_ad_unit_id:
        type: string
        description: "banner_ad_unit_id"
      rewarded_ad_unit_id:
        type: string
        description: "rewarded_ad_unit_id"
      interstitial_ad_unit_id:
        type: string
        description: "interstitial_ad_unit_id"
      interstitial_high_ad_unit_id:
        type: string
        description: "interstitial_high_ad_unit_id"
      interstitial_medium_ad_unit_id:
        type: string
        description: "interstitial_medium_ad_unit_id"
  UNIFIED_ID_RESPONSE:
    type: object
    properties:
      advertisingToken:
        type: string
      identityExpires:
        type: string
        format: date-time
      refreshExpires:
        type: string
        format: date-time
      refreshFrom:
        type: string
        format: date-time
  RAMP_ID_RESPONSE:
    type: object
    properties:
      SHA256:
        type: string
      SHA1:
        type: string
      MD5:
        type: string