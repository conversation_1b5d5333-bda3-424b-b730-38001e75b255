package com.justplayapps.service.rewarding.bonusbank

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.`java-time`.timestamp

object EngagementCarouselBankTable : Table("playtime.engagement_carousel_bank") {
  val userId = varchar("user_id", 36)
  val taskId = varchar("task_id", 36)
  val bankBalance = decimal("bank_balance", 18, 6)
  val isClaimed = bool("is_claimed")
  val createdAt = timestamp("created_at")
  val updatedAt = timestamp("updated_at")
}
