package com.justplayapps.service.rewarding.bonusbank

import com.moregames.base.util.BigDecimalAsString
import com.moregames.playtime.user.bonuscashbar.dto.BonusCashBarMilestone
import kotlinx.serialization.Serializable

@Serializable
data class BonusCashBarClaimResult(
  val claims: List<BonusCashBarClaim>,
)

@Serializable
data class BonusCashBarClaim(
  val barClaimed: <PERSON>ole<PERSON>,
  val barReward: BigDecimalAsString?,
  val claimedMilestones: List<BonusCashBarMilestone>,
)