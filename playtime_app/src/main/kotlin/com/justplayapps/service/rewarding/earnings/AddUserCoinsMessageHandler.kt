package com.justplayapps.service.rewarding.earnings

import com.google.inject.Inject
import com.justplayapps.service.rewarding.bonusbank.BonusBankStorage
import com.justplayapps.service.rewarding.bonusbank.toBonusBankVariation
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents.AddUserCoinsMessage.*
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents.AddUserCoinsMessage.DataCase.*
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.additionalOfferCoins
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.offerWallCoins
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.userGameCoins
import com.justplayapps.service.rewarding.earnings.proto.userCoinsAddedEvent
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.isEm3
import com.moregames.base.bus.MessageBus
import com.moregames.base.bus.MessageHandler
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.fromProto
import java.math.BigDecimal

class AddUserCoinsMessageHandler @Inject constructor(
  private val userCurrentCoinsBalancePersistenceService: UserCurrentCoinsBalancePersistenceService,
  private val abTestingFacade: AbTestingFacade,
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService,
  private val emExperimentBaseService: EmExperimentBaseService,
  private val messageBus: MessageBus,
  private val bonusBankStorage: BonusBankStorage,
) {

  @MessageHandler
  suspend fun handle(message: RewardingEvents.AddUserCoinsMessage) {
    val platform: AppPlatform = message.platform.fromProto()
    val trackVisibleCoins = abTestingFacade.isCoinsDoNotResetParticipant(message.userId, platform)
    val abInflatingCoinsMultiplier = emExperimentBaseService.inflatingCoinsMultiplier(message.userId)

    when (message.dataCase) {
      GAME_COINS -> processGameCoins(message.userId, platform, trackVisibleCoins, message.gameCoins, abInflatingCoinsMultiplier)
      ADDITIONAL_OFFER_COINS -> processAdditionalOfferCoins(message.userId, platform, trackVisibleCoins, message.additionalOfferCoins, abInflatingCoinsMultiplier)
      OFFER_WALL_COINS -> processOfferWallCoins(message.userId, platform, trackVisibleCoins, message.offerWallCoins, abInflatingCoinsMultiplier)
      null, DATA_NOT_SET -> {
        throw IllegalArgumentException("Unsupported add coins message type ${message.dataCase}")
      }
    }
  }

  private suspend fun processGameCoins(
    userId: String,
    platform: AppPlatform,
    trackVisibleCoins: Boolean,
    data: AddGameCoinsMessage,
    abInflatingCoinsMultiplier: Int
  ) {
    val coinsEarnedUncut = data.coinsEarned.fromProto()
    if (coinsEarnedUncut == BigDecimal.ZERO) return

    val coinsToAdd = applyBonusBankReturnCoinsWithCut(userId, platform, coinsEarnedUncut)

    val em2Participation = abTestingFacade.getEm2Participation(userId)
    when {
      em2Participation is AbTestingService.IsExperimentParticipant.No ->
        userCurrentCoinsBalancePersistenceService.updateCurrentGamesCoinsBalance(userId, coinsToAdd.toInt(), trackVisibleCoins)

      // em3 coins has its own flow, see com.justplayapps.service.rewarding.earnings.em3.dto.AddRevenueCoinsEvent
      em2Participation.isEm3() -> return

      else -> {
        userCurrentCoinsBalancePersistenceService.updateEm2CurrentGamesCoinsBalance(userId, coinsToAdd, trackVisibleCoins)
      }
    }

    val coins = userCurrentCoinsBalanceService.getUserCurrentCoinsBalance(userId, platform).coins

    messageBus.publish(
      userCoinsAddedEvent {
        this.userId = userId
        this.coins = coins
        this.coinsAdded = (coinsToAdd * abInflatingCoinsMultiplier.toBigDecimal()).toLong()
        this.gameCoinsData = userGameCoins {
          this.forceCommandNotification = data.forceCommandNotification
          this.gameId = data.gameId
          if (data.hasCommandId()) {
            this.commandId = data.commandId
          }
        }
      }
    )
  }

  private suspend fun processAdditionalOfferCoins(
    userId: String,
    platform: AppPlatform,
    trackVisibleCoins: Boolean,
    data: AddAdditionalOfferCoinsMessage,
    abInflatingCoinsMultiplier: Int
  ) {
    // TODO: add BonusBank processing

    val coinsAdded: Long
    if (abTestingFacade.isEm2Participant(userId)) {
      userCurrentCoinsBalancePersistenceService.updateEm2AdditionalOfferCurrentCoinsBalance(userId, data.coinsEarned.fromProto(), trackVisibleCoins)
      coinsAdded = (data.coinsEarned.fromProto() * abInflatingCoinsMultiplier.toBigDecimal()).toLong()
    } else {
      userCurrentCoinsBalancePersistenceService.updateAdditionalOfferCurrentCoinsBalance(userId, data.coinsEarned.fromProto().toInt(), data.nonInflatedCoinsEarned.value, trackVisibleCoins)
      coinsAdded = data.coinsEarned.fromProto().toLong()
    }

    val coins = userCurrentCoinsBalanceService.getUserCurrentCoinsBalance(userId, platform).coins

    messageBus.publish(
      userCoinsAddedEvent {
        this.userId = userId
        this.coins = coins
        this.coinsAdded = coinsAdded
        this.additionalOfferCoinsData = additionalOfferCoins {
          this.offerId = data.offerId
        }
      }
    )
  }

  private suspend fun processOfferWallCoins(
    userId: String,
    platform: AppPlatform,
    trackVisibleCoins: Boolean,
    data: AddOfferWallCoinsMessage,
    abInflatingCoinsMultiplier: Int
  ) {
    val isEm2Participant = abTestingFacade.isEm2Participant(userId)

    val nonInflatedEarnedCoinsUncut =
      if (isEm2Participant) data.coinsEarned.fromProto()
      else data.nonInflatedCoinsEarned.value.toBigDecimal()

    val nonInflatedCoinsToAdd = applyBonusBankReturnCoinsWithCut(userId, platform, nonInflatedEarnedCoinsUncut)
    val inflatedCoinsToAdd = nonInflatedCoinsToAdd
      .times(abInflatingCoinsMultiplier.toBigDecimal())
      .toInt()

    if (isEm2Participant) {
      userCurrentCoinsBalancePersistenceService.updateEm2AdditionalOfferCurrentCoinsBalance(userId, nonInflatedCoinsToAdd, trackVisibleCoins)
    } else {
      userCurrentCoinsBalancePersistenceService.updateAdditionalOfferCurrentCoinsBalance(
        userId, inflatedCoinsToAdd, nonInflatedCoinsToAdd.toInt(), trackVisibleCoins
      )
    }

    val coins = userCurrentCoinsBalanceService.getUserCurrentCoinsBalance(userId, platform).coins

    messageBus.publish(
      userCoinsAddedEvent {
        this.userId = userId
        this.coins = coins
        this.coinsAdded = inflatedCoinsToAdd.toLong()
        this.offerWallCoinsData = offerWallCoins {  }
      }
    )
  }

  private suspend fun applyBonusBankReturnCoinsWithCut(userId: String, platform: AppPlatform, coins: BigDecimal): BigDecimal {
    val bank =
      abTestingFacade.assignedVariation(userId, ClientExperiment.BONUS_BANK).toBonusBankVariation()
        ?.bank
        ?.also {
          it.init(userId, platform, bonusBankStorage)
        }
        ?: return coins

    return bank.cutCoins(coins)
  }
}