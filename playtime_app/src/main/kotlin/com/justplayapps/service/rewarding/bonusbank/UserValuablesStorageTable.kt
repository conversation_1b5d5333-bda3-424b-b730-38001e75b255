package com.justplayapps.service.rewarding.bonusbank

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.`java-time`.timestamp

object UserValuablesStorageTable : Table("playtime.user_valuables_storage") {
  val userId = varchar("user_id", 36)
  val amount = decimal("amount", 18, 6)
  val valuableType = varchar("valuable_type", 50)
  val createdAt = timestamp("created_at")
  val updatedAt = timestamp("updated_at")
}
