package com.justplayapps.service.rewarding.bonus

import com.google.inject.Inject
import com.google.protobuf.Empty
import com.google.protobuf.Timestamp
import com.justplayapps.service.rewarding.bonus.proto.UserBonus.*
import com.justplayapps.service.rewarding.bonus.proto.UserBonusBalanceApiGrpcKt
import com.justplayapps.service.rewarding.bonus.proto.getBonusBankEarningsResponse
import com.justplayapps.service.rewarding.bonus.proto.uniqueBonusKeyOrNull
import com.moregames.base.util.fromProto
import com.moregames.base.util.toProto

class UserBonusBalanceApiImpl @Inject constructor(
  private val userBonusBalanceService: UserBonusBalanceService,
) : UserBonusBalanceApiGrpcKt.UserBonusBalanceApiCoroutineImplBase() {

  override suspend fun hasBonusFor(request: HasBonusForRequest): HasBonusForResponse {
    return HasBonusForResponse.newBuilder()
      .setHasBonus(
        userBonusBalanceService.hasBonusFor(
          request.userId,
          request.bonusType.fromProto(),
          if (request.hasUniqueBonusKey()) request.uniqueBonusKey.value else null
        )
      )
      .build()
  }

  override suspend fun findLastWelcomeCoinsDate(request: FindLastWelcomeCoinsDateRequest): FindLastWelcomeCoinsDateResponse {
    val timestamp = userBonusBalanceService.findLastWelcomeCoinsDate(request.userId) ?: return FindLastWelcomeCoinsDateResponse.getDefaultInstance()
    return FindLastWelcomeCoinsDateResponse.newBuilder()
      .setTimestamp(
        Timestamp.newBuilder()
          .setSeconds(timestamp.epochSecond)
          .setNanos(timestamp.nano)
          .build()
      )
      .build()
  }

  override suspend fun addBonusCoins(request: AddBonusCoinsRequest): Empty {
    userBonusBalanceService.addBonusCoins(
      userId = request.userId,
      appPlatform = request.platform.fromProto(),
      coinsAmount = request.coinsAmount.toBigDecimal(),
      bonusBalanceType = request.bonusBalanceType.fromProto(),
      uniqueBonusKey = request.uniqueBonusKeyOrNull?.value,
    )
    return Empty.getDefaultInstance()
  }

  override suspend fun getBonusBankEarnings(request: GetBonusBankEarningsRequest): GetBonusBankEarningsResponse {
    return getBonusBankEarningsResponse {
      this.earnings = userBonusBalanceService.getBonusBankEarnings(request.userId).toProto()
    }
  }
}