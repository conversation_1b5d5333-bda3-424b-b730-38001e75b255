package com.justplayapps.service.rewarding.bonusbank

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.`java-time`.timestamp

object EngagementCarouselCfgTable : Table("playtime.engagement_carousel_cfg") {
  val userId = varchar("user_id", 36)
  val wasEverClaimed = bool("was_ever_claimed")
  val diamondPrice = decimal("diamond_price", 18, 6)
  val createdAt = timestamp("created_at")
  val updatedAt = timestamp("updated_at")
}
