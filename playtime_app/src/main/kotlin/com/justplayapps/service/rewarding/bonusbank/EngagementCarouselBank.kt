package com.justplayapps.service.rewarding.bonusbank

import com.moregames.base.util.alert
import com.moregames.base.util.logger
import java.math.BigDecimal
import kotlin.reflect.KClass

class EARNINGS(val earnings: BigDecimal) : BaseRewardType
class DIAMOND : BaseRewardType
class DIAMONDS_REWARD(val earnings: BigDecimal) : BaseRewardType

open class BankSplit(val rewardType: KClass<out BaseRewardType>, val percentageToUser: BigDecimal, val percentageToStorage: BigDecimal) :
  BaseRewardType {
  init {
    if ((percentageToStorage + percentageToUser).compareTo(BigDecimal.ONE) != 0)
      throw IllegalStateException("Invalid BankSplit percentage configuration. Sum should be 1")
  }
}

class BOOSTER : BankSplit(BOOSTER::class, 0.toBigDecimal(), 1.toBigDecimal())

val REVENUE_CUT_FACTOR = BigDecimal("0.5")
val DIAMONDS_FOR_REWARD = 5.toBigDecimal()

class EngagementCarouselBank(
  val firstReward: SingleReward,
  reward: BaseReward,
  val revenueCutFactor: BigDecimal = REVENUE_CUT_FACTOR
) : BaseBank(reward) {

  override suspend fun cutCoins(coins: BigDecimal): BigDecimal {
    return coins
  }

  suspend fun cutRevenue(revenue: BigDecimal, taskId: String): BigDecimal {
    val bank = bonusBankStorage.getEngCarouselBank(userId, taskId)
    val revenueToStore = revenue * revenueCutFactor
    bonusBankStorage.saveEngCarouselBank(
      bank.copy(
        bankBalance = bank.bankBalance + revenueToStore,
      )
    )
    return revenue - revenueToStore
  }

  suspend fun claimReward(taskId: String, commandId: String) {
    val bank = bonusBankStorage.getEngCarouselBank(userId, taskId)
    if (bank.isClaimed) throw IllegalStateException("Reward for $taskId is already claimed")

    val cfg = bonusBankStorage.getEngCarouselCfg(userId)
    if (cfg.isFirstClaim()) {
      val (reward, revenueToBank) = firstReward.splitReward(bank.bankBalance)
      bonusBankStorage.claimReward(userId, platform, listOf(reward))
      bonusBankStorage.storeUserValue(userId, revenueToBank, ValuableType.REVENUE)

      bonusBankStorage.saveEngCarouselCfg(
        cfg.copy(wasEverClaimed = true)
      )
      bonusBankStorage.saveEngCarouselBank(bank.copy(isClaimed = true))
    } else {
      // check does user have enough revenue to win a diamond
      val bankRevenue = bonusBankStorage.getUserValue(userId, ValuableType.REVENUE)
      if (cfg.diamondPrice <= bankRevenue) {
        processDiamondReward(cfg, bankRevenue)
        return
      }

      val (reward, revenueToBank) = reward.splitReward(bank.bankBalance)
      bonusBankStorage.claimReward(userId, platform, listOf(reward))
      bonusBankStorage.storeUserValue(userId, bankRevenue + revenueToBank, ValuableType.REVENUE)
      bonusBankStorage.saveEngCarouselBank(bank.copy(isClaimed = true))
    }
  }

  suspend fun calculateDiamondFirstPrice(taskId: String) {
    val cfg = bonusBankStorage.getEngCarouselCfg(userId)
    if (cfg.diamondPrice > 0.toBigDecimal()) {
      logger().alert("Impossible case. Diamond price should be calculated here only once. [$userId], [$taskId]")
      return
    }
    val bank = bonusBankStorage.getEngCarouselBank(userId, taskId)
    if (bank.bankBalance <= 0.toBigDecimal()) {
      logger().alert("Impossible case. We received task completed event to early. [$userId], [$taskId]")
      return
    }
    bonusBankStorage.saveEngCarouselCfg(
      cfg.copy(diamondPrice = bank.bankBalance * 2.toBigDecimal())
    )
  }

  // It is a base mechanic for EngagementCarouselBank so I expect we use it everywhere
  private fun BaseReward.splitReward(revenue: BigDecimal): Pair<BaseRewardType, BigDecimal> {
    val reward = (this.reward as BankSplit)
    with(reward) {
      return when (rewardType) {
        EARNINGS::class -> {
          EARNINGS(revenue * percentageToUser) to revenue * percentageToStorage
        }

        EM2_COINS::class -> {
          EM2_COINS(revenue * percentageToUser * 220.toBigDecimal()) to revenue * percentageToStorage
        }

        BOOSTER::class -> {
          BOOSTER() to revenue * percentageToStorage
        }

        else -> throw IllegalArgumentException("${rewardType.simpleName} is not supported")
      }
    }
  }

  private suspend fun processDiamondReward(cfg: EngCarouselConfig, bankRevenue: BigDecimal) {
    val diamonds = bonusBankStorage.getUserValue(userId, ValuableType.DIAMONDS) + 1.toBigDecimal()
    if (diamonds < DIAMONDS_FOR_REWARD) {
      bonusBankStorage.claimReward(userId, platform, listOf(DIAMOND()))
      bonusBankStorage.storeUserValue(userId, diamonds, ValuableType.DIAMONDS)
    } else {
      bonusBankStorage.claimReward(userId, platform, listOf(DIAMONDS_REWARD(cfg.diamondPrice * 5.toBigDecimal())))
      bonusBankStorage.storeUserValue(userId, 0.toBigDecimal(), ValuableType.DIAMONDS)
      val newDiamondPrice = recalculateDiamondPrice(cfg.diamondPrice)
      bonusBankStorage.saveEngCarouselCfg(cfg.copy(diamondPrice = newDiamondPrice))
    }
    bonusBankStorage.storeUserValue(userId, bankRevenue - cfg.diamondPrice, ValuableType.REVENUE)
  }

  private fun recalculateDiamondPrice(currentPrice: BigDecimal): BigDecimal {
    // prices after $2 per diamond: 2, 4, 10, 20
    val postCapPrices = listOf(
      BigDecimal("2"),
      BigDecimal("4"),
      BigDecimal("10"),
      BigDecimal("20")
    )
    return when {
      currentPrice < 2.toBigDecimal() -> {
        val doubled = currentPrice * 2.toBigDecimal()
        doubled.coerceAtMost(2.toBigDecimal())
      }

      else -> {
        val nextPrice = postCapPrices.firstOrNull { it > currentPrice }
        nextPrice ?: currentPrice
      }
    }
  }
}