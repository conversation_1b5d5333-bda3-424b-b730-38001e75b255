package com.justplayapps.service.rewarding.bonusbank

import com.google.inject.Inject
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.util.insertOrUpdate
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.select
import java.math.BigDecimal
import javax.inject.Singleton

@Singleton
class UserValuablesStoragePersistenceService @Inject constructor(database: Database) : BasePersistenceService(database) {

  suspend fun save(userId: String, amount: BigDecimal, valuableType: ValuableType) = dbQuery {
    UserValuablesStorageTable.insertOrUpdate(UserValuablesStorageTable.userId) {
      it[UserValuablesStorageTable.userId] = userId
      it[UserValuablesStorageTable.amount] = amount
      it[UserValuablesStorageTable.valuableType] = valuableType.name
    }
  }

  suspend fun get(userId: String, valuableType: ValuableType): BigDecimal = dbQuery {
    UserValuablesStorageTable
      .slice(UserValuablesStorageTable.amount)
      .select { (UserValuablesStorageTable.userId eq userId) and (UserValuablesStorageTable.valuableType eq valuableType.name) }
      .map { it[UserValuablesStorageTable.amount] }
      .firstOrNull() ?: BigDecimal.ZERO
  }
}

enum class ValuableType {
  EM2_COINS, DIAMONDS, REVENUE
}