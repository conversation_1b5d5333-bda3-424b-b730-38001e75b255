package com.justplayapps.service.rewarding.bonusbank

import com.google.inject.Inject
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.util.insertOrUpdate
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.select
import javax.inject.Singleton

@Singleton
class BonusBankPersistenceService @Inject constructor(database: Database) : BasePersistenceService(database) {

  suspend fun saveBonusBank(bank: BonusBank) = dbQuery {
    BonusBankTable.insertOrUpdate(BonusBankTable.bankBalance, BonusBankTable.lastClaimedBalance) {
      it[BonusBankTable.userId] = bank.userId
      it[BonusBankTable.bankBalance] = bank.bankBalance
      it[BonusBankTable.lastClaimedBalance] = bank.lastClaimedBalance
    }
  }

  suspend fun getBonusBank(userId: String): BonusBank = dbQuery {
    BonusBankTable
      .slice(BonusBankTable.userId, BonusBankTable.bankBalance, BonusBankTable.lastClaimedBalance)
      .select(BonusBankTable.userId eq userId)
      .map {
        BonusBank(
          userId = it[BonusBankTable.userId],
          bankBalance = it[BonusBankTable.bankBalance],
          lastClaimedBalance = it[BonusBankTable.lastClaimedBalance]
        )
      }
      .firstOrNull()
      ?: BonusBank(
        userId = userId,
        bankBalance = 0.toBigDecimal(),
        lastClaimedBalance = 0.toBigDecimal(),
      )
  }

  suspend fun saveEngCarouselBank(bank: EngCarouselBank) = dbQuery {
    EngagementCarouselBankTable.insertOrUpdate(EngagementCarouselBankTable.userId, EngagementCarouselBankTable.taskId) {
      it[EngagementCarouselBankTable.userId] = bank.userId
      it[EngagementCarouselBankTable.taskId] = bank.taskId
      it[EngagementCarouselBankTable.bankBalance] = bank.bankBalance
      it[EngagementCarouselBankTable.isClaimed] = bank.isClaimed
    }
  }

  suspend fun getEngCarouselBank(userId: String, taskId: String): EngCarouselBank = dbQuery {
    EngagementCarouselBankTable
      .slice(
        EngagementCarouselBankTable.userId,
        EngagementCarouselBankTable.taskId,
        EngagementCarouselBankTable.bankBalance,
        EngagementCarouselBankTable.isClaimed
      )
      .select((EngagementCarouselBankTable.userId eq userId) and (EngagementCarouselBankTable.taskId eq taskId))
      .map {
        EngCarouselBank(
          userId = it[EngagementCarouselBankTable.userId],
          taskId = it[EngagementCarouselBankTable.taskId],
          bankBalance = it[EngagementCarouselBankTable.bankBalance],
          isClaimed = it[EngagementCarouselBankTable.isClaimed]
        )
      }
      .firstOrNull()
      ?: EngCarouselBank(
        userId = userId,
        taskId = taskId,
        bankBalance = 0.toBigDecimal(),
        isClaimed = false,
      )
  }

  suspend fun saveEngCarouselCfg(cfg: EngCarouselConfig) = dbQuery {
    EngagementCarouselCfgTable.insertOrUpdate(EngagementCarouselCfgTable.userId) {
      it[EngagementCarouselCfgTable.userId] = cfg.userId
      it[EngagementCarouselCfgTable.wasEverClaimed] = cfg.wasEverClaimed
      it[EngagementCarouselCfgTable.diamondPrice] = cfg.diamondPrice
    }
  }

  suspend fun getEngCarouselCfg(userId: String): EngCarouselConfig = dbQuery {
    EngagementCarouselCfgTable
      .slice(EngagementCarouselCfgTable.userId, EngagementCarouselCfgTable.wasEverClaimed, EngagementCarouselCfgTable.diamondPrice)
      .select(EngagementCarouselCfgTable.userId eq userId)
      .map {
        EngCarouselConfig(
          userId = it[EngagementCarouselCfgTable.userId],
          wasEverClaimed = it[EngagementCarouselCfgTable.wasEverClaimed],
          diamondPrice = it[EngagementCarouselCfgTable.diamondPrice],
        )
      }
      .firstOrNull()
      ?: EngCarouselConfig(
        userId = userId,
        diamondPrice = 0.toBigDecimal(),
        wasEverClaimed = false,
      )
  }
}