package com.justplayapps.service.rewarding.bonus

import com.google.inject.Inject
import com.justplayapps.service.rewarding.earnings.EmExperimentBaseService
import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalancePersistenceService
import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalanceService
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.bonusCoins
import com.justplayapps.service.rewarding.earnings.proto.userCoinsAddedEvent
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.justplayapps.service.rewarding.revenue.RevenuePersistenceService
import com.moregames.base.abtesting.variations.CoinsDoNotResetVariation
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.NonGameCoinsAddedEventDto
import com.moregames.base.user.UserBonusBalanceType
import java.math.BigDecimal

class UserBonusBalanceService @Inject constructor(
  private val abTestingFacade: AbTestingFacade,
  private val userBonusBalancePersistenceService: UserBonusBalancePersistenceService,
  private val userCurrentCoinsBalancePersistenceService: UserCurrentCoinsBalancePersistenceService,
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService,
  private val revenuePersistenceService: RevenuePersistenceService,
  private val emExperimentBaseService: EmExperimentBaseService,
  private val messageBus: MessageBus,
) {

  suspend fun hasBonusFor(userId: String, bonusBalanceType: UserBonusBalanceType, uniqueBonusKey: String? = null) =
    userBonusBalancePersistenceService.hasBonusFor(userId, bonusBalanceType, uniqueBonusKey)

  suspend fun addBonusCoins(
    userId: String,
    appPlatform: AppPlatform,
    coinsAmount: BigDecimal,
    bonusBalanceType: UserBonusBalanceType,
    uniqueBonusKey: String? = null
  ) {

    val bonusAdded = userBonusBalancePersistenceService.addBonusCoins(
      userId = userId, coinsAmount = coinsAmount, bonusBalanceType = bonusBalanceType, uniqueBonusKey = uniqueBonusKey
    )

    if (bonusAdded) {
      val trackVisibleCoins = abTestingFacade.isCoinsDoNotResetParticipant(userId, appPlatform)
      when {
        abTestingFacade.isEm2Participant(userId) ->
          userCurrentCoinsBalancePersistenceService.updateEm2CurrentBonusBalance(userId, coinsAmount, trackVisibleCoins)

        else ->
          userCurrentCoinsBalancePersistenceService.updateCurrentBonusBalance(userId, coinsAmount, trackVisibleCoins)
      }
      messageBus.publish(NonGameCoinsAddedEventDto(userId))
      val coins = userCurrentCoinsBalanceService.getUserCurrentCoinsBalance(userId, appPlatform).coins
      val abInflatingCoinsMultiplier = emExperimentBaseService.inflatingCoinsMultiplier(userId)
      messageBus.publish(
        userCoinsAddedEvent {
          this.userId = userId
          this.coins = coins
          this.coinsAdded = (coinsAmount * abInflatingCoinsMultiplier.toBigDecimal()).toLong()
          this.bonusCoinsData = bonusCoins {}
        }
      )
    }

    if (bonusBalanceType == UserBonusBalanceType.COINS_AFTER_CASHOUT) {
      applyNonResettableCoinsExp(userId, appPlatform)
    }
  }

  suspend fun findLastWelcomeCoinsDate(userId: String) = userBonusBalancePersistenceService.findLastWelcomeCoinsDate(userId)

  private suspend fun applyNonResettableCoinsExp(userId: String, platform: AppPlatform) {
    val variation = abTestingFacade.coinsDoNotResetVariation(userId, platform) ?: return

    when (variation) {
      CoinsDoNotResetVariation.Simple -> {
        userCurrentCoinsBalanceService.resetVisibleCoins(userId)
      }

      CoinsDoNotResetVariation.Complete -> {
        userCurrentCoinsBalanceService.resetVisibleCoins(userId)
        userCurrentCoinsBalanceService.resetConvertibleCoinsEm2(userId)
        revenuePersistenceService.resetCurrentRevenue(userId)
      }

      CoinsDoNotResetVariation.Partially -> {
        val goalCoins = userCurrentCoinsBalanceService.loadUninflatedGoalCoins(userId).toBigDecimal()
        userCurrentCoinsBalanceService.setVisibleCoins(userId, goalCoins)
      }

      CoinsDoNotResetVariation.AtAll -> {}
      else -> {}
    }
  }

  suspend fun getBonusBankEarnings(userId: String): BigDecimal {
    val coins = userBonusBalancePersistenceService.getBonusBankUnpaidCoins(userId)

    val earnings = (coins * 0.5.toBigDecimal()) / 220.toBigDecimal() // TODO we should somehow move 220 to RS
    return earnings
  }
}