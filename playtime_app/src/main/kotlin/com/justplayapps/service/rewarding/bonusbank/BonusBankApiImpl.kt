package com.justplayapps.service.rewarding.bonusbank


import com.google.protobuf.empty
import com.justplayapps.playtime.rewarding.bonusbank.BonusBankApiGrpcKt
import com.justplayapps.playtime.rewarding.bonusbank.GetBonusCashBarStateResponseKt.BonusCashBarStateKt.bonusCashBarStateMilestone
import com.justplayapps.playtime.rewarding.bonusbank.GetBonusCashBarStateResponseKt.bonusCashBarState
import com.justplayapps.playtime.rewarding.bonusbank.RewardingBonusBank
import com.justplayapps.playtime.rewarding.bonusbank.getBonusCashBarStateResponse
import com.justplayapps.service.rewarding.earnings.EmExperimentBaseService
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.util.fromProto
import com.moregames.base.util.logger
import com.moregames.base.util.toProto
import com.moregames.playtime.util.roundDownToWhole
import java.math.BigDecimal
import javax.inject.Inject

class BonusBankApiImpl @Inject constructor(
  private val abTestingFacade: AbTestingFacade,
  private val bonusBankStorage: BonusBankStorage,
  private val emExperimentBaseService: EmExperimentBaseService,
) : BonusBankApiGrpcKt.BonusBankApiCoroutineImplBase() {

  override suspend fun getBonusCashBarState(request: RewardingBonusBank.GetBonusCashBarStateRequest):
    RewardingBonusBank.GetBonusCashBarStateResponse {

    val bbVariation = bbVariation(request.userId)

    if (bbVariation !is BonusBankVariation.BonusCashBar) {
      logger().error("User variation is not BonusCashBar. userId = ${request.userId}")

      return getBonusCashBarStateResponse {
        this.userId = request.userId
        this.disabled = empty { }
      }
    }

    val userBank = bbVariation.bank.also { it.init(request.userId, request.platform.fromProto(), bonusBankStorage) }
    val bcbBank = (userBank as? BonusCashBarBank) ?: run {
      logger().error("User bonus bank is not BonusCashBarBank. userId = ${request.userId}")

      return getBonusCashBarStateResponse {
        this.userId = request.userId
        this.disabled = empty { }
      }
    }

    val bcbState = bcbBank.getBonusBarWithMilestonesState()
    val abInflatingCoinsMultiplier = emExperimentBaseService.inflatingCoinsMultiplier(request.userId).toBigDecimal()

    return getBonusCashBarStateResponse {
      this.userId = request.userId
      this.enabled = bonusCashBarState {
        this.valueToReach = (bcbState.coinGoal.times(abInflatingCoinsMultiplier)).roundDownToWhole().toProto()
        this.currentValue = (bcbState.coins.times(abInflatingCoinsMultiplier)).roundDownToWhole().toProto()
        this.reward = bcbState.reward.getCoinsReward(abInflatingCoinsMultiplier).toProto()
        this.readyToClaim = bcbState.readyToClaim
        bcbState.milestones
          .filter { milestone -> milestone.reward is EM2_COINS }
          .forEach { milestone ->
            this.milestone.add(
              bonusCashBarStateMilestone {
                this.valueToReach = (milestone.coinGoal.times(abInflatingCoinsMultiplier)).roundDownToWhole().toProto()
                this.reward = milestone.reward.getCoinsReward(abInflatingCoinsMultiplier).toProto()
                this.status = milestone.status.toProto()
              }
            )
          }
      }
    }
  }

  private suspend fun bbVariation(userId: String): BonusBankVariation? =
    abTestingFacade.assignedVariation(userId, ClientExperiment.BONUS_BANK).toBonusBankVariation()

  private fun BaseRewardType.getCoinsReward(abInflatingCoinsMultiplier: BigDecimal): BigDecimal =
    ((this as? EM2_COINS)?.coins ?: BigDecimal.ZERO)
      .times(abInflatingCoinsMultiplier)
      .roundDownToWhole()
}