package com.justplayapps.service.rewarding.bonusbank

import com.moregames.base.dto.AppPlatform
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.random.Random

sealed interface BaseRewardType
sealed interface BaseReward {
  val reward: BaseRewardType
}

class EM2_COINS(val coins: BigDecimal) : BaseRewardType

val COINS_CUT_FACTOR = BigDecimal("0.8")

data class MilestoneWithReward(val milestone: BigDecimal, val reward: BaseRewardType)

class ChanceBased(val chance: BigDecimal, val reward: BaseRewardType)

class SingleReward(override val reward: BaseRewardType) : BaseReward
class ChanceBasedReward(val chances: List<ChanceBased>) : BaseReward {
  init {
    if (chances.sumOf { it.chance }.compareTo(BigDecimal.ONE) != 0)
      throw IllegalStateException("Invalid ChanceBasedReward percentage configuration. Sum should be 1")
  }

  override val reward: BaseRewardType
    get() = pickByChance(chances)
}

fun pickByChance(list: List<ChanceBased>): BaseRewardType {
  val rand = BigDecimal(Random.nextDouble()).setScale(5, RoundingMode.HALF_UP)
  var cumulative = BigDecimal.ZERO

  for (item in list) {
    cumulative += item.chance
    if (rand <= cumulative) {
      return item.reward
    }
  }

  throw IllegalStateException("No chance was met")
}

sealed class BaseBank(
  val reward: BaseReward,
) {
  protected lateinit var bonusBankStorage: BonusBankStorage
  protected lateinit var userId: String
  protected lateinit var platform: AppPlatform

  fun init(userId: String, platform: AppPlatform, bonusBankStorage: BonusBankStorage) {
    this.userId = userId
    this.bonusBankStorage = bonusBankStorage
    this.platform = platform
  }

  abstract suspend fun cutCoins(coins: BigDecimal): BigDecimal
}

sealed class BaseBonusBarBank(
  val coinGoal: BigDecimal,
  reward: BaseReward,
  val coinsCutFactor: BigDecimal = COINS_CUT_FACTOR
) : BaseBank(reward) {

  override suspend fun cutCoins(coins: BigDecimal): BigDecimal {
    val bank = bonusBankStorage.getBonusBank(userId)
    val coinsToAdd = coins * coinsCutFactor
    bonusBankStorage.saveBonusBank(
      bank.copy(
        bankBalance = bank.bankBalance + coinsToAdd,
      )
    )
    return coinsToAdd
  }

  protected open suspend fun processRewards(rewards: List<BaseRewardType>) {
    bonusBankStorage.claimReward(userId, platform, rewards)
  }

  open suspend fun claimReward() {
    val bank = bonusBankStorage.getBonusBank(userId)

    if (!isClaimValid(bank)) throw IllegalStateException("Claim is not possible")

    val fullCycles = bank.bankBalance.divide(coinGoal).toInt()

    val rawRewards = List(fullCycles) { reward.reward } + claimAdditionalRewards(bank)

    persistRewards(rawRewards, bank)
  }

  protected suspend fun persistRewards(rewards: List<BaseRewardType>, bank: BonusBank) {
    val aggregatedReward = aggregateRewards(rewards)

    processRewards(aggregatedReward)

    val remainderBalance = bank.bankBalance.remainder(coinGoal)

    bonusBankStorage.saveBonusBank(
      bank.copy(
        bankBalance = remainderBalance,
        lastClaimedBalance = remainderBalance,
      )
    )
  }

  protected open fun isClaimValid(bank: BonusBank): Boolean = bank.bankBalance >= coinGoal

  protected open fun claimAdditionalRewards(bank: BonusBank): List<BaseRewardType> = emptyList()

  private fun aggregateRewards(rewards: List<BaseRewardType>): List<BaseRewardType> {
    var totalCoins = BigDecimal.ZERO

    for (reward in rewards) {
      when (reward) {
        is EM2_COINS -> totalCoins += reward.coins
        else -> continue
      }
    }

    return listOf(
      EM2_COINS(totalCoins),
    )
  }

}

abstract class BaseBankWithMilestones(
  coinGoal: BigDecimal,
  reward: BaseReward,
  coinsCutFactor: BigDecimal = COINS_CUT_FACTOR
) : BaseBonusBarBank(coinGoal, reward, coinsCutFactor) {
  abstract val milestones: List<MilestoneWithReward>

  override fun claimAdditionalRewards(bank: BonusBank): List<BaseRewardType> {
    /**
     * for long chains general picture consists of:
     *     - cycle in which last claim was done. Those milestones could be partly claimed already.
     *     - next full cycles. All milestones in them could be taken without checks.
     *     - last cycle, unfinished. Those milestones could be already claimed or good to be claimed or yet in progress for this call
     */

    val lastClaimCycleRewards = getSingleCycleAdditionalRewards(bank)
    val fullAndLastCyclesBank = BonusBank(
      userId = userId,
      bankBalance = bank.bankBalance - this.coinGoal,
      lastClaimedBalance = BigDecimal.ZERO, // all following cycles could ignore last claim
    )

    val fullCycles = fullAndLastCyclesBank.bankBalance.divide(coinGoal).toInt()
    val fullCyclesRewards =
      if (fullCycles > 0) {
        val milestoneSumPerCycle = milestones.map { it.reward }
        (1..fullCycles).flatMap { milestoneSumPerCycle }
      } else emptyList()

    val lastCycleBank = BonusBank(
      userId = userId,
      bankBalance = fullAndLastCyclesBank.bankBalance.remainder(coinGoal),
      lastClaimedBalance = BigDecimal.ZERO,
    )
    val lastCycleRewards = getSingleCycleAdditionalRewards(lastCycleBank)

    return (lastClaimCycleRewards + fullCyclesRewards + lastCycleRewards)
  }

  override fun isClaimValid(bank: BonusBank): Boolean =
    bank.bankBalance >= coinGoal || milestones.any { bank.bankBalance >= it.milestone && bank.lastClaimedBalance < it.milestone }

  suspend fun getBonusBarWithMilestonesState(): BonusBarWithMilestonesState {
    val bank = bonusBankStorage.getBonusBank(userId)

    val fullCycles = bank.bankBalance.divide(coinGoal).toInt()

    val remainderBank = BonusBank(
      userId = userId,
      bankBalance = bank.bankBalance.remainder(coinGoal),
      // if last claim happened on previous cycle in current cycle milestones could be only READY_TO_CLAIM or IN_PROGRESS
      lastClaimedBalance = (bank.lastClaimedBalance.takeIf { fullCycles == 0 } ?: BigDecimal.ZERO)
    )

    val milestones = getSingleCycleAdditionalRewardsState(remainderBank)

    return BonusBarWithMilestonesState(
      coinGoal = this.coinGoal,
      coins = remainderBank.bankBalance,
      reward = reward.reward,
      readyToClaim = fullCycles > 0,
      milestones = milestones
    )
  }

  /**
   * while actually repeating calculation logic of parent's claimReward, it also returns detailed description of what claim consists of
   */
  suspend fun claim(): List<BonusBankWithMilestonesClaim> {
    val bank = bonusBankStorage.getBonusBank(userId)

    if (!isClaimValid(bank)) throw IllegalStateException("Claim is not possible")

    /**
     * "first cycle" - cycle during which previous claim was done
     * "middle cycles" - full cycles after "first cycle"
     * "current cycle" - cycle user is currently on
     */

    val firstCycleClaim = getSingleCycleClaim(bank)
      ?.let { listOf(it) }
      ?: emptyList()

    // we could just do dumb-cycle until balance is less than goal, but it's too boring
    val middleFullCycles = (bank.bankBalance.divide(coinGoal).toInt() - 1).coerceAtLeast(0)

    val middleFullCyclesClaims = List(middleFullCycles) {
      BonusBankWithMilestonesClaim(
        reward = reward.reward,
        milestones = milestones.map { milestone ->
          BonusBankMilestoneState(
            coinGoal = milestone.milestone,
            reward = milestone.reward,
            status = BonusBankMilestoneStatus.CLAIMED
          )
        }
      )
    }

    val lastCycleClaim =
      if (firstCycleClaim.firstOrNull()?.reward != null) { // only fully completed first cycle gives place for more than one cycle.
        val lastCycleBank =
          bank.copy(
            bankBalance = bank.bankBalance.remainder(coinGoal),
            lastClaimedBalance = BigDecimal.ZERO
          )
        getSingleCycleClaim(lastCycleBank)?.let { listOf(it) } ?: emptyList()
      } else emptyList()

    val claimData = firstCycleClaim + middleFullCyclesClaims + lastCycleClaim

    persistRewards(claimData.toRewards(), bank)

    return claimData
  }

  private fun getSingleCycleAdditionalRewards(bank: BonusBank): List<BaseRewardType> =
    getSingleCycleAdditionalRewardsState(bank)
      .filter { (_, _, status) -> status == BonusBankMilestoneStatus.READY_TO_CLAIM }
      .map { (_, reward, _) -> reward }


  private fun getSingleCycleAdditionalRewardsState(bank: BonusBank): List<BonusBankMilestoneState> =
    milestones.map { milestone ->
      BonusBankMilestoneState(
        coinGoal = milestone.milestone,
        reward = milestone.reward,
        status = when {
          milestone.milestone <= bank.lastClaimedBalance -> BonusBankMilestoneStatus.CLAIMED
          milestone.milestone <= bank.bankBalance -> BonusBankMilestoneStatus.READY_TO_CLAIM
          else -> BonusBankMilestoneStatus.IN_PROGRESS
        }
      )
    }

  private fun getSingleCycleClaim(bank: BonusBank): BonusBankWithMilestonesClaim? {
    val milestonesToClaim = getSingleCycleAdditionalRewardsState(bank)
      .filter { milestone -> milestone.status == BonusBankMilestoneStatus.READY_TO_CLAIM }
      .map { milestone -> milestone.copy(status = BonusBankMilestoneStatus.CLAIMED) }

    val barClaim = bank.bankBalance >= this.coinGoal

    if (!barClaim && milestonesToClaim.isEmpty()) return null

    return BonusBankWithMilestonesClaim(
      reward = if (barClaim) reward.reward else null,
      milestones = milestonesToClaim
    )
  }


  fun List<BonusBankWithMilestonesClaim>.toRewards(): List<BaseRewardType> =
    this.flatMap { claim ->
      claim.milestones.map { milestone -> milestone.reward } +
        (claim.reward?.let { listOf(it) } ?: emptyList())
    }
}

class BonusCashBarBank(
  coinGoal: BigDecimal,
  reward: BaseReward,
  override val milestones: List<MilestoneWithReward>,
) : BaseBankWithMilestones(coinGoal, reward)

class CarouselBank(
  coinGoal: BigDecimal,
  reward: BaseReward,
) : BaseBonusBarBank(coinGoal, reward)

class PiggyBank(
  coinGoal: BigDecimal,
  reward: BaseReward,
) : BaseBonusBarBank(coinGoal, reward) {

  override suspend fun processRewards(rewards: List<BaseRewardType>) {
    val coins = rewards.filterIsInstance<EM2_COINS>().sumOf { it.coins }
    bonusBankStorage.storeUserValue(userId, coins, ValuableType.EM2_COINS)
  }
}

data class BonusBarWithMilestonesState(
  val coinGoal: BigDecimal,
  val coins: BigDecimal,
  val reward: BaseRewardType,
  val readyToClaim: Boolean,
  val milestones: List<BonusBankMilestoneState> = emptyList(),
)

data class BonusBankMilestoneState(
  val coinGoal: BigDecimal,
  val reward: BaseRewardType,
  val status: BonusBankMilestoneStatus
)

data class BonusBankWithMilestonesClaim(
  val reward: BaseRewardType?, // null when no whole bar claim
  val milestones: List<BonusBankMilestoneState>
)

enum class BonusBankMilestoneStatus {
  IN_PROGRESS, READY_TO_CLAIM, CLAIMED;
}