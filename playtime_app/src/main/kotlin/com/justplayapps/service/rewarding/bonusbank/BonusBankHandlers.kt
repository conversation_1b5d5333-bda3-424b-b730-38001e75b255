package com.justplayapps.service.rewarding.bonusbank


import com.google.inject.Inject
import com.justplayapps.playtime.rewarding.bonusbank.RewardingBonusBank.BonusBankClaim
import com.justplayapps.service.rewarding.earnings.EmExperimentBaseService
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.bus.MessageHandler
import com.moregames.base.messaging.commands.CommandsClient
import com.moregames.base.util.fromProto
import com.moregames.base.util.logger
import com.moregames.playtime.user.bonuscashbar.dto.BonusCashBarMilestone
import com.moregames.playtime.user.bonuscashbar.dto.BonusCashBarMilestoneStatus
import com.moregames.playtime.util.roundDownToWhole
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.math.BigDecimal

class BonusBankHandlers @Inject constructor(
  private val commandsClient: CommandsClient,
  private val jsonSerializer: Json,
  private val abTestingFacade: AbTestingFacade,
  private val bonusBankStorage: BonusBankStorage,
  private val emExperimentBaseService: EmExperimentBaseService,
) {

  @MessageHandler
  suspend fun handleBonusBankClaim(message: BonusBankClaim) {
    val claimResult =
      when (val bbVariation = bbVariation(message.userId)) {
        is BonusBankVariation.BonusCashBar -> claimBonusCashBar(message, bbVariation)
        else -> {
          logger().error("Unsupported Bonus Bank variation for claim. userId = ${message.userId}, variation = ${bbVariation?.key}")

          BonusCashBarClaimResult(
            claims = listOf()
          )
        }
      }

    val result = jsonSerializer.encodeToString(claimResult)

    commandsClient.completeCommandSync(
      message.commandId,
      mapOf("data" to result)
    )
  }

  @MessageHandler
  suspend fun handleEngagementCarouselClaimCommand(message: RewardingEvents.EngagementCarouselClaimCommand) {
    val variation = abTestingFacade.assignedVariation(message.userId, ClientExperiment.ENGAGEMENT_CAROUSEL)
    if (variation == DEFAULT) return

    val bank = (variation.toBonusBankVariation()?.bank as EngagementCarouselBank)
      .also { it.init(message.userId, message.platform.fromProto(), bonusBankStorage) }

    bank.claimReward(message.taskId, message.commandId)
  }

  @MessageHandler
  suspend fun handleTaskCompletedEvent(message: RewardingEvents.FirstTaskCompletedEvent) {
    val variation = abTestingFacade.assignedVariation(message.userId, ClientExperiment.ENGAGEMENT_CAROUSEL)
    if (variation == DEFAULT) return

    val bank = (variation.toBonusBankVariation()?.bank as EngagementCarouselBank)
      .also { it.init(message.userId, message.platform.fromProto(), bonusBankStorage) }

    bank.calculateDiamondFirstPrice(message.taskId)
  }

  private suspend fun claimBonusCashBar(message: BonusBankClaim, variation: BonusBankVariation.BonusCashBar): BonusCashBarClaimResult {

    val userBank = variation.bank.also { it.init(message.userId, message.platform.fromProto(), bonusBankStorage) }
    val abInflatingCoinsMultiplier = emExperimentBaseService.inflatingCoinsMultiplier(message.userId).toBigDecimal()

    val bcbBank = (userBank as? BonusCashBarBank) ?: run {
      logger().error("User bonus bank is not BonusCashBarBank. userId = ${message.userId}")

      return BonusCashBarClaimResult(
        claims = listOf()
      )
    }

    return try {
      bcbBank.claim()
    } catch (ex: Exception) {
      logger().error("User bonus bank claim exception. userId = ${message.userId}", ex)

      return BonusCashBarClaimResult(
        claims = listOf()
      )
    }
      .map { bcbClaim ->
        val rewardAmount = (bcbClaim.reward as? EM2_COINS)?.coins?.times(abInflatingCoinsMultiplier)?.roundDownToWhole()

        BonusCashBarClaim(
          barClaimed = rewardAmount != null,
          barReward = rewardAmount,
          claimedMilestones = bcbClaim.milestones.toBonusCashBarMilestones(abInflatingCoinsMultiplier),
        )
      }
      .let { BonusCashBarClaimResult(claims = it) }
  }

  private fun List<BonusBankMilestoneState>.toBonusCashBarMilestones(abInflatingCoinsMultiplier: BigDecimal): List<BonusCashBarMilestone> =
    this
      .map { milestone ->
        val reward = (milestone.reward as? EM2_COINS)?.coins?.times(abInflatingCoinsMultiplier)?.roundDownToWhole()
        val goal = milestone.coinGoal.times(abInflatingCoinsMultiplier).roundDownToWhole()

        BonusCashBarMilestone(
          valueToReach = goal,
          reward = reward ?: BigDecimal.ZERO,
          status = BonusCashBarMilestoneStatus.CLAIMED
        )
      }

  private suspend fun bbVariation(userId: String): BonusBankVariation? =
    abTestingFacade.assignedVariation(userId, ClientExperiment.BONUS_BANK).toBonusBankVariation()
}