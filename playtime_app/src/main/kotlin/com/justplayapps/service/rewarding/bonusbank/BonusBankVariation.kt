package com.justplayapps.service.rewarding.bonusbank

import com.moregames.base.abtesting.BaseVariation
import com.moregames.base.abtesting.BaseVariationDefault
import java.math.BigDecimal

sealed class BonusBankVariation(val key: String, val bank: BaseBank) {
  data object BonusCashBar : BonusBankVariation(
    key = "bonusCashBar",
    bank = BonusCashBarBank(
      // numbers assume we have x2000 multiplier, though changing multiplier will not break arithmetic, could only affect UI
      coinGoal = BigDecimal("500"),
      milestones = listOf(
        MilestoneWithReward(BigDecimal("125"), EM2_COINS(BigDecimal("7.5"))),
        MilestoneWithReward(BigDecimal("250"), EM2_COINS(BigDecimal("7.5"))),
        MilestoneWithReward(BigDecimal("375"), EM2_COINS(BigDecimal("10"))),
      ),
      reward = SingleReward(EM2_COINS(BigDecimal("100")))
    )
  )

  data object CarouselV1 : BonusBankVariation(
    key = "carouselV1",
    bank = CarouselBank(
      coinGoal = 1_000_000.toBigDecimal(),
      reward = ChanceBasedReward(
        listOf(
          ChanceBased(0.56.toBigDecimal(), EM2_COINS(25_000.toBigDecimal())),
          ChanceBased(0.25.toBigDecimal(), EM2_COINS(50_000.toBigDecimal())),
          ChanceBased(0.185.toBigDecimal(), EM2_COINS(100_000.toBigDecimal())),
          ChanceBased(0.005.toBigDecimal(), EM2_COINS(1_000_000.toBigDecimal())),
        )
      )
    )
  )

  data object BiggyBankV1 : BonusBankVariation(
    key = "piggyBankV1",
    bank = PiggyBank(
      coinGoal = 1_000_000.toBigDecimal(),
      reward = SingleReward(EM2_COINS(250_000.toBigDecimal()))
    )
  )

  data object EngagementCarouselV1 : BonusBankVariation(
    key = "engagementCarousel",
    bank = EngagementCarouselBank(
      firstReward = SingleReward(
        BankSplit(EARNINGS::class, 1.toBigDecimal(), 0.toBigDecimal())
      ),
      reward = ChanceBasedReward(
        listOf(
          ChanceBased(0.3.toBigDecimal(), BankSplit(EM2_COINS::class, 0.15.toBigDecimal(), 0.85.toBigDecimal())),
          ChanceBased(0.1.toBigDecimal(), BankSplit(EM2_COINS::class, 0.3.toBigDecimal(), 0.7.toBigDecimal())),
          ChanceBased(0.3.toBigDecimal(), BankSplit(EARNINGS::class, 0.5.toBigDecimal(), 0.5.toBigDecimal())),
          ChanceBased(0.2.toBigDecimal(), BankSplit(EARNINGS::class, 1.toBigDecimal(), 0.toBigDecimal())),
          ChanceBased(0.1.toBigDecimal(), BOOSTER())
        )
      )
    )
  )
}

fun BaseVariation.toBonusBankVariation(): BonusBankVariation? {
  if (this == BaseVariationDefault) return null
  return BonusBankVariation::class.sealedSubclasses
    .mapNotNull { it.objectInstance }
    .first { it.key == this.getKey() }
}