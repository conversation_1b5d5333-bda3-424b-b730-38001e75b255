package com.justplayapps.service.rewarding.bonus

import org.jetbrains.exposed.dao.id.IntIdTable
import org.jetbrains.exposed.sql.`java-time`.timestamp

object UserBonusesBalanceTable : IntIdTable("playtime.user_bonuses_balance") {
  val userId = varchar("user_id", 36)
  val bonusType = varchar("bonus_type", 36)
  val uniqueBonusKey = varchar("unique_bonus_key", 36)
  val coins = integer("coins")
  val decimalCoins = decimal("decimals_coins", 18, 6)
  val cashoutTransactionId = varchar("cashout_transaction_id", 36).nullable()
  val createdAt = timestamp("created_at")
}
