package com.justplayapps.service.rewarding.bonus

import com.google.inject.Inject
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.user.UserBonusBalanceType
import com.moregames.base.user.UserBonusBalanceType.WELCOME_COINS
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import java.math.BigDecimal
import javax.inject.Singleton

@Singleton
class UserBonusBalancePersistenceService @Inject constructor(database: Database) : BasePersistenceService(database) {

  suspend fun addBonusCoins(userId: String, coinsAmount: BigDecimal, bonusBalanceType: UserBonusBalanceType, uniqueBonusKey: String? = null): Boolean =
    dbQuery {
      UserBonusesBalanceTable.insertIgnoreAndGetId {
        it[UserBonusesBalanceTable.userId] = userId
        it[coins] = 0
        it[decimalCoins] = coinsAmount
        it[bonusType] = bonusBalanceType.name
        it[UserBonusesBalanceTable.uniqueBonusKey] = uniqueBonusKey ?: ""
      } ?: return@dbQuery false
      return@dbQuery true
    }

  suspend fun findLastWelcomeCoinsDate(userId: String) = dbQuery {
    UserBonusesBalanceTable
      .slice(UserBonusesBalanceTable.createdAt)
      .select((UserBonusesBalanceTable.userId eq userId) and (UserBonusesBalanceTable.bonusType eq WELCOME_COINS.name))
      .orderBy(UserBonusesBalanceTable.createdAt, SortOrder.DESC)
      .limit(1)
      .map { it[UserBonusesBalanceTable.createdAt] }
      .firstOrNull()
  }

  suspend fun hasBonusFor(userId: String, bonusBalanceType: UserBonusBalanceType, uniqueBonusKey: String? = null) = dbQuery {
    UserBonusesBalanceTable
      .slice(UserBonusesBalanceTable.userId)
      .select {
        (UserBonusesBalanceTable.userId eq userId) and
          (UserBonusesBalanceTable.bonusType eq bonusBalanceType.name) and
          (uniqueBonusKey?.let { UserBonusesBalanceTable.uniqueBonusKey eq uniqueBonusKey } ?: Op.TRUE)
      }
      .limit(1)
      .firstOrNull() != null
  }

  suspend fun setCashoutTransactionForBonusBankCoins(userId: String, cashoutTransactionId: String) = dbQuery {
    UserBonusesBalanceTable.update({ (UserBonusesBalanceTable.userId eq userId) and (UserBonusesBalanceTable.bonusType eq UserBonusBalanceType.BONUS_BANK_REWARD.name) and (UserBonusesBalanceTable.cashoutTransactionId.isNull()) }) {
      it[UserBonusesBalanceTable.cashoutTransactionId] = cashoutTransactionId
    }
  }

  suspend fun resetCashoutTransactionForBonusBankCoins(userId: String, cashoutTransactionId: String) = dbQuery {
    UserBonusesBalanceTable.update({
      (UserBonusesBalanceTable.userId eq userId) and (UserBonusesBalanceTable.bonusType eq UserBonusBalanceType.BONUS_BANK_REWARD.name) and (UserBonusesBalanceTable.cashoutTransactionId eq cashoutTransactionId)
    }) {
      it[UserBonusesBalanceTable.cashoutTransactionId] = null
    }
  }

  suspend fun getBonusBankUnpaidCoins(userId: String) = dbQuery {
    UserBonusesBalanceTable
      .slice(UserBonusesBalanceTable.decimalCoins)
      .select {
        (UserBonusesBalanceTable.userId eq userId) and
          (UserBonusesBalanceTable.bonusType eq UserBonusBalanceType.BONUS_BANK_REWARD.name) and
          (UserBonusesBalanceTable.cashoutTransactionId eq null)
      }
      .map { it[UserBonusesBalanceTable.decimalCoins] }
      .sumOf { it }
  }
}