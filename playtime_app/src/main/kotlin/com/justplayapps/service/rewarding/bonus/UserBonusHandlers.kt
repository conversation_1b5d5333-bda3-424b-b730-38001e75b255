package com.justplayapps.service.rewarding.bonus

import com.google.inject.Inject
import com.justplayapps.playtime.proto.PlaytimeEvents
import com.justplayapps.service.payment.proto.PaymentEvents
import com.moregames.base.bus.EffectHandler

class UserBonusHandlers @Inject constructor(
  private val userBonusBalancePersistenceService: UserBonusBalancePersistenceService
) {

  @EffectHandler
  suspend fun cashoutCreated(message: PlaytimeEvents.CashoutCreatedEvent) {
    with(message) {
      userBonusBalancePersistenceService.setCashoutTransactionForBonusBankCoins(userId, cashoutTransactionId)
    }
  }

  @EffectHandler
  suspend fun cashoutRejected(message: PaymentEvents.CashoutRejectedEvent) {
    with(message) {
      userBonusBalancePersistenceService.resetCashoutTransactionForBonusBankCoins(userId, cashoutTransactionId)
    }
  }
}
