package com.justplayapps.service.rewarding.bonusbank

import com.google.inject.Inject
import com.justplayapps.service.rewarding.bonus.UserBonusBalanceService
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.user.UserBonusBalanceType
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import java.math.BigDecimal
import java.util.*

class BonusBankStorage @Inject constructor(
  private val bonusBankPersistenceService: BonusBankPersistenceService,
  private val userValuablesStoragePersistenceService: UserValuablesStoragePersistenceService,
  private val userBonusBalanceService: UserBonusBalanceService,
  private val messageBus: MessageBus,
) {

  suspend fun getBonusBank(userId: String): BonusBank {
    return bonusBankPersistenceService.getBonusBank(userId)
  }

  suspend fun saveBonusBank(bank: BonusBank) {
    bonusBankPersistenceService.saveBonusBank(bank)
  }

  suspend fun getEngCarouselBank(userId: String, taskId: String): EngCarouselBank {
    return bonusBankPersistenceService.getEngCarouselBank(userId, taskId)
  }

  suspend fun saveEngCarouselBank(bank: EngCarouselBank) {
    bonusBankPersistenceService.saveEngCarouselBank(bank)
  }

  suspend fun getEngCarouselCfg(userId: String): EngCarouselConfig {
    return bonusBankPersistenceService.getEngCarouselCfg(userId)
  }

  suspend fun saveEngCarouselCfg(cfg: EngCarouselConfig) {
    bonusBankPersistenceService.saveEngCarouselCfg(cfg)
  }

  suspend fun claimReward(userId: String, platform: AppPlatform, rewards: List<BaseRewardType>, sendClaimedEvent: Boolean = false) {
    rewards.forEach { reward ->
      when (reward) {
        is EM2_COINS -> {
          if (reward.coins <= BigDecimal.ZERO) return@forEach
          userBonusBalanceService.addBonusCoins(userId, platform, reward.coins, UserBonusBalanceType.BONUS_BANK_REWARD, UUID.randomUUID().toString())
        }
        // TODO
        is EARNINGS -> {

        }

        is DIAMOND -> {
//          if (sendClaimedEvent) {
//            messageBus.publish(
//              rewardClaimedEvent {
//                this.userId = userId
//                this.amount = 1.toBigDecimal().toProto()
//                this.commandId = ""
//                this.type = DIAMOND
//              }
//            )
//          }
        }

        is DIAMONDS_REWARD -> {

        }

        is BankSplit -> {
          logger().alert("Invalid reward type: ${reward.rewardType}. Fix it!")
        }
      }
    }
  }

  suspend fun storeUserValue(userId: String, amount: BigDecimal, valuableType: ValuableType) {
    if (amount <= BigDecimal.ZERO) return
    userValuablesStoragePersistenceService.save(userId, amount, valuableType)
  }

  suspend fun getUserValue(userId: String, valuableType: ValuableType): BigDecimal {
    return userValuablesStoragePersistenceService.get(userId, valuableType)
  }
}

data class BonusBank(
  val userId: String,
  val bankBalance: BigDecimal,
  val lastClaimedBalance: BigDecimal,
)

data class EngCarouselBank(
  val userId: String,
  val taskId: String,
  val bankBalance: BigDecimal,
  val isClaimed: Boolean,
)

data class EngCarouselConfig(
  val userId: String,
  val wasEverClaimed: Boolean,
  val diamondPrice: BigDecimal,
) {
  fun isFirstClaim(): Boolean = !wasEverClaimed
}