package com.moregames.playtime.ios

import com.moregames.base.user.iosDeviceMajorVersionFromAgentOrNull
import com.moregames.base.util.logger
import com.moregames.playtime.games.IosGameService
import com.moregames.playtime.games.IosGamesServiceV2
import com.moregames.playtime.ios.dto.NewGamesApiDto
import com.moregames.playtime.ios.dto.NewGamesApiV2Dto
import com.moregames.playtime.ios.dto.PlayedGamesApiDto
import com.moregames.playtime.ios.dto.PlayedGamesApiV2Dto
import com.moregames.playtime.user.UserController.Companion.userId
import com.moregames.playtime.util.getLocale
import io.ktor.application.*
import io.ktor.response.*
import io.ktor.routing.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class IosGamesController @Inject constructor(
  private val iosGameService: IosGameService,
  private val iosGamesServiceV2: IosGamesServiceV2,
) {

  fun startRouting(route: Route) = with(route) {
    route("/games") {
      regularGames()
      route("/v2") {
        gamesV2()
      }
    }
  }

  private fun Route.regularGames() {
    get("/played") {
      call.respond(
        PlayedGamesApiDto(
          games = iosGameService.loadPlayedGames(userId(), getLocale(logger()))
        )
      )
    }
    get("/new") {
      call.respond(
        NewGamesApiDto(
          games = iosGameService.loadNewGames(userId(), getLocale(logger()), iosDeviceMajorVersionFromAgentOrNull())
        )
      )
    }
  }

  private fun Route.gamesV2() {
    get("/played") {
      call.respond(
        PlayedGamesApiV2Dto(
          games = iosGamesServiceV2.loadPlayedGames(userId(), getLocale(logger()))
        )
      )
    }
    get("/new") {
      call.respond(
        NewGamesApiV2Dto(
          games = iosGamesServiceV2.loadNewGames(userId(), getLocale(logger()))
        )
      )
    }
    get("/all") {
      call.respond(
        NewGamesApiV2Dto(
          games = iosGamesServiceV2.loadAllGames(userId(), getLocale(logger()))
        )
      )
    }
  }
}