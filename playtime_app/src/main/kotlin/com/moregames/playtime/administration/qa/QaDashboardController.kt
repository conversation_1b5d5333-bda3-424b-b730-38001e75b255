package com.moregames.playtime.administration.qa

import com.google.api.services.playintegrity.v1.model.DecodeIntegrityTokenRequest
import com.google.inject.Inject
import com.moregames.base.abtesting.AbTestingPersistenceService
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.dto.Experiment
import com.moregames.base.app.BuildVariant
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.config.ServicesRegistry
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.encryption.HashService
import com.moregames.base.externalip.ExternalIpService
import com.moregames.base.messaging.AppNotification
import com.moregames.base.messaging.customnotification.*
import com.moregames.base.messaging.customnotification.BackgroundAction.REFRESH_OFFERS
import com.moregames.base.messaging.customnotification.BackgroundAction.REFRESH_USER
import com.moregames.base.messaging.customnotification.CustomNotificationSize.MEDIUM
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_APP_ID
import com.moregames.base.util.Constants.JUSTPLAY_APPLICATION_ID
import com.moregames.base.util.TimeService
import com.moregames.playtime.administration.blacklist.BlacklistController.AffectedUserIdsResponseDto
import com.moregames.playtime.administration.dto.AssignedVariationsResponse
import com.moregames.playtime.administration.dto.ExperimentKeyParam
import com.moregames.playtime.administration.dto.ExperimentVariationsDto
import com.moregames.playtime.administration.user.UserAdministrationController.ExperimentFinishDto
import com.moregames.playtime.administration.user.UserAdministrationController.ExperimentStartDto
import com.moregames.playtime.app.CoreModule.Companion.CROSS_SERVICE_HTTP_CLIENT
import com.moregames.playtime.app.GitProperties
import com.moregames.playtime.app.PlaytimeSecrets
import com.moregames.playtime.games.GamesFallbackService
import com.moregames.playtime.games.fallback.dto.android.AndroidFallbackApiDto
import com.moregames.playtime.games.fallback.dto.ios.IosFallbackApiDto
import com.moregames.playtime.notifications.PushNotificationAnalyticalLabel.QA_NOTIFICATION
import com.moregames.playtime.user.verification.SeonClient
import com.moregames.playtime.user.verification.dto.EmailCheckResponseDto
import com.moregames.playtime.util.PlayIntegrityClientProvider
import com.papsign.ktor.openapigen.APITag
import com.papsign.ktor.openapigen.annotations.Request
import com.papsign.ktor.openapigen.annotations.Response
import com.papsign.ktor.openapigen.annotations.parameters.PathParam
import com.papsign.ktor.openapigen.annotations.parameters.QueryParam
import com.papsign.ktor.openapigen.route.info
import com.papsign.ktor.openapigen.route.path.auth.OpenAPIAuthenticatedRoute
import com.papsign.ktor.openapigen.route.path.auth.get
import com.papsign.ktor.openapigen.route.path.auth.patch
import com.papsign.ktor.openapigen.route.path.auth.post
import com.papsign.ktor.openapigen.route.response.OpenAPIPipelineResponseContext
import com.papsign.ktor.openapigen.route.response.respond
import com.papsign.ktor.openapigen.route.route
import com.papsign.ktor.openapigen.route.tag
import io.ktor.auth.*
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.http.*
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import javax.inject.Named
import javax.inject.Singleton

@Singleton
class QaDashboardController @Inject constructor(
  private val buildVariant: BuildVariant,
  private val abTestingService: AbTestingService,
  private val abTestingPersistenceService: AbTestingPersistenceService,
  @Named(CROSS_SERVICE_HTTP_CLIENT) private val crossServiceHttpClient: HttpClient,
  private val qaDashboardService: QaDashboardService,
  private val seonClient: SeonClient,
  private val gamesFallbackService: GamesFallbackService,
  private val timeService: TimeService,
  private val externalIpService: ExternalIpService,
  private val playIntegrityClientProvider: PlayIntegrityClientProvider,
  private val encryptionService: EncryptionService,
  private val hashService: HashService,
  private val applicationConfig: ApplicationConfig,
) {
  private val root = "/qa"

  fun startRouting(adminRoute: OpenAPIAuthenticatedRoute<UserIdPrincipal>) {
    if (buildVariant == BuildVariant.PRODUCTION)
      throw IllegalStateException("Not allowed in production mode")
    info()
    adminRoute.route(root) {
      info("QA endpoints")
      tag(QaTags.Users) {
        route("/users") {
          info("User QA endpoints")
          route("/whitelist-user") {
            post<UserOrTrackingIdParam, HttpStatusCode, WhitelistUserRequestDto, UserIdPrincipal>(
              info("[Autotests] Whitelist user", "Whitelist user by userid or trackingId (idfa/idfv) and drop fraud transactions"),
              exampleRequest = WhitelistUserRequestDto(true)
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.whitelistUser(userId, request.dropFraudTransactions)
              }
            }
          }
          route("/drop-user-cache") {
            get<UserOrTrackingIdParam, HttpStatusCode, UserIdPrincipal>(
              info("Drop user data cache", "Drop user's cache: app version, countryCode, tracking data, limiting tracking and so on by userId or trackingId")
            ) { params ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.dropUserCache(userId)
              }
            }
          }
          route("/whitelist-trackingId") {
            post<TrackingIdParam, HttpStatusCode, Unit, UserIdPrincipal>(
              info("Add user trackingId to whiteList")
            ) { params, _ ->
              qaDashboardService.whitelistTrackingData(params.trackingId)
              respond(HttpStatusCode.OK)
            }
          }
          route("/remove-trackingId-from-whitelist") {
            post<TrackingIdParam, HttpStatusCode, Unit, UserIdPrincipal>(
              info("remove user trackingId from whiteList")
            ) { params, _ ->
              qaDashboardService.removeTrackingIdFromWhitelist(params.trackingId)
              respond(HttpStatusCode.OK)
            }
          }
          route("/blacklist-trackingId") {
            post<TrackingIdParam, AffectedUserIdsResponseDto, BlacklistTrackingIdRequestDto, UserIdPrincipal>(
              exampleRequest = BlacklistTrackingIdRequestDto("some reason")
            ) { params, request ->
              val affectedUserIds = qaDashboardService.blacklistTrackingId(params.trackingId, request.reason)
              respond(AffectedUserIdsResponseDto(affectedUserIds))
            }
          }
          route("/verify-email-seon") {
            post<Unit, EmailCheckResponseDto, QaEmailCheckRequestDto, UserIdPrincipal>(
              exampleRequest = QaEmailCheckRequestDto("<EMAIL>")
            ) { _, request ->
              val email = request.email
              respond(seonClient.checkEmail(email))
            }
          }
          route("/verify-face") {
            post<UserOrTrackingIdParam, HttpStatusCode, Unit, UserIdPrincipal>(
              info("[Autotests] Verify user's face", "Verify user's face by userId or trackingId"),
            ) { params, _ ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.verifyFace(userId)
              }
            }
          }
          route("/verify-ios-specific-steps") {
            post<UserOrTrackingIdParam, HttpStatusCode, Unit, UserIdPrincipal>(
              info("Verify user", "Verify IOS user's specific steps by userId or trackingId"),
            ) { params, _ ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.verifyIosSpecificSteps(userId)
              }
            }
          }
          route("/remove-face") {
            get<UserOrTrackingIdParam, HttpStatusCode, UserIdPrincipal>(
              info(
                "[Autotests] Remove user's faces",
                "Remove user's faces by userId or trackingId from facetec search database."
              )
            ) { params ->
              val userIds = qaDashboardService.fetchAllUserIds(params)
              userIds?.forEach { userId -> qaDashboardService.removeFaceByUserId(userId) }
              respond(HttpStatusCode.OK)
            }
          }
          route("/send-generic-notification") {
            post<UserOrTrackingIdParam, HttpStatusCode, NotificationTextRequestDto, UserIdPrincipal>(
              info("Send generic notification", "Send notification with custom text by userId or trackingId.")
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.sendGenericNotification(userId, request.notificationText)
              }
            }
          }
          route("/send-free-form-custom-notification") {
            post<UserOrTrackingIdParam, HttpStatusCode, QaFreeFormCustomNotificationApiDto, UserIdPrincipal>(
              info("Send custom notification as string-string map", "Send custom notification via firebase. Does not work for IOS currently."),
              exampleRequest = QaFreeFormCustomNotificationApiDto(
                notificationType = "customNotification",
                data = mapOf("key" to "value"),
              )
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.sendQaFreeFormCustomNotification(userId, request)
              }
            }
          }
          route("/send-custom-notification") {
            post<UserOrTrackingIdParam, HttpStatusCode, CustomNotificationDto, UserIdPrincipal>(
              info(
                "Send custom notification",
                "Send custom notification via firebase"
              ),
              exampleRequest = CustomNotificationDto(
                id = CustomNotificationDto.NotificationChannelId.CUSTOM_NOTIFICATION,
                notificationId = "22a44fc9-a9bf-4817-bbcf-4e51ee9ea590",
                notificationType = CustomNotificationType.CUSTOM_NOTIFICATION,
                title = "some title",
                shortDescription = "some short description",
                icon = "https://storage.googleapis.com/public-playtime/images/block_puzzle_offer_icon.jpg",
                image = "https://storage.googleapis.com/public-playtime/images/block_puzzle_offer_image.jpg",
                backgroundColor = "#A090B0",
                size = MEDIUM,
                countdownTimerTarget = "60000",
                vibrationEnabled = true,
                soundEnabled = false,
                onClickAction = OnClickActionApiDto.openLinkInPopUp("https://www.justplayapps.com/privacy-policy"),
                backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
                textColor = "#5262FB",
                actionButtons = listOf(
                  ButtonApiDto("Play", "#5262FB", "#FFFFFF", ButtonAction.scrollToOfferAndOpenPreGameScreen(200032)),
                  ButtonApiDto("Discard", "#5262FB", "#FFFFFF", ButtonAction.discardNotification())
                ),
                label = QA_NOTIFICATION.apiName
              )
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.sendCustomNotifications(userId, request)
              }
            }
          }
          route("/send-email-notification") {
            post<UserOrTrackingIdParam, HttpStatusCode, QaEmailNotificationRequest, UserIdPrincipal>(
              info(summary = "Send email notification", description = "Send email notification"),
              exampleRequest = QaEmailNotificationRequest(
                name = "Vart Deider",
                email = "<EMAIL>",
                sendGridTemplateId = "d-d1c6c92d85c648bead0e89a86b17947b"
              )
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.sendEmailNotification(userId, request)
              }
            }
          }
          route("/change-creation-timestamp") {
            post<UserOrTrackingIdParam, HttpStatusCode, ChangeUserCreationTimestampRequestDto, UserIdPrincipal>(
              info("Change user creation timestamp", "This will allow to check logic that rely on user creation time"),
              exampleRequest = ChangeUserCreationTimestampRequestDto(Instant.now().minus(5, ChronoUnit.HOURS))
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.changeUserCreationTimestamp(userId, request.newCreationDate)
              }
            }
          }
          route("/change-country-code") {
            post<UserOrTrackingIdParam, HttpStatusCode, ChangeUserCountryCodeRequestDto, UserIdPrincipal>(
              info("Change user country code", "This will allow to check logic that rely on user country code"),
              exampleRequest = ChangeUserCountryCodeRequestDto("CA")
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.changeUserCountryCode(userId, request.countryCode)
              }
            }
          }
          route("/change-user-locale") {
            post<UserOrTrackingIdParam, HttpStatusCode, ChangeUserLocaleRequestDto, UserIdPrincipal>(
              info("Change user locale", "This will allow to check logic that rely on user locale"),
              exampleRequest = ChangeUserLocaleRequestDto("fr-CA")
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.changeUserLocale(userId, request.languageTag)
              }
            }
          }
          route("/add-generic-revenue") {
            post<UserOrTrackingIdParam, HttpStatusCode, AddGenericRevenueRequestDto, UserIdPrincipal>(
              info(
                "Add revenue to user account",
                "Add revenue to user account associated with specific user id or tracking Id. (Individual cashout periods only)"
              ),
              exampleRequest = AddGenericRevenueRequestDto(RevenueSource.APPLOVIN, BigDecimal("1.00"))
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.addGenericRevenue(userId, request.source, request.revenueAmount, request.gameId)
              }
            }
          }
          route("/add-earnings") {
            post<UserOrTrackingIdParam, HttpStatusCode, AddEarningsRequestDto, UserIdPrincipal>(
              info("[Autotests] Add earnings to user account", "Add earnings to user account by tracking id or user id + allow cashout"),
              exampleRequest = AddEarningsRequestDto(
                amountUSD = BigDecimal("1.00"),
                currencyCode = "USD",
                amount = BigDecimal("1.00")
              )
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.addEarnings(userId, request)
              }
            }
          }
          route("/add-coins") {
            post<UserOrTrackingIdParam, HttpStatusCode, AddCoinsRequestDto, UserIdPrincipal>(
              info("Add coins to user account", "Add coins to user account associated with specific trackingId"),
              exampleRequest = AddCoinsRequestDto(
                applicationId = TREASURE_MASTER_APP_ID,
                score = 17,
                amount = 0,
                isNewRecord = true,
              )
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.addCoins(userId, request)
              }
            }
          }
          route("/add-offerwall-offer-completion-coins") {
            post<UserOrTrackingIdParam, HttpStatusCode, QaTapjoyCoinsReport, UserIdPrincipal>(
              info(
                "Add Tapjoy offerwall offer completion coins",
                "Add Tapjoy offerwall offer completion coins to user account associated with specific user id or tracking Id."
              ),
              exampleRequest = QaTapjoyCoinsReport(
                coins = 1100,
                revenue = BigDecimal("5.0"),
                currencySale = BigDecimal("1.0")
              )
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.addOfferwallOfferCompletionCoins(userId, request)
              }
            }
          }
          route("/add-fyber-offerwall-offer-completion-coins") {
            post<UserOrTrackingIdParam, HttpStatusCode, QaTapjoyCoinsReport, UserIdPrincipal>(
              info(
                "Add Fyber offerwall offer completion coins",
                "Add Fyber offerwall offer completion coins to user account associated with specific user Id or Tracking Id."
              ),
              exampleRequest = QaTapjoyCoinsReport(
                coins = 1100,
                revenue = BigDecimal("5.0"),
                currencySale = BigDecimal("1.0")
              )
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.addFyberOfferwallOfferCompletionCoins(userId, request)
              }
            }
          }
          route("/fraudScore") {
            patch<UserOrTrackingIdParam, HttpStatusCode, QaSetFraudScoreTotal, UserIdPrincipal>(
              info("Sets fraud score", "Set desired total fraud score for user with specified user Id or tracking Id"),
              exampleRequest = QaSetFraudScoreTotal(
                score = 100,
              )
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.setFraudScore(userId, request.score)
              }
            }
          }
          route("/frozenFraudScore") {
            patch<UserOrTrackingIdParam, HttpStatusCode, QaSetFraudScoreTotal, UserIdPrincipal>(
              info("Sets frozen fraud score", "Set desired total frozen fraud score for user with specified user id or tracking Id"),
              exampleRequest = QaSetFraudScoreTotal(
                score = 100,
              )
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.setFrozenFraudScore(userId, request.score)
              }
            }
          }
          route("/pass-strong-attestation") {
            post<UserOrTrackingIdParam, HttpStatusCode, Unit, UserIdPrincipal> { params, _ ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.passStrongAttestation(userId)
              }
            }
          }
          route("/set-usual-cashout-periods") {
            post<UserOrTrackingIdParam, HttpStatusCode, SetUsualCashoutPeriodsRequestDto, UserIdPrincipal>(
              exampleRequest = SetUsualCashoutPeriodsRequestDto(manualCashoutPeriodsRun = true)
            ) { params, request ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.setUsualCashoutPeriods(userId, request.manualCashoutPeriodsRun)
              }
            }
          }
          route("/pass-device-attestation") {
            post<UserOrTrackingIdParam, HttpStatusCode, Unit, UserIdPrincipal> { params, _ ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.passDeviceAttestation(userId)
              }
            }
          }
          route("/pass-jailBreak-check") {
            post<UserOrTrackingIdParam, HttpStatusCode, Unit, UserIdPrincipal> { params, _ ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.passJailBreakCheck(userId)
              }
            }
          }
          route("/cashout-period-end") {
            get<UserOrTrackingIdParam, HttpStatusCode, UserIdPrincipal>(
              info(
                "Trigger cashout period end for a special user",
                "Trigger cashout period end for a qa user, created with speedUpCashoutPeriodEnd setting",
              )
            ) { params ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.endCashoutPeriod(userId)
              }
            }
          }
          route("/force-highly-trusted") {
            post<UserOrTrackingIdParam, HttpStatusCode, Unit, UserIdPrincipal>(
              info("Force highly trusted user", "Force user to be highly trusted")
            ) { params, _ ->
              resolveUserIdAndApply(params) { userId ->
                qaDashboardService.forceHighlyTrusted(userId)
              }
            }
          }
        }
      }
      tag(QaTags.Experiments) {
        route("/experiments") {
          info("Experiments QA endpoints")

          route("") {
            get<Unit, List<ExperimentDto>, UserIdPrincipal>(
              info("[Autotests] Get experiments", "View list of experiments with statuses")
            ) {
              respond(
                abTestingPersistenceService.loadNotFinishedExperiments()
                  .filter { it.key in ClientExperiment.entries.map { it.key } } // filtering out offboarded exps
                  // jumping lines are always bad
                  .sortedByDescending { exp -> exp.id }
                  .map { exp -> ExperimentDto.from(exp) })
            }
          }
          route("/{experimentKey}/variations") {
            get<ExperimentKeyParam, ExperimentVariationsDto, UserIdPrincipal>(
              info("[Autotests] Get experiment variations allocation", "View experiment variations allocation.")
            ) { params ->
              respond(
                ExperimentVariationsDto.from(abTestingService.loadVariationsForExperiment(params.experimentKey))
              )
            }
            post<ExperimentKeyParam, HttpStatusCode, ExperimentVariationsDto, UserIdPrincipal>(
              info("[Autotests] Update experiment variations allocation", "Change experiment variations allocation.")
            ) { params, request ->
              abTestingService.updateVariationsForExperiment(
                params.experimentKey,
                request.variations
              )
              respond(HttpStatusCode.OK)
            }
          }
          route("/assigned-variations") {
            get<UserOrTrackingIdParam, AssignedVariationsResponse, UserIdPrincipal>(
              info("[Autotests] Get variations assigned to user", "")
            ) { params ->
              val userId = qaDashboardService.resolveUserId(params)
              if (userId != null) {
                respond(qaDashboardService.getAssignedVariations(userId))
              } else {
                respond(
                  AssignedVariationsResponse(
                    userId = "unknown userId",
                    experiments = emptyList()
                  )
                )
              }
            }
          }
          route("/{experimentKey}/start") {
            post<ExperimentKeyParam, HttpStatusCode, ExperimentStartDto, UserIdPrincipal>(
              info(
                summary = "[Autotests] Starts experiment",
                description =
                  "Starts AbTesting experiment. StartAt is `now` by default. Reopens experiment if it was finished previously. " +
                    "Responses error if unknown experiment."
              ),
              exampleRequest = ExperimentStartDto(
                startAt = timeService.now(),
                minimumAppVersion = 42,
              )
            ) { params, request ->
              abTestingService.startExperiment(
                experimentKey = params.experimentKey,
                startAt = request.startAt ?: timeService.now(),
                minimumAppVersion = request.minimumAppVersion,
              )
              respond(HttpStatusCode.OK)
            }
          }
          route("/{experimentKey}/finish") {
            post<ExperimentKeyParam, HttpStatusCode, ExperimentFinishDto, UserIdPrincipal>(
              info(
                summary = "[Autotests] Finishes experiment",
                description = "Finishes AbTesting experiment. FinishAt is `now` by default. Responses error if unknown experiment."
              ),
              exampleRequest = ExperimentFinishDto(finishAt = timeService.now())
            ) { params, request ->
              abTestingService.finishExperiment(
                experimentKey = params.experimentKey,
                finishAt = request.finishAt ?: timeService.now(),
              )
              respond(HttpStatusCode.OK)
            }
          }
          route("/reassign-variation") {
            post<UserOrTrackingIdParam, HttpStatusCode, ReassignUserRequestApiDto, UserIdPrincipal>(
              info(
                summary = "[Autotests] Reassign user to variation",
                description = "Reassign user to variation in experiment. Responses error if unknown experiment or variation."
              )
            ) { params, req ->
              val userId = qaDashboardService.resolveUserId(params)
              if (userId != null && qaDashboardService.reassignVariationAndReturnSuccess(userId, req)) {
                respond(HttpStatusCode.OK)
              } else {
                respond(HttpStatusCode.BadRequest)
              }
            }
          }
        }
      }
      tag(QaTags.Fallback) {
        route("/fallback") {
          info("BE fallback endpoints")
          route("/android") {
            get<Unit, Map<String, List<AndroidFallbackApiDto>>, UserIdPrincipal>(
              info("For ANDROID")
            ) {
              val androidMarkets = gamesFallbackService.getAndroidFallback()
              respond(
                mapOf("markets" to androidMarkets)
              )
            }
          }
          route("/ios") {
            get<Unit, Map<String, List<IosFallbackApiDto>>, UserIdPrincipal>(
              info("For IOS")
            ) {
              respond(
                mapOf("markets" to gamesFallbackService.getIosFallback())
              )
            }
          }
        }
      }
      tag(QaTags.`Coin Calculations`) {
        route("/coin-calculation") {
          route("/mode") {
            get<UserOrTrackingIdParam, String, UserIdPrincipal>(
              info("Get coin calculation mode", "Get coin calculation mode for user")
            ) {
              val response =
                crossServiceHttpClient.get<UserStatusResponseDto>(
                  ServicesRegistry.GAME_PROGRESS.getPath(
                    BuildVariant.TEST,
                    applicationConfig.isK8s
                  ) + "/coin-calculation-mode"
                ) {
                  parameter("userId", it.userId)
                }
              respond(response.mode)
            }
            route("/prod") {
              post<UserOrTrackingIdParam, HttpStatusCode, Unit, UserIdPrincipal>(
                info("Set production mode for calculation", "Set production mode for calculation for user")
              ) { params, _ ->
                resolveUserIdAndApply(params) { userId ->
                  crossServiceHttpClient.post<Unit>(
                    ServicesRegistry.GAME_PROGRESS.getPath(
                      BuildVariant.TEST,
                      applicationConfig.isK8s
                    ) + "/coin-calculation-mode/remove-test"
                  ) {
                    parameter("userId", userId)
                  }
                }
              }
            }
            route("/test") {
              post<UserOrTrackingIdParam, HttpStatusCode, Unit, UserIdPrincipal>(
                info("Set test mode for calculation", "Set test mode for calculation for user")
              ) { params, _ ->
                resolveUserIdAndApply(params) { userId ->
                  crossServiceHttpClient.post<Unit>(
                    ServicesRegistry.GAME_PROGRESS.getPath(
                      BuildVariant.TEST,
                      applicationConfig.isK8s
                    ) + "/coin-calculation-mode/set-test"
                  ) {
                    parameter("userId", userId)
                  }
                }
              }
            }
          }
        }
      }
      tag(QaTags.Other) {
        route("git-info") {
          get<Unit, Map<String, String>, UserIdPrincipal>(
            info("Get git info", "Get git info")
          ) {
            respond(GitProperties)
          }
        }
        route("external-ip") {
          get<Unit, Map<String, String>, UserIdPrincipal>(
            info("Get external IP", "Get external IP")
          ) {
            respond(mapOf("My current IP address is" to externalIpService.getExternalIp()))
          }
        }
        route("play-integrity-token") {
          post<Unit, String, IntegrityTokenRequest, UserIdPrincipal> { _, request ->
            val client = playIntegrityClientProvider.buildClient(JUSTPLAY_APPLICATION_ID, PlaytimeSecrets.PLAY_INTEGRITY_SERVICE_ACCOUNT_KEY)
            val requestObj = DecodeIntegrityTokenRequest()
              .setIntegrityToken(request.token)

            val resp = client.v1().decodeIntegrityToken(JUSTPLAY_APPLICATION_ID, requestObj).execute().tokenPayloadExternal.toString()

            respond(resp)
          }
        }
        route("/{encryptedValue}/decrypt") {
          get<EncryptedValue, Map<String, String>, UserIdPrincipal>(
            info("Decrypt encrypted value", "Decrypt encrypted value")
          ) { value ->
            respond(
              mapOf("Decrypted value:" to encryptionService.decryptOrEmpty(value.encryptedValue))
            )
          }
        }
        route("/{email}/hash") {
          get<EmailValue, Map<String, String>, UserIdPrincipal>(
            info("Get email hash", "Get email hash")
          ) { value ->
            respond(
              mapOf("Email hash:" to hashService.emailSha256(value.email))
            )
          }
        }
      }
    }
  }

  @Serializable
  data class UserOrTrackingIdParam(
    @QueryParam("User Id") val userId: String?,
    @QueryParam("Tracking Id") val trackingId: String?
  )

  @Serializable
  data class TrackingIdParam(
    @QueryParam("Tracking Id") val trackingId: String
  )

  @Request("Add user to whitelist")
  @Serializable
  data class WhitelistUserRequestDto(
    val dropFraudTransactions: Boolean
  )

  @Request("Set usual cashout period settings")
  @Serializable
  data class SetUsualCashoutPeriodsRequestDto(
    val manualCashoutPeriodsRun: Boolean = true
  )

  @Request("User tracking id blacklist reason")
  @Serializable
  data class BlacklistTrackingIdRequestDto(
    val reason: String,
  )

  @Request("Notification text")
  @Serializable
  data class NotificationTextRequestDto(
    val notificationText: String
  )

  @Request("Change creation timestamp of specific user")
  @Serializable
  data class ChangeUserCreationTimestampRequestDto(
    @Contextual val newCreationDate: Instant
  )

  @Request("Change country code of specific user")
  @Serializable
  data class ChangeUserCountryCodeRequestDto(
    val countryCode: String
  )

  @Request("Change user locale of specific user")
  @Serializable
  data class ChangeUserLocaleRequestDto(
    val languageTag: String
  )

  @Request("Add coins request")
  @Serializable
  data class AddCoinsRequestDto(
    val applicationId: String,
    val score: Long,
    val amount: Long,
    val isNewRecord: Boolean,
  )

  @Request("Add coins request")
  @Serializable
  data class QaTapjoyCoinsReport(
    val coins: Int,
    @Contextual val revenue: BigDecimal,
    @Contextual val currencySale: BigDecimal
  )

  @Request("Email notification request")
  @Serializable
  data class QaEmailNotificationRequest(
    val name: String,
    val email: String,
    val sendGridTemplateId: String
  )

  @Request("Set fraud score total")
  @Serializable
  data class QaSetFraudScoreTotal(
    val score: Int
  )

  @Request("Add generic revenue request")
  @Serializable
  data class AddGenericRevenueRequestDto(
    val source: RevenueSource,
    @Contextual val revenueAmount: BigDecimal,
    val gameId: Int? = null,
  )

  @Serializable
  data class UserStatusResponseDto(
    val mode: String,
  )

  @Request("Add earnings request")
  @Serializable
  data class AddEarningsRequestDto(
    @Contextual val amountUSD: BigDecimal,
    @Contextual val amount: BigDecimal? = null,
    val currencyCode: String? = null
  )

  @Request("Experiment variations")
  @Response("Experiment variations")
  @Serializable
  data class ExperimentDto(
    val key: String,
    val minimumAppVersion: Int?,
    val status: ExperimentStatus,
  ) {

    @Serializable
    enum class ExperimentStatus {
      NOT_STARTED,
      RUNNING,
    }

    companion object {
      fun from(exp: Experiment): ExperimentDto =
        ExperimentDto(
          key = exp.key,
          minimumAppVersion = exp.minimumAppVersion,
          status = if (exp.startedAt != null) ExperimentStatus.RUNNING else ExperimentStatus.NOT_STARTED,
        )
    }
  }

  @Request("check email request")
  @Serializable
  data class QaEmailCheckRequestDto(
    val email: String
  )

  enum class QaTags(override val description: String) : APITag {
    Users("User QA endpoints"),
    Experiments("Experiments QA endpoints"),
    Fallback("BE fallback endpoints"),
    Other("OTHER"),
    `Coin Calculations`("Coin calculation mode"),
  }

  private suspend inline fun OpenAPIPipelineResponseContext<HttpStatusCode>.resolveUserIdAndApply(params: UserOrTrackingIdParam, block: (id: String) -> Unit) {
    val userId = qaDashboardService.resolveUserId(params)
    if (userId != null) {
      block(userId)
      respond(HttpStatusCode.OK)
    } else {
      respond(HttpStatusCode.BadRequest)
    }
  }
}

@Serializable
data class IntegrityTokenRequest(
  val token: String
)

@Serializable
data class EncryptedValue(
  @PathParam("Encrypted value") val encryptedValue: String
)

@Serializable
data class EmailValue(
  @PathParam("Email value") val email: String
)

@Serializable
data class ReassignUserRequestApiDto(
  val experimentKey: String,
  val variationKey: String,
)

@Request("Custom notification free form request")
@Serializable
data class QaFreeFormCustomNotificationApiDto(
  val notificationType: String? = null,
  val label: String? = null,
  val data: Map<String, String>,
) : AppNotification {
  override fun getMessageType() = notificationType ?: "customNotification"
  override fun getAnalyticsLabel(): String = label.orEmpty()
}