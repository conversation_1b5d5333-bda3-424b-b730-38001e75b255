package com.moregames.playtime.administration.user

import com.google.inject.Inject
import com.moregames.base.secret.SecretService
import com.moregames.playtime.administration.dto.FacetecFacesRequestDto
import com.moregames.playtime.administration.dto.FacetecFacesResponseDto
import com.moregames.playtime.app.PlaytimeSecrets
import com.moregames.playtime.user.UserPersistenceService
import javax.inject.Singleton

@Singleton
class FacetecFacesService @Inject constructor(
  private val userPersistenceService: UserPersistenceService,
  private val mongodbApiClient: MongodbApiClient,
  private val secretService: SecretService
) {
  companion object {
    const val imageUrlTemplate =
      "https://playtime.us.justplay-internal.com/admin/facetec-dashboard/api/liveness/image?tid="
  }

  suspend fun getUsersInfo(facesRequest: FacetecFacesRequestDto): FacetecFacesResponseDto {
    val metabaseUserDashboardTemplate = secretService.secretValue(PlaytimeSecrets.METABASE_USER_DASHBOARD_LINK) +
      "?user_id="

    val usersFromGaids = facesRequest.googleAdIds.flatMap { userPersistenceService.fetchAllUserIds(it) }

    return (facesRequest.userIds + usersFromGaids)
      .map { userId ->
        val listOfFacelinks = mongodbApiClient
          .getUserImageTids(userId)
          .map { imageUrlTemplate + it }

        FacetecFacesResponseDto.UserRecord(
          metabaseUserDashboardLink = metabaseUserDashboardTemplate + userId,
          listOfFaces = listOfFacelinks
        )
      }
      .toList()
      .let { FacetecFacesResponseDto(it) }
  }

  suspend fun deleteFace(externalDatabaseRefID: String): Int? {
    return mongodbApiClient.deleteFace(externalDatabaseRefID)
  }
}