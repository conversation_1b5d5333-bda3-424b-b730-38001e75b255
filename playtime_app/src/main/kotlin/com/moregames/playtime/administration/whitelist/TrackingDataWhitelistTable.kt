package com.moregames.playtime.administration.whitelist

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.`java-time`.timestamp
import java.time.Instant

object TrackingDataWhitelistTable : Table("playtime.tracking_data_whitelist") {
  val trackingId = varchar("tracking_id", 36)
  val trackingType = varchar("tracking_type", 36)
  val appPlatform = varchar("app_platform", 36)
  val createdAt = timestamp("created_at").clientDefault { Instant.now() }
}