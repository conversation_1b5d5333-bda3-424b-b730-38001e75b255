package com.moregames.playtime.administration.qa

import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService
import com.moregames.base.abtesting.AbTestingPersistenceService
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ExperimentBase
import com.moregames.base.abtesting.findVariation
import com.moregames.base.app.OfferWallType
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.messaging.customnotification.CustomNotificationDto
import com.moregames.base.messaging.dto.CashoutPeriodEndedEventDto
import com.moregames.base.messaging.dto.EmailNotificationEventDto
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource
import com.moregames.base.util.TimeService
import com.moregames.playtime.administration.blacklist.BlacklistService
import com.moregames.playtime.administration.dto.AssignedVariationsResponse
import com.moregames.playtime.administration.qa.QaDashboardController.*
import com.moregames.playtime.buseffects.InvalidateUserCacheEffect
import com.moregames.playtime.checks.ExaminationService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.notifications.MassPushNotificationService
import com.moregames.playtime.notifications.NotificationsFacade
import com.moregames.playtime.revenue.fyber.FyberReportingManager
import com.moregames.playtime.revenue.fyber.dto.FyberCoinsReport
import com.moregames.playtime.revenue.tapjoy.TapjoyCoinsReportDto
import com.moregames.playtime.revenue.tapjoy.TapjoyReportingManager
import com.moregames.playtime.subscribers.CashoutPeriodEndedMessagePushSubscriber
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.attestation.UserAttestationPersistenceService
import com.moregames.playtime.user.cashout.CashoutStatusService
import com.moregames.playtime.user.fraudscore.FraudScoreChangeReason
import com.moregames.playtime.user.fraudscore.FraudScorePersistenceService
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.offer.AndroidOfferwallService
import com.moregames.playtime.user.offer.AndroidOfferwallTypeService
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.user.tracking.TrackingService
import com.moregames.playtime.user.verification.VerificationPersistenceService
import com.moregames.playtime.user.verification.VerificationService
import com.moregames.playtime.user.verification.dto.VerificationSessionDto
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationStep
import com.moregames.playtime.util.DEFAULT_USER_LOCALE
import io.ktor.client.*
import io.ktor.client.request.*
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class QaDashboardService @Inject constructor(
  private val cashoutPeriodEndedMessagePushSubscriber: CashoutPeriodEndedMessagePushSubscriber,
  private val notificationsFacade: NotificationsFacade,
  private val timeService: TimeService,
  private val userService: UserService,
  private val encryptionService: EncryptionService,
  private val massPushNotificationService: MassPushNotificationService,
  private val trackingService: TrackingService,
  private val fraudScorePersistenceService: FraudScorePersistenceService,
  private val blacklistService: BlacklistService,
  private val verificationPersistenceService: VerificationPersistenceService,
  private val marketService: MarketService,
  private val userPersistenceService: UserPersistenceService,
  private val verificationService: VerificationService,
  private val userEarningsPersistenceService: UserEarningsPersistenceService,
  private val cashoutStatusService: CashoutStatusService,
  private val httpClient: HttpClient,
  private val androidOfferwallService: AndroidOfferwallService,
  private val androidOfferwallTypeService: AndroidOfferwallTypeService,
  private val tapjoyReportingManager: TapjoyReportingManager,
  private val fyberReportingManager: FyberReportingManager,
  private val fraudScoreService: FraudScoreService,
  private val userAttestationPersistenceService: UserAttestationPersistenceService,
  private val qaUserSettingsService: QaUserSettingsService,
  private val examinationService: ExaminationService,
  private val abTestingPersistenceService: AbTestingPersistenceService,
  private val abTestingService: AbTestingService,
  private val messageBus: MessageBus,
) {

  suspend fun endCashoutPeriod(userId: String) {
    cashoutPeriodEndedMessagePushSubscriber.handle(
      CashoutPeriodEndedEventDto(
        userId = userId,
        periodStart = timeService.now(),
        periodEnd = timeService.now(),
      )
    )
  }

  suspend fun sendQaFreeFormCustomNotification(userId: String, notification: QaFreeFormCustomNotificationApiDto) {
    notificationsFacade.sendMessage(userId, notification)
  }

  suspend fun sendCustomNotifications(userId: String, notification: CustomNotificationDto) {
    massPushNotificationService.sendCustomPushNotifications(mapOf(userId to notification))
  }

  suspend fun sendEmailNotification(userId: String, request: QaEmailNotificationRequest) {
    messageBus.publish(
      EmailNotificationEventDto(
        userId = userId,
        encryptedName = encryptionService.encrypt(request.name) ?: throw IllegalArgumentException("name"),
        encryptedEmail = encryptionService.encrypt(request.email) ?: throw IllegalArgumentException("email"),
        sendGridTemplateId = request.sendGridTemplateId
      )
    )
  }

  suspend fun whitelistUser(userId: String, dropFraudTransactions: Boolean) {
    userService.whitelistUser(userId)
    if (dropFraudTransactions) {
      fraudScorePersistenceService.dropTransactions(userId)
    }
  }

  suspend fun dropUserCache(userId: String) {
    messageBus.publish(InvalidateUserCacheEffect(userId))
  }

  suspend fun whitelistTrackingData(trackingId: String) {
    try {
      userService.whitelistGoogleAdId(trackingId)
    } catch (_: UserRecordNotFoundException) {
      userService.whitelistTrackingData(TrackingData(trackingId, IDFV, IOS))
    }
  }

  suspend fun removeTrackingIdFromWhitelist(trackingId: String) {
    trackingService.removeGoogleAdIdsFromWhitelist(setOf(trackingId))
    trackingService.removeTrackingDataSetFromWhitelist(setOf(TrackingData(trackingId, IDFV, IOS)))
  }

  suspend fun blacklistTrackingId(trackingId: String, reason: String): Set<String> {
    val affectedUserIds = mutableSetOf<String>()
    affectedUserIds.addAll(blacklistService.blacklistGoogleAdIds(setOf(trackingId), reason, false))
    affectedUserIds.addAll(blacklistService.blacklistTrackingData(setOf(TrackingData(trackingId, IDFV, IOS)), reason, false))
    return affectedUserIds
  }

  suspend fun verifyFace(userId: String) {
    val sessionId = UUID.randomUUID().toString()
    verificationPersistenceService.createSession(
      VerificationSessionDto(
        sessionId = sessionId,
        userId = userId,
        verification = listOf(
          VerificationStep(
            type = VerificationSessionDto.VerificationType.FACE,
            status = VerificationSessionDto.VerificationStatus.VERIFIED,
            order = 1
          )
        ),
        expiredAt = Instant.now().plus(1, ChronoUnit.HOURS)
      )
    )
  }

  suspend fun verifyIosSpecificSteps(userId: String) {
    val sessionId = UUID.randomUUID().toString()
    verificationPersistenceService.createSession(
      VerificationSessionDto(
        sessionId = sessionId,
        userId = userId,
        verification = listOf(
          VerificationStep(
            type = VerificationSessionDto.VerificationType.FACE,
            status = VerificationSessionDto.VerificationStatus.VERIFIED,
            order = 1
          ),
          VerificationStep(
            type = VerificationSessionDto.VerificationType.EXAMINATION,
            status = VerificationSessionDto.VerificationStatus.VERIFIED,
            order = 2
          ),
          VerificationStep(
            type = VerificationSessionDto.VerificationType.JAIL_BREAK,
            status = VerificationSessionDto.VerificationStatus.VERIFIED,
            order = 3
          ),
          VerificationStep(
            type = VerificationSessionDto.VerificationType.LOCATION,
            status = VerificationSessionDto.VerificationStatus.VERIFIED,
            order = 4
          ),
        ),
        expiredAt = Instant.now().plus(1, ChronoUnit.HOURS)
      )
    )
    userService.updateGpsLocationCountry(userId, marketService.getAllowedCountries().first())
  }

  suspend fun removeFaceByUserId(userId: String) {
    verificationService.removeFaces(userId)
  }

  suspend fun sendGenericNotification(userId: String, notificationText: String) {
    massPushNotificationService.sendGenericPushNotifications(userIds = setOf(userId), text = notificationText)
  }

  suspend fun changeUserCreationTimestamp(userId: String, newCreationDate: Instant) {
    userPersistenceService.updateUserCreatedAt(userId, newCreationDate)
    messageBus.publish(InvalidateUserCacheEffect(userId))
  }

  suspend fun changeUserCountryCode(userId: String, countryCode: String) {
    userPersistenceService.updateUserCountryCode(userId, countryCode)
  }

  suspend fun changeUserLocale(userId: String, languageTag: String) {
    val locale = Locale.LanguageRange
      .parse(languageTag)
      .firstOrNull()
      ?.range
      ?.let(Locale::forLanguageTag)
      ?: DEFAULT_USER_LOCALE
    userPersistenceService.updateDeviceLocale(userId, locale)
  }

  suspend fun addGenericRevenue(userId: String, source: RevenueSource, revenueAmount: BigDecimal, gameId: Int?) {
    messageBus.publish(
      RevenueReceivedEventDto(
        eventId = UUID.randomUUID().toString(),
        userId = userId,
        timestamp = Instant.now(),
        source = source,
        amount = revenueAmount,
        createdAt = Instant.now(),
        networkId = -1,
        gameId = gameId,
      )
    )
  }

  suspend fun addEarnings(userId: String, request: AddEarningsRequestDto) {
    userEarningsPersistenceService.qaAddEarnings(
      userId = userId,
      amountUsd = request.amountUSD,
      amount = request.amount ?: request.amountUSD,
      currencyCode = request.currencyCode ?: "USD"
    )
    cashoutStatusService.enableCashout(userId)
  }

  suspend fun addCoins(userIdParam: String, request: AddCoinsRequestDto) {
    val userDto = userService.getUser(userIdParam)
    val idfv = if (userDto.trackingData != null && userDto.trackingData.type == IDFV) userDto.trackingData.id else ""
    httpClient.get<String>(
      "https://test-dot-orchestrator-dot-playspot-server-dev.appspot.com/webhooks/games/progress" +
        "?game_id=${request.applicationId}" +
        "&user_id=$userIdParam" +
        "&idfv=$idfv" +
        "&score=${request.score}" +
        "&amount=${request.amount}" +
        "&is_new_record=${request.isNewRecord}"
    )
  }

  suspend fun addOfferwallOfferCompletionCoins(userId: String, request: QaTapjoyCoinsReport) {
    if (!androidOfferwallService.shouldShowOfferwalls(userId)) {
      throw IllegalArgumentException("User should be able to use offerwall!")
    }
    if (OfferWallType.TAPJOY !in androidOfferwallTypeService.getOfferwallTypes(userId)) {
      throw IllegalArgumentException("User can't use Tapjoy offerwall")
    }
    val tapjoyCoinsReport = TapjoyCoinsReportDto(
      id = UUID.randomUUID().toString(),
      userId = userId,
      coins = request.coins,
      signature = "test signature",
      revenue = request.revenue,
      currencySale = request.currencySale,
      macAddress = null
    )
    tapjoyReportingManager.onTapjoyCoinsReport(tapjoyCoinsReport)
  }

  suspend fun addFyberOfferwallOfferCompletionCoins(userId: String, request: QaTapjoyCoinsReport) {
    if (!androidOfferwallService.shouldShowOfferwalls(userId)) {
      throw IllegalArgumentException("User should be able to use offerwall!")
    }
    if (OfferWallType.FYBER !in androidOfferwallTypeService.getOfferwallTypes(userId)) {
      throw IllegalArgumentException("User can't use Fyber offerwall")
    }
    val coinsReport = FyberCoinsReport(
      transactionId = UUID.randomUUID().toString(),
      userId = userId,
      amount = request.coins,
      currencyName = "coins",
      currencyId = "coins",
      signature = "test signature",
      payout = request.revenue,
      vcsEnabled = request.currencySale > BigDecimal.ONE,
    )
    fyberReportingManager.onIncomingReport(coinsReport)
  }

  suspend fun setFraudScore(userId: String, score: Int) {
    // "raw" total - without taking into account frozen and whitelist
    val currentScore = fraudScorePersistenceService.getUserFraudScore(userId)
    fraudScoreService.addTransactions(
      listOf(
        FraudScorePersistenceService.FraudScoreTransaction(
          userId = userId,
          amount = score - currentScore,
          reasonType = FraudScoreChangeReason.MANUAL_FS,
          reasonUniqueId = UUID.randomUUID().toString(),
          description = "makes total FS == $score",
        )
      )
    )
  }

  suspend fun setFrozenFraudScore(userId: String, score: Int) {
    fraudScorePersistenceService.setFrozenFraudScore(userId, score.toDouble())
  }

  suspend fun passStrongAttestation(userId: String) {
    userAttestationPersistenceService.saveUserPassedStrongAttestation(userId)
  }

  suspend fun setUsualCashoutPeriods(userId: String, manualCashoutPeriodsRun: Boolean) {
    qaUserSettingsService.setUserSettings(
      userId = userId,
      settings = listOfNotNull(
        QaUserSetting.USE_USUAL_CASHOUT_PERIODS,
        QaUserSetting.SPEED_UP_CASHOUT_PERIOD_END.takeIf { manualCashoutPeriodsRun }
      )
    )
  }

  suspend fun forceHighlyTrusted(userId: String) {
    qaUserSettingsService.forceHighlyTrusted(userId)
  }

  suspend fun passDeviceAttestation(userId: String) {
    examinationService.markUserAsPassedDeviceAttestation(userId)
  }

  suspend fun passJailBreakCheck(userId: String) {
    userPersistenceService.trackJailBreakCheck(userId, jailBreak = false)
  }

  suspend fun getAssignedVariations(userId: String): AssignedVariationsResponse {
    val variations = abTestingPersistenceService.loadActiveAssignedVariationsFor(userId)
    return AssignedVariationsResponse(
      userId = userId,
      experiments = variations.map {
        AssignedVariationsResponse.AssignedVariationsExperiment(
          experiment = ExperimentDto.from(it.experiment),
          variationKey = it.key,
          activated = it.isUserActivatedForVariation
        )
      }
    )
  }

  suspend fun reassignVariationAndReturnSuccess(userId: String, req: ReassignUserRequestApiDto): Boolean {
    val experiment = runCatching { ExperimentBase.byKey(req.experimentKey) }.getOrNull()
    val variation = experiment?.findVariation(req.variationKey)
    if (experiment == null || variation == null) {
      return false
    }
    abTestingService.reassignUserToVariation(userId, experiment, variation)
    return true
  }

  suspend fun resolveUserId(params: UserOrTrackingIdParam): String? {
    return params.userId
      ?: if (params.trackingId != null) {
        userService.fetchUserId(params.trackingId)
      } else {
        null
      }
  }

  suspend fun fetchAllUserIds(params: UserOrTrackingIdParam): List<String> {
    return if (params.userId != null)
      listOf(params.userId)
    else
      if (params.trackingId != null) {
        userService.fetchAllUserIds(params.trackingId)
      } else emptyList()
  }
}
