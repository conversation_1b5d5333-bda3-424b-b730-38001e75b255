package com.moregames.playtime.coins

import com.google.inject.Inject
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents.UserCoinsAddedEvent.DataCase.*
import com.justplayapps.service.rewarding.earnings.proto.commandIdOrNull
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.bus.AsyncEffect
import com.moregames.base.bus.EffectHandler
import com.moregames.base.bus.MessageBus
import com.moregames.base.bus.MessageHandler
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.offers.dto.OfferAction
import com.moregames.base.util.ClientVersionsSupport.IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE
import com.moregames.base.util.logger
import com.moregames.playtime.buseffects.SendCommandBasedBalanceUpdatedNotification
import com.moregames.playtime.games.Em2IosBoostedGameService
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.CashoutOfferBalanceUpdate
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.BalanceUpdatedNotification
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.offers.CashoutOffersService
import com.moregames.playtime.user.offer.OfferPersistenceService
import com.moregames.playtime.user.onboarding.progressbar.buseffects.OnboardingProgressBarOfferwallCoinsReceivedEffect
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.concurrent.schedule

class UserCoinsAddedEventHandler @Inject constructor(
  private val offerPersistenceService: OfferPersistenceService,
  private val abTestingService: AbTestingService,
  private val userService: UserService,
  private val cashoutOffersService: CashoutOffersService,
  private val messageBus: MessageBus,
  private val em2IosBoostedGameService: Em2IosBoostedGameService,
) {

  @MessageHandler
  suspend fun handle(event: RewardingEvents.UserCoinsAddedEvent) {
    val coinsTotal = event.coins
    val coinsAdded = event.coinsAdded
    val userId = event.userId

    when (event.dataCase) {
      ADDITIONAL_OFFER_COINS_DATA -> {
        val offer = offerPersistenceService.loadAdditionalOffer(event.additionalOfferCoinsData.offerId)
        val hideCoins = offer.action == OfferAction.VIDEO_AD && abTestingService.isEm3Participant(userId)
        if ((offer.offerCompletionNotificationDelaySeconds ?: 0) > 0) {
          Timer().schedule(TimeUnit.SECONDS.toMillis(offer.offerCompletionNotificationDelaySeconds!!.toLong())) {
            messageBus.publishAsync(BalanceUpdatedNotification(userId, coinsTotal, coinsAdded, hideCoins).toEffect())
          }
        } else {
          messageBus.publishAsync(BalanceUpdatedNotification(userId, coinsTotal, coinsAdded, hideCoins).toEffect())
        }
      }

      GAME_COINS_DATA -> {
        val user = try {
          userService.getUser(userId)
        } catch (_: UserRecordNotFoundException) {
          logger().warn("User '${userId}' was not found")
          null
        }
          ?: return

        when {
          // other cases use coins and should not show zeroes
          (coinsTotal <= 0) -> return

          isInGameBalance(userId, user.appPlatform, event.gameCoinsData.commandIdOrNull?.value, event.gameCoinsData.forceCommandNotification) -> sendCommandBaseNotification(event.gameCoinsData.commandId.value, coinsTotal)

          hasActiveOffers(userId, user.appPlatform, event.gameCoinsData.gameId) ->
            messageBus.publishAsync(CashoutOfferBalanceUpdate(userId, coinsTotal, false).toEffect())

          user.appPlatform == IOS && user.appVersion <= IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE &&
            em2IosBoostedGameService.isIosBoostedGame(event.gameCoinsData.gameId) ->
            messageBus.publishAsync(BalanceUpdatedNotification(userId, coinsTotal, coinsAdded, isIosBoostedGameCoins = true).toEffect())

          else -> messageBus.publishAsync(BalanceUpdatedNotification(userId, coinsTotal, coinsAdded,false).toEffect())
        }
      }

      OFFER_WALL_COINS_DATA -> {
        messageBus.publishAsync(OnboardingProgressBarOfferwallCoinsReceivedEffect(userId))
        messageBus.publishAsync(BalanceUpdatedNotification(userId, coinsTotal, coinsAdded).toEffect())
      }

      BONUS_COINS_DATA, EM3_GAME_COINS_DATA -> {
        messageBus.publishAsync(BalanceUpdatedNotification(userId, coinsTotal, coinsAdded).toEffect())
      }

      null, DATA_NOT_SET -> {
        throw IllegalArgumentException("Unsupported coins added event type ${event.dataCase}")
      }
    }
  }

  @EffectHandler
  suspend fun em3UserGameProgressUpdated(effect: Em3UserGameProgressUpdatedEffect) {
    val coins = 0L // For EM3 balance update notification game coins amount doesn't make sense cz we always hide them
    val user = try {
      userService.getUser(effect.userId)
    } catch (_: UserRecordNotFoundException) {
      logger().warn("User '${effect.userId}' was not found")
      null
    }
      ?: return

    when {
      isInGameBalance(effect.userId, user.appPlatform, effect.commandId, effect.forceCommandNotification) -> sendCommandBaseNotification(effect.commandId, coins) // it doesn't properly work with EM3 at all, should be discussed with PMs and GameDev team

      hasActiveOffers(effect.userId, user.appPlatform, effect.gameId) ->
        messageBus.publishAsync(CashoutOfferBalanceUpdate(effect.userId, coins, true).toEffect())

      else -> messageBus.publishAsync(BalanceUpdatedNotification(effect.userId, coins, coins,true).toEffect())
    }
  }

  private suspend fun isInGameBalance(userId: String, platform: AppPlatform, commandId: String?, forceCommandNotification: Boolean) =
    commandId != null &&
      (forceCommandNotification || abTestingService.isGameBalanceUpdateNotificationParticipant(
        userId,
        platform,
        activate = true
      ))

  private suspend fun sendCommandBaseNotification(commandIdParam: String?, coins: Long) {
    val commandId = commandIdParam ?: return // return will never happen there

    messageBus.publish(SendCommandBasedBalanceUpdatedNotification(commandId, coins))
  }

  private suspend fun hasActiveOffers(userId: String, platform: AppPlatform, gameId: Int?) =
    gameId != null && platform == ANDROID && cashoutOffersService.hasActiveOfferForGame(userId, gameId)


  data class Em3UserGameProgressUpdatedEffect(
    val userId: String,
    val commandId: String?,
    val forceCommandNotification: Boolean,
    val gameId: Int?,
  ) : AsyncEffect
}