package com.moregames.playtime.web

import com.google.inject.Inject
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.dto.TrackingDataType
import com.moregames.base.messaging.dto.WebAppUserAdditionalDataEventDto
import com.moregames.base.messaging.dto.WebAppUserJailBreakCheckEventDto
import com.moregames.base.messaging.dto.WebAppUserRegistrationEventDto
import com.moregames.base.messaging.dto.WebAppVerificationGpsLocationCheckEventDto
import com.moregames.base.user.dto.WebUserAdditionalData.*
import com.moregames.base.user.dto.WebUserStatusDto
import com.moregames.base.user.dto.WebUsersInGameVerification.DEVICE_EXAMINATION
import com.moregames.base.user.dto.WebUsersInGameVerification.JAIL_BREAK
import com.moregames.playtime.buseffects.WebAppGpsLocationCheckEventEffect
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.ios.examination.IosExaminationService
import com.moregames.playtime.notifications.status.UserNotificationStatusService
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.tracking.TrackingData
import javax.inject.Singleton

@Singleton
class WebUserService @Inject constructor(
  private val userService: UserService,
  private val notificationStatusService: UserNotificationStatusService,
  private val userPersistenceService: UserPersistenceService,
  private val iosExaminationService: IosExaminationService,
  private val fraudScoreService: FraudScoreService,
  private val gamesService: GamesService,
  private val messageBus: MessageBus,
) {

  suspend fun registerUser(messageDto: WebAppUserRegistrationEventDto) = with(messageDto) {
    val user = userService.getUser(userId, includingDeleted = true)
    deviceToken?.let {
      userService.updateDeviceToken(
        userId = userId,
        deviceToken = it,
        appPlatform = user.appPlatform
      )
    }
    notificationEnabled?.let {
      notificationStatusService.setUserNotificationsStatus(
        userId = userId,
        areEnabled = it
      )
    }
    userService.updateTrackingData(
      userId = userId,
      trackingData = TrackingData(id = idfv, type = TrackingDataType.IDFV, platform = user.appPlatform),
      appVersion = AppVersionDto(user.appPlatform, user.appVersion)
    )
  }

  suspend fun getUserStatus(userId: String): WebUserStatusDto {
    val externalIds = userService.fetchExternalIds(userId)
    val notificationStatusTracked = notificationStatusService.isNotificationsStatusTracked(userId)
    val deviceToken = userPersistenceService.loadDeviceTokensForUsers(listOf(userId))[userId]
    val wasSuccessfullyExamined = iosExaminationService.wasSuccessfullyExamined(userId)

    return WebUserStatusDto(
      requiredDataFields = listOfNotNull(
        IDFV.takeIf { externalIds?.trackingData == null },
        IDFA.takeIf { externalIds?.idfa == null },
        FIREBASE_DEVICE_TOKEN.takeIf { deviceToken == null },
        FIREBASE_APP_INSTANCE_ID.takeIf { externalIds?.firebaseAppId == null },
        NOTIFICATION_STATE.takeIf { !notificationStatusTracked },
        ADJUST_ID.takeIf { externalIds?.adjustId == null }
      ),
      verifications = listOfNotNull(
        DEVICE_EXAMINATION.takeIf { !wasSuccessfullyExamined },
        JAIL_BREAK, // we can try to always check for jailBreak for now
      )
    )
  }

  suspend fun updateAdditionalData(message: WebAppUserAdditionalDataEventDto) = with(message) {
    val user = userService.getUser(userId, includingDeleted = true)
    deviceToken?.let {
      userService.updateDeviceToken(
        userId = userId,
        deviceToken = it,
        appPlatform = user.appPlatform
      )
    }
    notificationEnabled?.let {
      notificationStatusService.setUserNotificationsStatus(
        userId = userId,
        areEnabled = it
      )
    }
    idfv?.let {
      userService.updateTrackingData(
        userId = userId,
        trackingData = TrackingData(id = it, type = TrackingDataType.IDFV, platform = user.appPlatform),
        appVersion = AppVersionDto(user.appPlatform, user.appVersion)
      )
    }
    idfa?.let {
      userService.addUserTrackingData(
        userId = userId,
        trackingData = TrackingData(id = it, type = TrackingDataType.IDFA, platform = user.appPlatform)
      )
    }
    firebaseAppInstanceId?.let {
      userService.addOrUpdateFirebaseAppInstanceId(userId, it)
    }
    adjustId?.let {
      userService.updateAdjustId(userId, it)
    }
  }

  suspend fun onJailBreakCheck(message: WebAppUserJailBreakCheckEventDto) = with(message) {
    userPersistenceService.trackJailBreakCheck(userId, jailBreak)
    if (message.jailBreak) {
      fraudScoreService.blockUserOnJailBreakUsageDetected(userId)
    }
  }

  suspend fun jailBreakCheckPassed(userId: String) = userPersistenceService.jailBreakCheckPassed(userId)

  suspend fun gpsLocationCheck(userId: String, message: WebAppVerificationGpsLocationCheckEventDto) = with(message) {
    val gameId = gamesService.getGameId(applicationId, platform = IOS)
      ?: throw IllegalArgumentException("Failed to get game id for application id $applicationId userid: $userId")

    userPersistenceService.trackGpsLocationCheckAskedGame(userId, gameId, provided)
    if (provided && location != null) {
      messageBus.publish(
        WebAppGpsLocationCheckEventEffect(
          userId = userId,
          sessionId = sessionId,
          location = location!!,
          isMocked = isMocked
        )
      )
    }
  }
}