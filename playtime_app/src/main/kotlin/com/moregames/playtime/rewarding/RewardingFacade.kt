package com.moregames.playtime.rewarding

import com.google.inject.Inject
import com.google.inject.Provider
import com.google.protobuf.StringValue
import com.justplayapps.service.rewarding.bonus.proto.*
import com.justplayapps.playtime.rewarding.bonusbank.BonusBankApiGrpc
import com.justplayapps.playtime.rewarding.bonusbank.getBonusCashBarStateRequest
import com.justplayapps.service.rewarding.bonus.proto.UserBonusBalanceApiGrpc
import com.justplayapps.service.rewarding.bonus.proto.addBonusCoinsRequest
import com.justplayapps.service.rewarding.bonus.proto.findLastWelcomeCoinsDateRequest
import com.justplayapps.service.rewarding.bonus.proto.hasBonusForRequest
import com.justplayapps.service.rewarding.earnings.UserEarnings
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCurrencyEarnings
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserEarningsWithOfferwallAmount
import com.justplayapps.service.rewarding.earnings.proto.*
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.dto.AppPlatform
import com.moregames.base.grpc.client.GenericFacade
import com.moregames.base.user.RevenueTotals
import com.moregames.base.user.UserBonusBalanceType
import com.moregames.base.util.ApplicationId.BUBBLE_CHIEF_APP_ID
import com.moregames.base.util.ApplicationId.EMOJICLICKERS_APP_ID
import com.moregames.base.util.ApplicationId.PIN_MASTER_APP_ID
import com.moregames.base.util.FOREVERGREEN_PREFIX
import com.moregames.base.util.GIMICA_PREFIX
import com.moregames.base.util.fromProto
import com.moregames.base.util.toProto
import com.moregames.playtime.user.RevenueByPeriod
import com.moregames.playtime.user.bonuscashbar.dto.BonusCashBar
import java.math.BigDecimal
import java.time.Instant
import java.util.*

class RewardingFacade @Inject constructor(
  private val bonusApi: Provider<UserBonusBalanceApiGrpc.UserBonusBalanceApiStub>,
  private val emApi: Provider<EmApiGrpc.EmApiStub>,
  private val genericFacade: GenericFacade,
  private val bonusBankApi: Provider<BonusBankApiGrpc.BonusBankApiStub>,
) {

  companion object {
    fun isGameAllowedForEm2Participants(applicationId: String) = when {
      applicationId == EMOJICLICKERS_APP_ID -> false
      applicationId.startsWith(GIMICA_PREFIX) -> true
      applicationId.startsWith(FOREVERGREEN_PREFIX) -> true
      applicationId == PIN_MASTER_APP_ID -> true
      applicationId == BUBBLE_CHIEF_APP_ID -> true
      else -> false
    }
  }

  suspend fun hasBonusFor(userId: String, bonusBalanceType: UserBonusBalanceType, uniqueBonusKey: String? = null): Boolean {
    return genericFacade.get(
      bonusApi.get()::hasBonusFor,
      hasBonusForRequest {
        this.userId = userId
        this.bonusType = bonusBalanceType.toProto()
        if (uniqueBonusKey != null) {
          this.uniqueBonusKey = StringValue.of(uniqueBonusKey)
        }
      }
    ).hasBonus
  }

  suspend fun addBonusCoinsIfNotExists(
    userId: String,
    appPlatform: AppPlatform,
    coinsAmount: Int,
    bonusBalanceType: UserBonusBalanceType,
    uniqueBonusKey: String? = null
  ): Boolean {
    if (hasBonusFor(userId, bonusBalanceType, uniqueBonusKey)) return false

    genericFacade.get(
      bonusApi.get()::addBonusCoins,
      addBonusCoinsRequest {
        this.userId = userId
        this.platform = appPlatform.toProto()
        this.coinsAmount = coinsAmount
        this.bonusBalanceType = bonusBalanceType.toProto()
        if (uniqueBonusKey != null) {
          this.uniqueBonusKey = StringValue.of(uniqueBonusKey)
        }
      }
    )

    return true
  }

  suspend fun findLastWelcomeCoinsDate(userId: String): Instant? {
    return genericFacade.get(
      bonusApi.get()::findLastWelcomeCoinsDate,
      findLastWelcomeCoinsDateRequest {
        this.userId = userId
      }
    ).let {
      if (!it.hasTimestamp()) return@let null
      return@let Instant.ofEpochSecond(it.timestamp.seconds, it.timestamp.nanos.toLong())
    }
  }

  suspend fun inflatingCoinsMultiplier(userId: String): Int {
    return genericFacade.get(
      emApi.get()::getInflatingCoinsMultiplier,
      inflatingCoinsMultiplierRequest { this.userId = userId }
    ).multiplier
  }

  suspend fun randomCoinsUsdAmountForEm2(userId: String): BigDecimal? {
    return genericFacade.get(
      emApi.get()::getRandomCoinsUsdAmountForEm2,
      getRandomCoinsUsdAmountForEm2Request { this.userId = userId }
    ).let {
      if (!it.hasAmount()) return@let null
      return@let it.amount.fromProto()
    }
  }

  suspend fun getWelcomeBonusAmount(userId: String, platform: AppPlatform): Int {
    return genericFacade.get(
      emApi.get()::getWelcomeBonusAmount,
      getWelcomeBonusAmountRequest {
        this.userId = userId
        this.platform = platform.toProto()
      }
    ).amount
  }

  suspend fun loadUnpaidUserCurrencyEarnings(userId: String): UserCurrencyEarnings? {
    return genericFacade.get(
      emApi.get()::loadUnpaidUserCurrencyEarnings,
      loadUnpaidUserCurrencyEarningsRequest {
        this.userId = userId
      }
    ).let {
      if (it == EarningModel.LoadUnpaidUserCurrencyEarningsResponse.getDefaultInstance()) return@let null

      return@let UserCurrencyEarnings(
        userCurrency = Currency.getInstance(it.userCurrencyCode),
        amountUsd = it.amountUsd.fromProto(),
        userCurrencyAmount = it.userCurrencyAmount.fromProto(),
        nonBoostedAmountUsd = if (it.hasNonBoostedAmountUsd()) it.nonBoostedAmountUsd.fromProto() else null,
        nonBoostedUserCurrencyAmount = if (it.hasNonBoostedUserCurrencyAmount()) it.nonBoostedUserCurrencyAmount.fromProto() else null,
      )
    }
  }

  suspend fun userEverHadEarnings(userId: String): Boolean {
    return genericFacade.get(
      emApi.get()::userEverHadEarnings,
      userEverHadEarningsRequest {
        this.userId = userId
      }
    ).hasEarnings
  }

  suspend fun getTotalUsdEarningsForUser(userId: String): BigDecimal {
    return genericFacade.get(
      emApi.get()::getTotalUsdEarningsForUser,
      getTotalUsdEarningsForUserRequest {
        this.userId = userId
      }
    ).amount.fromProto()
  }

  suspend fun getRevenueTotals(userId: String): RevenueTotals? {
    return genericFacade.get(
      emApi.get()::getRevenueTotals,
      getRevenueTotalsRequest {
        this.userId = userId
      }
    ).let {
      if (!it.hasRevenue() && !it.hasOfferwalRevenue() && !it.hasDay0Revenue() && !it.hasDay2Revenue()) return@let null

      return@let RevenueTotals(
        revenue = it.revenue.fromProto(),
        offerwallRevenue = it.offerwalRevenue.fromProto(),
        day0Revenue = it.day0Revenue.fromProto(),
        day2Revenue = it.day2Revenue.fromProto(),
      )
    }
  }

  suspend fun loadUserEarningsForMetaId(metaId: Int): UserEarnings? {
    return genericFacade.get(
      emApi.get()::loadUserEarningsForMetaId,
      loadUserEarningsForMetaIdRequest {
        this.metaId = metaId
      }
    ).let {
      if (it == EarningModel.LoadUserEarningsForMetaIdResponse.getDefaultInstance()) return@let null

      return@let UserEarnings(
        userId = it.userId,
        metaUserEarningsId = if (it.hasMetaUserEarningsId()) it.metaUserEarningsId.value else null,
        calculatedUserEarningUsd = it.calculatedUserEarningsUsd.fromProto(),
        cashoutTransactionId = if (it.hasCashoutTransactionId()) it.cashoutTransactionId.value else null,
      )
    }
  }

  suspend fun noEarningsAfter(userId: String, fromDate: Instant): Boolean {
    return genericFacade.get(
      emApi.get()::noEarningsAfter,
      noEarningsAfterRequest {
        this.userId = userId
        this.fromDate = fromDate.toProto()
      }
    ).noEarnings
  }

  suspend fun getNonCashedUserEarningsWithOfferwallAmount(userId: String): UserEarningsWithOfferwallAmount {
    return genericFacade.get(
      emApi.get()::getNonCashedUserEarningsWithOfferwallAmount,
      getNonCashedUserEarningsWithOfferwallAmountRequest {
        this.userId = userId
      }
    ).let {
      UserEarningsWithOfferwallAmount(
        totalRevenue = it.totalRevenue.fromProto(),
        offerwallRevenue = it.offerwallRevenue.fromProto(),
      )
    }
  }

  suspend fun getLatestRevenueTime(userId: String): Instant? {
    return genericFacade.get(
      emApi.get()::getLatestRevenueTime,
      getLatestRevenueTimeRequest {
        this.userId = userId
      }
    ).let {
      if (it == EarningModel.GetLatestRevenueTimeResponse.getDefaultInstance()) return@let null

      return@let it.revenueTime.fromProto()
    }
  }

  suspend fun getUnpaidUserEarningsUSD(userId: String): BigDecimal {
    return genericFacade.get(
      emApi.get()::getUnpaidUserEarningsUSD,
      getUnpaidUserEarningsUSDRequest {
        this.userId = userId
      }
    ).earningsUsd.fromProto()
  }

  suspend fun getRevenueSumForUserForTimeInterval(userId: String, startTime: Instant, endTime: Instant): BigDecimal {
    return genericFacade.get(
      emApi.get()::getRevenueSumForUserForTimeInterval,
      getRevenueSumForUserForTimeIntervalRequest {
        this.userId = userId
        this.startTime = startTime.toProto()
        this.endTime = endTime.toProto()
      }
    ).revenue.fromProto()
  }

  suspend fun getApplovinRevenue5minPeriodsCount(userId: String, since: Instant): Map<Int, Long> {
    return genericFacade.get(
      emApi.get()::getApplovinRevenue5minPeriodsCount,
      getApplovinRevenue5minPeriodsCountRequest {
        this.userId = userId
        this.since = since.toProto()
      }
    ).periodsMap
  }

  suspend fun getLastLowEarningsCashoutPeriodEnd(userId: String): Instant? {
    return genericFacade.get(
      emApi.get()::getLastLowEarningsCashoutPeriodEnd,
      getLastLowEarningsCashoutPeriodEndRequest {
        this.userId = userId
      }
    ).let {
      if (it.hasPeriodEnd()) it.periodEnd.fromProto() else null
    }
  }

  suspend fun getUserNonBannerApplovinRevenueTransactionsCountByHours(userId: String, since: Instant): List<RevenueByPeriod> {
    return genericFacade.get(
      emApi.get()::getUserNonBannerApplovinRevenueTransactionsCountByHours,
      getUserNonBannerApplovinRevenueTransactionsCountByHoursRequest {
        this.userId = userId
        this.since = since.toProto()
      }
    ).revenueByPeriodsList.map { period ->
      RevenueByPeriod(
        periodStart = period.periodStart.fromProto(),
        transactionsCount = period.transactionsCount,
      )
    }
  }

  suspend fun get5MinIntervalsWithRevenueByGames(userId: String, since: Instant): Map<Int, List<Instant>> {
    return genericFacade.get(
      emApi.get()::get5MinIntervalsWithRevenueByGames,
      get5MinIntervalsWithRevenueByGamesRequest {
        this.userId = userId
        this.since = since.toProto()
      }
    ).periodsMap.map {
      it.key to it.value.intervalsList.map { interval -> interval.fromProto() }
    }
      .toMap()
  }

  suspend fun getUserPerGameRevenue(userId: String): Map<Int, BigDecimal> {
    return genericFacade.get(
      emApi.get()::getUserPerGameRevenue,
      getUserPerGameRevenueRequest {
        this.userId = userId
      }
    ).gamesRevenueList.associate {
      it.gameId to it.revenue.fromProto()
    }
  }

  suspend fun getUserCurrentCoinsBalance(userId: String, platform: AppPlatform): UserCurrentCoinsGoalBalance {
    return genericFacade.get(
      emApi.get()::getUserCurrentCoinsBalance,
      getUserCurrentCoinsBalanceRequest {
        this.userId = userId
        this.platform = platform.toProto()
      }
    ).let {
      UserCurrentCoinsGoalBalance(
        coins = it.coins,
        gameCoins = it.gameCoins,
        goalCoins = it.goalCoins,
      )
    }
  }

  suspend fun loadUninflatedGoalCoins(userId: String): Int {
    return genericFacade.get(
      emApi.get()::getUninflatedGoalCoins,
      getUninflatedGoalCoinsRequest {
        this.userId = userId
      }
    ).goalCoins
  }

  suspend fun getBonusBankEarnings(userId: String): BigDecimal {
    return genericFacade.get(
      bonusApi.get()::getBonusBankEarnings,
      getBonusBankEarningsRequest { this.userId = userId }
    ).earnings.fromProto()
  }

  suspend fun getBonusCashBarState(userId: String, appPlatform: AppPlatform): BonusCashBar? {
    return genericFacade
      .get(
        bonusBankApi.get()::getBonusCashBarState,
        getBonusCashBarStateRequest {
          this.userId = userId
          this.platform = appPlatform.toProto()
        }
      )
      .let { BonusCashBar.from(it) }
  }


  suspend fun getUserRevenueLast2Days(userId: String): BigDecimal {
    return genericFacade.get(
      emApi.get()::getUserRevenueLast2Days,
      getUserRevenueLast2DaysRequest {
        this.userId = userId
      }
    ).revenue.fromProto()
  }
}