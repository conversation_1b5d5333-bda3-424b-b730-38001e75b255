package com.moregames.playtime.subscribers

import com.google.inject.Inject
import com.justplayapps.playtime.games.progress.proto.calculateTargetEarningsCoefficientCommand
import com.justplayapps.service.rewarding.earnings.EarningsCalculationsService.Companion.revenueCutFactor
import com.justplayapps.service.rewarding.earnings.dto.EarningsCalculationResult
import com.justplayapps.service.rewarding.earnings.dto.fromProto
import com.justplayapps.service.rewarding.earnings.dto.simpleEarnings
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.bus.MessageBus
import com.moregames.base.bus.MessageHandler
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.messaging.dto.XHoursPassedSinceNoEarningsCheckDto
import com.moregames.base.messaging.dto.XHoursPassedSinceUnclaimedEarningsCheckDto
import com.moregames.base.util.TimeService
import com.moregames.base.util.toProto
import com.moregames.playtime.buseffects.CashoutPeriodEndedEffect
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.MissedEarningsNotification
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import com.moregames.playtime.user.cashout.CashoutStatusService
import com.moregames.playtime.util.plus
import java.math.BigDecimal
import kotlin.time.Duration.Companion.minutes

class RevenueToEarningsConvertedEventSubscriber @Inject constructor(
  private val cashoutPeriodsService: CashoutPeriodsService,
  private val cashoutStatusService: CashoutStatusService,
  private val userService: UserService,
  private val timeService: TimeService,
  private val abTestingService: AbTestingService,
  private val messageBus: MessageBus,
) {

  @MessageHandler
  suspend fun handle(message: RewardingEvents.RevenueToEarningsConvertedEvent) {
    val earnings = message.result.fromProto()
    if (earnings.simpleEarnings.amount > BigDecimal.ZERO) {
      handlePeriodWithEarnings(message.userId, earnings)
    } else {
      handlePeriodWithoutEarnings(message.userId)
    }

    messageBus.publish(CashoutPeriodEndedEffect(message.userId, earnings))
  }

  private suspend fun handlePeriodWithEarnings(userId: String, earnings: EarningsCalculationResult) {
    cashoutStatusService.enableCashout(userId)
    cashoutPeriodsService.createNextCashoutPeriod(userId, hasEarnings = true)

    emitCalculateTargetEarningsCoefficient(userId, earnings)
  }

  private suspend fun handlePeriodWithoutEarnings(userId: String) {
    val pushNotificationsAllowed = userService.getUser(userId, includingDeleted = true).appPlatform == ANDROID
    val (periodCounter, noEarningsCounter, _) = cashoutPeriodsService.getCashoutPeriodCounters(userId)
    if (noEarningsCounter == 0) {
      // shifting cashouts no-earnings counter to 1 by creating new cashout period. this helps to prevent infinite generating of cashout periods if user is inactive
      cashoutPeriodsService.createNextCashoutPeriod(userId, hasEarnings = false)

      messageBus.publish(
        XHoursPassedSinceUnclaimedEarningsCheckDto(userId, timeService.now(), 1),
        timeService.now() + 1.minutes
      )

      if (periodCounter == 1 && !abTestingService.isEm2Participant(userId)) {
        messageBus.publish(
          XHoursPassedSinceNoEarningsCheckDto(userId, timeService.now(), 1), // this event adds coins and sends BalanceUpdate notification. EM1 only.
          timeService.now() + 1.minutes
        )
        //other cases of no earnings and no savings are covered by on successful cashout event
      }
    }

    if (!pushNotificationsAllowed) return

    /**
     * this condition means that it works on:
     * - first CP without earnings (periodCounter == 1, noEarningsCounter == 0)
     * - seconds CP without earnings (periodCounter == 2, noEarningsCounter == 1)
     */
    if (periodCounter - noEarningsCounter == 1 && periodCounter <= 2) {
      //all periods were without earnings
      messageBus.publishAsync(PushNotificationEffect(MissedEarningsNotification(userId = userId)))
    }
    if (userService.shouldNotifyOnEmptyCPNotification(userId)) {
      messageBus.publishAsync(PushNotificationEffect(MissedEarningsNotification(userId = userId)))
    }
  }

  private suspend fun emitCalculateTargetEarningsCoefficient(userId: String, earnings: EarningsCalculationResult) {
    if (!abTestingService.isTargetEarningsParticipant(userId)) return
    val em2Earnings = (earnings as? EarningsCalculationResult.Em2) ?: return

    messageBus.publish(
      calculateTargetEarningsCoefficientCommand {
        this.userId = userId
        gameCoins = em2Earnings.em2CoinsBalance.gameCoins.toProto()
        applovinRevenue = em2Earnings.realGameRevenue.toProto()
        coinsForOneDollar = em2Earnings.coinsForOneDollar.toProto()
        cutFactor = revenueCutFactor.toProto()
      }
    )
  }
}