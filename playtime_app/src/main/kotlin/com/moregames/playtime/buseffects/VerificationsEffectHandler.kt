package com.moregames.playtime.buseffects

import com.moregames.base.bus.Effect
import com.moregames.base.bus.EffectHandler
import com.moregames.base.dto.AppPlatform
import com.moregames.playtime.user.verification.VerificationService
import javax.inject.Inject

class VerificationsEffectHandler @Inject constructor(
  private val verificationService: VerificationService
) {

  @EffectHandler
  suspend fun handleWebAppGpsLocationCheckEventEffect(effect: WebAppGpsLocationCheckEventEffect) = with(effect) {
    verificationService.verifyGpsLocation(
      sessionId = sessionId,
      location = location,
      isMocked = isMocked,
      appPlatform = AppPlatform.IOS_WEB
    )
  }
}

data class WebAppGpsLocationCheckEventEffect(
  val userId: String,
  val sessionId: String,
  val location: String,
  val isMocked: Boolean?,
) : Effect


