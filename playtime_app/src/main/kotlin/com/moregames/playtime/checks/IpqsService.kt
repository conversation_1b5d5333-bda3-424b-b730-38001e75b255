package com.moregames.playtime.checks

import com.google.inject.Inject
import com.moregames.base.util.TimeService
import javax.inject.Singleton

@Singleton
class IpqsService @Inject constructor(
  private val ipQualityScoreServiceClient: IpQualityScoreServiceClient,
  private val ipqsDataPersistenceService: IpqsDataPersistenceService,
  private val timeService: TimeService
) {
  suspend fun checkIp(ip: String) {
    ipQualityScoreServiceClient.checkIpQualityScore(ip)?.let { ipqsData ->
      ipqsDataPersistenceService.putIpqsData(
        ip,
        timeService.now(),
        ipqsData
      )
    }
  }
}