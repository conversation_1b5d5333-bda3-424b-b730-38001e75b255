package com.moregames.playtime.carousel

import com.google.inject.Singleton
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.util.TimeService
import com.moregames.playtime.carousel.domain.UserCarouselTaskEntity
import org.jetbrains.exposed.sql.*
import java.util.*
import javax.inject.Inject

@Singleton
class CarouselPersistenceService @Inject constructor(
  database: Database,
  private val timeService: TimeService,
) : BasePersistenceService(database) {
  suspend fun findVisibleCarouselTasks(userId: String) = dbQuery {
    UserCarouselTask
      .join(CarouselTaskTable, JoinType.INNER, UserCarouselTask.taskDefinitionId, CarouselTaskTable.id)
      .select { UserCarouselTask.userId eq userId and (UserCarouselTask.state neq UserCarouselTaskState.COMPLETED) }
      .map { it.toUserCarouselTask() }
  }

  suspend fun findActiveCarouselTaskPerGame(userId: String, gameId: Int) = dbQuery {
    UserCarouselTask
      .join(CarouselTaskTable, JoinType.INNER, UserCarouselTask.taskDefinitionId, CarouselTaskTable.id)
      .select { (UserCarouselTask.userId eq userId) and (CarouselTaskTable.gameId eq gameId) and (UserCarouselTask.state inList listOf(UserCarouselTaskState.IN_PROGRESS)) }
      .map { it.toUserCarouselTask() }
  }

  suspend fun getUserCarouselTask(taskId: UUID) = dbQuery {
    UserCarouselTask
      .join(CarouselTaskTable, JoinType.INNER, UserCarouselTask.taskDefinitionId, CarouselTaskTable.id)
      .select { UserCarouselTask.id eq taskId }
      .firstOrNull()
      ?.toUserCarouselTask()
  }

  suspend fun markTaskAsStarted(taskId: UUID) {
    updateState(taskId, UserCarouselTaskState.IN_PROGRESS)
  }

  suspend fun markTaskAsFinished(taskId: UUID) {
    updateState(taskId, UserCarouselTaskState.UNCLAIMED)
  }

  suspend fun markTaskAsClaimed(taskId: UUID) {
    updateState(taskId, UserCarouselTaskState.CLAIMED)
  }

  suspend fun completeTask(taskId: UUID) = dbQuery {
    UserCarouselTask.update({ UserCarouselTask.id eq taskId }) {
      it[state] = UserCarouselTaskState.COMPLETED
      it[completedAt] = timeService.now()
    }
  }

  suspend fun updateTaskProgress(taskId: UUID, progress: Int, achievement: String? = null) = dbQuery {
    UserCarouselTask.update({ UserCarouselTask.id eq taskId }) { row ->
      row[UserCarouselTask.progress] = progress
      achievement?.also { row[UserCarouselTask.achievement] = it }
    }
  }

  private suspend fun updateState(taskId: UUID, state: UserCarouselTaskState) = dbQuery {
    UserCarouselTask.update({ UserCarouselTask.id eq taskId }) {
      it[UserCarouselTask.state] = state
    }
  }

  private fun ResultRow.toUserCarouselTask() = UserCarouselTaskEntity(
    taskId = this[UserCarouselTask.id].value,
    userId = this[UserCarouselTask.userId],
    taskDefinitionId = this[UserCarouselTask.taskDefinitionId],
    state = this[UserCarouselTask.state],
    progress = this[UserCarouselTask.progress],
    achievement = this[UserCarouselTask.achievement],
    completedAt = this[UserCarouselTask.completedAt],
    titleTranslation = this[CarouselTaskTable.title],
    icon = this[CarouselTaskTable.icon],
    progressMax = this[CarouselTaskTable.progressMax],
    gameId = this[CarouselTaskTable.gameId],
    calculator = this[CarouselTaskTable.calculator],
    enabled = this[CarouselTaskTable.enabled],
    order = this[CarouselTaskTable.order],
  )
}