package com.moregames.playtime.carousel

import org.jetbrains.exposed.sql.Table

object CarouselTaskTable : Table("playtime.carousel_task") {
  val id = varchar("id", 36)
  val gameId = integer("game_id")
  val title = varchar("title", 1000)
  val icon = varchar("icon", 255)
  val progressMax = integer("progress_max")
  val order = integer("order")
  val calculator = varchar("calculator", 50) // TODO should be enum
  val enabled = bool("enabled")

  override val primaryKey: PrimaryKey = PrimaryKey(id)
}