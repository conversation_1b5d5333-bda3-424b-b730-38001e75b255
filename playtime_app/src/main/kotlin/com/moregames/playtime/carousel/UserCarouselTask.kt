package com.moregames.playtime.carousel

import com.moregames.base.table.UuidV7PkTable
import org.jetbrains.exposed.sql.`java-time`.timestamp

object UserCarouselTask : UuidV7PkTable("playtime.user_carousel_task") {
  val userId = varchar("user_id", 36)
  val taskDefinitionId = varchar("task_definition_id", 36)
  val state = enumerationByName("state", 50, UserCarouselTaskState::class)
  val progress = integer("progress")
  val achievement = text("achievement").nullable()
  val completedAt = timestamp("completed_at").nullable()
}

