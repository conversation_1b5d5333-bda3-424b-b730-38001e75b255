package com.moregames.playtime.carousel.domain

import com.moregames.playtime.carousel.UserCarouselTaskState
import com.moregames.playtime.user.challenge.progress.achievement.AchievementDto
import java.time.Instant
import java.util.*

data class UserCarouselTask(
  val taskId: UUID,
  val userId: String,
  val state: UserCarouselTaskState,
  val progress: Int,
  val definition: TaskDefinition,
  val achievement: AchievementDto?,
  val completedAt: Instant?,
)

data class TaskDefinition(
  val gameId: Int,
  val titleTranslation: String,
  val icon: String,
  val progressMax: Int,
  val order: Int,
  val calculator: String, // TODO should be enum
  val enabled: Boolean,
)