package com.moregames.playtime.carousel.domain

import com.moregames.playtime.carousel.UserCarouselTaskState
import java.time.Instant
import java.util.*

data class UserCarouselTaskEntity(
  val taskId: UUID,
  val userId: String,
  val state: UserCarouselTaskState,
  val progress: Int,
  val achievement: String?,
  val completedAt: Instant?,
  val taskDefinitionId: String,
  val gameId: Int,
  val titleTranslation: String,
  val icon: String,
  val progressMax: Int,
  val order: Int,
  val calculator: String, // TODO should be enum
  val enabled: Boolean,
)