package com.moregames.playtime.games.addwidget

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.ClientExperiment.ANDROID_BEGINNER_FRIENDLY_GAMES
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.variations.AndroidNewOfferButtonVariation
import com.moregames.base.offers.dto.OffersConfig
import com.moregames.base.util.ApplicationId.ATLANTIS_BOUNCE_APP_ID
import com.moregames.base.util.ApplicationId.BALL_BOUNCE_APP_ID
import com.moregames.base.util.ApplicationId.BLOCKBUSTER_APP_ID
import com.moregames.base.util.ApplicationId.BLOCK_HOLE_CLASH_APP_ID
import com.moregames.base.util.ApplicationId.BLOCK_SLIDER_APP_ID
import com.moregames.base.util.ApplicationId.BRICK_DOKU_APP_ID
import com.moregames.base.util.ApplicationId.BUBBLE_CHIEF_APP_ID
import com.moregames.base.util.ApplicationId.BUBBLE_POP_APP_ID
import com.moregames.base.util.ApplicationId.CARS_MERGE_APP_ID
import com.moregames.base.util.ApplicationId.COLOR_LOGIC_APP_ID
import com.moregames.base.util.ApplicationId.CRYSTAL_CRUSH_APP_ID
import com.moregames.base.util.ApplicationId.DICE_LOGIC_APP_ID
import com.moregames.base.util.ApplicationId.EMOJICLICKERS_APP_ID
import com.moregames.base.util.ApplicationId.HEXA_PUZZLE_FUN_APP_ID
import com.moregames.base.util.ApplicationId.HEX_MATCH_APP_ID
import com.moregames.base.util.ApplicationId.IDLE_MERGE_FUN_APP_ID
import com.moregames.base.util.ApplicationId.MAD_SMASH_APP_ID
import com.moregames.base.util.ApplicationId.MARBLE_MADNESS_APP_ID
import com.moregames.base.util.ApplicationId.MERGE_BLAST_APP_ID
import com.moregames.base.util.ApplicationId.MIX_BLOX_APP_ID
import com.moregames.base.util.ApplicationId.PIN_MASTER_APP_ID
import com.moregames.base.util.ApplicationId.PUZZLE_POP_BLASTER_APP_ID
import com.moregames.base.util.ApplicationId.SIDE_AND_ROLL_APP_ID
import com.moregames.base.util.ApplicationId.SLICE_PUZZLE_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_APP_ID
import com.moregames.base.util.ApplicationId.SPACE_CONNECT_APP_ID
import com.moregames.base.util.ApplicationId.SPIDER_SOLITAIRE_APP_ID
import com.moregames.base.util.ApplicationId.SPIRAL_DROP_APP_ID
import com.moregames.base.util.ApplicationId.SUDOKU_APP_ID
import com.moregames.base.util.ApplicationId.SUGAR_RUSH_APP_ID
import com.moregames.base.util.ApplicationId.TANGRAM_APP_ID
import com.moregames.base.util.ApplicationId.TILE_MATCH_PRO_APP_ID
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_APP_ID
import com.moregames.base.util.ApplicationId.TRIVIA_MADNESS_APP_ID
import com.moregames.base.util.ApplicationId.WATER_SORTER_APP_ID
import com.moregames.base.util.ApplicationId.WOODEN_PUZZLE_APP_ID
import com.moregames.base.util.ApplicationId.WORD_KITCHEN_APP_ID
import com.moregames.base.util.ApplicationId.WORD_SEEKER_APP_ID
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.AndroidOnlineUsersService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import com.moregames.playtime.user.offer.GameAdditionalWidgets
import com.moregames.playtime.user.offer.GameStyle
import kotlin.math.roundToInt
import kotlin.math.roundToLong

@Singleton
class GameAdditionalWidgetService @Inject constructor(
  imageService: ImageService,
  private val abTestingService: AbTestingService,
  private val cashoutPeriodsService: CashoutPeriodsService,
  private val rewardingFacade: RewardingFacade,
  private val userService: UserService,
  private val androidOnlineUsersService: AndroidOnlineUsersService
) {

  companion object {
    private const val BASE_MONEY_VALUE = 11932779L
    private const val DECREASE_STEP_IN_PERCENT = 0.1
    private const val SUB_TEXT = "Play & Earn \$%s"
    private const val DEFAULT_APP_ID = "default_app_id"
  }

  private val moneyBadgeUrl = imageService.toUrl("game_widget_money_paid_out_en.png")
  private val defaultTeaserImageUrl = imageService.toUrl("game_widget_default_teaser.png")
  private val tmTeaserImageUrl = imageService.toUrl("game_widget_tm_teaser.png")
  private val ballBounceTeaserImageUrl = imageService.toUrl("game_widget_ball_bounce_teaser.png")
  private val solitaireTeaserImageUrl = imageService.toUrl("game_widget_solitaire_teaser.png")
  private val mergeBlastBlockBusterBounceTeaserImageUrl = imageService.toUrl("game_widget_mb_bb_teaser.png")

  suspend fun createAdditionalWidgets(index: Int, userId: String): GameAdditionalWidgets =
    GameAdditionalWidgets(
      moneyPaidOut = getMoneyPaidOut(index),
      moneyBadgeUrl = moneyBadgeUrl,
      beginnerFriendly = getBeginnerFriendlyFLag(index, userId)
    )

  suspend fun createExtendedAdditionalWidget(index: Int, userId: String, applicationId: String, offersConfig: OffersConfig): ExtendedGameAdditionalWidget {
    val assignedVariation = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_NEW_OFFER_BUTTON)
    if (assignedVariation == DEFAULT) {
      return ExtendedGameAdditionalWidget(
        widgets = createAdditionalWidgets(index, userId),
        subText = offersConfig.buttonText,
        style = GameStyle.LARGE_IMAGE
      )
    }
    return when (assignedVariation as AndroidNewOfferButtonVariation) {
      AndroidNewOfferButtonVariation.AddCTAOldPayout -> {
        ExtendedGameAdditionalWidget(
          widgets = createAdditionalWidgets(index, userId),
          subText = SUB_TEXT.format(calculateCTAAmount(index)),
          style = GameStyle.WIDGET_REVAMPED_V1
        )
      }

      AndroidNewOfferButtonVariation.AddCTANewPayout -> {
        ExtendedGameAdditionalWidget(
          widgets = createAdditionalWidgets(index, userId).copy(moneyBadgeUrl = null),
          subText = SUB_TEXT.format(calculateCTAAmount(index)),
          style = GameStyle.WIDGET_REVAMPED_V1
        )
      }

      AndroidNewOfferButtonVariation.FullSet -> {
        ExtendedGameAdditionalWidget(
          widgets = createAdditionalWidgets(index, userId).copy(
            moneyBadgeUrl = null,
            teaserLabelUrl = newOfferButtonExperimentFields[applicationId]?.teaserLabelUrl ?: newOfferButtonExperimentFields[DEFAULT_APP_ID]?.teaserLabelUrl,
            storeRating = newOfferButtonExperimentFields[applicationId]?.storeRating ?: newOfferButtonExperimentFields[DEFAULT_APP_ID]?.storeRating,
            playersLive = calculatePlayersOnlineLabel(index)
          ),
          subText = SUB_TEXT.format(calculateCTAAmount(index)),
          style = GameStyle.WIDGET_REVAMPED_V1
        )
      }
    }
  }


  private fun getMoneyPaidOut(index: Int): Long = calculateNumbersForGame(index)

  private fun calculateNumbersForGame(index: Int): Long {
    var result = BASE_MONEY_VALUE
    repeat(index) {
      result -= (result * DECREASE_STEP_IN_PERCENT).roundToLong()
    }
    return result
  }

  private fun calculatePlayersOnlineLabel(index: Int): String {
    val activeUsers = androidOnlineUsersService.getActiveUsers()

    val preset = when (index) {
      0 -> 0.80
      1 -> 0.76
      2 -> 0.79
      3 -> 0.73
      else -> null
    }

    if (preset != null) {
      return "${(activeUsers * preset).roundToLong() / 1000}k Playing"
    } else {
      var result = (activeUsers * 0.73).roundToLong()
      repeat(index - 3) {
        result -= (result * 0.07).roundToLong()
      }
      return "${result / 1000}k Playing"
    }
  }

  private fun calculateCTAAmount(index: Int): Int {
    return when (index) {
      0 -> 243
      1 -> 218
      2 -> 223
      3 -> 204
      4 -> 198
      5 -> 201
      6 -> 193
      else -> {
        var result = 193
        repeat(index - 6) {
          result -= (result * 0.045).roundToInt()
        }
        result
      }

    }
  }

  private suspend fun getBeginnerFriendlyFLag(index: Int, userId: String): Boolean? {
    return (index == 0 &&
      abTestingService.isUserExperimentParticipant(userId, ANDROID_BEGINNER_FRIENDLY_GAMES) &&
      //show on the first CP
      (cashoutPeriodsService.getCurrentCashoutPeriod(userId).isFirst() ||
        //show if user has no earnings
        !rewardingFacade.userEverHadEarnings(userId) ||
        //show if user has no played games
        noGamesPlayed(userId))
      ).takeIf { it }
  }

  private suspend fun noGamesPlayed(userId: String): Boolean =
    null ==
      (if (abTestingService.isEm2Participant(userId = userId)) userService.getUserLastGameEm2CoinsDate(userId)
      else userService.getUserLastGameCoinsDate(userId))

  private val newOfferButtonExperimentFields = mapOf(
    TREASURE_MASTER_APP_ID to NewOfferButtonExperimentFields("4.5", tmTeaserImageUrl),
    SOLITAIRE_VERSE_APP_ID to NewOfferButtonExperimentFields("4.5", solitaireTeaserImageUrl),
    SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID to NewOfferButtonExperimentFields(null, solitaireTeaserImageUrl),
    MERGE_BLAST_APP_ID to NewOfferButtonExperimentFields("4.5", mergeBlastBlockBusterBounceTeaserImageUrl),
    MAD_SMASH_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    BALL_BOUNCE_APP_ID to NewOfferButtonExperimentFields("4.5", ballBounceTeaserImageUrl),
    SUGAR_RUSH_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    WOODEN_PUZZLE_APP_ID to NewOfferButtonExperimentFields("4.6", defaultTeaserImageUrl),
    PUZZLE_POP_BLASTER_APP_ID to NewOfferButtonExperimentFields("4.6", defaultTeaserImageUrl),
    BUBBLE_POP_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    MARBLE_MADNESS_APP_ID to NewOfferButtonExperimentFields("4.6", defaultTeaserImageUrl),
    EMOJICLICKERS_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    MIX_BLOX_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    HEXA_PUZZLE_FUN_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    WORD_SEEKER_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    TRIVIA_MADNESS_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    HEX_MATCH_APP_ID to NewOfferButtonExperimentFields("4.6", defaultTeaserImageUrl),
    CARS_MERGE_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    BLOCK_HOLE_CLASH_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    IDLE_MERGE_FUN_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    BLOCK_SLIDER_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    BRICK_DOKU_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    SUDOKU_APP_ID to NewOfferButtonExperimentFields("4.6", defaultTeaserImageUrl),
    WORD_KITCHEN_APP_ID to NewOfferButtonExperimentFields("4.6", defaultTeaserImageUrl),
    WATER_SORTER_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    COLOR_LOGIC_APP_ID to NewOfferButtonExperimentFields("4.6", defaultTeaserImageUrl),
    DICE_LOGIC_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    CRYSTAL_CRUSH_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    SPIRAL_DROP_APP_ID to NewOfferButtonExperimentFields("4.6", defaultTeaserImageUrl),
    TILE_MATCH_PRO_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    SPACE_CONNECT_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    BLOCKBUSTER_APP_ID to NewOfferButtonExperimentFields("4.5", mergeBlastBlockBusterBounceTeaserImageUrl),
    TANGRAM_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    PIN_MASTER_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    BUBBLE_CHIEF_APP_ID to NewOfferButtonExperimentFields("4.7", defaultTeaserImageUrl),
    SIDE_AND_ROLL_APP_ID to NewOfferButtonExperimentFields("4.7", defaultTeaserImageUrl),
    SLICE_PUZZLE_APP_ID to NewOfferButtonExperimentFields("4.6", defaultTeaserImageUrl),
    ATLANTIS_BOUNCE_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),
    SPIDER_SOLITAIRE_APP_ID to NewOfferButtonExperimentFields("4.8", solitaireTeaserImageUrl),
    DEFAULT_APP_ID to NewOfferButtonExperimentFields("4.5", defaultTeaserImageUrl),

    )

  data class NewOfferButtonExperimentFields(
    val storeRating: String?,
    val teaserLabelUrl: String?,
  )


}

data class ExtendedGameAdditionalWidget(
  val widgets: GameAdditionalWidgets,
  val style: GameStyle,
  val subText: String
)

