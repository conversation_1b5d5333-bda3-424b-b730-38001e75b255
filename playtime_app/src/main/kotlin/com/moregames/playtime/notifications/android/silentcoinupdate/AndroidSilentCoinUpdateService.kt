package com.moregames.playtime.notifications.android.silentcoinupdate

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.variations.AndroidSilentCoinsUpdateNotificationVariation
import com.moregames.base.app.BuildVariant
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.TimeService
import com.moregames.base.util.buildCache
import kotlinx.coroutines.async
import javax.inject.Singleton

@Singleton
class AndroidSilentCoinUpdateService @Inject constructor(
  buildVariant: BuildVariant,
  private val abTestingService: AbTestingService,
  private val timeService: TimeService,
  private val androidSilentCoinUpdatePersistenceService: AndroidSilentCoinUpdatePersistenceService,
  private val coroutineScope: Provider<IoCoroutineScope>,
) {

  private val userFirstNotificationCache = buildCache(buildVariant, expireAfter = FIRST_NOTIFICATION_SENT_CACHE_MINUTES) { userId: String ->
    coroutineScope.get().async {
      androidSilentCoinUpdatePersistenceService.isFirstNotificationSent(userId)
    }
  }

  suspend fun isFirstNotificationSent(userId: String): Boolean =
    userFirstNotificationCache.get(userId).await()

  suspend fun storeFirstNotificationSent(userId: String) {
    androidSilentCoinUpdatePersistenceService.storeFirstNotificationSent(userId, notifiedAt = timeService.now())
    userFirstNotificationCache.invalidate(userId)
  }

  suspend fun silentCoinUpdateNotificationSettings(userId: String): SilentNotificationSettings {
    val variation =
      abTestingService.assignedVariationValue(
        userId,
        ClientExperiment.ANDROID_SILENT_COINS_UPDATE_NOTIFICATION
      ) as? AndroidSilentCoinsUpdateNotificationVariation
        ?: return SilentNotificationSettings()
    if (variation.firstFull) {
      val isFirstSent = isFirstNotificationSent(userId)
      if (isFirstSent) {
        return SilentNotificationSettings(soundEnabled = false, vibrationEnabled = variation.vibrate)
      } else {
        storeFirstNotificationSent(userId)
        return SilentNotificationSettings()
      }
    } else {
      return SilentNotificationSettings(soundEnabled = false, vibrationEnabled = variation.vibrate)
    }
  }

  data class SilentNotificationSettings(
    val vibrationEnabled: Boolean? = null,
    val soundEnabled: Boolean? = null,
  )

  companion object {
    const val FIRST_NOTIFICATION_SENT_CACHE_MINUTES = 30L
  }
}
