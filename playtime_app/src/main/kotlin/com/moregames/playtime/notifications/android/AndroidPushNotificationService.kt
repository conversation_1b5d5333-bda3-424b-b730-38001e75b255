package com.moregames.playtime.notifications.android

import com.google.inject.Inject
import com.google.inject.Singleton
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCurrencyEarnings
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.variations.SpecialCashoutOffersVariation
import com.moregames.base.abtesting.variations.SpecialCashoutOffersVariation.*
import com.moregames.base.messaging.customnotification.*
import com.moregames.base.messaging.customnotification.BackgroundAction.*
import com.moregames.base.messaging.customnotification.ButtonActionName.OPEN_FIRST_FOUND_INSTALLED_GAME
import com.moregames.base.messaging.customnotification.CustomNotificationDto.NotificationChannelId
import com.moregames.base.messaging.customnotification.CustomNotificationSize.LARGE
import com.moregames.base.messaging.customnotification.CustomNotificationSize.MEDIUM
import com.moregames.base.messaging.dto.GenericPushNotificationScheduledEventDto
import com.moregames.base.messaging.dto.PushNotificationDto.PushNotificationType.*
import com.moregames.base.messaging.dto.PushNotificationDto.PushNotificationType.REACH_COIN_GOAL
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.TimeService
import com.moregames.base.util.format
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.boost.BoostedModeService
import com.moregames.playtime.notifications.NotificationsFacade
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.*
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.*
import com.moregames.playtime.notifications.PushNotificationAnalyticalLabel
import com.moregames.playtime.notifications.android.cashoutfailed.CashoutFailedAppNotification
import com.moregames.playtime.notifications.android.cashoutprocessed.CashoutProcessedAppNotification
import com.moregames.playtime.notifications.android.ratingprompt.RatingPromptAppNotification
import com.moregames.playtime.notifications.android.readytocashout.ReadyToCashoutAppNotification
import com.moregames.playtime.notifications.android.silentcoinupdate.AndroidSilentCoinUpdateService
import com.moregames.playtime.notifications.android.survey.SurveyAppNotification
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.TranslationResource.*
import com.moregames.playtime.translations.UntranslatedStringsStorage.BALANCE_UPDATE_BODY_CASH_OUT_OFFER
import com.moregames.playtime.translations.UntranslatedStringsStorage.BALANCE_UPDATE_BODY_NO_COINS
import com.moregames.playtime.translations.UntranslatedStringsStorage.BALANCE_UPDATE_BODY_NO_COINS_CASH_OUT_OFFER
import com.moregames.playtime.translations.UntranslatedStringsStorage.BALANCE_UPDATE_BODY_SPECIAL_OFFERS_NO_COINS
import com.moregames.playtime.translations.UntranslatedStringsStorage.BALANCE_UPDATE_TITLE_NO_COINS
import com.moregames.playtime.translations.UntranslatedStringsStorage.BALANCE_UPDATE_TITLE_SPECIAL_OFFERS_NO_COINS
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.cashout.CashoutPeriodsConfigService
import com.moregames.playtime.user.cashout.CashoutService.Companion.HIDE_EARNINGS_CASHOUT_AVAILABLE_HEADER
import com.moregames.playtime.user.cashout.HideCashoutAmountExperimentService
import com.moregames.playtime.user.cashout.ReadyToCashoutExperimentService
import com.moregames.playtime.user.gamerank.GameRank
import com.moregames.playtime.user.inactivityreminder.ReminderNotificationService.Companion.USER_NAME_PLACEHOLDER
import com.moregames.playtime.util.roundDownToSecondDigit
import java.text.NumberFormat
import java.time.Duration
import java.util.*
import kotlin.time.Duration.Companion.hours

@Singleton
class AndroidPushNotificationService @Inject constructor(
  private val translationService: UserTranslationService,
  private val notificationsFacade: NotificationsFacade,
  private val randomGenerator: RandomGenerator,
  private val imageService: ImageService,
  private val hideCashoutAmountExperimentService: HideCashoutAmountExperimentService,
  private val readyToCashoutExperimentService: ReadyToCashoutExperimentService,
  private val abTestingService: AbTestingService,
  private val boostedModeService: BoostedModeService,
  private val balanceUpdatedNotificationWithAmountExpService: BalanceUpdatedNotificationWithAmountExpService,
  private val timeService: TimeService,
  private val androidSilentCoinUpdateService: AndroidSilentCoinUpdateService,
  private val cashoutPeriodsConfigService: CashoutPeriodsConfigService,
) {

  companion object {
    private const val TWO_MINUTES_DISPLAY_DELAY = "120000"
    private const val COLOR_NEBULA_BLUE = "#5262FB"
    private const val COLOR_WHITE = "#FFFFFF"
    private const val COLOR_PIGMENT_GREEN = "#3AAF49"
    private const val COLOR_WIZARDS_BREW = "#A090B0"
    private const val COLOR_DEEP_CARMINE = "#A9203E"
    private const val COLOR_CERISE = "#D442AE"
    private const val COLOR_DOGWOOD_ROSE = "#CE1662"
    private const val COLOR_DEEP_PINK = "#FF1185"
    private const val COLOR_BLUE = "#2D9CDB"
    private const val COLOR_LAVENDER_VIOLET = "#BB6BD9"
    private const val COLOR_GOLDENROD = "#E19E03"
    private const val COLOR_SATURATED_GREEN = "#34C759"
  }

  suspend fun sendFirstCoinsForGameNotification(notification: FirstCoinsForGamePushNotification, locale: Locale) = with(notification) {
    notificationsFacade.sendGenericPushNotification(
      GenericPushNotificationScheduledEventDto(
        userId = userId,
        title = translationService.translateOrDefault(KEEP_PLAYING_GAME, locale, userId).replace("{gameName}", gameName),
        notificationText = translationService.translateOrDefault(MORE_YOU_PLAY_MORE_YOU_EARN, locale, userId),
        notificationType = AFTER_FIRST_COINS_FOR_GAME,
        label = label
      )
    )
  }

  suspend fun sendOnFirstCashoutOfDayNotification(notification: FirstCashoutOfDayNotification, locale: Locale) = with(notification) {
    notificationsFacade.sendGenericPushNotification(
      GenericPushNotificationScheduledEventDto(
        userId = userId,
        title = translationService.translateOrDefault(CONGRATULATIONS_ON_CASHOUT, locale, userId),
        notificationText = translationService.translateOrDefault(COME_BACK_AND_EARN, locale, userId),
        notificationType = AFTER_FIRST_CASHOUT_OF_DAY,
        label = label
      )
    )
  }

  suspend fun sendXMinutesToCashoutPeriodEndNotification(notification: XMinutesToCashoutPeriodEndNotification, locale: Locale) = with(notification) {
    notificationsFacade.sendGenericPushNotification(
      GenericPushNotificationScheduledEventDto(
        userId = userId,
        title = translationService.translateOrDefault(NOTIFICATION_30MIN_TO_CASHOUT, locale, userId),
        notificationText = translationService.translateOrDefault(NOTIFICATION_KEEP_PLAYING, locale, userId),
        notificationType = THIRTY_MINUTES_TO_CASHOUT,
        label = label
      )
    )
  }

  suspend fun sendReachCoinGoalNotification(notification: ReachCoinGoalNotification, locale: Locale) = with(notification) {
    val (title, text) = if (coinGoalReached) {
      (translationService.translateOrDefault(KEEP_PLAYING_TO_MAXIMIZE_EARNINGS, locale, userId) to
        translationService.translateOrDefault(ALL_COINS_OVER_COIN_GOAL_ARE_EARNINGS, locale, userId))
    } else {
      translationService.translateOrDefault(TranslationResource.REACH_COIN_GOAL, locale, userId) to
        translationService.translateOrDefault(YOU_NOT_REACHED_COIN_GOAL_REACH_IT, locale, userId)
    }

    notificationsFacade.sendGenericPushNotification(
      GenericPushNotificationScheduledEventDto(
        userId = userId,
        title = title,
        notificationText = text,
        notificationType = REACH_COIN_GOAL,
        label = label,
      )
    )
  }

  suspend fun sendMissedEarningsNotification(notification: MissedEarningsNotification, locale: Locale) = with(notification) {
    notificationsFacade.sendGenericPushNotification(
      GenericPushNotificationScheduledEventDto(
        userId = userId,
        title = translationService.translateOrDefault(JUST_PLAY_A_FEW_MINUTES_TO_MAKE_MONEY, locale, userId),
        notificationText = translationService.translateOrDefault(YOU_MISSED_OUT_ON_EARNINGS, locale, userId),
        notificationType = AFTER_EMPTY_FIRST_CASHOUT_PERIOD,
        label = label
      )
    )
  }

  suspend fun sendInactivityReminder(notification: InactivityReminder, locale: Locale) = with(notification) {
    val (title, text) = if (userFirstName == null) {
      translationService.tryTranslate(notificationConfig.title, locale, userId) to
        translationService.tryTranslate(notificationConfig.message, locale, userId)
    } else {
      translationService.tryTranslate(notificationConfig.personalizedTitle, locale, userId).replace(USER_NAME_PLACEHOLDER, userFirstName) to
        translationService.tryTranslate(notificationConfig.personalizedMessage, locale, userId).replace(USER_NAME_PLACEHOLDER, userFirstName)
    }

    notificationsFacade.sendGenericPushNotification(
      GenericPushNotificationScheduledEventDto(
        userId = userId,
        title = title,
        notificationText = text,
        label = label
      )
    )
  }

  suspend fun sendBalanceUpdatedNotification(notification: BalanceUpdatedNotification, locale: Locale, cashout2xOfferActive: Boolean = false) =
    with(notification) {
      val coinsTotalFormatted = NumberFormat.getNumberInstance(locale).format(coins)

      val bm = boostedModeService.findCurrentBoostedMode(notification.userId)
      val uiConfig = bm?.uiConfig?.balanceUpdate
      if (uiConfig != null) {
        val boostedCoins = (bm.coinsCoefficient * coins).toLong().let { NumberFormat.getNumberInstance(locale).format(it) }
        customNotification(
          id = NotificationChannelId.BALANCE_UPDATE,
          title = translationService.tryTranslate(uiConfig.titleTranslation, locale, userId),
          shortDescription = if (hideCoins) {
            translationService.tryTranslate(uiConfig.descriptionNoCoinsTranslation, locale, userId)
          } else
            translationService.tryTranslate(uiConfig.descriptionTranslation, locale, userId)
              .format(boostedCoins),
          iconFilename = "notification-coin-balance-updated-icon.png",
          size = MEDIUM,
          backgroundColor = COLOR_NEBULA_BLUE,
          textColor = COLOR_WHITE,
          backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
          label = label,
          collapseKey = CustomNotificationCollapseKey.BALANCE_UPDATE,
        ).also { notificationsFacade.sendMessage(userId, it) }
        return@with
      }

      val silentSettings = androidSilentCoinUpdateService.silentCoinUpdateNotificationSettings(userId = userId)

      if (cashout2xOfferActive) {
        val (title, body) = if (hideCoins) {
          BALANCE_UPDATE_TITLE_SPECIAL_OFFERS_NO_COINS to BALANCE_UPDATE_BODY_SPECIAL_OFFERS_NO_COINS
        } else {
          "Special Offer Coins Collected" to "You now have $coinsTotalFormatted special coins"
        }

        notificationsFacade.sendMessage(
          userId = userId,
          message = CustomNotificationDto(
            id = NotificationChannelId.BALANCE_UPDATE,
            notificationId = randomGenerator.nextUUID(),
            title = title,
            backgroundColor = COLOR_CERISE,
            textColor = COLOR_WHITE,
            icon = imageService.toUrl("notification-coin-balance-updated-icon.png"),
            size = LARGE,
            shortDescription = body,
            vibrationEnabled = silentSettings.vibrationEnabled ?: true,
            soundEnabled = silentSettings.soundEnabled ?: true,
            backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
            collapseKey = CustomNotificationCollapseKey.BALANCE_UPDATE,
            label = label
          )
        )
        return@with
      }

      val (title, body) = if (hideCoins) {
        BALANCE_UPDATE_TITLE_NO_COINS to BALANCE_UPDATE_BODY_NO_COINS
      } else {
        balanceUpdatedNotificationWithAmountExpService.getTitleAndBody(userId, locale, coins, coinsAdded)
      }

      customNotification(
        id = NotificationChannelId.BALANCE_UPDATE,
        title = title,
        shortDescription = body,
        iconFilename = "notification-coin-balance-updated-icon.png",
        size = MEDIUM,
        backgroundColor = COLOR_NEBULA_BLUE,
        textColor = COLOR_WHITE,
        vibrationEnabled = silentSettings.vibrationEnabled,
        soundEnabled = silentSettings.soundEnabled,
        backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
        label = label,
        collapseKey = CustomNotificationCollapseKey.BALANCE_UPDATE,
      )
        .let { notificationsFacade.sendMessage(userId, it) }
    }

  suspend fun sendCashoutFailedNotification(notification: CashoutFailedNotification) = with(notification) {
    notificationsFacade.sendMessage(
      userId = cashoutTransaction.userId,
      message = CashoutFailedAppNotification(
        cashoutTransactionId = cashoutTransaction.cashoutTransactionId,
        amountString = cashoutTransaction.userCurrencyAmount.format(cashoutTransaction.userCurrency),
        providerType = cashoutTransaction.provider,
        label = label
      )
    )
  }

  suspend fun sendCashoutProcessedNotification(notification: CashoutProcessedNotification) = with(notification) {
    notificationsFacade.sendMessage(
      userId = cashoutTransaction.userId,
      message = CashoutProcessedAppNotification(
        cashoutTransactionId = cashoutTransaction.cashoutTransactionId,
        amountString = cashoutTransaction.userCurrencyAmount.format(cashoutTransaction.userCurrency),
        providerType = cashoutTransaction.provider,
        label = label
      )
    )
  }

  suspend fun sendEarningsAddedNotification(notification: EarningsAddedNotification, locale: Locale) = with(notification) {
    // i'm very sorry for cpStart hack. i'm truly sorry. the problem is that this push is designed to be sent AFTER CP ends physically. so we have 2 options:
    // 1. propagate cpStart through 1278491238749123 layers of our logics
    // 2. just "rewind" time to 3 hours ago. in this case BM FW will "detect" corresponding BM session
    // i've chosen second one after some consultations. first way is not suitable for cool guys as we are.
    val cashoutPeriodsConfig = cashoutPeriodsConfigService.getCashoutPeriodConfig(userId)
    val boostedMode = boostedModeService.findCurrentBoostedMode(
      userId = userId,
      cpStart = timeService.now() - Duration.ofMinutes(cashoutPeriodsConfig.defaultCashoutPeriodMinutes)
    )?.uiConfig?.readyToCashout
    if (boostedMode != null) {
      sendReadyToCashoutCustomAppNotification(
        userId,
        title = translationService.tryTranslate(boostedMode.titleTranslation, locale, userId),
        description = translationService.tryTranslate(boostedMode.descriptionTranslation, locale, userId),
        label
      )
      return@with
    }

    if (hideCashoutAmountExperimentService.shouldShowGiftBox(userId, userHasCashouts, earnings.amountUsd)) {
      sendEarningsAddedNotification(userId = userId, label = notification.label)
    } else {
      val readyToCashoutExperimentConfig = readyToCashoutExperimentService.getReadyToCashoutExperimentConfig(
        userId = userId,
        unclaimedUsdEarnings = earnings
      )

      if (readyToCashoutExperimentConfig != null) {
        sendReadyToCashoutCustomAppNotification(
          userId,
          title = readyToCashoutExperimentConfig.title!!,
          description = readyToCashoutExperimentConfig.description!!,
          label = label
        )
      } else {
        sendReadyToCashoutAppNotification(userId = userId, label = label, earnings = earnings)
      }
    }
  }

  private suspend fun sendEarningsAddedNotification(userId: String, label: String) {
    notificationsFacade.sendMessage(
      userId = userId,
      CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        id = NotificationChannelId.READY_TO_CASH_OUT,
        collapseKey = CustomNotificationCollapseKey.EARNINGS_ADDED,
        title = HIDE_EARNINGS_CASHOUT_AVAILABLE_HEADER,
        size = MEDIUM,
        backgroundColor = COLOR_PIGMENT_GREEN,
        icon = imageService.toUrl("notification_cashout.png"),
        onClickAction = OnClickActionApiDto.routeToCashout(),
        textColor = COLOR_WHITE,
        label = label
      )
    )
  }

  private suspend fun sendReadyToCashoutAppNotification(userId: String, label: String, earnings: UserCurrencyEarnings) {
    notificationsFacade.sendMessage(
      userId, ReadyToCashoutAppNotification(
        amountString = earnings.userCurrencyAmount.roundDownToSecondDigit().format(earnings.userCurrency),
        label = label
      )
    )
  }

  private suspend fun sendReadyToCashoutCustomAppNotification(
    userId: String,
    title: String,
    description: String,
    label: String,
  ) {
    notificationsFacade.sendMessage(
      userId,
      message = CustomNotificationDto(
        id = NotificationChannelId.READY_TO_CASH_OUT,
        notificationId = randomGenerator.nextUUID(),
        title = title,
        shortDescription = description,
        size = MEDIUM,
        backgroundColor = COLOR_PIGMENT_GREEN,
        icon = imageService.toUrl("notification_cashout.png"),
        onClickAction = OnClickActionApiDto.routeToCashout(),
        textColor = COLOR_WHITE,
        label = label
      )
    )
  }

  suspend fun sendRatingPromptCommand(notification: RatingPromptCommand) = with(notification) {
    notificationsFacade.sendMessage(userId, RatingPromptAppNotification(TWO_MINUTES_DISPLAY_DELAY, label))
  }

  suspend fun sendWelcomeCoinsOfferAvailableNotification(notification: WelcomeCoinsOfferAvailableNotification, locale: Locale) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        title = translationService.translateOrDefault(WELCOME_COINS_OFFER_AVAILABLE, locale, userId),
        size = MEDIUM,
        backgroundColor = COLOR_WIZARDS_BREW,
        label = label,
      )
    )
  }

  suspend fun sendGooglePlayOpenedNotification(notification: GooglePlayOpenedNotification, locale: Locale) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        title = translationService.translateOrDefault(GOOGLE_PLAYSTORE_OPENED_NOTIFICATION_TITLE, locale, userId),
        shortDescription = translationService.translateOrDefault(GOOGLE_PLAYSTORE_OPENED_NOTIFICATION_DESCRIPTION, locale, userId)
          .replace("{gameName}", gameName),
        size = MEDIUM,
        backgroundColor = COLOR_WIZARDS_BREW,
        onClickAction = OnClickActionApiDto.discardNotification(),
        label = label
      )
    )
  }

  suspend fun sendSurveyNotification(notification: SurveyNotification) = with(notification) {
    notificationsFacade.sendMessage(userId, SurveyAppNotification(surveyId = surveyId, label = label))
  }

  suspend fun sendGameUnlockedNotification(notification: GameUnlockedNotification, locale: Locale) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        title = translationService.translateOrDefault(GAME_UNLOCK_NOTIFICATION_TITLE, locale, userId),
        shortDescription = translationService.translateOrDefault(GAME_UNLOCK_NOTIFICATION_DESCRIPTION, locale, userId).replace("{gameName}", gameName),
        size = LARGE,
        backgroundColor = COLOR_NEBULA_BLUE,
        textColor = COLOR_WHITE,
        backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
        actionButtons = listOf(
          ButtonApiDto(
            name = translationService.translateOrDefault(GAME_UNLOCK_NOTIFICATION_BUTTON, locale, userId),
            textColor = COLOR_NEBULA_BLUE,
            background = COLOR_WHITE,
            action = ButtonAction.scrollToUnlockedGameAndHideWidget(widgetId),
          )
        ),
        onClickAction = OnClickActionApiDto.scrollToUnlockedGameAndHideWidget(widgetId),
        androidBackgroundOnly = true,
        label = label
      )
    )
  }

  suspend fun sendRemindToPlayNotification(notification: RemindToPlayNotification) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        title = title,
        shortDescription = description,
        size = LARGE,
        backgroundColor = COLOR_NEBULA_BLUE,
        textColor = COLOR_WHITE,
        backgroundActions = listOf(REPLACE_PLACEHOLDERS),
        actionButtons = listOf(
          ButtonApiDto(
            name = "Play Now!",
            textColor = COLOR_NEBULA_BLUE,
            background = COLOR_WHITE,
            action = ButtonAction(OPEN_FIRST_FOUND_INSTALLED_GAME),
          )
        ),
        onClickAction = OnClickActionApiDto(OnClickActionName.OPEN_FIRST_FOUND_INSTALLED_GAME),
        label = label
      )
    )
  }

  suspend fun sendGameCoinGoalReachedNotification(notification: GameCoinGoalReachedNotification) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        title = title,
        shortDescription = text,
        label = label
      )
    )
  }

  suspend fun sendContinueIncompleteCashoutNotification(notification: ContinueIncompleteCashoutNotification) = with(notification) {
    val formattedAmount = earnings.userCurrencyAmount.roundDownToSecondDigit().format(earnings.userCurrency)
    val formattedDescription = "Finish cashing-out your {formatted_amount} now".replace(
      oldValue = "{formatted_amount}",
      newValue = "$formattedAmount ${earnings.userCurrency.currencyCode}"
    )

    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        title = "Let's Get You Back on Track!",
        shortDescription = formattedDescription,
        longDescription = formattedDescription,
        backgroundColor = COLOR_PIGMENT_GREEN,
        size = MEDIUM,
        vibrationEnabled = true,
        soundEnabled = true,
        onClickAction = OnClickActionApiDto.continueIncompleteCashout(),
        textColor = COLOR_WHITE,
        label = label
      )
    )
  }

  suspend fun sendInstallGameReminder(notification: InstallGameReminder) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        title = title,
        shortDescription = text,
        textColor = COLOR_WHITE,
        backgroundColor = COLOR_PIGMENT_GREEN,
        size = LARGE,
        icon = icon?.let { imageService.toUrl(it) } ?: "",
        image = image?.let { imageService.toUrl(it) },
        onClickAction = onClickAction,
        actionButtons = listOf(
          ButtonApiDto(
            name = "Play",
            textColor = COLOR_PIGMENT_GREEN,
            background = COLOR_WHITE,
            action = buttonAction
          )
        ),
        label = label,
      )
    )
  }

  suspend fun sendUnclaimedEarningNotification(notification: UnclaimedEarningNotification) = with(notification) {
    notificationsFacade.sendGenericPushNotification(
      GenericPushNotificationScheduledEventDto(
        userId = userId,
        title = title,
        notificationText = text,
        label = label,
      )
    )
  }

  suspend fun sendRewardEarningsAdded(notification: RewardEarningsAddedNotification) = with(notification) {
    val formattedAmount = earnings.userCurrencyAmount.roundDownToSecondDigit().format(earnings.userCurrency)

    // same notification for ios is not implemented (just because we're not launching on ios yet)
    // subject to translations

    val translatedTitle =
      translationService
        .translateOrDefault(REWARD_EARNINGS_NOTIFICATION_TITLE, notification.locale, notification.userId)
        .replace("{formatted_amount}", formattedAmount)

    customNotification(
      id = NotificationChannelId.READY_TO_CASH_OUT,
      collapseKey = CustomNotificationCollapseKey.EARNINGS_ADDED,
      title = translatedTitle,
      iconFilename = "challenges/challenges_earnings_push_icon.png",
      size = MEDIUM,
      backgroundColor = COLOR_DEEP_CARMINE,
      textColor = COLOR_WHITE,
      onClickAction = OnClickActionApiDto.routeToCashout(),
      soundEnabled = true,
      vibrationEnabled = true,
      label = label,
    )
      .let { notificationsFacade.sendMessage(userId, it) }
  }

  suspend fun sendCashout2xOfferActivatedNotification(notification: Cashout2xOfferActivatedNotification) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        id = NotificationChannelId.CASHOUT_2X_OFFER_ACTIVATED,
        notificationId = randomGenerator.nextUUID(),
        title = if (variation.isBm) {
          "Limited Time Boost Active"
        } else "3-Hour \$\$\$\$ Bonus Active!",
        backgroundColor = COLOR_CERISE,
        textColor = COLOR_WHITE,
        icon = imageService.toUrl("Special%20Offer%20Badge.png"),
        size = LARGE,
        shortDescription = "Enjoy special coins and more $$$ until the timer ends",
        vibrationEnabled = true,
        soundEnabled = true,
        countdownTimerTarget = offerDuration.toString(),
        collapseKey = CustomNotificationCollapseKey.CASHOUT_2X_OFFER_ACTIVATED,
        label = label,
      )
    )
  }

  suspend fun sendChallengeCompleted(notification: ChallengeCompletedNotification) =
    customNotification(
      id = NotificationChannelId.CUSTOM_NOTIFICATION,
      title = translationService.translateOrDefault(CHALLENGES_COMPLETED_NOTIFICATION_TITLE, notification.locale, notification.userId),
      iconFilename = "JustPlay%20Golden%20Ticket.png",
      size = MEDIUM,
      backgroundColor = COLOR_DOGWOOD_ROSE,
      textColor = COLOR_WHITE,
      soundEnabled = true,
      vibrationEnabled = true,
      onClickAction = OnClickActionApiDto.routeToMain(),
      label = notification.label,
    )
      .let { notificationsFacade.sendMessage(notification.userId, it) }

  suspend fun sendGameRankUpdated(notification: GameRankUpdatedNotification) = with(notification) {
    val rank = when (updatedRank) {
      GameRank.ZERO -> return@with
      GameRank.ONE -> "⭐"
      GameRank.TWO -> "⭐⭐"
      GameRank.THREE -> "⭐⭐⭐"
    }
    GenericPushNotificationScheduledEventDto(
      userId = userId,
      notificationText = "Your rank in $gameName is $rank!",
      label = label,
    ).let { notificationsFacade.sendGenericPushNotification(it) }
  }

  suspend fun sendChallengeSpecialOfferActivatedNotification(notification: ChallengeSpecialOfferActivatedNotification) =
    customNotification(
      id = NotificationChannelId.CHALLENGE_SPECIAL_OFFER_ACTIVATED,
      title = "Your Special Offer is Active!",
      shortDescription = "Enjoy special coins & more\n\$\$\$ until the timer ends!",
      iconFilename = "boosted_mode/notification_special_offer_banner.png",
      size = LARGE,
      backgroundColor = COLOR_LAVENDER_VIOLET,
      textColor = COLOR_WHITE,
      soundEnabled = false,
      vibrationEnabled = true,
      countdownTimerTarget = notification.offerDuration.toString(),
      onClickAction = OnClickActionApiDto.routeToMain(),
      label = notification.label,
    ).let { notificationsFacade.sendMessage(notification.userId, it) }

  suspend fun sendSpecialChallengeCompleted(notification: SpecialChallengeCompletedNotification) =
    customNotification(
      id = NotificationChannelId.CUSTOM_NOTIFICATION,
      title = translationService.translateOrDefault(SPECIAL_CHALLENGE_QUEST_COMPLETED_NOTIFICATION_TITLE, notification.locale, notification.userId),
      iconFilename = "Key.png",
      size = LARGE,
      backgroundColor = COLOR_GOLDENROD,
      textColor = COLOR_WHITE,
      soundEnabled = true,
      vibrationEnabled = true,
      onClickAction = OnClickActionApiDto.routeToMain(),
      label = notification.label,
      shortDescription = translationService.translateOrDefault(
        SPECIAL_CHALLENGE_QUEST_COMPLETED_NOTIFICATION_DESCRIPTION,
        notification.locale,
        notification.userId
      ),
    )
      .let { notificationsFacade.sendMessage(notification.userId, it) }

  suspend fun sendSpecialChallengeClaimed(notification: SpecialChallengeClaimedNotification) =
    customNotification(
      id = NotificationChannelId.CUSTOM_NOTIFICATION,
      title = translationService.translateOrDefault(SPECIAL_CHALLENGE_KEY_COLLECTED_NOTIFICATION_TITLE, notification.locale, notification.userId),
      iconFilename = "closed%20treasure%20box.png",
      size = LARGE,
      backgroundColor = COLOR_GOLDENROD,
      textColor = COLOR_WHITE,
      soundEnabled = true,
      vibrationEnabled = true,
      onClickAction = OnClickActionApiDto.routeToMain(),
      label = notification.label,
      shortDescription = translationService.translateOrDefault(
        SPECIAL_CHALLENGE_KEY_COLLECTED_NOTIFICATION_DESCRIPTION,
        notification.locale,
        notification.userId
      ),
    )
      .let { notificationsFacade.sendMessage(notification.userId, it) }


  suspend fun sendSpecialChallengeEarningsAdded(notification: RewardSpecialChallengesEarningsAddedNotification) = with(notification) {
    val formattedAmount = earnings.userCurrencyAmount.roundDownToSecondDigit().format(earnings.userCurrency)

    val translatedTitle =
      translationService
        .translateOrDefault(SPECIAL_CHALLENGE_BONUS_RECEIVED_NOTIFICATION_TITLE, notification.locale, notification.userId)
        .replace("{formatted_amount}", formattedAmount)
    val description = translationService
      .translateOrDefault(SPECIAL_CHALLENGE_BONUS_RECEIVED_NOTIFICATION_DESCRIPTION, notification.locale, notification.userId)

    customNotification(
      id = NotificationChannelId.CUSTOM_NOTIFICATION,
      title = translatedTitle,
      iconFilename = "treasure%20box.png",
      size = LARGE,
      backgroundColor = COLOR_SATURATED_GREEN,
      textColor = COLOR_WHITE,
      onClickAction = OnClickActionApiDto.routeToCashout(),
      soundEnabled = true,
      vibrationEnabled = true,
      shortDescription = description,
      label = label,
    )
      .let { notificationsFacade.sendMessage(userId, it) }
  }

  suspend fun sendKeepDoingChallenges(notification: KeepDoingChallengesNotification) =
    customNotification(
      id = NotificationChannelId.KEEP_DOING_CHALLENGES,
      title = notification.title,
      shortDescription = notification.body,
      iconFilename = "Challenges%20Icon.png",
      size = LARGE,
      backgroundColor = COLOR_DEEP_PINK,
      textColor = COLOR_WHITE,
      soundEnabled = true,
      vibrationEnabled = true,
      onClickAction = OnClickActionApiDto.routeToMain(),
      label = notification.label,
    )
      .let { notificationsFacade.sendMessage(notification.userId, it) }

  suspend fun sendLuckyHourNotification(notification: LuckyHourNotification) {
    if (notification.endTime != null) {
      val timeTillEnd = Duration.between(timeService.now(), notification.endTime)
      notificationsFacade.sendMessage(
        notification.userId, customNotification(
          id = NotificationChannelId.LUCKY_HOUR,
          title = "\$\$\$ Lucky Hour Started!",
          backgroundColor = "#FFB800",
          textColor = "#FFFFFF",
          iconFilename = "Lucky%20Cash%20Badge.png",
          size = LARGE,
          shortDescription = "The next ${timeTillEnd.toMinutes()} minutes we’re giving a \$\$\$\$ CASH BONUS to the most active player!",
          vibrationEnabled = true,
          soundEnabled = true,
          countdownTimerTarget = timeTillEnd.toMillis().toString(),
          label = notification.label,
        )
      )
      return
    }
    notificationsFacade.sendMessage(
      notification.userId, customNotification(
        id = NotificationChannelId.LUCKY_HOUR,
        title = "\$\$\$ Lucky Hour Started!",
        backgroundColor = "#FFB800",
        textColor = "#FFFFFF",
        iconFilename = "Lucky%20Cash%20Badge.png",
        size = LARGE,
        shortDescription = "The next 60 minutes we’re giving a \$\$\$\$ CASH BONUS to the most active player!",
        vibrationEnabled = true,
        soundEnabled = true,
        countdownTimerTarget = 1.hours.inWholeMilliseconds.toString(),
        label = notification.label,
      )
    )
  }

  suspend fun sendCashoutOfferStartedNotification(notification: CashoutOfferStarted) {
    val variation =
      abTestingService.assignedVariationValue(notification.userId, ClientExperiment.SPECIAL_CASHOUT_OFFERS) as? SpecialCashoutOffersVariation ?: return
    val title = when (variation) {
      ThreeCashoutOffers -> "Special Offer Active!"
      ThreeRandomCashoutOffers,
      ThreeRandom1EarningCashoutOffers,
      ThreeRandom05EarningCashoutOffers,
      ThreeRandom025EarningCashoutOffers -> "1-Hour \$\$\$ Bonus Active!"
    }
    notificationsFacade.sendMessage(
      notification.userId, customNotification(
        id = NotificationChannelId.SPECIAL_CASHOUT_OFFER_ACTIVATED,
        title = title,
        backgroundColor = COLOR_CERISE,
        textColor = COLOR_WHITE,
        iconFilename = "Special%20Offer%20Badge.png",
        size = LARGE,
        shortDescription = "Enjoy special coins and more \$\$\$ from ${notification.gameName} until the timer ends",
        vibrationEnabled = true,
        soundEnabled = true,
        countdownTimerTarget = 1.hours.inWholeMilliseconds.toString(),
        label = notification.label,
        collapseKey = CustomNotificationCollapseKey.CUSTOM_NOTIFICATION,
      )
    )
  }

  suspend fun sendCashoutOfferBalanceUpdatedNotification(notification: CashoutOfferBalanceUpdate, locale: Locale) {
    val coinsTotalFormatted = NumberFormat.getNumberInstance(locale).format(notification.coinsBalance)

    val body =
      if (notification.hideCoins) BALANCE_UPDATE_BODY_NO_COINS_CASH_OUT_OFFER
      else BALANCE_UPDATE_BODY_CASH_OUT_OFFER.replace("{coins}", coinsTotalFormatted)

    val silentSettings = androidSilentCoinUpdateService.silentCoinUpdateNotificationSettings(userId = notification.userId)

    notificationsFacade.sendMessage(
      notification.userId, customNotification(
        id = NotificationChannelId.BALANCE_UPDATE,
        title = "Special Coins Collected",
        shortDescription = body,
        backgroundColor = "#D442AE",
        textColor = COLOR_WHITE,
        iconFilename = "notification-coin-balance-updated-icon.png",
        size = LARGE,
        vibrationEnabled = silentSettings.vibrationEnabled ?: true,
        soundEnabled = silentSettings.soundEnabled ?: true,
        label = notification.label,
        collapseKey = CustomNotificationCollapseKey.CASHOUT_OFFER_BALANCE_UPDATE,
      )
    )
  }

  fun getDontLoseYourStreakNotification() =
    CustomNotificationDto(
      id = NotificationChannelId.CASH_STREAK,
      notificationId = randomGenerator.nextUUID(),
      title = "Your streak is in danger!",
      backgroundColor = COLOR_BLUE,
      textColor = COLOR_WHITE,
      icon = imageService.toUrl("Cash%20Streak%20Fire.png"),
      size = LARGE,
      shortDescription = "Don’t lose it. Come and extend!",
      vibrationEnabled = true,
      soundEnabled = true,
      label = PushNotificationAnalyticalLabel.DONT_LOSE_YOUR_STREAK_REMINDER.apiName
    )


  suspend fun sendDayStreakRewardReadyNotification(notification: DayStreakRewardReadyNotification) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      CustomNotificationDto(
        id = NotificationChannelId.CASH_STREAK,
        notificationId = randomGenerator.nextUUID(),
        title = "Your streak reward is ready!",
        backgroundColor = COLOR_BLUE,
        textColor = COLOR_WHITE,
        icon = imageService.toUrl("Cash%20Streak%20Gift%20Box%20Closed.png"),
        size = LARGE,
        shortDescription = "The reward is waiting for you to be claimed!",
        vibrationEnabled = true,
        soundEnabled = true,
        label = label
      )
    )
  }

  suspend fun sendOfferwallNowAllowedNotification(notification: OnOfferwallNowAllowedNotification) = with(notification) {
    notificationsFacade.sendGenericPushNotification(
      GenericPushNotificationScheduledEventDto(
        userId = userId,
        title = "\uD83D\uDD25 You’ve Unlocked High Payouts!", //🔥 You’ve Unlocked High Payouts!,
        notificationText = "Try our special offers now — they’re your ticket to cashing out big. Don’t let this chance slip away!",
        label = label,
      )
    )
  }

  @Suppress("SameParameterValue")
  private fun customNotification(
    id: NotificationChannelId,
    title: String,
    shortDescription: String? = null,
    iconFilename: String,
    size: CustomNotificationSize,
    backgroundColor: String,
    textColor: String,
    onClickAction: OnClickActionApiDto? = null,
    backgroundActions: List<BackgroundAction>? = null,
    label: String,
    collapseKey: CustomNotificationCollapseKey? = null,
    vibrationEnabled: Boolean? = null,
    soundEnabled: Boolean? = null,
    countdownTimerTarget: String? = null,
  ) = CustomNotificationDto(
    notificationId = randomGenerator.nextUUID(),
    id = id,
    notificationType = CustomNotificationType.CUSTOM_NOTIFICATION,
    collapseKey = collapseKey,
    title = title,
    shortDescription = shortDescription,
    icon = imageService.toUrl(iconFilename),
    size = size,
    backgroundColor = backgroundColor,
    textColor = textColor,
    onClickAction = onClickAction,
    backgroundActions = backgroundActions,
    label = label,
    vibrationEnabled = vibrationEnabled,
    soundEnabled = soundEnabled,
    countdownTimerTarget = countdownTimerTarget,
  )
}
