package com.moregames.playtime.app

import com.google.inject.Inject
import com.moregames.base.app.BuildVariant
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.ktor.cors
import com.moregames.base.ktor.cronInterceptor
import com.moregames.base.ktor.defaultApiStatusPage
import com.moregames.base.messaging.auth.HttpClientAuthInterceptor
import com.moregames.base.secret.SecretService
import com.moregames.base.util.LogEnhancer.Companion.USER_ID
import com.moregames.base.util.basicAuth
import com.moregames.base.util.cronLogger
import com.moregames.base.util.installLogging
import com.moregames.base.util.logger
import com.moregames.playtime.checks.ChecksController
import com.moregames.playtime.clientapp.ClientAppController
import com.moregames.playtime.health.HealthController
import com.moregames.playtime.ios.IosController
import com.moregames.playtime.location.UserLocationController
import com.moregames.playtime.revenue.adjoe.AdjoeReportingController
import com.moregames.playtime.revenue.bitlabs.BitlabsReportingController
import com.moregames.playtime.revenue.fyber.FyberReportingController
import com.moregames.playtime.revenue.tapjoy.TapjoyReportingController
import com.moregames.playtime.service.ExtGamesServiceController
import com.moregames.playtime.service.GamesListServiceController
import com.moregames.playtime.service.admin.AdminServiceController
import com.moregames.playtime.translations.TranslationController
import com.moregames.playtime.user.*
import com.moregames.playtime.user.gamerank.UserGameRankService
import com.moregames.playtime.web.WebController
import com.moregames.playtime.webhook.WebHooksRegistry
import io.ktor.application.*
import io.ktor.auth.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.request.*
import io.ktor.response.*
import io.ktor.routing.*
import io.ktor.serialization.*
import kotlinx.serialization.json.Json
import org.slf4j.Logger
import javax.inject.Singleton

private const val PLAYTIME_AUTH_CONFIG_NAME = "playtime-api"
private const val PLAYTIME_WEB_AUTH_CONFIG_NAME = "playtime-web-api"
private const val PLAYTIME_BASIC_AUTH_REALM = "playtime server"
private const val PLAYTIME_BASIC_AUTH_USER = "playtime"

@Singleton
class ApiManager @Inject constructor(
  private val userController: UserController,
  private val iosController: IosController,
  private val webController: WebController,
  private val cronController: CronController,
  private val fyberReportingController: FyberReportingController,
  private val tapjoyReportingController: TapjoyReportingController,
  private val adjoeReportingController: AdjoeReportingController,
  private val bitlabsReportingController: BitlabsReportingController,
  private val checksController: ChecksController,
  private val systemController: SystemController,
  private val taskController: TaskController,
  private val webHooksRegistry: WebHooksRegistry,
  private val healthController: HealthController,
  private val buildVariant: BuildVariant,
  private val secretService: SecretService,
  private val clientAppController: ClientAppController,
  private val serviceGamesListController: GamesListServiceController,
  private val serviceExperimentController: AdminServiceController,
  private val serviceTranslationsController: TranslationController,
  private val extController: ExtController,
  private val interController: InterController,
  private val userLocationController: UserLocationController,
  private val androidGamesController: AndroidGamesController,
  private val application: Application,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val json: Json,
  private val httpClientAuthInterceptor: HttpClientAuthInterceptor,
  private val headersCheckController: HeadersCheckController,
  private val extGamesServiceController: ExtGamesServiceController,
  private val userGameRankService: UserGameRankService,
) {
  private val userIdContextRegexp = Regex("/playtime/(ios/)?users/(.{36})(?:/.*)?")

  suspend fun initApi() {
    application.install(XForwardedHeaderSupport) // MUST BE BEFORE CORS!!!
    application.install(CORS, cors(buildVariant))
    application.install(ContentNegotiation) {
      json(
        json = json
      )
    }
    application.installLogging(buildVariant, featureFlagsFacade) {
      mdc(USER_ID) { call ->
        // TODO: user context should be recognized by authentication: https://app.asana.com/0/****************/****************/f
        val groups = userIdContextRegexp.find(call.request.path())?.groupValues
        groups?.getOrNull(groups.size - 1)
      }
    }
    initAuthentication(application)
    initRouting(application)
  }

  private fun initRouting(application: Application) {
    application.routing {
      initPlaytimeRoute()
      initCronRoute()
      initOfferwallCallbacks()
      initWebHooks()
      initGamesExaminationRoute()
      initWarmup()
      initHealthRoute()
      initServiceRoute()
      taskController.initRouting(this)
      extController.startRouting(this)
      interController.startRouting(this, statusPageConfig())
    }
  }

  private fun Routing.initHealthRoute() {
    route("/health") {
      install(StatusPages, statusPageConfig())

      healthController.startRouting(this)
    }
  }

  private fun Routing.initWarmup() {
    get("/_ah/warmup") {
      call.respond(HttpStatusCode.OK)
    }
  }

  private fun Routing.initOfferwallCallbacks() {
    route("/revenues") {
      install(StatusPages, statusPageConfig())

      route("/fyber") {
        fyberReportingController.startRouting(this)
      }
      route("/tapjoy") {
        tapjoyReportingController.startRouting(this)
      }
      route("/adjoe") {
        adjoeReportingController.startRouting(this)
      }
      route("/bitlabs") {
        bitlabsReportingController.startRouting(this)
      }
    }
  }

  private fun Routing.initCronRoute() {
    route("/cron/") {
      install(StatusPages, statusPageConfig(cronLogger()))
      cronInterceptor(buildVariant)
      cronController.startRouting(this)
    }
  }

  private fun Routing.initPlaytimeRoute() {
    route("/playtime/") {
      install(StatusPages, statusPageConfig())
      authenticate(PLAYTIME_AUTH_CONFIG_NAME) {
        checksController.startRouting(this)
        clientAppController.startRouting(this)
        userController.startRouting(this)
        iosController.startRouting(this)
        systemController.startRouting(this)
        serviceTranslationsController.startRouting(this)
        userLocationController.startRouting(this)
        headersCheckController.startRouting(this)
      }
      authenticate(PLAYTIME_WEB_AUTH_CONFIG_NAME) {
        webController.startRouting(this)
      }
    }
  }

  private suspend fun initAuthentication(application: Application) {
    val playtimePassword = secretService.secretValue(PlaytimeSecrets.BASIC_AUTH_PLAYTIME)
    application.authentication {
      basicAuth(PLAYTIME_AUTH_CONFIG_NAME, PLAYTIME_BASIC_AUTH_REALM, PLAYTIME_BASIC_AUTH_USER, playtimePassword)
    }
    application.authentication {
      basicAuth(
        PLAYTIME_WEB_AUTH_CONFIG_NAME,
        PLAYTIME_BASIC_AUTH_REALM,
        PLAYTIME_BASIC_AUTH_USER,
        ",887(Zq(N.!C"
      ) // TODO: move password to secrets: https://app.asana.***************************************************/****************
    }
  }

  private fun Routing.initWebHooks() {
    route("/webhooks/") {
      webHooksRegistry.init(this)
    }
  }

  private fun Routing.initGamesExaminationRoute() {
    route("/games/android/") {
      androidGamesController.startTaskRouting(this)
    }
  }

  private fun Routing.initServiceRoute() {
    route("/service/") {
      with(httpClientAuthInterceptor) { installInterceptor() }
      serviceGamesListController.startRouting(this)
      serviceExperimentController.startRouting(this)
      extGamesServiceController.startRouting(this)
    }
  }

  private fun statusPageConfig(logger: Logger = logger()): StatusPages.Configuration.() -> Unit = {
    defaultApiStatusPage(buildVariant, logger)
  }
}
