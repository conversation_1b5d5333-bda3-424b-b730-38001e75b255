package com.moregames.playtime.service

import com.google.inject.Singleton
import com.google.protobuf.Int32Value
import com.google.protobuf.Value
import com.google.protobuf.struct
import com.justplayapps.games.status.GameStatus.FirstLaunchRequest
import com.justplayapps.games.status.GameStatus.GameStatusRequest
import com.justplayapps.games.status.challengeProgressTracking
import com.justplayapps.games.status.gameStatusResponse
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.variations.CommonGameVariation
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.GenericPayloadApiDto
import com.moregames.base.messaging.dto.WebAppVerificationGpsLocationCheckEventDto
import com.moregames.base.util.encodeToBase64
import com.moregames.base.util.fromBase64
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.games.examination.GameExaminationService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.fraudscore.HighlyTrustedUsersService
import com.moregames.playtime.user.usergame.UserGameService
import com.moregames.playtime.web.WebUserService
import io.ktor.application.*
import io.ktor.http.HttpStatusCode.Companion.NotFound
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.request.*
import io.ktor.response.*
import io.ktor.routing.*
import javax.inject.Inject

@Singleton
class ExtGamesServiceController @Inject constructor(
  private val gameExaminationService: GameExaminationService,
  private val gamesService: GamesService,
  private val userGameService: UserGameService,
  private val abTestingService: AbTestingService,
  private val userService: UserService,
  private val challengeService: ChallengeService,
  private val webUserService: WebUserService,
  private val highlyTrustedUsersService: HighlyTrustedUsersService,
) {

  fun startRouting(serviceRoute: Route) = with(serviceRoute) {
    route("/ext/games") {
      android()
      ios()
      webclient()
    }
  }

  private fun Route.android() {
    route("/android/{userId}") {
      post("/status") {
        val userId = call.parameters["userId"]!!
        val message = call.receive<GenericPayloadApiDto>().payload.fromBase64(GameStatusRequest.parser())

        val gameId = gamesService.getGameId(message.packageId, AppPlatform.ANDROID)
        if (gameId == null) {
          call.respond(NotFound)
          return@post
        }

        val exps = abTestingService.getAssignedGameVariations(userId, gameId)

        val variables = exps.map { it.value }.filterIsInstance<CommonGameVariation>()
          .associate { it.variableName to Value.newBuilder().setStringValue(it.variableValue).build() }

        val response = gameStatusResponse {
          this.userId = userId
          this.adVariant = userGameService.getGamesAdVariantValue(userId, gameId).orEmpty()
          this.lft = userService.getDay0RevenueForUser(userId)?.toPlainString().orEmpty()
          this.examinationRequired = gameExaminationService.examinationRequired(userId, gameId)
          this.inGameBalanceNotificationsEnabled = abTestingService.isGameBalanceUpdateNotificationParticipant(userId, AppPlatform.ANDROID, activate = false)
          this.progressTracking = challengeProgressTracking {
            challengeService.getChallengeProgressTracking(userId, gameId)?.also {
              this.limit = Int32Value.of(it.limit)
            }
          }
          this.isHt = highlyTrustedUsersService.isHighlyTrustedUser(userId)
          this.gameVariables = struct {
            fields.putAll(variables)
          }
        }

        call.respond(GenericPayloadApiDto(response.encodeToBase64()))
      }
      post("/first-launch") {
        val userId = call.parameters["userId"]!!
        val message = call.receive<GenericPayloadApiDto>().payload.fromBase64(FirstLaunchRequest.parser())

        val gameId = gamesService.getGameId(message.packageId, AppPlatform.ANDROID)
        if (gameId == null) {
          call.respond(NotFound)
          return@post
        }

        abTestingService.assignActiveGameExperiments(userId, gameId, message.gameVersion)
        call.respond(OK)
      }
    }
  }

  private fun Route.ios() {
    route("/ios/{userId}") {
      post("/status") {
        val userId = call.parameters["userId"]!!
        val message = call.receive<GenericPayloadApiDto>().payload.fromBase64(GameStatusRequest.parser())

        val gameId = gamesService.getGameId(message.packageId, AppPlatform.IOS)
        if (gameId == null) {
          call.respond(NotFound)
          return@post
        }

        val exps = abTestingService.getAssignedGameVariations(userId, gameId)

        val variables = exps.map { it.value }.filterIsInstance<CommonGameVariation>()
          .associate { it.variableName to Value.newBuilder().setStringValue(it.variableValue).build() }

        val response = gameStatusResponse {
          this.userId = userId
          this.adVariant = userGameService.getGamesAdVariantValue(userId, gameId).orEmpty()
          this.lft = userService.getDay0RevenueForUser(userId)?.toPlainString().orEmpty()
          this.examinationRequired = gameExaminationService.examinationRequired(userId, gameId)
          this.inGameBalanceNotificationsEnabled = abTestingService.isGameBalanceUpdateNotificationParticipant(userId, AppPlatform.IOS, activate = false)
          this.progressTracking = challengeProgressTracking {
            challengeService.getChallengeProgressTracking(userId, gameId)?.also {
              this.limit = Int32Value.of(it.limit)
            }
          }
          this.isHt = highlyTrustedUsersService.isHighlyTrustedUser(userId)
          this.gameVariables = struct {
            fields.putAll(variables)
          }
        }

        call.respond(GenericPayloadApiDto(response.encodeToBase64()))
      }
      post("/first-launch") {
        val userId = call.parameters["userId"]!!
        val message = call.receive<GenericPayloadApiDto>().payload.fromBase64(FirstLaunchRequest.parser())

        val gameId = gamesService.getGameId(message.packageId, AppPlatform.IOS)
        if (gameId == null) {
          call.respond(NotFound)
          return@post
        }

        abTestingService.assignActiveGameExperiments(userId, gameId, message.gameVersion)
        call.respond(OK)
      }
    }
  }

  private fun Route.webclient() {
    route("/webclient/{userId}") {
      get("/status") {
        val userId = call.parameters["userId"]!!
        call.respond(webUserService.getUserStatus(userId))
      }
      post("/gps-location") {
        val userId = call.parameters["userId"]!!
        val message = call.receive<WebAppVerificationGpsLocationCheckEventDto>()
        webUserService.gpsLocationCheck(userId, message)
        call.respond(OK)
      }
    }
  }
}