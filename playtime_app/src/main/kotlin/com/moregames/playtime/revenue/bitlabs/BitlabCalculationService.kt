package com.moregames.playtime.revenue.bitlabs

import com.moregames.base.util.logger
import com.moregames.playtime.earnings.CashoutSettingsService
import com.moregames.playtime.revenue.bitlabs.BitlabCalculationService.BitlabCalculationResult.*
import com.moregames.playtime.revenue.bitlabs.BitlabReport.Receipt.ReceiptState
import com.moregames.playtime.revenue.bitlabs.BitlabReport.Survey.SurveyState
import java.math.BigDecimal
import java.math.RoundingMode
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class BitlabCalculationService @Inject constructor(
  private val cashoutSettingsService: CashoutSettingsService,
) {
  suspend fun calculateUsdAndCoins(report: BitlabReport): BitlabCalculationResult {
    return when (report) {
      is BitlabReport.Receipt -> calculateReceipt(report)
      is BitlabReport.Survey -> calculateSurvey(report)
    }
  }

  private suspend fun calculateSurvey(report: BitlabReport.Survey): BitlabCalculationResult {
    return when (report.state) {
      SurveyState.COMPLETE, SurveyState.START_BONUS -> doCalculation(report)
      SurveyState.SCREENOUT -> if (report.rewardPaid == BigDecimal.ZERO) NoRevenue else doCalculation(report)
      SurveyState.RECONCILIATION -> RevenueReconciled(report.rewardPaid)
    }
  }

  private suspend fun calculateReceipt(report: BitlabReport.Receipt): BitlabCalculationResult {
    return when (report.state) {
      ReceiptState.COMPLETED -> doCalculation(report)
      ReceiptState.RECONCILED -> RevenueReconciled(report.rewardPaid)
      ReceiptState.PENDING, ReceiptState.REVIEWING -> NoRevenue
    }
  }

  private suspend fun doCalculation(report: BitlabReport): RevenueReceived {
    val offerwallCoinsToUsdConversionRatio = cashoutSettingsService.getOfferwallCoinsToUsdConversionRatio()
    val usdAmountBasedOnBitlabsCoins =
      report.rewardValue.toBigDecimal()
        .divide(offerwallCoinsToUsdConversionRatio, 6, RoundingMode.HALF_UP)

    return if (report.rewardPaid > BigDecimal.ZERO &&
      (usdAmountBasedOnBitlabsCoins.divide(report.rewardPaid, 2, RoundingMode.HALF_UP) > BigDecimal(1.1) ||
        usdAmountBasedOnBitlabsCoins.divide(report.rewardPaid, 2, RoundingMode.HALF_UP) < BigDecimal(0.9))
    ) {
      logger().error("Bitlabs webhook issue: $report , calculated amount: $usdAmountBasedOnBitlabsCoins .")
      val estimatedBitlabsCoinsByPayout = report.rewardPaid
        .multiply(offerwallCoinsToUsdConversionRatio)
        .toInt()
      RevenueReceived(
        usdAmount = report.rewardPaid,
        coins = estimatedBitlabsCoinsByPayout
      )
    } else {
      RevenueReceived(
        usdAmount = usdAmountBasedOnBitlabsCoins,
        coins = report.rewardValue
      )
    }
  }

  sealed interface BitlabCalculationResult {
    data class RevenueReceived(val usdAmount: BigDecimal, val coins: Int) : BitlabCalculationResult
    data class RevenueReconciled(val usdAmount: BigDecimal) : BitlabCalculationResult
    data object NoRevenue : BitlabCalculationResult
  }
}