package com.moregames.playtime.user.offer

import com.google.inject.Inject
import com.moregames.base.util.TimeService
import com.moregames.playtime.games.AndroidGameService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserPersistenceService.GamePlayStatusDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.offer.lock.AndroidLockedGamesService
import com.moregames.playtime.user.offer.lock.AndroidLockedGamesService.GamesAvailableAndLocked
import com.moregames.playtime.user.pregamescreen.AndroidPreGameScreenService
import com.moregames.playtime.user.promotion.event.manager.PromotionEventService
import java.time.temporal.ChronoUnit
import java.util.*
import javax.inject.<PERSON>ton

@Singleton
class OffersOrderService @Inject constructor(
  private val userPersistenceService: UserPersistenceService,
  private val offerPersistenceService: OfferPersistenceService,
  private val timeService: TimeService,
  private val translationService: UserTranslationService,
  private val androidGameService: AndroidGameService,
  private val androidLockedGamesService: AndroidLockedGamesService,
  private val userService: UserService,
  private val marketService: MarketService,
  private val androidGameOrderExperimentService: AndroidGameOrderExperimentService,
  private val showVideoPreviewExperimentService: ShowVideoPreviewExperimentService,
  private val androidPreGameScreenService: AndroidPreGameScreenService,
  private val promotionConfigService: PromotionEventService,
) {
  companion object {
    const val LOW_COINS_THRESHOLD = 150
    const val POINTS_FOR_GAME_WITH_COINS = 110
    const val POINTS_FOR_GAME_WITH_RECENT_COINS = 120
    const val POINTS_FOR_GAME_WITH_LOW_COINS = 330
  }

  suspend fun loadGamesOrdered(
    userId: String,
    userGameCoins: Map<Int, GamePlayStatusDto>,
    locale: Locale,
    loadLatGamesOnly: Boolean
  ): GamesAvailableAndLocked {
    val user = userService.getUser(userId)

    var games = androidGameService.loadGameOffers(user, locale, loadLatGamesOnly)
      .map {
        val coinsInfo = userGameCoins[it.id]
        if (coinsInfo?.lastPlayedAt != null && coinsInfo.coins > 0)
          it.copy(lastPlayedAt = coinsInfo.lastPlayedAt)
        else
          it
      }
      .let { sortGameOffers(userId, it, userGameCoins) }

    val gamesAvailableAndLocked = androidLockedGamesService.getAvailableAndLockedGames(games, userGameCoins)
    games = gamesAvailableAndLocked.availableGames

    games = showVideoPreviewExperimentService.applyExperiment(userId, games)
    games = androidPreGameScreenService.applyExperiment(userId, games)
    return gamesAvailableAndLocked.copy(availableGames = games)
  }

  suspend fun loadRecentUnlockedGame(userId: String, preloadedGameCoins: Map<Int, GamePlayStatusDto>? = null): AndroidGameOffer? {
    val userGameCoins = preloadedGameCoins ?: userService.loadUserGameCoins(userId)
    val limitedTrackingInfo = userPersistenceService.getLimitedTrackingInfo(userId)
    val locale = userService.getUser(userId).locale
    val (_, _, recentUnlockedGame) = loadGamesOrdered(
      userId,
      userGameCoins,
      locale,
      loadLatGamesOnly = limitedTrackingInfo.isLimited(marketService)
    )
    return recentUnlockedGame
  }

  suspend fun loadAdditionalOffersOrdered(userId: String, locale: Locale): List<AdditionalOffer> =
    offerPersistenceService.loadAdditionalOffersForUser(userId)
      .map { offer -> offer.translate(locale, userId) }
      .sortedBy { it.orderKey }

  private suspend fun AdditionalOffer.translate(locale: Locale, userId: String): AdditionalOffer =
    this.copy(
      title = this.title?.let { translationService.tryTranslate(it, locale, userId) },
      subtext = this.subtext?.let { translationService.tryTranslate(it, locale, userId) },
      bodyText = this.bodyText?.let { translationService.tryTranslate(it, locale, userId) },
      rewardText = this.rewardText?.let { translationService.tryTranslate(it, locale, userId) },
    )

  private suspend fun sortGameOffers(userId: String, games: List<AndroidGameOffer>, userGameCoins: Map<Int, GamePlayStatusDto>): List<AndroidGameOffer> {
    return applySortingByCoins(userId, games, userGameCoins)
      .let { androidGameOrderExperimentService.applyOrderExperiment(userId, it, userGameCoins) }
      .let { promotionConfigService.applyPromotionOrder(userId, it) }
  }


  private suspend fun applySortingByCoins(userId: String, games: List<AndroidGameOffer>, userGameCoins: Map<Int, GamePlayStatusDto>): List<AndroidGameOffer> {
    if (userPersistenceService.loadUserCreationDate(userId).isAfter(timeService.now().minus(1, ChronoUnit.HOURS))) {
      return games
    }

    return games.sortedBy { game ->
      val coins = userGameCoins[game.id]?.coins ?: 0
      game.orderKey * 100 +
        (if (coins > 0) POINTS_FOR_GAME_WITH_COINS else 0) +
        (if (userGameCoins[game.id]?.playedRecently == true) POINTS_FOR_GAME_WITH_RECENT_COINS else 0) +
        (if (coins in 1 until LOW_COINS_THRESHOLD) POINTS_FOR_GAME_WITH_LOW_COINS else 0)
    }
  }
}
