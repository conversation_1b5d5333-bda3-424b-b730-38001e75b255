package com.moregames.playtime.user.offer.lock

import com.google.inject.Inject
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.offer.AndroidGameOffer
import java.util.*
import javax.inject.Singleton

@Singleton
class AndroidLockedGamesService @Inject constructor(
  private val androidGameUnlockReminderService: AndroidGameUnlockReminderService,
  private val translationService: UserTranslationService,
) {
  val gameCutConfig: GamesCutConfig
    get() = GamesCutConfig(alwaysDisplayedGamesCount = 7, unlockingGamesMultiplier = 2)

  val unlockedAndLockedGamesInfoConfig: UnlockedAndLockedGamesWidgetsConfig
    get() = UnlockedAndLockedGamesWidgetsConfig(
      lockedGamesIconsCount = 5,
      lockedGamesWidgetId = 210100,
      unlockedGameWidgetId = 210099,
      unlockedGameWidgetIcon = "welcome_bonus_offer.png"
    )

  fun getAvailableAndLockedGames(games: List<AndroidGameOffer>, userGameCoins: Map<Int, UserPersistenceService.GamePlayStatusDto>): GamesAvailableAndLocked {
    val gamesToShow = getUnlockedGames(games, userGameCoins)
    val yetLockedGames = games - gamesToShow.toSet()
    val recentUnlockedGame = if (userGameCoins.isNotEmpty()) gamesToShow.getOrNull(gamesToShow.size - (gameCutConfig.unlockingGamesMultiplier + 1)) else null

    return GamesAvailableAndLocked(
      availableGames = gamesToShow,
      yetLockedGames = yetLockedGames,
      recentUnlockedGame = recentUnlockedGame,
    )
  }

  suspend fun formatUnlockedAndLockedGamesWidgets(
    userId: String,
    locale: Locale,
    recentUnlockedGame: AndroidGameOffer?,
    yetLockedGames: List<AndroidGameOffer>
  ): UnlockedAndLockedGamesWidgets {
    val unlockedGameInfo =
      if (recentUnlockedGame != null && androidGameUnlockReminderService.getReminderDismissedForGame(userId) != recentUnlockedGame.id)
        AndroidUnlockedGameInfo(
          unlockedGame = recentUnlockedGame,
          text = translationService.translateOrDefault(TranslationResource.GAME_UNLOCK_REMINDER_TEXT, locale, userId),
          orderKey = 0,
        ) else null
    val lockedGamesInfo = if (yetLockedGames.isNotEmpty())
      AndroidLockedGamesInfo(
        lockedCount = yetLockedGames.size,
        gameIcons = yetLockedGames.take(unlockedAndLockedGamesInfoConfig.lockedGamesIconsCount).map { it.iconFilename },
        orderKey = 0,
      ) else null
    return UnlockedAndLockedGamesWidgets(
      unlockedGameInfo = unlockedGameInfo,
      lockedGamesInfo = lockedGamesInfo,
    )
  }

  private fun getUnlockedGames(games: List<AndroidGameOffer>, userGameCoins: Map<Int, UserPersistenceService.GamePlayStatusDto>): List<AndroidGameOffer> {
    var additionalGamesCount = gameCutConfig.alwaysDisplayedGamesCount + userGameCoins.size * gameCutConfig.unlockingGamesMultiplier
    // Take games with coins + additionalGamesCount other games
    return games.mapNotNull { game ->
      when {
        (userGameCoins[game.id]?.coins ?: 0) > 0 -> game // take all games with coins
        additionalGamesCount > 0 -> {
          additionalGamesCount--; game
        } // take additionalGamesCount games without coins according to base order
        else -> null
      }
    }
  }

  data class GamesAvailableAndLocked(
    val availableGames: List<AndroidGameOffer>,
    val yetLockedGames: List<AndroidGameOffer>,
    val recentUnlockedGame: AndroidGameOffer?,
  )

  data class UnlockedAndLockedGamesWidgets(
    val unlockedGameInfo: AndroidUnlockedGameInfo?,
    val lockedGamesInfo: AndroidLockedGamesInfo?,
  )

  inner class GamesCutConfig(
    val alwaysDisplayedGamesCount: Int,
    val unlockingGamesMultiplier: Int,
  )

  inner class UnlockedAndLockedGamesWidgetsConfig(
    val lockedGamesIconsCount: Int,
    val lockedGamesWidgetId: Int,
    val unlockedGameWidgetId: Int,
    val unlockedGameWidgetIcon: String,
  )
}