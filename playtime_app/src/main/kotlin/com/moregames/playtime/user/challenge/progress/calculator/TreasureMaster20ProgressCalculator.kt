package com.moregames.playtime.user.challenge.progress.calculator

import com.google.inject.Singleton
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.logger
import com.moregames.playtime.user.challenge.dto.ChallengeProgress
import com.moregames.playtime.user.challenge.dto.UserChallenge
import com.moregames.playtime.user.challenge.progress.ChallengeGameProgressCalculator
import kotlin.math.min

/**
 * This calculator works correctly only within 20 levels of TM.
 */
@Singleton
class TreasureMaster20ProgressCalculator : ChallengeGameProgressCalculator {
  override fun calculateProgress(
    userChallengeProgressDto: UserChallengeProgressDto,
    userChallenge: UserChallenge
  ): ChallengeProgress {
    logger().debug("Calculating progress {}", userChallengeProgressDto)
    val progress = when (userChallengeProgressDto) {
      is UserChallengeProgressDto.TmProgressDto -> calculateLegacyTmProgress(userChallengeProgressDto) ?: userChallenge.progress
      is UserChallengeProgressDto.LevelIdProgressDto -> calculateCorrectTmProgress(userChallengeProgressDto)
      else -> {
        logger().warn("Incoming progressDto of incorrect type")
        return ChallengeProgress(userChallenge.progress, userChallenge.achievement)
      }
    }
    val resultProgress = min(progress, userChallenge.challenge.progressMax)
    return ChallengeProgress(resultProgress, userChallenge.achievement)
  }

  private fun calculateLegacyTmProgress(incomingProgress: UserChallengeProgressDto.TmProgressDto): Int? = incomingProgress.level

  private fun calculateCorrectTmProgress(userChallengeProgressDto: UserChallengeProgressDto.LevelIdProgressDto): Int = userChallengeProgressDto.levelId.toInt()
}