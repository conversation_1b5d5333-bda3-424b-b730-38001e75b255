package com.moregames.playtime.user.challenge.progress

import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import com.moregames.playtime.user.challenge.dto.ChallengeProgress
import com.moregames.playtime.user.challenge.dto.UserChallenge
import com.moregames.playtime.user.challenge.progress.achievement.AchievementDto
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlin.math.min

abstract class AbstractUniqueLevelProgressCalculator(
  private val json: <PERSON>son,
) : ChallengeGameProgressCalculator {

  companion object {
    const val MAX_LEVELS = 500
  }


  override fun calculateProgress(
    userChallengeProgressDto: UserChallengeProgressDto,
    userChallenge: UserChallenge
  ): ChallengeProgress {
    logger().debug("Calculating progress {}", userChallengeProgressDto)
    if (userChallenge.progress == userChallenge.challenge.progressMax) {
      return ChallengeProgress(userChallenge.progress, userChallenge.achievement)
    }
    val previousAchievement = previousAchievement(userChallenge.achievement)
    val achievementAddition = nextAchievement(userChallengeProgressDto)
    val nextAchievement = AchievementDto(previousAchievement.levels union achievementAddition.levels)
    if (nextAchievement.levels.size > MAX_LEVELS) {
      logger().alert("Numbers of level is too big $userChallengeProgressDto. Max level is $MAX_LEVELS")
      return ChallengeProgress(userChallenge.challenge.progressMax, previousAchievement.asString())
    }
    val calculatedProgression = calculateProgress(nextAchievement)
    val actualProgress = min(calculatedProgression, userChallenge.challenge.progressMax)
    return ChallengeProgress(actualProgress, nextAchievement.asString())
  }

  abstract fun nextAchievement(userChallengeProgressDto: UserChallengeProgressDto): AchievementDto

  open fun previousAchievement(achievementAsString: String?): AchievementDto {
    if (achievementAsString.isNullOrBlank()) {
      return AchievementDto.empty()
    }
    return try {
      json.decodeFromString(achievementAsString)
    } catch (e: Exception) {
      logger().error("Error parsing Achievement string", e)
      return AchievementDto.empty()
    }
  }

  open fun AchievementDto.asString(): String? {
    return json.encodeToString(this)
  }

  open fun calculateProgress(achievement: AchievementDto): Int = achievement.levels.size
}