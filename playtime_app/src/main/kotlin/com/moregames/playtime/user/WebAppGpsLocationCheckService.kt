package com.moregames.playtime.user

import com.google.inject.Inject
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.AppPlatform.IOS_WEB
import com.moregames.playtime.games.GamesService
import javax.inject.Singleton

@Singleton
class WebAppGpsLocationCheckService @Inject constructor(
  private val userPersistenceService: UserPersistenceService,
  private val gamesService: GamesService,
  private val userService: UserService,
) {

  suspend fun getApplicationId(userId: String): String? {
    val playedGames = userService.loadUserGameCoins(userId).values
      .sortedByDescending { it.lastPlayedAt }
    val askedGames = userPersistenceService.getGpsLocationCheckAskedGames(userId)

    val game = playedGames
      .firstOrNull { it.gameId !in askedGames }
      ?: playedGames.firstOrNull()

    return game?.let { game ->
      val user = userService.getUser(userId, includingDeleted = true)
      gamesService.getGameById(
        appPlatform = user.appPlatform.takeIf { it != IOS_WEB } ?: IOS,
        gameId = game.gameId
      )?.applicationId
    }
  }
}