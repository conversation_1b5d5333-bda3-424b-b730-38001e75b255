package com.moregames.playtime.user.challenge

import com.google.inject.Inject
import com.google.inject.Provider
import com.google.inject.Singleton
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.variations.SpecialOfferAfterChallengeClaimVariation
import com.moregames.base.app.BuildVariant
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.exceptions.BaseException.Companion.DEFAULT_EXTERNAL_MESSAGE
import com.moregames.base.util.TimeService
import com.moregames.base.util.format
import com.moregames.base.util.logger
import com.moregames.playtime.earnings.currency.CurrencyExchangeService
import com.moregames.playtime.earnings.currency.dto.CurrencyExchangeResultDto
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.common.ChallengeSpecialOfferClaimsService
import com.moregames.playtime.user.challenge.dto.ChallengeEventState
import com.moregames.playtime.user.challenge.dto.UserChallengeEvent
import com.moregames.playtime.user.challenge.dto.bq.UserChallengeEventUpdatedBqDto
import com.moregames.playtime.user.challenge.dto.claim.event.ClaimBoxType
import com.moregames.playtime.user.challenge.dto.claim.event.ClaimEventRequestApiDto
import com.moregames.playtime.user.challenge.dto.claim.event.ClaimEventResponseApiDto
import com.moregames.playtime.util.plus
import com.moregames.playtime.util.roundDownToSecondDigit
import java.math.BigDecimal
import java.time.Duration
import java.util.*
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration

@Singleton
class ClaimChallengeEventService @Inject constructor(
  private val challengeRewardingService: ChallengeRewardingService,
  private val marketService: MarketService,
  private val currencyExchangeService: CurrencyExchangeService,
  private val challengeService: ChallengeService,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val timeService: TimeService,
  private val abTestingService: AbTestingService,
  private val cashoutPeriodsService: CashoutPeriodsService,
  private val buildVariantProvider: Provider<BuildVariant>,
  private val challengeSpecialOfferClaimsService: ChallengeSpecialOfferClaimsService,
) {
  suspend fun claimEvent(userId: String, request: ClaimEventRequestApiDto): ClaimEventResponseApiDto {
    val userCurrency = marketService.getUserCurrency(userId)

    val event = challengeService.getUserChallengeEvent(userId, request.challengeEventId)
    if (event == null || !event.state.isFinal()) {
      return BigDecimal.ZERO.toClaimEventResponse(userCurrency, DEFAULT_EXTERNAL_MESSAGE)
    }
    if (event.state == ChallengeEventState.CLAIMED) {
      logger().warn("Duplicate claim of event ${request.challengeEventId} for user $userId")
      return event.earnings.toClaimEventResponse(userCurrency)
    }

    val earnings = challengeRewardingService.calculateChallengeEventRewardUsd(event).toUserCurrency(userCurrency)
    val rewarded = challengeRewardingService.giveChallengeEventReward(event, earnings)
    val claimed = (rewarded && challengeService.claimEvent(request.challengeEventId, userId, earnings.usdAmount))

    if (claimed) {
      sendClaimedChallengeEventToBq(event, earnings)
      val specialOffer = getBoostedModeSpecialOffer(userId)
      return earnings.usdAmount.toClaimEventResponse(userCurrency, specialOffer = specialOffer)
    } else {
      logger().warn("Parallel claiming of event ${request.challengeEventId} for user $userId")
    }

    val updatedEvent = challengeService.getUserChallengeEvent(userId, request.challengeEventId)
    if (updatedEvent == null || updatedEvent.state != ChallengeEventState.CLAIMED) {
      logger().warn("Invalid state of event ${request.challengeEventId} for user $userId")
      return BigDecimal.ZERO.toClaimEventResponse(userCurrency, DEFAULT_EXTERNAL_MESSAGE)
    }

    return updatedEvent.earnings.toClaimEventResponse(userCurrency)
  }

  private suspend fun getBoostedModeSpecialOffer(userId: String): ClaimEventResponseApiDto.SpecialOffer? {
    val variation = abTestingService.assignedVariationValue(userId, ClientExperiment.SPECIAL_OFFER_AFTER_CHALLENGE_CLAIM) as? SpecialOfferAfterChallengeClaimVariation
      ?: return null

    // exp overlap
    val currentCashoutPeriod = cashoutPeriodsService.getCurrentCashoutPeriod(userId)
    if (buildVariantProvider.get() == BuildVariant.PRODUCTION && !currentCashoutPeriod.durationAtLeast3h())
      return null

    // BM 15 mins condition check
    if (Duration.between(timeService.now(), currentCashoutPeriod.periodEnd) < 15.minutes.toJavaDuration())
      return null

    // BM days since last offer check
    val lastClaimedSpecialOfferAt = challengeSpecialOfferClaimsService.findLastClaimedSpecialOfferAt(userId)
    if (lastClaimedSpecialOfferAt != null && (lastClaimedSpecialOfferAt + variation.gap).isAfter(timeService.now()))
      return null

    return ClaimEventResponseApiDto.SpecialOffer(
      title = "Want to Double your next Cash-Out?",
      description = "Claim the offer to earn up to 200% for all games!",
      timeToClaim = 120,
    )
  }

  private suspend fun sendClaimedChallengeEventToBq(
    event: UserChallengeEvent,
    earnings: CurrencyExchangeResultDto
  ) {
    challengeService.getChallengeEventById(event.eventId)?.let {
      bigQueryEventPublisher.publish(
        UserChallengeEventUpdatedBqDto(
          userId = event.userId,
          challengeEventId = event.eventId,
          state = ChallengeEventState.CLAIMED,
          earnings = earnings.usdAmount,
          applovinNonBannerRevenue = event.applovinNonBannerRevenue ?: BigDecimal.ZERO,
          eventStart = it.dateFrom,
          eventEnd = it.dateTo,
          createdAt = timeService.now(),
        )
      )
    }
  }

  private fun CurrencyExchangeResultDto.toAmountString(): String =
    this.amount.roundDownToSecondDigit().format(this.userCurrency)

  private suspend fun BigDecimal.toUserCurrency(currency: Currency): CurrencyExchangeResultDto =
    currencyExchangeService.convert(this, currency)

  private suspend fun BigDecimal.toClaimEventResponse(
    currency: Currency,
    errorMessage: String? = null,
    specialOffer: ClaimEventResponseApiDto.SpecialOffer? = null
  ) = ClaimEventResponseApiDto(
    amountString = toUserCurrency(currency).toAmountString(),
    boxType = ClaimBoxType.forEarnings(this),
    specialOffer = specialOffer,
    errorMessage = errorMessage
  )
}