package com.moregames.playtime.user.cashout.offers

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.variations.SpecialCashoutOffersVariation
import com.moregames.base.abtesting.variations.SpecialCashoutOffersVariation.*
import com.moregames.base.bus.MessageBus
import com.moregames.base.db.Transactor
import com.moregames.base.dto.AppPlatform
import com.moregames.base.exceptions.BaseException
import com.moregames.base.exceptions.ErrorCode
import com.moregames.base.exceptions.ErrorType
import com.moregames.base.exceptions.PlaytimeErrorCodes
import com.moregames.base.util.TimeService
import com.moregames.playtime.buseffects.CashoutOfferActivatedEvent
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.offers.CashoutOffersDao.CashoutOfferSetEntity.CashoutOfferEntity
import com.moregames.playtime.util.plus
import java.time.Duration
import java.time.Instant
import javax.inject.Inject
import javax.inject.Provider
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract
import kotlin.time.Duration.Companion.hours

class CashoutOffersService @Inject constructor(
  private val cashoutOffersDao: CashoutOffersDao,
  private val userService: Provider<UserService>,
  private val timeService: TimeService,
  private val transactor: Transactor,
  private val messageBus: MessageBus,
  private val gamesService: GamesService,
  private val abTestingService: AbTestingService,
) {

  suspend fun getOfferSet(userId: String): CashoutOfferSet {
    val lastOffer = cashoutOffersDao.findLastOfferSet(userId) ?: return CashoutOfferSet.NotCreated

    return if (lastOffer.closedAt != null) {
      CashoutOfferSet.Cooldown(lastOffer.closedAt)
    } else {
      CashoutOfferSet.Active(lastOffer.id, lastOffer.userId, lastOffer.offers.toDomain(timeService.now()))
    }
  }

  suspend fun createSetIfNeeded(userId: String) {
    when (val set = getOfferSet(userId)) {
      is CashoutOfferSet.Active -> return
      is CashoutOfferSet.Cooldown -> {
        if (Duration.between(set.closedAt, timeService.now()).toHours() >= 24) {
          createNewSet(userId)
        }
      }

      CashoutOfferSet.NotCreated -> createNewSet(userId)
    }
  }

  suspend fun closeSetIfNeeded(userId: String) {
    when (val set = getOfferSet(userId)) {
      is CashoutOfferSet.Active -> cashoutOffersDao.closeSet(set.setId)
      is CashoutOfferSet.Cooldown, CashoutOfferSet.NotCreated -> {} // do nothing here
    }
  }

  private suspend fun createNewSet(userId: String) {
    val variation = abTestingService.assignedVariationValue(userId, ClientExperiment.SPECIAL_CASHOUT_OFFERS) as? SpecialCashoutOffersVariation ?: return

    val gamesInSet = when (variation) {
      ThreeCashoutOffers -> {
        val userLastPlayedGames = userService.get().loadUserGameCoins(userId)
          .asSequence()
          .map { it.value }
          .filter { it.lastPlayedAt != null }
          .sortedByDescending { it.lastPlayedAt }
          .map { it.gameId }
          .take(3)
          .toMutableList()
        if (userLastPlayedGames.size < 3) {
          userLastPlayedGames += fallbackGames.filter { it !in userLastPlayedGames }.take(3 - userLastPlayedGames.size)
        }
        userLastPlayedGames
      }

      ThreeRandomCashoutOffers,
      ThreeRandom1EarningCashoutOffers,
      ThreeRandom025EarningCashoutOffers,
      ThreeRandom05EarningCashoutOffers -> randomGamesSet.shuffled().take(3)
    }

    cashoutOffersDao.createNewSet(userId, gamesInSet)
  }

  suspend fun activateOffer(userId: String, offerId: String): Instant = transactor.inTransaction {
    val set = getOfferSet(userId)
    validateOfferActivation(set, offerId)

    val activeUntilDate = timeService.now() + 1.hours
    cashoutOffersDao.activateOffer(offerId, activeUntilDate)
    val game = set.offers.first { it.id == offerId }.gameId.let { gamesService.getGameById(AppPlatform.ANDROID, it) } ?: return@inTransaction activeUntilDate
    messageBus.publish(CashoutOfferActivatedEvent(userId, game.id, game.name, activeUntilDate))
    activeUntilDate
  }

  @OptIn(ExperimentalContracts::class)
  private fun validateOfferActivation(set: CashoutOfferSet, offerId: String) {
    contract {
      returns() implies (set is CashoutOfferSet.Active)
    }
    when (set) {
      is CashoutOfferSet.Active -> {
        if (set.offers.filterIsInstance<CashoutOffer.Active>().isNotEmpty())
          throw CashoutOfferAlreadyActiveException(set.userId)

        if (offerId !in set.offers.map { it.id })
          throw IllegalArgumentException("Offer $offerId not found in the active set")
      }

      is CashoutOfferSet.Cooldown, CashoutOfferSet.NotCreated -> throw IllegalStateException("No active offer set for user")
    }
  }

  private fun List<CashoutOfferEntity>.toDomain(now: Instant) = map {
    when {
      it.activeUntilDate == null -> CashoutOffer.Unclaimed(it.id, it.gameId)
      it.activeUntilDate > now -> CashoutOffer.Active(it.id, it.gameId, it.activeUntilDate)
      else -> CashoutOffer.Claimed(it.id, it.gameId)
    }
  }

  suspend fun hasActiveOfferForGame(userId: String, gameId: Int): Boolean {
    val set = (getOfferSet(userId) as? CashoutOfferSet.Active) ?: return false

    return set.offers.any { it.gameId == gameId && it is CashoutOffer.Active }
  }

  sealed interface CashoutOfferSet {
    data object NotCreated : CashoutOfferSet
    data class Cooldown(val closedAt: Instant) : CashoutOfferSet
    data class Active(val setId: Int, val userId: String, val offers: List<CashoutOffer>) : CashoutOfferSet
  }

  sealed interface CashoutOffer {
    val id: String
    val gameId: Int

    data class Unclaimed(override val id: String, override val gameId: Int) : CashoutOffer
    data class Active(override val id: String, override val gameId: Int, val activeUntilDate: Instant) : CashoutOffer
    data class Claimed(override val id: String, override val gameId: Int) : CashoutOffer
  }

  companion object {
    val fallbackGames = listOf(
      200039, // TM
      200051, // zentiles
      200044, // solitaire verse
    )
    val randomGamesSet = setOf(
      200039, // TM
      200051, // zentiles
      200044, // solitaire verse
      200068, // tile match pro
      200072, // Tangram Heaven
      200062, // Water Sorter
      200046, // Word Seeker,
      200069, // Space Connect
    )
  }

  class CashoutOfferAlreadyActiveException(userId: String) : BaseException(
    internalMessage = "User $userId already has an active offer",
    externalMessage = "You can only have 1 active offer at a time",
  ) {
    override val errorCode: ErrorCode = PlaytimeErrorCodes.CASHOUT_OFFER_ALREADY_ACTIVE
    override val errorType: ErrorType = ErrorType.INPUT_ERROR
  }
}