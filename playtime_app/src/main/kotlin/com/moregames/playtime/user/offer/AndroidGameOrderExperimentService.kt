package com.moregames.playtime.user.offer

import com.google.inject.Inject
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.Variations
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.logger
import com.moregames.playtime.app.isAndroidBlockBuster3rdEnabled
import com.moregames.playtime.buseffects.ForceReassignVariationMessage
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.UserPersistenceService
import javax.inject.Singleton

@Singleton
class AndroidGameOrderExperimentService @Inject constructor(
  private val abTestingService: AbTestingService,
  private val gamePersistenceService: GamePersistenceService,
  private val gamesService: GamesService,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val messageBus: MessageBus,
) {

  companion object {
    private val fixedFirst11List = listOf(
      ApplicationId.MAD_SMASH_APP_ID,
      ApplicationId.SOLITAIRE_VERSE_APP_ID,
      ApplicationId.SOLITAIRE_CLASSIC_APP_ID,
      ApplicationId.WOODEN_PUZZLE_APP_ID,
      ApplicationId.BUBBLE_POP_APP_ID,
      ApplicationId.TREASURE_MASTER_APP_ID,
      ApplicationId.BALL_BOUNCE_APP_ID,
      ApplicationId.SUGAR_RUSH_APP_ID,
      ApplicationId.MERGE_BLAST_APP_ID,
      ApplicationId.WORD_SEEKER_APP_ID,
      ApplicationId.MIX_BLOX_APP_ID,
      ApplicationId.WATER_SORTER_APP_ID,
    )
  }


  suspend fun applyOrderExperiment(
    userId: String,
    games: List<AndroidGameOffer>,
    userGameCoins: Map<Int, UserPersistenceService.GamePlayStatusDto>
  ): List<AndroidGameOffer> {
    val variation = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_GAMES_ORDER)
    return when (variation) {
      Variations.ANDROID_FIXED_FIRST_11_ORDER -> reorderFixed11Games(games)
      Variations.ANDROID_COINS_FIRST_11_ORDER -> reorderCoins11Games(games, userGameCoins)
      Variations.ANDROID_BLOCK_BUSTER_1ST_ORDER -> reorderForBlockBuster1st(userId, games)
      Variations.ANDROID_BLOCK_INSTEAD_WBP -> replaceWbpWithBb(games)
      Variations.ANDROID_PIN_MASTER_1ST_ORDER -> reorderForPinMaster(games)
      Variations.ANDROID_BLOCK_BUSTER_3RD_ORDER -> reorderForBlockBuster3rd(games)
      Variations.ANDROID_BUBBLE_CHEF_1ST_ORDER -> reorderForBubbleChef(games)
      else -> games
    }
  }

  private suspend fun reorderForBlockBuster1st(userId: String, offers: List<AndroidGameOffer>): List<AndroidGameOffer> {
    if (featureFlagsFacade.isAndroidBlockBuster3rdEnabled()) {
      messageBus.publish(
        ForceReassignVariationMessage(
          userId = userId,
          experiment = ClientExperiment.ANDROID_GAMES_ORDER,
          variation = Variations.ANDROID_BLOCK_BUSTER_3RD_ORDER
        )
      )
      return reorderForBlockBuster3rd(offers)
    }

    val treasureMaster = offers.filter { it.applicationId == ApplicationId.TREASURE_MASTER_APP_ID }
    val blockBuster = offers.filter { it.applicationId == ApplicationId.BLOCKBUSTER_APP_ID }
    val otherOffers = offers.filter {
      !setOf(ApplicationId.TREASURE_MASTER_APP_ID, ApplicationId.BLOCKBUSTER_APP_ID).contains(it.applicationId)
    }
    return blockBuster + otherOffers.take(1) + treasureMaster + otherOffers.drop(1)
  }

  private fun reorderForBlockBuster3rd(offers: List<AndroidGameOffer>): List<AndroidGameOffer> {
    val blockBuster = offers.filter { it.applicationId == ApplicationId.BLOCKBUSTER_APP_ID }
    val otherOffers = offers.filter {
      it.applicationId != ApplicationId.BLOCKBUSTER_APP_ID
    }
    return otherOffers.take(2) + blockBuster + otherOffers.drop(2)
  }

  private fun reorderForPinMaster(offers: List<AndroidGameOffer>): List<AndroidGameOffer> {
    val treasureMaster = offers.filter { it.applicationId == ApplicationId.TREASURE_MASTER_APP_ID }
    val pinMaster = offers.filter { it.applicationId == ApplicationId.PIN_MASTER_APP_ID }
    val solitaire = offers.filter { it.applicationId == ApplicationId.SOLITAIRE_VERSE_APP_ID }
    val otherOffers = offers.filter {
      it.applicationId !in arrayOf(ApplicationId.TREASURE_MASTER_APP_ID, ApplicationId.PIN_MASTER_APP_ID, ApplicationId.SOLITAIRE_VERSE_APP_ID)
    }
    return pinMaster + solitaire + treasureMaster + otherOffers
  }

  private fun replaceWbpWithBb(offers: List<AndroidGameOffer>): List<AndroidGameOffer> {
    val blockBuster = offers.find { it.applicationId == ApplicationId.BLOCKBUSTER_APP_ID } ?: return offers
    val woodenPuzzle = offers.find { it.applicationId == ApplicationId.WOODEN_PUZZLE_APP_ID } ?: return offers
    val head = offers.takeWhile { it.applicationId != ApplicationId.WOODEN_PUZZLE_APP_ID }
      .filter { it.applicationId != ApplicationId.BLOCKBUSTER_APP_ID }
    val tail = offers.dropWhile { it.applicationId != ApplicationId.WOODEN_PUZZLE_APP_ID }.drop(1)
      .filter { it.applicationId != ApplicationId.BLOCKBUSTER_APP_ID }
    return head + blockBuster + tail + woodenPuzzle
  }

  private fun reorderCoins11Games(
    games: List<AndroidGameOffer>,
    userGameCoins: Map<Int, UserPersistenceService.GamePlayStatusDto>
  ): List<AndroidGameOffer> {
    val gameWithCoins = games.filter { (userGameCoins[it.id]?.coins ?: 0) > 0 }
    val fixedOrder = fixedFirst11List.mapIndexed { index, applicationId -> applicationId to index }.toMap()
    val gamesFrom11ListWithoutCoins = games
      .filter { !gameWithCoins.contains(it) }
      .filter { fixedFirst11List.contains(it.applicationId) }
      .sortedBy { fixedOrder[it.applicationId] }
    val tail = games
      .filter { !gameWithCoins.contains(it) }
      .filter { !fixedFirst11List.contains(it.applicationId) }
    return gameWithCoins + gamesFrom11ListWithoutCoins + tail
  }

  private fun reorderFixed11Games(games: List<AndroidGameOffer>): List<AndroidGameOffer> {
    val applicationIdMap = games.associateBy { it.applicationId }
    val head = fixedFirst11List.mapNotNull { applicationIdMap[it] }
    val tail = games.filter { !fixedFirst11List.contains(it.applicationId) }
    return head + tail
  }

  private fun reorderForBubbleChef(offers: List<AndroidGameOffer>): List<AndroidGameOffer> {
    val bubbleChef = offers.filter { it.applicationId == ApplicationId.BUBBLE_CHIEF_APP_ID }
    val treasureMaster = offers.filter { it.applicationId == ApplicationId.TREASURE_MASTER_APP_ID }
    val secondOffer = offers.getOrNull(1)?.takeIf { it.applicationId != ApplicationId.BUBBLE_CHIEF_APP_ID && it.applicationId != ApplicationId.TREASURE_MASTER_APP_ID }
    val otherOffers = offers.filter { it.applicationId != ApplicationId.BUBBLE_CHIEF_APP_ID && it.applicationId != ApplicationId.TREASURE_MASTER_APP_ID && it != secondOffer }
    return bubbleChef + listOfNotNull(secondOffer) + treasureMaster + otherOffers
  }

  suspend fun addGameToTheTop(userId: String, offers: List<AndroidGameOffer>): List<AndroidGameOffer> {
    val variation = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_GAMES_ORDER)

    val applicationId = when (variation) {
      Variations.ANDROID_BUBBLE_CHEF_1ST_ORDER -> ApplicationId.BUBBLE_CHIEF_APP_ID
      Variations.ANDROID_PIN_MASTER_1ST_ORDER -> ApplicationId.PIN_MASTER_APP_ID
      else -> return offers
    }

    if (offers.any { it.applicationId == applicationId }) {
      return offers
    }

    val gameId = gamesService.getGameId(applicationId, AppPlatform.ANDROID)
    if (gameId == null) {
      logger().warn("Not found gameId for $applicationId")
      return offers
    }
    val topOfferList = gamePersistenceService.loadAndroidGamesByIds(setOf(gameId))
    return topOfferList + offers
  }
}