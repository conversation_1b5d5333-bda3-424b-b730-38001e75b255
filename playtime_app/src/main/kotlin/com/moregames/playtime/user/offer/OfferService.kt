package com.moregames.playtime.user.offer

import com.google.inject.Inject
import com.moregames.base.offers.dto.OfferAction
import java.time.Instant

class OfferService @Inject constructor(
  private val offerPersistenceService: OfferPersistenceService
) {
  suspend fun additionalOfferCompleted(userId: String, action: OfferAction, after: Instant): Boolean =
    offerPersistenceService.additionalOfferCompleted(userId, action, after)
}