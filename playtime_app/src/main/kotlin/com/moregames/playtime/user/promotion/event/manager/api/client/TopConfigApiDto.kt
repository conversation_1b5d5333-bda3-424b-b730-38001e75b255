package com.moregames.playtime.user.promotion.event.manager.api.client

import com.moregames.base.messaging.customnotification.OnClickActionApiDto
import kotlinx.serialization.Serializable

@Serializable
data class TopConfigApiDto(
  val backgroundImage: String? = null,
  val imageClickAction: OnClickActionApiDto? = null,
  val foregroundImage: String? = null,
  val gradientTop: String? = null,
  val gradientBottom: String? = null,
  val cashoutButtonColor: String? = null,
  val durationInMillis: Int? = null,
  val order: Int? = null,
)
