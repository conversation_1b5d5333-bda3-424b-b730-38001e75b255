package com.moregames.playtime.user.cashout

import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import com.google.inject.Inject
import com.google.inject.Provider
import com.justplayapps.playtime.proto.cashoutCreatedEvent
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCurrencyEarnings
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment.*
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.abtesting.variations.AndroidCashout2xOfferVariation
import com.moregames.base.abtesting.variations.PaymentProviderSurveyVariation
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.BuildVariant.PRODUCTION
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.app.PaymentProviderType.Companion.payPalAndVenmoProviders
import com.moregames.base.app.PaymentProviderType.Companion.payPalProviders
import com.moregames.base.bus.MessageBus
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.config.CashoutConfig
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.*
import com.moregames.base.dto.TrackingDataType
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.encryption.HashService
import com.moregames.base.ipregistry.IpData
import com.moregames.base.messaging.dto.AdMarketEvent
import com.moregames.base.messaging.dto.CashoutRequestCreatedEventDto
import com.moregames.base.messaging.dto.XHoursPassedSinceNoEarningsCheckDto
import com.moregames.base.table.UserCashoutTransactionsTable
import com.moregames.base.table.UserCashoutTransactionsTable.Status.FAILED
import com.moregames.base.table.UserCashoutTransactionsTable.Status.REQUESTED
import com.moregames.base.user.UserBonusBalanceType
import com.moregames.base.user.UserPersonals
import com.moregames.base.util.*
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.app.ga.GoogleAnalyticsService
import com.moregames.playtime.app.messaging.dto.AdjustCustomEvent
import com.moregames.playtime.app.messaging.dto.MolocoInAppEvent
import com.moregames.playtime.boost.BoostedModeService
import com.moregames.playtime.buseffects.DeletePopupMessageEffect
import com.moregames.playtime.buseffects.SendMolocoInAppEventEffect
import com.moregames.playtime.checks.IpService
import com.moregames.playtime.earnings.CashoutSettingsService
import com.moregames.playtime.earnings.currency.CurrencyExchangeService
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.ios.cashoutcoins.CashoutCoinsService
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.tracking.AdMarketService
import com.moregames.playtime.tracking.AdjustApiClient
import com.moregames.playtime.tracking.AdjustService
import com.moregames.playtime.translations.TranslationResource.IOS_CASHOUT_EMAIL_HINT
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.*
import com.moregames.playtime.user.PopupMessageReason.EARNINGS_THRESHOLD_REACHED
import com.moregames.playtime.user.cashout.CashoutService.TransactionsFilterType.*
import com.moregames.playtime.user.cashout.dto.*
import com.moregames.playtime.user.cashout.exception.NoEarningsToCashoutException
import com.moregames.playtime.user.cashout.offers.CashoutOffersService
import com.moregames.playtime.user.cashout.offers.CashoutOffersService.CashoutOffer
import com.moregames.playtime.user.cashout.offers.CashoutOffersService.CashoutOfferSet.*
import com.moregames.playtime.user.dto.CashoutButtonStyle
import com.moregames.playtime.user.dto.PaymentProviderSurvey
import com.moregames.playtime.user.dto.UserExternalIds
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.highlightedgames.AndroidHighlightedGamesService
import com.moregames.playtime.user.interview.UserInterviewService
import com.moregames.playtime.user.offer.AndroidInstallationLinkProvider
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarService
import com.moregames.playtime.user.survey.SurveyService
import com.moregames.playtime.user.survey.paymentprovider.PaymentProviderSurveyService
import com.moregames.playtime.util.DEFAULT_USER_LOCALE
import com.moregames.playtime.util.roundDownToSecondDigit
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.ExperimentalSerializationApi
import java.math.BigDecimal
import java.text.NumberFormat
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import java.util.concurrent.TimeUnit
import javax.inject.Singleton
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration

@OptIn(ExperimentalSerializationApi::class)
@Singleton
class CashoutService @Inject constructor(
  private val cashoutPersistenceService: CashoutPersistenceService,
  private val cashoutPeriodsService: CashoutPeriodsService,
  private val cashoutStatusService: CashoutStatusService,
  private val cashoutValidationService: CashoutValidationService,
  private val paymentProvidersService: PaymentProvidersService,
  private val userService: UserService,
  private val userPersistenceService: UserPersistenceService,
  private val fraudScoreService: FraudScoreService,
  private val marketService: MarketService,
  private val abTestingService: AbTestingService,
  private val userCheckManager: UserCheckManager,
  private val userEarningsPersistenceService: UserEarningsPersistenceService,
  private val messageBus: MessageBus,
  private val timeService: TimeService,
  private val buildVariantProvider: Provider<BuildVariant>,
  private val translationService: UserTranslationService,
  private val cashoutSettingsService: CashoutSettingsService,
  private val accountsService: AccountsService,
  private val cashoutWithholdsService: CashoutWithholdsAndBonusesService,
  private val imageService: ImageService,
  private val ipService: IpService,
  private val applicationConfig: ApplicationConfig,
  private val coroutineScope: Provider<IoCoroutineScope>,
  private val googleAnalyticsService: GoogleAnalyticsService,
  private val encryptionService: EncryptionService,
  private val userInterviewService: UserInterviewService,
  private val adMarketService: AdMarketService,
  private val cashoutCoinsService: CashoutCoinsService,
  private val adjustService: AdjustService,
  private val adjustApiClient: AdjustApiClient,
  private val paymentProviderSurveyService: PaymentProviderSurveyService,
  private val hashService: HashService,
  private val incompleteCashoutService: IncompleteCashoutService,
  private val cashoutPeriodsConfigService: CashoutPeriodsConfigService,
  private val hideCashoutAmountExperimentService: HideCashoutAmountExperimentService,
  private val surveyService: SurveyService,
  private val userPopupMessagesService: UserPopupMessagesService,
  private val cashoutOffersService: CashoutOffersService,
  private val gamesService: GamesService,
  private val androidInstallationLinkProvider: AndroidInstallationLinkProvider,
  private val onboardingProgressBarService: OnboardingProgressBarService,
  private val androidHighlightedGamesService: AndroidHighlightedGamesService,
  private val rewardingFacade: RewardingFacade,
  private val currencyExchangeService: CurrencyExchangeService,
  private val boostedModeService: BoostedModeService,
) {
  companion object {
    private val TOTAL_EARNINGS_SOCIALSHARE_MIN_USD: BigDecimal = BigDecimal.ONE
    private val scReachedNEventRevenueThresholds =
      listOf(
        BigDecimal(10.0) to "sc_reached_10",
        BigDecimal(9.0) to "sc_reached_9",
        BigDecimal(8.0) to "sc_reached_8",
        BigDecimal(7.0) to "sc_reached_7",
        BigDecimal(6.0) to "sc_reached_6",
        BigDecimal(5.0) to "sc_reached_5",
        BigDecimal(4.0) to "sc_reached_4",
        BigDecimal(3.0) to "sc_reached_3",
        BigDecimal(2.0) to "sc_reached_2",
        BigDecimal(1.0) to "sc_reached_1",
        BigDecimal(0.5) to "sc_reached_05",
      )
    private val scReachedMinXEventRevenueThresholds =
      listOf(
        BigDecimal(10.0) to "sc_reached_min_10",
        BigDecimal(9.0) to "sc_reached_min_9",
        BigDecimal(8.0) to "sc_reached_min_8",
        BigDecimal(7.0) to "sc_reached_min_7",
        BigDecimal(6.0) to "sc_reached_min_6",
        BigDecimal(5.0) to "sc_reached_min_5",
        BigDecimal(4.0) to "sc_reached_min_4",
        BigDecimal(3.0) to "sc_reached_min_3",
        BigDecimal(2.0) to "sc_reached_min_2",
        BigDecimal(1.0) to "sc_reached_min_1",
        BigDecimal(0.5) to "sc_reached_min_05",
      )

    const val HIDE_EARNINGS_CASHOUT_AVAILABLE_HEADER = "Thanks for playing! Your cash reward is ready"
  }

  private val cashoutStatusConfigs: LoadingCache<CacheKey, ProviderImagesCfg> = CacheBuilder
    .newBuilder()
    .also {
      if (buildVariantProvider.get() == BuildVariant.TEST) {
        it.expireAfterWrite(1, TimeUnit.MINUTES)
      } else {
        it.expireAfterWrite(10, TimeUnit.MINUTES)
      }
    }
    .build(CacheLoader.from { cacheKey ->
      runBlocking {
        cashoutPersistenceService.getProviderImagesConfig(cacheKey!!.countryCode, cacheKey.cashoutEnabled)
      }
    })

  suspend fun getCashoutStatus(userId: String, locale: Locale): CashoutStatus {
    val user = userPersistenceService.loadCoinGoalUser(userId)
    val coinsBalance = rewardingFacade.getUserCurrentCoinsBalance(userId, user.appPlatform)
    val availableCashoutAmount = getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(userId).roundDownToSecondDigit()
    val userHasCashouts = userHasCashouts(userId)

    val cashoutConfig = cashoutSettingsService.getCashoutConfig()
      .let { config ->
        val userCoinsStr = coinsBalance.coins
          .takeIf { it != 0L }
          ?.let { coins -> NumberFormat.getNumberInstance(locale).format(coins) }
          ?.plus(" ") ?: ""

        with(translationService) {
          config
            .applyHideCashoutAmountExp(userId, userHasCashouts, availableCashoutAmount.amountUsd)
            .let { toCopy ->
              toCopy.copy(
                headerTextCashoutAvailable = tryTranslate(toCopy.headerTextCashoutAvailable, locale, userId),
                headerTextCashoutUnavailable = tryTranslate(toCopy.headerTextCashoutUnavailable, locale, userId).replace("{{coins}}", userCoinsStr),
                headerTextCashoutWithPeriod = tryTranslate(toCopy.headerTextCashoutWithPeriod, locale, userId),
                disclaimer = tryTranslate(toCopy.disclaimer, locale, userId)
              )
            }
        }
      }

    fraudScoreService.banUserWithHighFraudScore(userId)
    val userCountryCode = marketService.getUserAllowedCountryCodeOrUS(userId)
    val isEnabled = cashoutValidationService.isCashoutAllowed(userId)
      && cashoutStatusService.isCashoutEnabled(userId) && isValidAmount(availableCashoutAmount.userCurrencyAmount)

    val currentCashoutPeriod = cashoutPeriodsService.getCurrentCashoutPeriod(userId)
    val nextCashoutInstant = currentCashoutPeriod.periodEnd

    if (cashoutValidationService.isCashoutThrottled(userId)) {
      return CashoutStatus.disabled(cashoutConfig, availableCashoutAmount.userCurrency, nextCashoutInstant, userHasCashouts)
    }
    val paymentProviders = paymentProvidersService.loadPaymentProviders(
      userId = userId,
      appPlatform = user.appPlatform,
      countryCode = userCountryCode,
      amountToCashout = availableCashoutAmount.userCurrencyAmount,
      locale
    ).map { provider ->
      if (user.appPlatform == IOS || user.appPlatform == IOS_WEB) provider.copy(
        emailHint = translationService.translateOrDefault(IOS_CASHOUT_EMAIL_HINT, locale, userId)
      ) else provider
    }
    val firstCpDurationMinutes = cashoutPeriodsConfigService.firstPeriodDuration(userId)
    val headerText = cashoutConfig.headerText(isEnabled, firstCpDurationMinutes.takeIf { currentCashoutPeriod.counter == 2 })

    if (isEnabled && abTestingService.isUserExperimentParticipant(userId, ANDROID_ONBOARDING_PROGRESS_BAR)) {
      onboardingProgressBarService.completeRouteToCashout(userId)
    }

    return CashoutStatus(
      isEnabled = isEnabled,
      // == 2 means previous was the very first. Was decided it is the only case when we want to show specific header text
      // https://app.asana.com/0/****************/****************/f
      headerText = headerText,
      iconFilename = cashoutConfig.iconFilename(isEnabled),
      nextCashout = nextCashoutInstant,
      currency = availableCashoutAmount.userCurrency,
      amount = availableCashoutAmount.userCurrencyAmount,
      bonusAmount = calculateBonusBankAmount(userId, availableCashoutAmount.userCurrency, availableCashoutAmount.userCurrencyAmount),
      amountBefore = availableCashoutAmount.nonBoostedUserCurrencyAmount,
      amountUsd = availableCashoutAmount.amountUsd,
      providers = paymentProviders,
      disclaimer = cashoutConfig.disclaimer,
      userHasCashouts = userHasCashouts,
    ).also {
      coroutineScope.get().launch {
        userInterviewService.onCashoutStatusGetCall(userId, user.appPlatform, it)
      }
    }
  }

  suspend fun demandCashout(userId: String, cashoutDemand: CashoutDemand, appPlatform: AppPlatform): CashoutDemandResponseApiDto {
    if (cashoutDemand.emailHash.isNotEmpty()) {
      fraudScoreService.onNewUserEmail(userId, cashoutDemand.emailHash, cashoutDemand.normalizedEmailHash)
    }
    requestCashout(userId, cashoutDemand)
    if (abTestingService.isUserExperimentParticipant(userId, ANDROID_INCOMPLETE_CASHOUT_RESTORING)) {
      incompleteCashoutService.removeTrackedInitiatedCashouts(userId)
    }
    if (abTestingService.isUserExperimentParticipant(userId, SPECIAL_CASHOUT_OFFERS)) {
      cashoutOffersService.closeSetIfNeeded(userId)
    }

    messageBus.publishAsync(DeletePopupMessageEffect(userId, EARNINGS_THRESHOLD_REACHED))
    surveyService.sendCashoutSurveyIfApplicable(userId, appPlatform)

    return CashoutDemandResponseApiDto(
      showRewardsIcon = true,
      providerIssuesMessage = getProviderIssuesMessage(cashoutDemand.provider, userId),
      cashout2xOffer = getCashout2xOffer(userId)
    )
  }

  suspend fun completeSuccessfulTransaction(cashoutTransactionId: String) {
    cashoutPersistenceService.updateTransactionStatus(cashoutTransactionId, UserCashoutTransactionsTable.Status.SUCCESSFUL)
    val cashoutTransaction = cashoutPersistenceService.loadTransaction(cashoutTransactionId)
    val userId = cashoutTransaction.userId

    val revenueForCashout = userEarningsPersistenceService.getCashoutRelatedRevenueSum(cashoutTransactionId)
    if (revenueForCashout > BigDecimal.ZERO) {
      sendSuccessfulCashoutWithRevenueEvent(userId, revenueForCashout)
    }
    createAndSendSuccessful7DayCashoutEvents(userId, revenueForCashout)
    createAndSendSuccessful30DayCashoutEvents(userId, revenueForCashout)

    val isVenmoPayment =
      !cashoutTransaction.userHandle.isNullOrEmpty() && cashoutTransaction.provider == PaymentProviderType.VENMO
    if (!isVenmoPayment) {
      userCheckManager.onSuccessfulCashout(cashoutTransaction)
    }
    if (!abTestingService.isEm2Participant(userId))
      messageBus.publish(
        message = XHoursPassedSinceNoEarningsCheckDto(userId = userId, fromDate = timeService.now(), counter = 1),
        delayUntil = timeService.now().plus(1, ChronoUnit.DAYS)
      )
    userPersistenceService.trackUserLastCashoutData(cashoutTransaction)
  }

  private suspend fun sendSuccessfulCashoutWithRevenueEvent(userId: String, revenue: BigDecimal) {
    val userExternalIds = userPersistenceService.fetchExternalIds(userId)
    if (userExternalIds == null) {
      logger().warn("Could not send event sc_with_revenue for user = $userId. Not enough identification data.")
      return
    }
    val platform = userExternalIds.trackingData?.platform
    val firebaseAppId = userExternalIds.firebaseAppId
    val userTrackingId = userExternalIds.trackingData?.id

    coroutineScope.get().launch {
      if (firebaseAppId != null && platform != null) {
        googleAnalyticsService.sendCashoutRevenueEvent(
          appInstanceId = firebaseAppId,
          userId = userId,
          appPlatform = platform,
          revenueAmount = revenue.toString()
        )
      }
    }

    coroutineScope.get().launch {
      val adjustId = userExternalIds.adjustId
      if (adjustId != null && platform != null) {
        adjustApiClient.sendCustomEvent(
          event = AdjustCustomEvent(
            eventType = AdMarketEvent.EventType.SC_WITH_REVENUE_PARTNER,
            googleAdId = userExternalIds.googleAdId,
            adjustId = adjustId,
            idfa = userExternalIds.idfa,
            trackingId = userTrackingId,
            trackingType = userExternalIds.trackingData.type.name,
            appPlatform = platform.name,
            createdAt = timeService.now(),
            userAgent = adjustService.getUserAgent(userId),
            ipAddress = adjustService.getUserIp(userId)
          ),
          partnerParams = mapOf("sc_currency" to "USD", "sc_revenue" to revenue.toString())
        )
      }
    }

    sendMolocoInAppEvent(userId, userExternalIds, revenue)
  }

  private suspend fun sendMolocoInAppEvent(
    userId: String,
    userExternalIds: UserExternalIds?,
    revenue: BigDecimal
  ) {
    userExternalIds?.trackingData?.let { trackingData ->
      val idfa = if (trackingData.type == TrackingDataType.IDFA) trackingData.id else userExternalIds.idfa
      val idfv = if (trackingData.type == TrackingDataType.IDFV) trackingData.id else null

      if (idfa != null || idfv != null) {
        val molocoEvent = MolocoInAppEvent(
          ipAddress = adjustService.getUserIp(userId),
          userAgent = adjustService.getUserAgent(userId),
          idfa = idfa,
          idfv = idfv,
          platform = trackingData.platform,
          amount = revenue,
          timestamp = timeService.now().toEpochMilli()
        )
        messageBus.publishAsync(SendMolocoInAppEventEffect(molocoEvent))
      } else {
        logger().info("[MOLOCO] No IDFA or IDFV. Event not sent for userId = $userId")
      }
    }
  }

  private fun createAndSendSuccessful7DayCashoutEvents(userId: String, cashoutRevenue: BigDecimal?) =
    coroutineScope.get().launch {
      val userData = userService.getUser(userId, includingDeleted = true)
      userData.createdAt.let { userCreationDate ->
        if (Duration.between(userCreationDate, timeService.now()).toDays() > 7) return@launch
      }

      val cashoutEventsList = buildList {
        scReachedNEventRevenueThresholds
          .firstOrNull { (revenueReached, _) -> revenueReached <= cashoutRevenue }
          ?.let { (_, eventName) -> add(eventName) }
      }

      adMarketService.sendMarketEvents(userId, cashoutEventsList)
    }

  private fun createAndSendSuccessful30DayCashoutEvents(userId: String, cashoutRevenue: BigDecimal?) =
    coroutineScope.get().launch {
      val user = userService.getUser(userId, includingDeleted = true)
      user.createdAt.let { userCreationDate ->
        if (Duration.between(userCreationDate, timeService.now()).toDays() > 30) return@launch
      }

      val cashoutEventsList = buildList {
        scReachedMinXEventRevenueThresholds
          .filter { (revenueReached, _) -> revenueReached <= cashoutRevenue }
          .forEach { (_, eventName) -> add(eventName) }
      }

      adMarketService.sendMarketEvents(userId, cashoutEventsList)

    }

  suspend fun rollbackFailedTransaction(cashoutTransactionId: String) {
    cashoutPersistenceService.updateTransactionStatus(cashoutTransactionId, FAILED)
    val cashoutTransaction = cashoutPersistenceService.loadTransaction(cashoutTransactionId)
    cashoutStatusService.enableCashout(cashoutTransaction.userId)
    cleanUpTransaction(cashoutTransactionId)
  }

  suspend fun getCashoutStats(userId: String): CashoutStatsApiDto {
    val userCashoutsByProvider = cashoutPersistenceService.calculateTotalCashoutsByProviderForUser(userId)
    val formatter = userCashoutsByProvider.values.firstOrNull()?.userCurrency?.getFormatter()
      ?: marketService.getUserCurrency(userId).getFormatter()
    val nonCashedOutEarnings = rewardingFacade.loadUnpaidUserCurrencyEarnings(userId)?.let {
      hideUserCurrencyEarningsAboveThreshold(it, userId).roundDownToSecondDigit()
    }

    val totalEarningsUsd = (nonCashedOutEarnings?.amountUsd ?: BigDecimal.ZERO) +
      userCashoutsByProvider.values.sumOf { it.amountUsd.roundDownToSecondDigit() }
    return CashoutStatsApiDto(
      totalEarningsAmount = formatter.format(
        (nonCashedOutEarnings?.userCurrencyAmount ?: BigDecimal.ZERO) + userCashoutsByProvider.values.sumOf { it.userCurrencyAmount.roundDownToSecondDigit() }
      ),
      totalCashoutAmount = formatter.format(userCashoutsByProvider.filter { !it.key.donation }.map { it.value }
        .sumOf { it.userCurrencyAmount }.roundDownToSecondDigit()),
      totalDonationsAmount = formatter.format(userCashoutsByProvider.filter { it.key.donation }.map { it.value }
        .sumOf { it.userCurrencyAmount }.roundDownToSecondDigit()),
      // may need more sophisticated check if user's currency is greatly not like USD
      userReachedEarningsToShare = (totalEarningsUsd >= TOTAL_EARNINGS_SOCIALSHARE_MIN_USD)
    )
  }

  suspend fun loadRecentTransactions(userId: String, until: Instant, filterByType: TransactionsFilterType): List<CashoutTransactionDto> =
    cashoutPersistenceService.loadRecentTransactions(userId, until)
      .filter {
        when (filterByType) {
          Donations -> it.provider in PaymentProviderType.donationProviders()
          Withdrawals -> it.provider in PaymentProviderType.withdrawalProviders()
          All -> true
        }
      }

  sealed interface TransactionsFilterType {
    object Donations : TransactionsFilterType
    object Withdrawals : TransactionsFilterType
    object All : TransactionsFilterType

    companion object {
      fun parse(value: String?) = when (value) {
        Constants.DONATIONS_FILTER_VALUE -> Donations
        Constants.WITHDRAWALS_FILTER_VALUE -> Withdrawals
        else -> All
      }
    }
  }

  suspend fun loadTransaction(cashoutTransactionId: String) = cashoutPersistenceService.loadTransaction(cashoutTransactionId)

  suspend fun userHasCashouts(userId: String): Boolean = cashoutPersistenceService.hasCashouts(userId)

  suspend fun obfuscateUserCashoutTransactionPersonals(userId: String): Int =
    cashoutPersistenceService.obfuscateUserCashoutTransactionPersonals(userId)

  suspend fun loadUserIdsForEmail(email: String): List<String> =
    cashoutPersistenceService.loadUserIdsForEmails(setOf(hashService.emailSha256(email))).map { (userId, _) -> userId }

  suspend fun getLastUserPersonals(userId: String): UserPersonals? =
    cashoutPersistenceService.getLastUserPersonals(userId)

  suspend fun getUserCashoutConfig(userId: String, cashoutStatus: CashoutStatus): CashoutApiDto {
    val user = userService.getUser(userId)
    val cashoutScreenProvidersImagesList = getProviderImagesConfig(userId, cashoutStatus.isEnabled)
      .imageFileNames.map { fileName -> imageService.toUrl(fileName) }
    val formatter = cashoutStatus.currency.getFormatter()

    val paymentProviders = cashoutStatus.providers.map { provider -> provider2apiDto(userId, provider, cashoutStatus.currency) }

    return CashoutApiDto(
      isEnabled = cashoutStatus.isEnabled,
      headerText = cashoutStatus.headerText,
      iconUrl = imageService.toUrl(cashoutStatus.iconFilename),
      nextCashoutTimestamp = cashoutStatus.nextCashout,
      amountText = formatter.format(cashoutStatus.amount),
      bonusAmountText = if (cashoutStatus.bonusAmount != null && cashoutStatus.bonusAmount > BigDecimal.ZERO) formatter.format(cashoutStatus.bonusAmount) else null,
      cashoutAmountBefore = cashoutStatus.amountBefore?.let { formatter.format(it) }, // part of BM
      providers = paymentProviders,
      disclaimer = cashoutStatus.disclaimer,
      showRewards = cashoutStatus.userHasCashouts,
      providersImageList = cashoutScreenProvidersImagesList,
      timestamp = timeService.now(),
      consentedToAnalytics = !userPersistenceService.getUserConsentInfo(userId).isNonConsensualGdprUser(marketService),
      giftBoxInsteadOfEarnings = hideCashoutAmountExperimentService.shouldShowGiftBox(userId, cashoutStatus.userHasCashouts, cashoutStatus.amountUsd),
      cashoutButtonStyle = CashoutButtonStyle.from(abTestingService.assignedVariationValue(userId, ANDROID_HIDE_EARNINGS)),
      paymentProviderSurvey = if (user.appPlatform != ANDROID) null else run {
        abTestingService.assignedVariationValue(userId, ANDROID_PAYMENT_PROVIDER_SURVEY)
          .let { it as? PaymentProviderSurveyVariation }
          ?.takeIf { !cashoutStatus.userHasCashouts && paymentProviderSurveyService.shouldShowSurvey(userId) }
          ?.let { PaymentProviderSurvey.fromVariation(it) }
      },
      cashoutProgressBarMode = AndroidCashoutProgressBarMode.fromVariationOrNull(abTestingService.assignedVariationValue(userId, ANDROID_CASHOUT_PROGRESS_BAR)),
      cashoutFormStyle = CashoutFormStyle.fromVariationOrNull(abTestingService.assignedVariationValue(userId, ANDROID_FULLSCREEN_CASHOUT_FORM_STYLE)),
      cashoutOffers = getCashoutOffers(userId),
      highlightedGamesOnCashoutCancel = androidHighlightedGamesService.shouldShowHighlightedGamesOnCashoutCancel(userId).takeIf { it },
      boostedMode = boostedModeService.findCurrentBoostedMode(userId)?.let { bm ->
        CashoutBoostedModeApiDto(
          hintText = bm.uiConfig.cashoutScreenHintTranslation?.let { translationService.tryTranslate(it, user.locale, userId) },
        )
      }
    )
  }

  suspend fun userHasSuccessfulCashout(userId: String) = cashoutPersistenceService.userHasSuccessfulCashout(userId)

  suspend fun findOneCLickCashoutData(userId: String, locale: Locale = DEFAULT_USER_LOCALE): CashoutTransactionInfoApiDto? {
    val lastSuccessfulTransaction = cashoutPersistenceService.loadLastSuccessfulTransaction(userId) ?: return null

    val userEarnings = getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(userId)
    if (userEarnings.amountUsd <= BigDecimal.ZERO) return null

    val provider = paymentProvidersService.loadPaymentProvider(
      providerType = lastSuccessfulTransaction.provider,
      countryCode = marketService.getUserAllowedCountryCodeOrUS(userId),
      userId = userId,
      amountToCashout = userEarnings.userCurrencyAmount,
      locale = locale
    )
    if (provider == null || !provider.enabled) return null

    val name = encryptionService.decryptOrEmpty(lastSuccessfulTransaction.encryptedUserName)
    val email = encryptionService.decryptOrEmpty(lastSuccessfulTransaction.encryptedEmail)
    val address = encryptionService.decryptOrEmpty(lastSuccessfulTransaction.encryptedAddress)

    return CashoutTransactionInfoApiDto(
      name = name,
      address = address,
      email = email,
      provider = provider2apiDto(userId, provider, userEarnings.userCurrency)
    )
  }

  suspend fun fillEmptyCashoutAddress(
    cashoutDemand: CashoutDemandApiDto,
    ipAddress: String
  ): CashoutDemandApiDto =
  // there possible second call to ipRegistry (first - in ipService.extractIpData).
    // can be avoided with very-short-living cache in IpRegistryService for ipRegistry result by IP.
    (ipService
      .getExtendedIpInfo(ipAddress)
      ?.locationString()
      ?: "<failed to retrieve location for ip = $ipAddress>")
      .let { retrievedAddress -> cashoutDemand.copy(address = retrievedAddress) }

  private fun IpData.Extended.locationString() = listOfNotNull(this.countryName, this.regionName, this.cityName)
    .joinToString(", ")


  private suspend fun requestCashout(userId: String, cashoutDemand: CashoutDemand) {
    val transactionId = UUID.randomUUID().toString()
    cashoutPersistenceService.createTransaction(userId, transactionId, cashoutDemand)

    val updatedRows = userEarningsPersistenceService.updateUnpaidUserEarningsForDemand(userId, transactionId) // avoid duplicate earnings payments
    if (updatedRows == 0) {
      cashoutPersistenceService.updateTransactionStatus(transactionId, FAILED)
      throw NoEarningsToCashoutException(userId)
    } else {
      cashoutPersistenceService.updateTransactionStatus(transactionId, REQUESTED)
      userEarningsPersistenceService.getEarningsSumForTransaction(transactionId).let { earningsSum ->
        hideUserCurrencyEarningsAboveThreshold(earningsSum, userId).roundDownToSecondDigit()
      }.also { allowedEarnings ->
        requestAsynchronousCashout(transactionId, userId, cashoutDemand, allowedEarnings)
        cashoutStatusService.disableCashout(userId)
      }
    }
  }

  private suspend fun requestAsynchronousCashout(
    transactionId: String,
    userId: String,
    cashoutDemand: CashoutDemand,
    transactionAmount: UserCurrencyEarnings
  ) {
    val withholdsAndBonusesAmounts = cashoutWithholdsService.getWithholdsAndBonusesAmounts()

    cashoutPersistenceService.updateCashoutTransactionCashoutAmount(
      transactionId = transactionId,
      amountUsd = transactionAmount.amountUsd + withholdsAndBonusesAmounts.amountUSDTotalChange(),
      operationalWithholdAmountUsd = -withholdsAndBonusesAmounts.amountUSDTotalChange(),
      userCurrency = transactionAmount.userCurrency,
      userCurrencyAmount = transactionAmount.userCurrencyAmount + withholdsAndBonusesAmounts.userCurrencyTotalChange(),
      operationalWithholdUserCurrencyAmount = -withholdsAndBonusesAmounts.userCurrencyTotalChange()
    )
    val appPlatform = userService.getUser(userId).appPlatform
    messageBus.publish(
      CashoutRequestCreatedEventDto(
        cashoutTransactionId = transactionId,
        userId = userId,
        amount = transactionAmount.amountUsd + withholdsAndBonusesAmounts.amountUSDTotalChange(),
        userCurrencyCode = transactionAmount.userCurrency.currencyCode,
        userCurrencyAmount = transactionAmount.userCurrencyAmount + withholdsAndBonusesAmounts.userCurrencyTotalChange(),
        provider = cashoutDemand.provider,
        encryptedName = cashoutDemand.encryptedName,
        encryptedEmail = cashoutDemand.encryptedEmail,
        emailHash = cashoutDemand.emailHash,
        countryCode = marketService.getUserAllowedCountryCodeOrUS(userId),
        encryptedAddress = cashoutDemand.encryptedAddress,
        market = applicationConfig.justplayMarket,
        createdAt = timeService.now(),
        account = accountsService.getAccount(cashoutDemand.provider),
        recipientHandle = cashoutDemand.userHandle,
        platform = appPlatform
      )
    )
    messageBus.publish(
      cashoutCreatedEvent {
        this.userId = userId
        this.cashoutTransactionId = transactionId
      }
    )
    cashoutCoinsService.getBonusCoinsAfterFirstCashout(userId, appPlatform)
      ?.let {
        rewardingFacade.addBonusCoinsIfNotExists(
          userId = userId,
          appPlatform = appPlatform,
          coinsAmount = it,
          bonusBalanceType = UserBonusBalanceType.COINS_AFTER_CASHOUT,
          uniqueBonusKey = userId // once in user's life
        )
      }
  }

  private suspend fun cleanUpTransaction(transactionId: String) {
    userEarningsPersistenceService.clearEarningsTransactionInfo(listOf(transactionId))
  }

  private suspend fun getProviderImagesConfig(userId: String, cashoutEnabled: Boolean): ProviderImagesCfg {
    val countryCode = marketService.getUserAllowedCountryCodeOrUS(userId)
    //!! we use US configs as a fallback if there are no configs for the user country
    return cashoutStatusConfigs.get(CacheKey(countryCode, cashoutEnabled))
  }

  private fun isValidAmount(amount: BigDecimal) = amount > BigDecimal.ZERO

  private suspend fun provider2apiDto(userId: String, provider: CashoutProvider, userCurrency: Currency) = CashoutProviderApiDto(
    displayName = provider.displayName,
    url = provider.url,
    videoUrl = provider.videoUrl,
    iconUrl = imageService.toUrl(provider.iconFilename),
    text = provider.text,
    shortText = provider.shortText,
    provider = provider.providerType,
    donation = provider.providerType.donation,
    disclaimer = provider.disclaimer,
    emailHint = provider.emailHint,
    minimumAmount = provider.minimumAmount?.format(userCurrency),
    maximumAmount = provider.maximumAmount?.format(userCurrency),
    enabled = provider.enabled,
    bonusEnabled = false,
    identifierType = provider.identifierType,
    identifierHint = provider.identifierHint
  ).let { applyAndroidPaypalHintsExp(userId, it) }

  private suspend fun CashoutConfig.applyHideCashoutAmountExp(userId: String, userHasCashouts: Boolean, unclaimedUsdEarnings: BigDecimal): CashoutConfig =
    if (hideCashoutAmountExperimentService.shouldShowGiftBox(userId, userHasCashouts, unclaimedUsdEarnings)) {
      this.copy(
        headerTextCashoutAvailable = HIDE_EARNINGS_CASHOUT_AVAILABLE_HEADER,
        headerTextCashoutWithPeriod = HIDE_EARNINGS_CASHOUT_AVAILABLE_HEADER,
      )
    } else this

  private suspend fun applyAndroidPaypalHintsExp(userId: String, provider: CashoutProviderApiDto): CashoutProviderApiDto {
    if (provider.provider !in payPalProviders()) return provider
    val variation = abTestingService.assignedVariationValue(userId, ANDROID_PAYPAL_HINTS)
    if (variation == DEFAULT) return provider

    return when (variation) {
      Variations.CHANGE_EMAIL -> provider.copy(identifierHint = "Email associated with your Paypal")
      Variations.CHANGE_EMAIL_AND_FOLLOWING_TEXT -> provider.copy(
        identifierHint = "Email associated with your Paypal",
        identifierExplanation = "In case you do not have a PayPal account, you will receive an invitation"
      )

      else -> provider
    }
  }

  private suspend fun getProviderIssuesMessage(provider: PaymentProviderType, userId: String): String? =
    if (provider in payPalAndVenmoProviders()) {
      userPopupMessagesService.getMessageForUser(userId, EndpointType.IOS_PAYPAL_ISSUE, hasEarnings = true)
    } else null


  private suspend fun getCashout2xOffer(userId: String): Cashout2xOfferApiDto? {
    val variation = abTestingService.assignedVariationValue(userId, ANDROID_CASHOUT_2X_OFFER) as? AndroidCashout2xOfferVariation
      ?: return null
    // exp overlap
    val currentCashoutPeriod = cashoutPeriodsService.getCurrentCashoutPeriod(userId)
    if (buildVariantProvider.get() == PRODUCTION && !currentCashoutPeriod.durationAtLeast3h())
      return null

    // BM 25 mins condition check
    if (variation.isBm && Duration.between(timeService.now(), currentCashoutPeriod.periodEnd) < 25.minutes.toJavaDuration())
      return null

    return Cashout2xOfferApiDto(
      title = "Double Your Next Cash-Out?",
      description = if (variation.isBm) {
        "Claim your offer before it's gone and earn up to 200% for limited time!"
      } else "Claim your offer before it's gone and earn up to 200% for the next 3 hours!",
      buttonText = "Claim Offer",
      claimPeriodMs = 120000
    )
  }

  private suspend fun getCashoutOffers(userId: String): List<CashoutOfferApiDto>? {
    if (!abTestingService.isUserExperimentParticipant(userId, SPECIAL_CASHOUT_OFFERS)) return null

    return when (val offerSet = cashoutOffersService.getOfferSet(userId)) {
      is Active -> {
        val games = offerSet.offers.mapNotNull { gamesService.getGameById(ANDROID, it.gameId) }
          .associateBy { it.id } // getGameById is cached
        offerSet.offers.mapNotNull {
          val game = games[it.gameId] ?: run {
            logger().alert("Can't find game for Cashout Offer: ${it.gameId}")
            return@mapNotNull null
          }
          CashoutOfferApiDto(
            cashoutOfferId = it.id,
            iconUrl = imageService.toUrl(game.iconFilename),
            applicationId = game.applicationId,
            activityName = game.activityName,
            installationLink = androidInstallationLinkProvider.provide(game.applicationId, userId),
            activeUntilDate = (it as? CashoutOffer.Active)?.activeUntilDate,
            status = when (it) {
              is CashoutOffer.Active -> CashoutOfferApiDto.Status.ACTIVE
              is CashoutOffer.Claimed -> CashoutOfferApiDto.Status.CLAIMED
              is CashoutOffer.Unclaimed -> CashoutOfferApiDto.Status.UNCLAIMED
            }
          )
        }
      }

      is Cooldown, NotCreated -> emptyList()
    }
  }

  private suspend fun calculateBonusBankAmount(userId: String, userCurrency: Currency, totalEarnings: BigDecimal): BigDecimal? {
    if (abTestingService.assignedVariationValue(userId, BONUS_BANK) == DEFAULT) {
      return null
    }
    val earningsUsd = rewardingFacade.getBonusBankEarnings(userId)
    val bonusBankEarnings = currencyExchangeService.convert(earningsUsd, userCurrency).amount

    return bonusBankEarnings.coerceAtMost(totalEarnings * 0.9.toBigDecimal())
  }

  suspend fun getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(userId: String) =
    hideUserCurrencyEarningsAboveThreshold(getNonCashedUserCurrencyEarnings(userId), userId)
      .hideInsignificantNonBoostedAmounts()

  suspend fun hideUserCurrencyEarningsAboveThreshold(currencyEarnings: UserCurrencyEarnings, userId: String): UserCurrencyEarnings =
    cashoutSettingsService.getUserMaxEarningsAmount(userId)
      .let { userEarningsThresholdUsd ->
        if (currencyEarnings.amountUsd <= userEarningsThresholdUsd) {
          currencyEarnings
        } else {
          val userCurrencyAmount = currencyExchangeService.convert(userEarningsThresholdUsd, currencyEarnings.userCurrency).amount

          UserCurrencyEarnings(
            amountUsd = userEarningsThresholdUsd,
            nonBoostedAmountUsd = currencyEarnings.nonBoostedAmountUsd?.min(userEarningsThresholdUsd),
            userCurrency = currencyEarnings.userCurrency,
            userCurrencyAmount = userCurrencyAmount,
            nonBoostedUserCurrencyAmount = currencyEarnings.nonBoostedUserCurrencyAmount?.min(userCurrencyAmount)
          )
        }
      }

  suspend fun getNonCashedUserCurrencyEarnings(userId: String): UserCurrencyEarnings =
    rewardingFacade.loadUnpaidUserCurrencyEarnings(userId)
      ?: UserCurrencyEarnings.zeroEarnings(marketService.getUserCurrency(userId))

  private fun UserCurrencyEarnings.hideInsignificantNonBoostedAmounts() =
    if (this.nonBoostedAmountUsd != null && (this.amountUsd - this.nonBoostedAmountUsd) < BigDecimal("0.01"))
      this.copy(
        nonBoostedAmountUsd = null,
        nonBoostedUserCurrencyAmount = null,
      )
    else this

  data class CacheKey(
    val countryCode: String,
    val cashoutEnabled: Boolean
  )
}
