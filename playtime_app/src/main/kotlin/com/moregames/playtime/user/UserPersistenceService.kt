package com.moregames.playtime.user

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.OfferWallType
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.AppPlatform.IOS_WEB
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.dto.PlatformDeviceTokenDto
import com.moregames.base.dto.TrackingDataType
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.table.*
import com.moregames.base.user.dto.CreateUserData
import com.moregames.base.util.*
import com.moregames.playtime.buseffects.InvalidateUserCacheEffect
import com.moregames.playtime.buseffects.InvalidateUserExternalIDsCacheEffect
import com.moregames.playtime.earnings.table.CountryLimitsTierTable
import com.moregames.playtime.earnings.table.LimitTierTable
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.notifications.NotificationType
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.cashout.dto.CashoutTransactionDto
import com.moregames.playtime.user.dto.*
import com.moregames.playtime.user.fraudscore.FraudScoreChangeReason.USER_IS_CONNECTED_FROM_NON_ALLOWED_COUNTRY
import com.moregames.playtime.user.fraudscore.table.UserFraudScoreFrozenScoreTable
import com.moregames.playtime.user.fraudscore.table.UserFraudScoreTable
import com.moregames.playtime.user.fraudscore.table.UserFraudScoreTransactionTable
import com.moregames.playtime.user.table.*
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.util.DEFAULT_USER_LOCALE
import com.moregames.playtime.util.buildLocale
import com.moregames.playtime.util.toNormalizedLanguageTag
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.*
import java.math.BigDecimal
import java.net.Inet4Address
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.util.*
import javax.inject.Singleton

@Singleton
class UserPersistenceService @Inject constructor(
  database: Database,
  private val rewardingFacade: RewardingFacade,
  private val timeService: TimeService,
  private val buildVariantProvider: Provider<BuildVariant>,
  private val encryptionService: EncryptionService,
  private val messageBus: MessageBus
) : BasePersistenceService(database) {

  companion object {
    const val MOCK_COIN_GOAL = 100
    const val IP_CHECK_CACHE_DAYS = 30L
    const val MAIN_COIN_GOAL_REACHED_LABEL = "mainCoinGoalReachedLabel"
    const val MAIN_COIN_GOAL_LABEL = "mainCoinGoalLabel"
    const val DAYS_TO_STORE_BY_PERIODS_DATA = 15L
    val offerwallTypesAlias = wrapAsExpression<String>(
      UserOfferwallTypeTable
        .slice(UserOfferwallTypeTable.type.groupConcat(separator = ","))
        .select { UserOfferwallTypeTable.userId eq UserTable.id }
    ).alias("offerwallTypes")
  }

  suspend fun getLimitedTrackingInfo(userId: String): LimitedTrackingInfo = dbQuery {
    UserTable
      .leftJoin(UserLimitedTrackingTable, { id }, { UserLimitedTrackingTable.userId })
      .slice(
        UserTable.id,
        UserTable.countryCode,
        UserTable.consentToAnalyticsAt,
        UserTable.consentToTargetedAdvertisementAt,
        UserLimitedTrackingTable.userId
      )
      .select { UserTable.id eq userId }
      .firstOrNull()
  }?.let {
    LimitedTrackingInfo(
      isLimitedTracking = it.getOrNull(UserLimitedTrackingTable.userId) != null,
      countryCode = it[UserTable.countryCode],
      isConsentedToAnalytics = it[UserTable.consentToAnalyticsAt] != null && it[UserTable.consentToTargetedAdvertisementAt] != null,
    )
  } ?: LimitedTrackingInfo(false, "US", true)

  suspend fun setLimitedTrackingUser(userId: String) = dbQuery {
    UserLimitedTrackingTable.insertIgnore { it[UserLimitedTrackingTable.userId] = userId }
  }.also { invalidateUserCache(userId) }

  suspend fun getUserConsentInfo(userId: String): LimitedTrackingInfo = dbQuery {
    UserTable
      .slice(
        UserTable.id,
        UserTable.countryCode,
        UserTable.consentToAnalyticsAt,
        UserTable.consentToTargetedAdvertisementAt
      )
      .select { UserTable.id eq userId }
      .firstOrNull()
  }?.let { // same as getLimitedTrackingInfo, but without checking UserLimitedTrackingTable
    LimitedTrackingInfo(
      isLimitedTracking = false,
      countryCode = it[UserTable.countryCode],
      isConsentedToAnalytics = it[UserTable.consentToAnalyticsAt] != null && it[UserTable.consentToTargetedAdvertisementAt] != null,
    )
  } ?: LimitedTrackingInfo(false, "US", true)

  suspend fun updateDeviceLocale(userId: String, locale: Locale) {
    dbQuery {
      UserAdditionalDataTable.update({ UserAdditionalDataTable.userId eq userId }) {
        it[deviceLocale] = locale.language.take(10)
        it[deviceLanguageTag] = locale.toNormalizedLanguageTag().take(30)
      }
    }.also { invalidateUserCache(userId) }
  }

  suspend fun updateGpsLocationCountry(userId: String, country: String) {
    dbQuery {
      UserAdditionalDataTable.update({ UserAdditionalDataTable.userId eq userId }) {
        it[gpsLocationCountry] = country
      }
    }
  }

  suspend fun findGpsLocationCountry(userId: String) = dbQuery {
    UserAdditionalDataTable
      .slice(UserAdditionalDataTable.gpsLocationCountry)
      .select { UserAdditionalDataTable.userId eq userId }
      .firstOrNull()?.get(UserAdditionalDataTable.gpsLocationCountry)
  }

  suspend fun getUser(userId: String, includingDeleted: Boolean = false): UserDto = dbQuery {
    val query = UserTable
      .leftJoin(UserLimitedTrackingTable, { id }, { UserLimitedTrackingTable.userId })
      .leftJoin(UserWhitelistTable, { UserTable.id }, { UserWhitelistTable.userId })
      .leftJoin(UserAdditionalDataTable, { UserTable.id }, { UserAdditionalDataTable.userId })
      .slice(
        UserTable.id,
        UserTable.googleAdId,
        UserTable.createdAt,
        UserTable.countryCode,
        UserTable.appVersion,
        UserTable.consentToAnalyticsAt,
        UserTable.consentToTargetedAdvertisementAt,
        UserTable.isBanned,
        UserTable.currentTrackingId,
        UserTable.currentTrackingType,
        UserTable.appPlatform,
        UserTable.isDeleted,
        UserLimitedTrackingTable.userId,
        UserWhitelistTable.userId,
        UserTable.lastActiveAtDay,
        offerwallTypesAlias,
        UserAdditionalDataTable.timeZone,
        UserAdditionalDataTable.deviceLanguageTag,
        UserAdditionalDataTable.deviceLocale,
      )
      .select { (UserTable.id eq userId) }
    if (!includingDeleted) {
      query.andWhere { UserTable.isDeleted eq false }
    }
    query.firstOrNull() ?: throw UserRecordNotFoundException(userId)
  }.toUserDto()

  suspend fun userExists(userId: String): Boolean =
    dbQuery {
      UserTable.select { (UserTable.id eq userId) and (UserTable.isDeleted eq false) }.count() > 0
    }

  suspend fun createUser(countryCode: String, createUserData: CreateUserData): String {
    val userId = UUID.randomUUID().toString()
    dbQuery {
      UserTable.insert {
        it[id] = userId
        it[UserTable.countryCode] = countryCode
        it[lastActiveAtDay] = LocalDate.now()
        it[appPlatform] = createUserData.appVersion.platform.name
        it[appVersion] = createUserData.appVersion.version
      }
      if (createUserData.userRequestDto != null) {
        UserAdditionalDataTable.insert {
          it[UserAdditionalDataTable.userId] = userId
          it[networkCountry] = createUserData.userRequestDto?.networkCountry?.uppercase()
          it[networkOperatorName] = createUserData.userRequestDto?.networkOperatorName?.take(100)
          it[simCountry] = createUserData.userRequestDto?.simCountry?.uppercase()
          it[simOperatorName] = createUserData.userRequestDto?.simOperatorName?.take(100)
          it[deviceLocale] = createUserData.userRequestDto?.deviceLocale?.take(10)
          it[deviceLanguageTag] = createUserData.userRequestDto?.deviceLanguageTag?.take(30)
          it[timeZone] = createUserData.userRequestDto?.timeZone
          it[isReviewer] = createUserData.isReviewer
        }
      }
      UserFraudScoreTable.insert {
        it[UserFraudScoreTable.userId] = userId
        it[score] = 0.0
      }
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        // todo: left for back-compatibility. remove after release
        it[UserLastEcpmRevenueTable.countryCode] = countryCode
      }
      if (createUserData.userRequestDto?.deviceSpecification != null) {
        UserDeviceTable.insert {
          it[UserDeviceTable.userId] = userId
          it[appPlatform] = createUserData.appVersion.platform.name
          it[osVersion] = createUserData.userRequestDto?.deviceSpecification?.osVersion
          it[modelName] = createUserData.userRequestDto?.deviceSpecification?.modelName
          it[ramSize] = createUserData.userRequestDto?.deviceSpecification?.ramSize
          it[fontScale] = createUserData.userRequestDto?.deviceSpecification?.fontScale?.toBigDecimal()
          it[density] = createUserData.userRequestDto?.deviceSpecification?.density
          it[densityScaleFactor] = createUserData.userRequestDto?.deviceSpecification?.densityScaleFactor?.toBigDecimal()
        }
      }
      UserAppVersionInfoTable.insert {
        it[this.userId] = userId
        it[this.appVersion] = createUserData.appVersion.version
        it[this.appPlatform] = createUserData.appVersion.platform.name
      }

      val simInfoList = createUserData.userRequestDto?.simInfoList ?: emptyList()
      if (simInfoList.isNotEmpty()) {
        UserMultiSimDataTable.batchInsert(simInfoList, shouldReturnGeneratedValues = false) { item ->
          this[UserMultiSimDataTable.userId] = userId
          this[UserMultiSimDataTable.networkCountry] = item.networkCountry.uppercase()
          this[UserMultiSimDataTable.networkOperatorName] = item.networkOperatorName.take(100)
          this[UserMultiSimDataTable.simCountry] = item.simCountry.uppercase()
          this[UserMultiSimDataTable.simOperatorName] = item.simOperatorName.take(100)
          this[UserMultiSimDataTable.simSlotIndex] = item.simSlotIndex
        }
      }
    }
    //if we don't really need it to be in the same transaction with user insert, we can do it in the next one for some cases
    dbQuery {
      ActiveUsersTable.insert {
        it[this.userId] = userId
        it[this.lastActiveAtDay] = LocalDate.now()
      }
    }
    return userId
  }

  suspend fun updateDeviceToken(userId: String, deviceToken: String) {
    dbQuery {
      val rowsUpdated = UserTable.update({ (UserTable.id eq userId) and (UserTable.isDeleted eq false) }) {
        it[UserTable.deviceToken] = deviceToken
      }
      if (rowsUpdated != 1) {
        throw UserRecordNotFoundException(userId)
      }
    }
  }

  suspend fun addOrUpdateFirebaseAppInstanceId(userId: String, firebaseAppInstanceId: String) {
    dbQuery {
      UserFirebaseAppInstanceTable.insertOrUpdate(UserFirebaseAppInstanceTable.firebaseAppInstanceId) {
        it[UserFirebaseAppInstanceTable.userId] = userId
        it[UserFirebaseAppInstanceTable.firebaseAppInstanceId] = firebaseAppInstanceId
      }
    }.also { messageBus.publishAsync(InvalidateUserExternalIDsCacheEffect(userId)) }
  }

  suspend fun getFirebaseAppInstanceId(userId: String): String? =
    dbQuery {
      UserFirebaseAppInstanceTable.slice(UserFirebaseAppInstanceTable.firebaseAppInstanceId)
        .select { UserFirebaseAppInstanceTable.userId eq userId }
        .firstOrNull()
        ?.get(UserFirebaseAppInstanceTable.firebaseAppInstanceId)
    }

  suspend fun updateUserConsent(userId: String, consentApiDto: ConsentApiDto) {
    if (consentApiDto.hasConsentedToAnalytics || consentApiDto.hasConsentedToTargetedAdvertisement) {
      dbQuery {
        val now = Instant.now()
        val updated = UserTable.update({ (UserTable.id eq userId) and (UserTable.isDeleted eq false) }) {
          if (consentApiDto.hasConsentedToAnalytics) {
            it[consentToAnalyticsAt] = now
          }
          if (consentApiDto.hasConsentedToTargetedAdvertisement) {
            it[consentToTargetedAdvertisementAt] = now
          }
        }
        if (updated < 1) {
          throw UserRecordNotFoundException(userId)
        }

        val librariesConsent = consentApiDto.librariesConsent
          ?.map { (library, isConsented) -> LibraryConsent(library, isConsented) }

        if (librariesConsent == null) return@dbQuery

        UserPrivacyLibraryConsentTable.batchReplace(librariesConsent, shouldReturnGeneratedValues = false) { consent ->
          this[UserPrivacyLibraryConsentTable.userId] = userId
          this[UserPrivacyLibraryConsentTable.libraryName] = consent.library
          this[UserPrivacyLibraryConsentTable.isConsented] = consent.isConsented
        }
      }

      invalidateUserCache(userId)
    } // else no consent given, nothing to do
  }

  suspend fun banUser(userId: String, reason: BanReason, description: String) {
    changeUserBanStatus(userId, reason, description, isBanned = true)
  }

  suspend fun unbanUser(userId: String, description: String) {
    changeUserBanStatus(userId, null, description, isBanned = false)
  }

  suspend fun whitelistUser(userId: String) {
    dbQuery {
      UserWhitelistTable.insertIgnore {
        it[UserWhitelistTable.userId] = userId
      }
    }
    invalidateUserCache(userId)
  }

  // Whitelisted user is not affected by ban and can receive notifications on test server
  suspend fun isUserWhitelisted(userId: String): Boolean =
    dbQuery {
      UserWhitelistTable.select { UserWhitelistTable.userId eq userId }.firstOrNull() != null
    }

  suspend fun deleteDeviceTokens(deviceTokens: List<String>) {
    dbQuery {
      UserTable.update({ UserTable.deviceToken inList deviceTokens }) { // Add index on deviceToken to support large lists
        it[deviceToken] = null
      }
    }
  }

  suspend fun fetchUserId(trackingId: String) =
    dbQuery {
      UserTable.slice(UserTable.id)
        .select { (UserTable.currentTrackingId eq trackingId) and (UserTable.isDeleted eq false) }
        .firstOrNull()
        ?.get(UserTable.id)
        ?: throw UserRecordNotFoundException(trackingId, UserTable.currentTrackingId.name)
    }

  suspend fun fetchUserIds(trackingIds: List<String>): List<UserIdAndTrackingId> =
    dbQuery {
      UserTable.slice(UserTable.currentTrackingId, UserTable.id)
        .select { (UserTable.currentTrackingId inList trackingIds) and (UserTable.isDeleted eq false) }
        .map { row -> UserIdAndTrackingId(row[UserTable.currentTrackingId]!!, row[UserTable.id]) }
    }

  suspend fun fetchUserId(trackingData: TrackingData) =
    dbQuery {
      UserTable.slice(UserTable.id)
        .select { trackingData.toUserTableEqPredicate() and (UserTable.isDeleted eq false) }
        .firstOrNull()
        ?.get(UserTable.id)
        ?: throw UserRecordNotFoundException(trackingData.id, UserTable.currentTrackingId.name)
    }

  suspend fun fetchAllUserIds(googleAdId: String) =
    dbQuery {
      UserTable.slice(UserTable.id).select { (UserTable.googleAdId eq googleAdId) }.map { it[UserTable.id] }
    }

  suspend fun fetchAllUserIds(trackingData: TrackingData) =
    dbQuery {
      UserTable.slice(UserTable.id)
        .select { trackingData.toUserTableEqPredicate() }
        .map { it[UserTable.id] }
    }

  suspend fun fetchGoogleAdId(userId: String) =
    dbQuery {
      (UserTable.slice(UserTable.googleAdId).select { (UserTable.id eq userId) and (UserTable.isDeleted eq false) }
        .firstOrNull() ?: throw UserRecordNotFoundException(userId))[UserTable.googleAdId]
    }

  suspend fun fetchTrackingData(userId: String) =
    dbQuery {
      (UserTable
        .slice(
          UserTable.currentTrackingId,
          UserTable.currentTrackingType,
          UserTable.appPlatform
        )
        .select { (UserTable.id eq userId) and (UserTable.isDeleted eq false) }
        .firstOrNull()
        ?: throw UserRecordNotFoundException(userId))
        .toTrackingData()
    }

  suspend fun loadUserIdsForGaids(googleAdIds: Set<String>): List<Pair<String, String>> =
    dbQuery {
      UserTable.slice(UserTable.id, UserTable.googleAdId).select { (UserTable.googleAdId inList googleAdIds) and (UserTable.isDeleted eq false) }
        .map { it[UserTable.id] to it[UserTable.googleAdId]!! }
    }

  suspend fun loadUserIdsForTrackingDataSet(trackingDataSet: Set<TrackingData>): List<UserTrackingData> =
    trackingDataSet
      .map { td ->
        dbQuery {
          UserTable.slice(UserTable.id)
            .select { (td.toUserTableEqPredicate()) and (UserTable.isDeleted eq false) }
            .map { UserTrackingData(it[UserTable.id], td) }
        }
      }
      .flatten()

  suspend fun loadActiveUserId(googleAdId: String, minLastActiveAtDay: LocalDate? = null) =
    dbQuery {
      val query = UserTable
        .slice(UserTable.id)
        .select {
          (UserTable.googleAdId eq googleAdId) and
            (UserTable.isDeleted eq false)
        }

      minLastActiveAtDay?.let { query.andWhere { UserTable.lastActiveAtDay greater minLastActiveAtDay } }

      query.map { it[UserTable.id] }.firstOrNull()
    }

  suspend fun saveUserIpReturnNew(userId: String, ip: String, countryCode: String) =
    dbQuery {
      if (UserIpsTable.select {
          (UserIpsTable.userId eq userId) and (UserIpsTable.ip eq ip) and
            (UserIpsTable.updatedAt greater Instant.now().minus(IP_CHECK_CACHE_DAYS, ChronoUnit.DAYS))
        }.count() > 0) {
        false
      } else {
        UserIpsTable.insertOrUpdate(UserIpsTable.updatedAt) {
          it[UserIpsTable.userId] = userId
          it[UserIpsTable.ip] = ip
          it[UserIpsTable.countryCode] = countryCode
        }
        true
      }
    }

  suspend fun loadUserCreationDate(userId: String) =
    dbQuery {
      UserTable.slice(UserTable.createdAt).select { UserTable.id eq userId }.firstOrNull()?.get(UserTable.createdAt)
        ?: throw UserRecordNotFoundException(userId)
    }

  suspend fun countUsersWithSameGoogleAdId(userId: String, googleAdId: String) =
    dbQuery {
      UserTable.select {
        (UserTable.id neq userId) and (UserTable.googleAdId eq googleAdId)
      }.count()
    }

  suspend fun countUsersWithSameTrackingData(userId: String, trackingData: TrackingData) =
    dbQuery {
      UserTable.select {
        (UserTable.id neq userId) and
          (UserTable.currentTrackingId eq trackingData.id) and
          (UserTable.currentTrackingType eq trackingData.type.name) and
          (UserTable.appPlatform eq trackingData.platform.name)
      }.count()
    }

  suspend fun loadRecentUsersAssociatedByIp(userId: String, ip: String, allowedCountries: Set<String>): List<AssociatedUser> =
    dbQuery {
      UserIpsTable
        .innerJoin(UserTable, { UserTable.id }, { UserIpsTable.userId })
        .leftJoin(UserPassedStrongAttestationCheckTable, { UserIpsTable.userId }, { UserPassedStrongAttestationCheckTable.userId })
        .leftJoin(UserAdditionalDataTable, { UserIpsTable.userId }, { UserAdditionalDataTable.userId })
        .leftJoin(UserFraudScoreFrozenScoreTable, { UserIpsTable.userId }, { UserFraudScoreFrozenScoreTable.userId })
        .join(
          UserFraudScoreTransactionTable,
          JoinType.LEFT,
          onColumn = UserFraudScoreTransactionTable.userId,
          otherColumn = UserTable.id,
          additionalConstraint = { UserFraudScoreTransactionTable.reasonType eq USER_IS_CONNECTED_FROM_NON_ALLOWED_COUNTRY.name }
        )
        .slice(UserIpsTable.userId, UserTable.isDeleted, UserTable.isBanned, UserFraudScoreTransactionTable.reasonType, UserFraudScoreFrozenScoreTable.amount)
        .select {
          (UserIpsTable.ip eq ip) and
            (UserIpsTable.updatedAt greater Instant.now().minus(IP_CHECK_CACHE_DAYS, ChronoUnit.DAYS)) and
            (UserIpsTable.userId neq userId) and
            not(
              UserAdditionalDataTable.simCountry.isNotNull() and
                UserAdditionalDataTable.networkCountry.isNotNull() and
                UserAdditionalDataTable.simOperatorName.isNotNull() and
                UserAdditionalDataTable.networkOperatorName.isNotNull() and
                UserPassedStrongAttestationCheckTable.userId.isNotNull() and
                UserTable.countryCode.inList(allowedCountries) and
                UserAdditionalDataTable.simCountry.inList(allowedCountries) and
                UserAdditionalDataTable.networkCountry.inList(allowedCountries) and
                CustomLongFunction("LENGTH", UserAdditionalDataTable.simOperatorName.trim()).greater(0L) and
                CustomLongFunction("LENGTH", UserAdditionalDataTable.networkOperatorName.trim()).greater(0L)
            )
        }.groupBy(UserIpsTable.userId, UserTable.isDeleted, UserTable.isBanned, UserFraudScoreTransactionTable.reasonType)
        .map {
          AssociatedUser(
            userId = it[UserIpsTable.userId],
            isDeleted = it[UserTable.isDeleted],
            isBanned = it[UserTable.isBanned],
            allowedCountriesOnly = it[UserFraudScoreTransactionTable.reasonType] != USER_IS_CONNECTED_FROM_NON_ALLOWED_COUNTRY.name,
            hasFrozenFraudScore = it.getOrNull(UserFraudScoreFrozenScoreTable.amount) != null
          )
        }.toList()
    }

  suspend fun loadUserCountries(userId: String, ipToExclude: String): Set<String> =
    dbQuery {
      UserIpsTable
        .slice(UserIpsTable.countryCode)
        .select { (UserIpsTable.userId eq userId) and (UserIpsTable.ip neq ipToExclude) }
        .withDistinct()
        .mapNotNull { it[UserIpsTable.countryCode] }
        .toSet()
    }

  suspend fun countUniqueIps(userId: String) =
    dbQuery {
      val userIps = UserIpsTable.alias("ips")
      UserIpsTable
        .innerJoin(userIps, { ip }, { userIps[UserIpsTable.ip] })
        .slice(UserIpsTable.ip, userIps[UserIpsTable.userId].countDistinct())
        .select {
          UserIpsTable.userId eq userId
        }
        .groupBy(UserIpsTable.ip)
        .having { userIps[UserIpsTable.userId].countDistinct() eq 1 }
        .toList()
        .size
    }

  suspend fun loadUserIdsForIps(ips: Set<String>): List<Pair<String, String>> =
    dbQuery {
      UserIpsTable
        .join(UserTable, JoinType.LEFT, UserIpsTable.userId, UserTable.id)
        .slice(UserIpsTable.userId, UserIpsTable.ip)
        .select { (UserIpsTable.ip inList ips) and (UserTable.isDeleted eq false) }
        .withDistinct(true)
        .map { it[UserIpsTable.userId] to it[UserIpsTable.ip] }
    }

  suspend fun saveFirebaseData(userId: String, firebaseUid: String) {
    dbQuery {
      UserFirebaseAuthTable.insertOrUpdate(
        UserFirebaseAuthTable.firebaseUid,
        UserFirebaseAuthTable.verificationDate
      ) {
        it[this.userId] = userId
        it[this.firebaseUid] = firebaseUid
        it[this.verificationDate] = timeService.now()
      }
    }
  }

  suspend fun loadUserAppVersionAndLastActivityDay(userId: String): Pair<AppVersionDto, LocalDate?> =
    dbQuery {
      (UserTable.slice(UserTable.appPlatform, UserTable.appVersion, UserTable.lastActiveAtDay)
        .select { UserTable.id eq userId }
        .firstOrNull() ?: throw UserRecordNotFoundException(userId))
        .let {
          AppVersionDto(
            AppPlatform.fromNullableValue(it[UserTable.appPlatform]),
            it[UserTable.appVersion] ?: 0
          ) to it[UserTable.lastActiveAtDay]
        }
    }

  suspend fun updateUserLastActivityDay(userId: String, day: LocalDate) {
    dbQuery {
      UserTable.update({ UserTable.id eq userId }) {
        it[lastActiveAtDay] = day
      }
    }
    invalidateUserCache(userId)
  }

  suspend fun updateUserAppVersion(userId: String, appVersion: AppVersionDto) {
    dbQuery {
      UserTable.update({ UserTable.id eq userId }) {
        it[UserTable.appVersion] = appVersion.version
        it[appPlatform] = appVersion.platform.name
      }
    }
    invalidateUserCache(userId)
  }

  suspend fun logUserAppVersionChange(userId: String, appVersion: AppVersionDto) {
    dbQuery {
      UserAppVersionInfoTable.insert {
        it[this.userId] = userId
        it[this.appVersion] = appVersion.version
        it[this.appPlatform] = appVersion.platform.name
      }
    }
  }

  suspend fun getUserFirstAppVersion(userId: String): Int? = dbQuery {
    UserAppVersionInfoTable
      .slice(UserAppVersionInfoTable.appVersion)
      .select { UserAppVersionInfoTable.userId eq userId }
      .orderBy(UserAppVersionInfoTable.createdAt)
      .limit(1)
      .firstOrNull()
      ?.let { it[UserAppVersionInfoTable.appVersion] }
  }

  suspend fun loadRecentCountryCodes(userId: String): List<String> =
    dbQuery {
      UserIpsTable
        .slice(UserIpsTable.countryCode)
        .select {
          (UserIpsTable.userId eq userId) and
            (UserIpsTable.updatedAt greater Instant.now().minus(IP_CHECK_CACHE_DAYS, ChronoUnit.DAYS))
        }
        .withDistinct()
        .mapNotNull { it[UserIpsTable.countryCode] }
    }

  suspend fun getIpsByUserId(userId: String): List<String> =
    dbQuery {
      UserIpsTable
        .slice(UserIpsTable.ip)
        .select { UserIpsTable.userId eq userId }
        .map { it[UserIpsTable.ip] }
    }

  suspend fun getLastIpV4ByUserId(userId: String): String? =
    dbQuery {
      UserIpsTable
        .slice(UserIpsTable.ip)
        .select { UserIpsTable.userId eq userId }
        .orderBy(UserIpsTable.updatedAt, SortOrder.DESC)
        .map { it[UserIpsTable.ip] }
        .firstOrNull { Inet4Address.getByName(it) is Inet4Address }
    }

  suspend fun loadDeviceTokensForUsers(userIds: List<String>): Map<String, PlatformDeviceTokenDto> = dbQuery {
    UserTable
      .slice(UserTable.id, UserTable.deviceToken, UserTable.appPlatform)
      .select {
        UserTable.id inList userIds and
          UserTable.deviceToken.isNotNull() and
          (UserTable.isDeleted eq false)
      }.associate {
        it[UserTable.id] to
          PlatformDeviceTokenDto(deviceToken = it[UserTable.deviceToken]!!, appPlatform = AppPlatform.fromNullableValue(it[UserTable.appPlatform]))
      }
  }

  suspend fun loadCoinGoalUser(userId: String): User {
    val abInflatingCoinsMultiplier = rewardingFacade.inflatingCoinsMultiplier(userId)

    val userData = dbQuery {
      UserTable
        .leftJoin(CashoutPeriodTable, { id }, { CashoutPeriodTable.userId })
        .slice(
          UserTable.id,
          UserTable.googleAdId,
          UserTable.deviceToken,
          UserTable.createdAt,
          UserTable.appPlatform,
          UserTable.appVersion,
          UserTable.countryCode,
          UserTable.consentToAnalyticsAt,
          UserTable.consentToTargetedAdvertisementAt,
          CashoutPeriodTable.coinGoal,
        )
        .select { (UserTable.id eq userId) and (UserTable.isDeleted eq false) }
        .firstOrNull()
        ?: throw UserRecordNotFoundException(userId)
    }

    val appPlatform = AppPlatform.fromNullableValue(userData[UserTable.appPlatform])

    val coinGoal = (userData.getOrNull(CashoutPeriodTable.coinGoal)
      ?: MOCK_COIN_GOAL.also { logger().error("Try to get cashout period for: $userId but no cashout period was found") })
      .times(abInflatingCoinsMultiplier)
//    val coinGoalReached = goalCoins >= coinGoal

    return User(
      userId = userData[UserTable.id],
      googleAdId = userData[UserTable.googleAdId],
      deviceToken = userData[UserTable.deviceToken],
      coinsGoal = coinGoal,
      createdAt = userData[UserTable.createdAt],
//      coinGoalLabel = if (coinGoalReached) MAIN_COIN_GOAL_REACHED_LABEL else MAIN_COIN_GOAL_LABEL,
//      coinGoalReached = coinGoalReached,
      expLabels = emptyMap(),
      appPlatform = appPlatform,
      appVersion = userData[UserTable.appVersion] ?: 0,
      countryCode = userData[UserTable.countryCode],
      isConsentedToAnalytics = userData[UserTable.consentToAnalyticsAt] != null &&
        userData[UserTable.consentToTargetedAdvertisementAt] != null,
    )
  }

  suspend fun loadPerGameCoinsForUser(userId: String): List<GamePlayStatusDto> = dbQuery {
    UserGameBalanceTotalsTable
      .leftJoin(
        UserGameFirstPlayedTable, { UserGameBalanceTotalsTable.userId }, { UserGameFirstPlayedTable.userId },
        { UserGameBalanceTotalsTable.gameId eq UserGameFirstPlayedTable.gameId }
      )
      .slice(
        UserGameBalanceTotalsTable.gameId,
        UserGameBalanceTotalsTable.coins,
        UserGameBalanceTotalsTable.lastPlayedAt,
        UserGameFirstPlayedTable.firstPlayedAt,
      )
      .select { (UserGameBalanceTotalsTable.userId eq userId) and (UserGameBalanceTotalsTable.coins greater 0) }
      .map {
        GamePlayStatusDto(
          gameId = it[UserGameBalanceTotalsTable.gameId],
          coins = it[UserGameBalanceTotalsTable.coins],
          playedRecently = (
            it[UserGameBalanceTotalsTable.lastPlayedAt]
              .isAfter(timeService.now().minus(1, ChronoUnit.DAYS))
            ),
          firstPlayedAt = it.getOrNull(UserGameFirstPlayedTable.firstPlayedAt),
          lastPlayedAt = it[UserGameBalanceTotalsTable.lastPlayedAt],
        )
      }
  }

  suspend fun loadRoundedPerGameCoinsForEm2User(userId: String): List<GamePlayStatusDto> = dbQuery {
    UserGameBalanceTotalsEm2Table
      .slice(
        UserGameBalanceTotalsEm2Table.gameId,
        UserGameBalanceTotalsEm2Table.coins,
        UserGameBalanceTotalsEm2Table.lastPlayedAt,
        UserGameBalanceTotalsEm2Table.firstPlayedAt,
      )
      .select { (UserGameBalanceTotalsEm2Table.userId eq userId) and (UserGameBalanceTotalsEm2Table.coins greater BigDecimal.ZERO) }
      .map {
        GamePlayStatusDto(
          gameId = it[UserGameBalanceTotalsEm2Table.gameId],
          coins = it[UserGameBalanceTotalsEm2Table.coins].toInt(),
          playedRecently = (
            it[UserGameBalanceTotalsEm2Table.lastPlayedAt]
              .isAfter(timeService.now().minus(1, ChronoUnit.DAYS))
            ),
          firstPlayedAt = it[UserGameBalanceTotalsEm2Table.firstPlayedAt],
          lastPlayedAt = it[UserGameBalanceTotalsEm2Table.lastPlayedAt],
        )
      }
  }

  data class GamePlayStatusDto(
    val gameId: Int,
    val coins: Int,
    val playedRecently: Boolean,
    val firstPlayedAt: Instant?,
    val lastPlayedAt: Instant?,
  )

  suspend fun updateUserCreatedAt(userId: String, newCreatedAt: Instant) {
    if (buildVariantProvider.get() == BuildVariant.PRODUCTION)
      throw IllegalStateException("Not allowed in production mode")
    dbQuery {
      val rowsUpdated = UserTable.update({ (UserTable.id eq userId) and (UserTable.isDeleted eq false) }) {
        it[createdAt] = newCreatedAt
      }
      if (rowsUpdated != 1) {
        throw UserRecordNotFoundException(userId)
      }
    }
    invalidateUserCache(userId)
  }

  suspend fun loadNewUserIdsAndCreationDates(dateFrom: Instant) =
    dbQuery {
      UserTable
        .slice(UserTable.id, UserTable.createdAt)
        .select { (UserTable.isDeleted eq false) and (UserTable.createdAt greater dateFrom) }
        .map { Pair(it[UserTable.id], it[UserTable.createdAt]) }
    }

  // Use marketService.getUserCountryCode for business purposes
  suspend fun getUserCountryCode(userId: String): String = dbQuery {
    UserTable
      .slice(UserTable.countryCode)
      .select { (UserTable.id eq userId) }
      .first()[UserTable.countryCode]
  }

  suspend fun updateUserCountryCode(userId: String, countryCode: String) {
    if (buildVariantProvider.get() == BuildVariant.PRODUCTION)
      throw IllegalStateException("Not allowed in production mode")
    dbQuery {
      val rowsUpdated = UserTable.update({ (UserTable.id eq userId) and (UserTable.isDeleted eq false) }) {
        it[UserTable.countryCode] = countryCode
      }
      if (rowsUpdated != 1) {
        throw UserRecordNotFoundException(userId)
      }
    }
    invalidateUserCache(userId)
  }

  suspend fun getUserAdditionalCountryInfo(userId: String): AdditionalCountryInfo? = dbQuery {
    UserAdditionalDataTable
      .slice(UserAdditionalDataTable.simCountry, UserAdditionalDataTable.networkCountry)
      .select { UserAdditionalDataTable.userId eq userId }
      .map {
        AdditionalCountryInfo(
          userId = userId,
          simCountry = it[UserAdditionalDataTable.simCountry]?.uppercase(),
          networkCountry = it[UserAdditionalDataTable.networkCountry]?.uppercase()
        )
      }
      .firstOrNull()
  }

  suspend fun isTopLevelDomainExists(domain: String): Boolean =
    (dbQuery { TopLevelDomainsTable.select { TopLevelDomainsTable.name eq domain }.count() } > 0)

  suspend fun markUserAsDeleted(userId: String) = dbQuery {
    UserTable.update({ UserTable.id eq userId }) {
      it[isDeleted] = true
    }
  }.also { invalidateUserCache(userId) }

  private suspend fun changeUserBanStatus(userId: String, reason: BanReason?, description: String, isBanned: Boolean) {
    dbQuery {
      val rowsUpdated = UserTable.update({ (UserTable.id eq userId) and (UserTable.isDeleted eq false) }) {
        it[this.isBanned] = isBanned
      }
      if (rowsUpdated != 1) {
        throw UserRecordNotFoundException(userId)
      }
      if (isBanned) {
        UserBanInfoTable.insertIgnore {
          it[UserBanInfoTable.userId] = userId
          it[UserBanInfoTable.reason] = reason!!.name
          it[UserBanInfoTable.description] = description
        }
      } else {
        UserBanInfoTable.deleteWhere {
          (UserBanInfoTable.userId eq userId)
        }
      }
    }
    invalidateUserCache(userId)
  }

  suspend fun removeBatchOfZeroGameTotals(batchSize: Int, userIdStartsWith: String) = dbQuery {
    UserGameBalanceTotalsTable.deleteWhere(limit = batchSize) {
      (UserGameBalanceTotalsTable.coins eq 0) and
        (UserGameBalanceTotalsTable.userId like ("$userIdStartsWith%"))
    }
  }

  suspend fun removeBatchOfCoinsTrackedByPeriods(batchSize: Int, userIdStartsWith: String) = dbQuery {
    UserGameCoinsBalanceByPeriodsTable.deleteWhere(limit = batchSize) {
      (UserGameCoinsBalanceByPeriodsTable.periodStart less timeService.now().minus(DAYS_TO_STORE_BY_PERIODS_DATA, ChronoUnit.DAYS)) and
        (UserGameCoinsBalanceByPeriodsTable.userId like ("$userIdStartsWith%"))
    }
  }

  suspend fun removeBatchOfFractionalCoinsTrackedByPeriods(batchSize: Int, userIdStartsWith: String) = dbQuery {
    UserGameCoinsBalanceByPeriodsEm2Table.deleteWhere(limit = batchSize) {
      (UserGameCoinsBalanceByPeriodsEm2Table.periodStart less timeService.now().minus(DAYS_TO_STORE_BY_PERIODS_DATA, ChronoUnit.DAYS)) and
        (UserGameCoinsBalanceByPeriodsEm2Table.userId like ("$userIdStartsWith%"))
    }
  }

  suspend fun markUserAsConnectedViaVpn(userId: String) = dbQuery {
    UserConnectedViaVpnTable.insertIgnore {
      it[UserConnectedViaVpnTable.userId] = userId
    }
  }

  suspend fun wasUserEverConnectedViaVpn(userId: String): Boolean = dbQuery {
    UserConnectedViaVpnTable
      .select { UserConnectedViaVpnTable.userId eq userId }
      .count() > 0
  }

  suspend fun obfuscateUserPersonals(userId: String): Int = dbQuery {
    UserTable.update({ UserTable.id eq userId }) {
      it[googleAdId] = "personals deleted"
      it[deviceToken] = ""
    }
  }

  suspend fun saveUserEmail(userId: String, email: String) {
    val encryptedEmail = encryptionService.encrypt(email)
    return dbQuery {
      UserEmailsTable.insertOrUpdate(UserEmailsTable.encryptedEmail) {
        it[UserEmailsTable.userId] = userId
        it[UserEmailsTable.encryptedEmail] = encryptedEmail
      }
    }
  }

  suspend fun getLastNotificationDateByType(userId: String, type: NotificationType): Instant? = dbQuery {
    UserLastNotificationTable
      .slice(UserLastNotificationTable.notifiedAt)
      .select {
        (UserLastNotificationTable.userId eq userId) and
          (UserLastNotificationTable.notificationType eq type.name)
      }
      .map { it[UserLastNotificationTable.notifiedAt] }
      .firstOrNull()
  }

  suspend fun trackUserNotification(userId: String, type: NotificationType, notifiedAt: Instant) = dbQuery {
    UserLastNotificationTable.insertOrUpdate(UserLastNotificationTable.notifiedAt) {
      it[UserLastNotificationTable.userId] = userId
      it[notificationType] = type.name
      it[UserLastNotificationTable.notifiedAt] = notifiedAt
    }
  }

  suspend fun updateAdjustId(userId: String, adjustId: String) {
    dbQuery {
      UserAdjustIdTable
        .insertOrUpdate(UserAdjustIdTable.adjustId) {
          it[UserAdjustIdTable.userId] = userId
          it[UserAdjustIdTable.adjustId] = adjustId
        }
    }.also { messageBus.publishAsync(InvalidateUserExternalIDsCacheEffect(userId)) }
  }

  suspend fun getAdjustId(userId: String): String? = dbQuery {
    UserAdjustIdTable
      .slice(UserAdjustIdTable.adjustId)
      .select { (UserAdjustIdTable.userId eq userId) }
      .firstOrNull()
      ?.let { it[UserAdjustIdTable.adjustId] }
  }

  suspend fun fetchExternalIds(userId: String): UserExternalIds? =
    dbQuery {
      val latestIosIdfa = wrapAsExpression<String>(
        TrackingDataTable
          .slice(TrackingDataTable.trackingId)
          .select { (TrackingDataTable.userId eq userId) and (TrackingDataTable.trackingType eq TrackingDataType.IDFA.name) }
          .orderBy(TrackingDataTable.createdAt, SortOrder.DESC)
          .limit(1)
      )

      UserTable
        .leftJoin(UserAdjustIdTable, { id }, { UserAdjustIdTable.userId })
        .leftJoin(UserFirebaseAppInstanceTable, { UserTable.id }, { UserFirebaseAppInstanceTable.userId })
        .slice(
          UserTable.id,
          UserTable.googleAdId,
          latestIosIdfa,
          UserTable.currentTrackingId,
          UserTable.currentTrackingType,
          UserTable.appPlatform,
          UserAdjustIdTable.adjustId,
          UserFirebaseAppInstanceTable.firebaseAppInstanceId
        )
        .select { (UserTable.id eq userId) and (UserTable.isDeleted eq false) }
        .map {
          UserExternalIds(
            userId = it[UserTable.id],
            googleAdId = it[UserTable.googleAdId],
            idfa = if (it[UserTable.appPlatform] in arrayOf(IOS.name, IOS_WEB.name)) it[latestIosIdfa] else null,
            adjustId = it.getOrNull(UserAdjustIdTable.adjustId),
            firebaseAppId = it.getOrNull(UserFirebaseAppInstanceTable.firebaseAppInstanceId),
            trackingData = it.toTrackingData()
          )
        }
        .firstOrNull()
    }

  suspend fun updateUserOnEmptyCPNotificationTable(userId: String, notificationsLeft: Int) = dbQuery {
    UserOnEmptyCPNotificationTable
      .insertOrUpdate(UserOnEmptyCPNotificationTable.notificationsLeft) {
        it[UserOnEmptyCPNotificationTable.userId] = userId
        it[UserOnEmptyCPNotificationTable.notificationsLeft] = notificationsLeft
      }
  }

  suspend fun reduceAmountOfNotificationsOnEmptyCPByOneReturnRowsUpdated(userId: String): Int =
    dbQuery {
      UserOnEmptyCPNotificationTable
        .update({
          (UserOnEmptyCPNotificationTable.userId eq userId) and
            (UserOnEmptyCPNotificationTable.notificationsLeft greater 0)
        }) {
          with(SqlExpressionBuilder) {
            it.update(notificationsLeft, notificationsLeft - 1)
          }
        }
    }

  suspend fun getUserLastGameCoinsDate(userId: String): Instant? = dbQuery {
    UserGameBalanceTotalsTable
      .slice(UserGameBalanceTotalsTable.lastPlayedAt.max())
      .select { UserGameBalanceTotalsTable.userId eq userId }
      .map { it.getOrNull(UserGameBalanceTotalsTable.lastPlayedAt.max()) }
      .firstOrNull()
  }

  suspend fun getUserLastGameEm2CoinsDate(userId: String): Instant? = dbQuery {
    UserGameBalanceTotalsEm2Table
      .slice(UserGameBalanceTotalsEm2Table.lastPlayedAt.max())
      .select { UserGameBalanceTotalsEm2Table.userId eq userId }
      .map { it.getOrNull(UserGameBalanceTotalsEm2Table.lastPlayedAt.max()) }
      .firstOrNull()
  }

  suspend fun getUserCoinsByPeriod(userId: String, periodStart: Instant, periodEnd: Instant): Long = dbQuery {
    UserGameCoinsBalanceByPeriodsTable
      .slice(UserGameCoinsBalanceByPeriodsTable.coins.sum())
      .select {
        (UserGameCoinsBalanceByPeriodsTable.userId eq userId) and
          (UserGameCoinsBalanceByPeriodsTable.periodStart greaterEq periodStart) and
          (UserGameCoinsBalanceByPeriodsTable.periodStart less periodEnd)
      }
      .firstOrNull()
      ?.let { it.getOrNull(UserGameCoinsBalanceByPeriodsTable.coins.sum())?.toLong() ?: 0L }
      ?: 0L
  }

  suspend fun getGamesCoinsByPeriod(periodStart: Instant, periodEnd: Instant): Map<String, Long> = dbQuery {
    // todo: possible optimization before going wild: first group, only then join GamesTable.
    UserGameCoinsBalanceByPeriodsTable
      .innerJoin(GamesTable, { gameId }, { id })
      .slice(GamesTable.applicationId, UserGameCoinsBalanceByPeriodsTable.coins.sum())
      .select {
        (UserGameCoinsBalanceByPeriodsTable.periodStart greaterEq periodStart) and
          (UserGameCoinsBalanceByPeriodsTable.periodStart less periodEnd)
      }
      .groupBy(UserGameCoinsBalanceByPeriodsTable.gameId, GamesTable.applicationId)
      .associate { row ->
        val coins = row[UserGameCoinsBalanceByPeriodsTable.coins.sum()]?.toLong() ?: 0L
        row[GamesTable.applicationId] to coins
      }
  }

  /**
   * @return Map<gameApplicationId, coins> for user
   */
  suspend fun getUserGamesCoinsByPeriod(userId: String, periodStart: Instant, periodEnd: Instant): Map<String, Long> = dbQuery {
    UserGameCoinsBalanceByPeriodsTable
      .innerJoin(GamesTable, { gameId }, { id })
      .slice(GamesTable.applicationId, UserGameCoinsBalanceByPeriodsTable.coins.sum())
      .select {
        (UserGameCoinsBalanceByPeriodsTable.userId eq userId) and
          (UserGameCoinsBalanceByPeriodsTable.periodStart greaterEq periodStart) and
          (UserGameCoinsBalanceByPeriodsTable.periodStart less periodEnd)
      }
      .groupBy(UserGameCoinsBalanceByPeriodsTable.gameId, GamesTable.applicationId)
      .associate { row ->
        val coins = row[UserGameCoinsBalanceByPeriodsTable.coins.sum()]?.toLong() ?: 0L
        row[GamesTable.applicationId] to coins
      }
  }

  suspend fun getUserCountryTierSettings(userId: String): CountryTierSettings? = dbQuery {
    LimitTierTable
      .innerJoin(CountryLimitsTierTable, { id }, { tierId })
      .innerJoin(UserTable, { CountryLimitsTierTable.countryCode }, { countryCode })
      .slice(LimitTierTable.maxCashoutAmountPercentage, LimitTierTable.dailyEarningsQuotas)
      .select { UserTable.id eq userId }
      .map { row ->
        CountryTierSettings(
          maxCashoutAmountMultiplier = row[LimitTierTable.maxCashoutAmountPercentage] / BigDecimal("100"),
          dailyEarningsQuotas = row[LimitTierTable.dailyEarningsQuotas].split(",").map { it.toBigDecimal() }
        )
      }
      .firstOrNull()
  }

  suspend fun countUsersWithSameGoogleAdId(userId: String): Long =
    dbQuery {
      UserTable
        .innerJoin(
          UserGoogleAdIdsTable, { UserGoogleAdIdsTable.googleAdId }, { UserTable.googleAdId },
          additionalConstraint = { UserGoogleAdIdsTable.userId neq UserTable.id }
        )
        .slice(UserGoogleAdIdsTable.userId.countDistinct())
        .select { UserTable.id eq userId }
        .map { it[UserGoogleAdIdsTable.userId.countDistinct()] }
        .firstOrNull() ?: 0
    }

  suspend fun isUserUnique(userId: String): Boolean = dbQuery {
    val usersTab2 = UserTable.alias("users2")
    UserTable
      .innerJoin(usersTab2, { currentTrackingId }, { usersTab2[UserTable.currentTrackingId] })
      .slice(UserTable.id)
      .select { UserTable.id eq userId and UserTable.currentTrackingId.isNotNull() }
      .map { it[UserTable.id] }
      .size <= 1
  }

  suspend fun markUsersAsSharedEmail(userIds: List<String>) = dbQuery {
    UserSharedEmailTable.batchInsert(userIds, ignore = true, shouldReturnGeneratedValues = false) { userId ->
      this[UserSharedEmailTable.userId] = userId
    }
  }

  suspend fun isUserSharedEmail(userId: String) = dbQuery {
    UserSharedEmailTable.select {
      UserSharedEmailTable.userId eq userId
    }.count() > 0
  }

  suspend fun requestUserDeletion(userId: String, hasCashoutData: Boolean) = dbQuery {
    UserAccountDeletionRequestTable
      .insertOrUpdate(UserAccountDeletionRequestTable.hasCashoutData) {
        it[UserAccountDeletionRequestTable.userId] = userId
        it[UserAccountDeletionRequestTable.hasCashoutData] = hasCashoutData
      }
  }

  suspend fun loadUserDevice(userId: String) = dbQuery {
    UserDeviceTable
      .select { UserDeviceTable.userId eq userId }
      .firstOrNull()?.let {
        UserDeviceSpecs(
          osVersion = it[UserDeviceTable.osVersion],
          modelName = it[UserDeviceTable.modelName],
          ramSize = it[UserDeviceTable.ramSize],
          fontScale = it[UserDeviceTable.fontScale],
          density = it[UserDeviceTable.density],
          densityScaleFactor = it[UserDeviceTable.densityScaleFactor]
        )
      }
  }

  suspend fun trackUserLastCashoutData(cashoutTransaction: CashoutTransactionDto) = dbQuery {
    UserLastCashoutDataTable
      .insertOrUpdate(
        UserLastCashoutDataTable.encryptedEmail,
        UserLastCashoutDataTable.encryptedName,
        UserLastCashoutDataTable.cashoutTransactionId
      )
      {
        it[userId] = cashoutTransaction.userId
        it[encryptedEmail] = cashoutTransaction.encryptedEmail
        it[encryptedName] = cashoutTransaction.encryptedUserName
        it[cashoutTransactionId] = cashoutTransaction.cashoutTransactionId
      }
  }

  suspend fun updateUserTimeZone(userId: String, timeZone: String) {
    dbQuery {
      UserAdditionalDataTable
        .insertOrUpdate(UserAdditionalDataTable.timeZone) {
          it[UserAdditionalDataTable.userId] = userId
          it[UserAdditionalDataTable.timeZone] = timeZone
        }
    }
  }

  suspend fun storeFirstVideoReward(userId: String, reward: VideoAdReward): Boolean = dbQuery {
    UserFirstVideoRewardTable.insertIgnore {
      it[UserFirstVideoRewardTable.userId] = userId
      it[UserFirstVideoRewardTable.reward] = reward.revenue.toBigDecimal()
    }.insertedCount == 1
  }

  suspend fun getFirstRewardsForUsersCreatedDuringPreviousDaysOrdered(days: Long): List<BigDecimal> = dbQuery {
    UserFirstVideoRewardTable
      .innerJoin(UserTable, { userId }, { id })
      .slice(UserFirstVideoRewardTable.reward)
      .select { (UserTable.createdAt greater timeService.now().minus(days, ChronoUnit.DAYS)) and (UserTable.countryCode eq "US") }
      .orderBy(UserFirstVideoRewardTable.reward, SortOrder.DESC)
      .map { it[UserFirstVideoRewardTable.reward] }
  }

  suspend fun writeCurrentEcpmGroupsThresholds(ecpmGroupsThresholds: Map<Int, BigDecimal>) = dbQuery {
    FirstVideoRewardEcpmGroupsThresholdsTable
      .insertIgnore {
        it[forDay] = LocalDate.now()
        it[group0] = ecpmGroupsThresholds[0]!!
        it[group1] = ecpmGroupsThresholds[1]!!
        it[group2] = ecpmGroupsThresholds[2]!!
        it[group3] = ecpmGroupsThresholds[3]!!
        it[group4] = ecpmGroupsThresholds[4]!!
        it[group5] = ecpmGroupsThresholds[5]!!
        it[group6] = ecpmGroupsThresholds[6]!!
        it[group7] = ecpmGroupsThresholds[7]!!
        it[group8] = ecpmGroupsThresholds[8]!!
        it[group9] = ecpmGroupsThresholds[9]!!
        it[group10] = ecpmGroupsThresholds[10]!!
        it[group11] = ecpmGroupsThresholds[11]!!
        it[group12] = ecpmGroupsThresholds[12]!!
        it[group13] = ecpmGroupsThresholds[13]!!
        it[group14] = ecpmGroupsThresholds[14]!!
        it[group15] = ecpmGroupsThresholds[15]!!
        it[group16] = ecpmGroupsThresholds[16]!!
        it[group17] = ecpmGroupsThresholds[17]!!
        it[group18] = ecpmGroupsThresholds[18]!!
        it[group19] = ecpmGroupsThresholds[19]!!
      }
  }

  suspend fun getCurrentEcpmGroupsThresholds() = dbQuery {
    FirstVideoRewardEcpmGroupsThresholdsTable
      .selectAll()
      .limit(1)
      .orderBy(FirstVideoRewardEcpmGroupsThresholdsTable.forDay, SortOrder.DESC)
      .firstOrNull()
      ?.let {
        listOf(
          0 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group0],
          1 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group1],
          2 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group2],
          3 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group3],
          4 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group4],
          5 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group5],
          6 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group6],
          7 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group7],
          8 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group8],
          9 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group9],
          10 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group10],
          11 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group11],
          12 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group12],
          13 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group13],
          14 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group14],
          15 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group15],
          16 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group16],
          17 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group17],
          18 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group18],
          19 to it[FirstVideoRewardEcpmGroupsThresholdsTable.group19],
        )
      }
  }

  suspend fun trackUserEcpmGroup(userId: String, ecpmGroup: Int) {
    dbQuery {
      UserEcpmGroupTable
        .insertIgnore {
          it[UserEcpmGroupTable.userId] = userId
          it[UserEcpmGroupTable.ecpmGroup] = ecpmGroup
        }
    }
  }

  suspend fun getUserEcpmGroup(userId: String): Int? = dbQuery {
    UserEcpmGroupTable
      .slice(UserEcpmGroupTable.ecpmGroup)
      .select { UserEcpmGroupTable.userId eq userId }
      .firstOrNull()
      ?.get(UserEcpmGroupTable.ecpmGroup)
  }

  suspend fun trackJailBreakCheck(userId: String, jailBreak: Boolean) = dbQuery {
    IosJailBreakCheckResultTable.insertOrUpdate(IosJailBreakCheckResultTable.isJailBroken) {
      it[IosJailBreakCheckResultTable.userId] = userId
      it[IosJailBreakCheckResultTable.isJailBroken] = jailBreak
    }
  }

  suspend fun jailBreakCheckPassed(userId: String): Boolean = dbQuery {
    IosJailBreakCheckResultTable
      .slice(IosJailBreakCheckResultTable.isJailBroken)
      .select { IosJailBreakCheckResultTable.userId eq userId }
      .firstOrNull()
      ?.get(IosJailBreakCheckResultTable.isJailBroken) == false
  }

  suspend fun trackGpsLocationCheckAskedGame(userId: String, gameId: Int, provided: Boolean) = dbQuery {
    GpsLocationCheckGameTable.insertIgnore() {
      it[GpsLocationCheckGameTable.userId] = userId
      it[GpsLocationCheckGameTable.gameId] = gameId
      it[GpsLocationCheckGameTable.provided] = provided
    }
  }

  suspend fun getGpsLocationCheckAskedGames(userId: String): List<Int> = dbQuery {
    GpsLocationCheckGameTable
      .slice(GpsLocationCheckGameTable.gameId)
      .select { GpsLocationCheckGameTable.userId eq userId }
      .map { it[GpsLocationCheckGameTable.gameId] }
  }


  private suspend fun invalidateUserCache(userId: String) {
    messageBus.publish(InvalidateUserCacheEffect(userId))
  }

  data class UserIdAndTrackingId(
    val trackingId: String,
    val userId: String
  )

  private fun ResultRow.toUserDto() = UserDto(
    id = this[UserTable.id],
    googleAdId = this[UserTable.googleAdId],
    createdAt = this[UserTable.createdAt],
    countryCode = this[UserTable.countryCode],
    limitedTrackingInfo = LimitedTrackingInfo(
      isLimitedTracking = this.getOrNull(UserLimitedTrackingTable.userId) != null,
      countryCode = this[UserTable.countryCode],
      isConsentedToAnalytics = this[UserTable.consentToAnalyticsAt] != null && this[UserTable.consentToTargetedAdvertisementAt] != null,
    ),
    appVersion = this[UserTable.appVersion] ?: 0,
    isBanned = this[UserTable.isBanned] && this.getOrNull(UserWhitelistTable.userId) == null,
    isWhitelisted = this.getOrNull(UserWhitelistTable.userId) != null,
    isDeleted = this[UserTable.isDeleted],
    trackingData = this.toTrackingData(),
    appPlatform = AppPlatform.fromNullableValue(this[UserTable.appPlatform]),
    lastActiveAtDay = this[UserTable.lastActiveAtDay],
    offerWallTypes = this[offerwallTypesAlias]?.split(",")?.map { OfferWallType.valueOf(it) }
      ?.sortedBy { if (it == OfferWallType.ADJOE) 1 else 0 }, //add adjoe variation support
    timeZone = this[UserAdditionalDataTable.timeZone],
    locale = buildLocale(
      deviceLanguageTag = this[UserAdditionalDataTable.deviceLanguageTag],
      deviceLocale = this[UserAdditionalDataTable.deviceLocale]
    )
  )
}

private fun ResultRow.toTrackingData(): TrackingData? {
  if (this[UserTable.currentTrackingId] == null) return null
  return TrackingData.fromValues(
    this[UserTable.currentTrackingId]!!,
    this[UserTable.currentTrackingType]!!,
    this[UserTable.appPlatform]!!,
  )
}

data class RevenueByPeriod(
  val periodStart: Instant,
  val transactionsCount: Int
)

data class CountryTierSettings(
  val maxCashoutAmountMultiplier: BigDecimal,
  val dailyEarningsQuotas: List<BigDecimal>
) {
  init {
    if (dailyEarningsQuotas.size != 8) {
      logger().alert("wrong settings of dailyEarningsQuotas detected")
      throw IllegalStateException("wrong settings of dailyEarningsQuotas detected")
    }
  }
}

@Serializable
data class UserDto(
  val id: String,
  val googleAdId: String? = null,
  val createdAt: InstantAsString,
  val countryCode: String,
  val limitedTrackingInfo: LimitedTrackingInfo,
  val appVersion: Int = 0,
  val isBanned: Boolean = false,
  val isWhitelisted: Boolean = false,
  val isDeleted: Boolean,
  val trackingData: TrackingData? = null,
  val appPlatform: AppPlatform,
  val lastActiveAtDay: LocalDateAsString? = null,
  val offerWallTypes: List<OfferWallType>? = null,
  val timeZone: String? = null, // timezone in format like "Europe/Berlin"
  val locale: LocaleAsString = DEFAULT_USER_LOCALE
// please make sure it's still possible to retrieve previously cached value after you add a property here
// also check if you have cache invalidation for the new property
// cache is here: CachedUserDataService
)

fun UserDto.appVersionDto() = AppVersionDto(this.appPlatform, this.appVersion)

data class AdditionalCountryInfo(
  val userId: String,
  val simCountry: String?,
  val networkCountry: String?
)

data class UserTrackingData(
  val userId: String,
  val trackingData: TrackingData,
)

@Serializable
data class LimitedTrackingInfo(
  val isLimitedTracking: Boolean,
  val countryCode: String,
  val isConsentedToAnalytics: Boolean,
) {
  fun isNonConsensualGdprUser(marketService: MarketService) =
    marketService.isGdprAppliesToCountry(countryCode) && !isConsentedToAnalytics

  fun isLimited(marketService: MarketService) =
    isLimitedTracking || isNonConsensualGdprUser(marketService)
}

data class UserDeviceSpecs(
  val osVersion: String? = null,
  val modelName: String? = null,
  val ramSize: Int? = null,
  val fontScale: BigDecimal? = null,
  val density: Int? = null,
  val densityScaleFactor: BigDecimal? = null
)

data class LibraryConsent(
  val library: String,
  val isConsented: Boolean,
)
