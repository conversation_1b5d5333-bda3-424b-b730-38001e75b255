package com.moregames.playtime.user

import com.google.inject.Inject
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.user.UserBonusBalanceType
import com.moregames.playtime.earnings.CashoutSettingsService
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.tracking.AdjustService
import com.moregames.playtime.user.UserService.Companion.DEFAULT_RANDOMLY_COIN_REWARD

class UserRandomBonusCoinsService @Inject constructor(
  private val abTestingService: AbTestingService,
  private val cashoutSettingsService: CashoutSettingsService,
  private val adjustService: AdjustService,
  private val userService: UserService,
  private val rewardingFacade: RewardingFacade,
) {

  suspend fun addRandomlyCoinsToUser(userId: String) {
    val coinsAmount = if (abTestingService.isEm2Participant(userId)) {
      rewardingFacade.randomCoinsUsdAmountForEm2(userId)
        ?.takeIf {
          adjustService.isOrganic(userId) == false &&
            userService.countUsersWithSameGoogleAdId(userId) == 0L
        }
        ?.multiply(cashoutSettingsService.getIronSourceCoinsToUsdConversionRatio())
        ?.toInt()
    } else {
      DEFAULT_RANDOMLY_COIN_REWARD
    }

    if (coinsAmount == null) return

    val user = userService.getUser(userId)

    rewardingFacade.addBonusCoinsIfNotExists(
      userId = userId,
      appPlatform = user.appPlatform,
      coinsAmount = coinsAmount,
      bonusBalanceType = UserBonusBalanceType.AB_COIN_REWARD_AFTER_X_MINUTES
    )
  }
}