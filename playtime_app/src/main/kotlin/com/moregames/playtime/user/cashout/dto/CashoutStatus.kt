package com.moregames.playtime.user.cashout.dto

import com.moregames.base.config.CashoutConfig
import com.moregames.playtime.util.roundDownToSecondDigit
import java.math.BigDecimal
import java.time.Instant
import java.util.*

data class CashoutStatus(
  val isEnabled: <PERSON>olean,
  val headerText: String,
  val iconFilename: String,
  val nextCashout: Instant,
  val currency: Currency,
  val amount: BigDecimal,
  val bonusAmount: BigDecimal?,
  val amountBefore: BigDecimal?,
  val amountUsd: BigDecimal,
  val providers: List<CashoutProvider>,
  val disclaimer: String,
  val userHasCashouts: Boolean,
) {
  companion object {
    fun disabled(cashoutConfig: CashoutConfig, currency: Currency, nextCashout: Instant, userHasCashouts: Boolean) = CashoutStatus(
      isEnabled = false,
      headerText = cashoutConfig.headerText(false),
      iconFilename = cashoutConfig.iconFilename(false),
      nextCashout = nextCashout,
      currency = currency,
      amount = BigDecimal.ZERO.roundDownToSecondDigit(),
      bonusAmount = null,
      amountBefore = null,
      amountUsd = BigDecimal.ZERO.roundDownToSecondDigit(),
      providers = emptyList(),
      disclaimer = cashoutConfig.disclaimer,
      userHasCashouts = userHasCashouts,
    )
  }
}
