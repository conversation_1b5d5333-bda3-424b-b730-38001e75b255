package com.moregames.playtime.user.verification

import com.google.inject.Inject
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.exceptions.ParameterRequiredException
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.user.appVersion
import com.moregames.base.user.dto.UserRequestMetadata
import com.moregames.base.util.Constants.VERIFICATION_SESSION_HEADER
import com.moregames.base.util.getRequestFromBodyOrNull
import com.moregames.playtime.app.PlaytimeFeatureFlags
import com.moregames.playtime.ios.examination.dto.IosExaminationChallengeApiDto
import com.moregames.playtime.ios.examination.dto.IosExaminationRequestApiDto
import com.moregames.playtime.user.EmailValidationService
import com.moregames.playtime.user.ParamExtractor
import com.moregames.playtime.user.UserController.Companion.iosExaminationEnv
import com.moregames.playtime.user.UserController.Companion.userAgent
import com.moregames.playtime.user.UserController.Companion.userId
import com.moregames.playtime.user.UserHandleValidationService
import com.moregames.playtime.user.WebAppGpsLocationCheckService
import com.moregames.playtime.user.dto.InitiateVerificationRequestDto
import com.moregames.playtime.user.verification.dto.GpsLocationRequestApiDto
import com.moregames.playtime.user.verification.dto.JailBreakRequestApiDto
import com.moregames.playtime.user.verification.dto.VerifyFaceLivenessRequestDto
import com.moregames.playtime.user.verification.dto.email.EmailValidationRequestApiDto
import com.moregames.playtime.user.verification.dto.email.EmailValidationResponseApiDto
import com.moregames.playtime.user.verification.dto.userhandle.UserHandleValidationRequestApiDto
import com.moregames.playtime.user.verification.dto.userhandle.UserHandleValidationResponseApiDto
import com.moregames.playtime.user.verification.exception.InvalidSessionIdException
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.http.HttpStatusCode.Companion.NotFound
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.request.*
import io.ktor.response.*
import io.ktor.routing.*
import kotlinx.serialization.ExperimentalSerializationApi
import javax.inject.Singleton

@OptIn(ExperimentalSerializationApi::class)
@Singleton
class VerificationController @Inject constructor(
  private val verificationService: VerificationService,
  private val retryableVerifyFaceService: RetryableVerifyFaceService,
  private val emailValidationService: EmailValidationService,
  private val userHandleValidationService: UserHandleValidationService,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val applicationConfig: ApplicationConfig,
  private val webAppGpsLocationCheckService: WebAppGpsLocationCheckService
) {
  companion object {
    val sessionId: ParamExtractor = { call.request.header(VERIFICATION_SESSION_HEADER) ?: throw InvalidSessionIdException() }
  }

  fun startRouting(root: Route) {
    root.route("/verification") {
      post("/initiate") {
        val request = getRequestFromBodyOrNull<InitiateVerificationRequestDto>()
        call.respond(
          verificationService.initiateVerification(
            userId(),
            request?.provider,
            request?.email,
            userIp = UserRequestMetadata.from(call.request, applicationConfig.isK8s).forwardedIp,
            appVersion = appVersion(),
          )
        )
      }

      post("/verify-face") {
        val request = call.receive<VerifyFaceLivenessRequestDto>()
        if (featureFlagsFacade.boolValue(PlaytimeFeatureFlags.FACE_VERIFICATION_RETRIES, false)) {
          when (val verifyFaceResult = retryableVerifyFaceService.verifyFace(sessionId(), userAgent(), request)) {
            VerifyFaceResult.InProgress -> call.respond(HttpStatusCode(429, "Too early"))
            is VerifyFaceResult.Failure -> call.respond(verifyFaceResult.response)
            is VerifyFaceResult.Success -> call.respond(verifyFaceResult.response)
          }
        } else {
          call.respond(verificationService.verifyFace(sessionId(), userAgent(), request))
        }
      }

      get("/examination") {
        call.respond(
          IosExaminationChallengeApiDto(
            challenge = verificationService.generateIosChallenge(sessionId())
          )
        )
      }

      post("/examination") {
        val request = call.receive<IosExaminationRequestApiDto>()
        verificationService.examineIosDevice(sessionId(), request, iosExaminationEnv())
        call.respond(OK)
      }

      post("/jailBreak") {
        val request = call.receive<JailBreakRequestApiDto>()
        verificationService.verifyIosJailBreak(sessionId(), request)
        call.respond(OK)
      }

      post("/location") {
        val request = call.receive<GpsLocationRequestApiDto>()
        verificationService.verifyGpsLocation(
          sessionId = sessionId(),
          location = request.location,
          isMocked = request.isMocked,
          appPlatform = appVersion().platform
        )
        call.respond(OK)
      }

      get("applicationId-for-location-check") {
        val applicationId = webAppGpsLocationCheckService.getApplicationId(userId())
        if (applicationId != null) {
          call.respond(GpsLocationCheckApplicationIdApiDto(applicationId))
        } else {
          call.respond(NotFound)
        }
      }

      post("/validateEmail") {
        val request = call.receive<EmailValidationRequestApiDto>()
        if (request.email.isNullOrBlank()) throw ParameterRequiredException("email")
        call.respond(EmailValidationResponseApiDto(emailValidationService.isRawEmailValid(request.email)))
      }

      post("/validateUserHandle") {
        val request = call.receive<UserHandleValidationRequestApiDto>()
        if (request.userHandle.isNullOrBlank()) throw ParameterRequiredException("userHandle")
        call.respond(UserHandleValidationResponseApiDto(userHandleValidationService.isUserHandleValid(request.userHandle)))
      }

    }
  }
}
