package com.moregames.playtime.user

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.ClientExperiment.*
import com.moregames.base.abtesting.Variations.*
import com.moregames.base.abtesting.variations.AndroidDemoGamesVariation
import com.moregames.base.abtesting.variations.AndroidOnboardingProgressBarVariation
import com.moregames.base.abtesting.variations.PaymentProviderSurveyVariation
import com.moregames.base.app.BuildVariant
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.user.appVersion
import com.moregames.base.user.dto.UserRequestMetadata
import com.moregames.base.util.ClientVersionsSupport.ANDROID_THREE_DOT_OPT_OUT_APP_VERSION
import com.moregames.base.util.ClientVersionsSupport.getDesiredAppVersion
import com.moregames.base.util.Constants.MARKET_HEADER
import com.moregames.base.util.TimeService
import com.moregames.base.util.format
import com.moregames.base.util.logger
import com.moregames.playtime.administration.qa.QaUserSettingsService
import com.moregames.playtime.boost.BoostedModeService
import com.moregames.playtime.boost.BoostedModeTranslationService
import com.moregames.playtime.boost.model.BoostedMode
import com.moregames.playtime.buseffects.AmplitudeEventEffectHandler
import com.moregames.playtime.cashstreak.CashStreakController
import com.moregames.playtime.checks.ExaminationController
import com.moregames.playtime.checks.ExaminationService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.general.TrackedEventsService
import com.moregames.playtime.ios.cashoutcoins.CashoutCoinsService
import com.moregames.playtime.ios.dto.IosExaminationEnv
import com.moregames.playtime.notifications.status.UserNotificationStatusService
import com.moregames.playtime.notifications.status.UserNotificationsStatusApiDto
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.translations.AndroidCoinsRenamingExpService
import com.moregames.playtime.translations.TranslationResource.*
import com.moregames.playtime.translations.UntranslatedStringsStorage.EM3_GOAL_BAR_MAIN_LABEL
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.bonuscashbar.BonusCashBarController
import com.moregames.playtime.user.cashout.*
import com.moregames.playtime.user.cashout.dto.AndroidCashoutProgressBarMode
import com.moregames.playtime.user.cashout2xoffer.Cashout2xOfferService
import com.moregames.playtime.user.challenge.ChallengeEventController
import com.moregames.playtime.user.coingoal.CoinGoalVariationsExpService
import com.moregames.playtime.user.dto.*
import com.moregames.playtime.user.dto.OnboardingProgressBarApiDto.OnboardingProgressBarStatus
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuActionApiDto
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuActionApiDto.Companion.accountDeletion
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuActionApiDto.Companion.openLinkInPopUp
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuActionApiDto.Companion.optOut
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuActionApiDto.Companion.tutorialHub
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuItem
import com.moregames.playtime.user.gamerank.UserGameRankService
import com.moregames.playtime.user.gamestories.AndroidGameStoriesModeApiDto
import com.moregames.playtime.user.gamestories.AndroidGameStoriesService
import com.moregames.playtime.user.highlightedgames.AndroidHighlightedGamesService
import com.moregames.playtime.user.offer.AndroidOffersController
import com.moregames.playtime.user.offer.WelcomeCoinsService
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarService
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarStepStatus
import com.moregames.playtime.user.promotion.event.manager.PromotionEventService
import com.moregames.playtime.user.survey.SurveyController
import com.moregames.playtime.user.survey.paymentprovider.PaymentProviderSurveyService
import com.moregames.playtime.user.toprunningbar.TopRunningBarService
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.user.tutorial.TutorialService
import com.moregames.playtime.user.usergame.UserGameService
import com.moregames.playtime.user.verification.VerificationController
import com.moregames.playtime.util.base64Encoded
import com.moregames.playtime.util.getLocale
import com.moregames.playtime.util.roundDownToSecondDigit
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.http.HttpStatusCode.Companion.NotFound
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.request.*
import io.ktor.response.*
import io.ktor.routing.*
import io.ktor.util.pipeline.*
import kotlinx.serialization.ExperimentalSerializationApi
import java.math.BigDecimal
import java.util.*
import javax.inject.Singleton

typealias ParamExtractor = PipelineContext<Unit, ApplicationCall>.() -> String

@Singleton
@ExperimentalSerializationApi
class UserController @Inject constructor(
  private val userService: UserService,
  private val cashoutService: CashoutService,
  private val cashoutPeriodsService: CashoutPeriodsService,
  private val cashoutStatusService: CashoutStatusService,
  private val abTestingService: AbTestingService,
  private val trackedEventsService: TrackedEventsService,
  private val userPopupMessagesService: UserPopupMessagesService,
  private val marketService: MarketService,
  private val timeService: TimeService,
  private val cashoutController: CashoutController,
  private val examinationController: ExaminationController,
  private val notificationsController: NotificationsController,
  private val androidOffersController: AndroidOffersController,
  private val paymentsController: PaymentsController,
  private val verificationController: VerificationController,
  private val surveyController: SurveyController,
  private val topRunningBarService: TopRunningBarService,
  private val userNotificationStatusService: UserNotificationStatusService,
  private val qaUserSettingsService: QaUserSettingsService,
  private val tutorialService: TutorialService,
  private val translationService: UserTranslationService,
  private val examinationService: ExaminationService,
  private val buildVariantProvider: Provider<BuildVariant>,
  private val applicationConfig: ApplicationConfig,
  private val androidOnlineUsersService: AndroidOnlineUsersService,
  private val userGameService: UserGameService,
  private val paymentProviderSurveyService: PaymentProviderSurveyService,
  private val cashoutCoinsService: CashoutCoinsService,
  private val faceScanPreScreenService: FaceScanPreScreenService,
  private val paymentProvidersService: PaymentProvidersService,
  private val amplitudeEventEffectHandler: AmplitudeEventEffectHandler,
  private val hideCashoutAmountExperimentService: HideCashoutAmountExperimentService,
  private val promotionEventService: PromotionEventService,
  private val challengeEventController: ChallengeEventController,
  private val cashout2xOfferService: Cashout2xOfferService,
  private val androidCoinsRenamingExpService: AndroidCoinsRenamingExpService,
  private val onboardingProgressBarService: OnboardingProgressBarService,
  private val coinGoalVariationsExpService: CoinGoalVariationsExpService,
  private val androidHighlightedGamesService: AndroidHighlightedGamesService,
  private val androidAnimationToCelebrateEarningsService: AndroidAnimationToCelebrateEarningsService,
  private val cashStreakController: CashStreakController,
  private val rewardingFacade: RewardingFacade,
  private val androidGameStoriesService: AndroidGameStoriesService,
  private val welcomeCoinsService: WelcomeCoinsService,
  private val boostedModeService: BoostedModeService,
  private val boostedModeTranslationService: BoostedModeTranslationService,
  private val bonusCashBarController: BonusCashBarController,
  private val userGameRankService: UserGameRankService,
) {
  fun startRouting(playtimeRoute: Route) {
    playtimeRoute.route("/users/") {
      examinationController.startRouting(this)

      route("/{$USER_ID_PARAMETER}") {
        intercept(ApplicationCallPipeline.Call) {
          userService.onUserSpecificRequest(
            userId = userId(),
            userRequestMetadata = UserRequestMetadata.from(call.request, applicationConfig.isK8s),
            appVersion = appVersion(),
            locale = getLocale(logger()),
            market = call.request.headers[MARKET_HEADER],
          )
        }
        user()
        userData()
        coins()
        topRunningBarConfiguration()
        notifications()
        promotionEventConfig()
        cashoutController.startRouting(this)
        notificationsController.startRouting(this)
        androidOffersController.startRouting(this)
        paymentsController.startRouting(this)
        verificationController.startRouting(this)
        surveyController.startRouting(this)
        challengeEventController.startRouting(this)
        cashout2xOffer()
      }
    }

    playtimeRoute.route("/android") {
      route("/users") {
        playersOnline()

        route("/{$USER_ID_PARAMETER}") {
          intercept(ApplicationCallPipeline.Call) {
            userService.onUserSpecificRequest(
              userId = userId(),
              userRequestMetadata = UserRequestMetadata.from(call.request, applicationConfig.isK8s),
              appVersion = appVersion(),
              locale = getLocale(logger()),
              market = call.request.headers[MARKET_HEADER],
            )
          }

          userGames()
          paymentProviderSurvey()
          onboardingProgressBar()
          highlightedGames()
          gameStories()
          cashStreakController.startRouting(this)
          bonusCashBarController.startRouting(this)
        }
      }
    }
  }

  private fun Route.onboardingProgressBar() {
    get("/onboarding-progress-bar") {
      val userId = userId()
      val progressBarSteps = onboardingProgressBarService.getProgressBar(userId)
      val status = if (progressBarSteps.filter { !it.isOptional }.all { it.status == OnboardingProgressBarStepStatus.COMPLETED }) {
        OnboardingProgressBarStatus.COMPLETED
      } else {
        OnboardingProgressBarStatus.INCOMPLETE
      }
      call.respond(
        OnboardingProgressBarApiDto(
          progressBarStatus = status,
          steps = progressBarSteps.map { it.toApiDto() }
        )
      )
    }
  }

  private fun Route.notifications() {
    post("/user-notifications-state") {
      val areEnabled = call.receive<UserNotificationsStatusApiDto>().enabled
      userNotificationStatusService.setUserNotificationsStatus(userId(), areEnabled)
      call.respond(OK)
    }
  }

  private fun Route.user() {
    get {
      val locale = getLocale(logger())
      var user = userService.loadCoinGoalUser(userId())
      val availableCashout = cashoutService.getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(user.userId)
      val availableCashoutAmount = availableCashout.userCurrencyAmount.roundDownToSecondDigit().takeIf { it > BigDecimal.ZERO }
      val availableCashoutAmountBefore =
        availableCashout.nonBoostedUserCurrencyAmount?.roundDownToSecondDigit()
          ?.takeIf { availableCashoutAmount != null }
      val cashoutPeriod = cashoutPeriodsService.getCurrentCashoutPeriod(user.userId)
      val cashoutAvailable = cashoutStatusService.isCashoutEnabled(user.userId) && availableCashoutAmount != null
      val coinsBalance = rewardingFacade.getUserCurrentCoinsBalance(user.userId, user.appPlatform)
      val abInflatingCoinsMultiplier = rewardingFacade.inflatingCoinsMultiplier(user.userId)
      val coinGoal = cashoutPeriod.coinGoal * abInflatingCoinsMultiplier
      val coinGoalReached = coinsBalance.goalCoins >= coinGoal

      with(appVersion()) {
        if (version < getDesiredAppVersion(platform)) {
          call.response.header(DESIRED_APP_VERSION_HEADER, getDesiredAppVersion(platform))
        }
      }

      trackedEventsService.prepareHeadersToSend(user.userId).forEach {
        call.response.header(it.key, it.value)
      }
      userPopupMessagesService.getMessageForUser(user.userId, EndpointType.GET_USER, cashoutAvailable)?.also {
        call.response.header(POPUP_MESSAGE_HEADER, it.base64Encoded())
      }
      val showTimerInCoinGoalSection = availableCashout.userCurrencyAmount >= BigDecimal("0.01") &&
        abTestingService.shouldShowTimerInCoinGoal(user.userId)

      val boostedMode = boostedModeService.findCurrentBoostedMode(user.userId)

      user = fillExpLabels(user, locale, boostedMode)

      val userHasCashouts = cashoutService.userHasCashouts(user.userId)
      val result = UserApiDto(
        user = user,
        coinsBalance = coinsBalance,
        cashoutAvailable = cashoutAvailable,
        cashoutAmount = availableCashoutAmount?.format(availableCashout.userCurrency) ?: "",
        cashoutAmountBefore = availableCashoutAmountBefore?.format(availableCashout.userCurrency),
        nextCashoutTimestamp = cashoutPeriod.periodEnd,
        useRewards = userHasCashouts,
        timestamp = timeService.now(),
        tutorialSteps = if (qaUserSettingsService.shouldSkipOnboarding(userId())) emptyList() else tutorialService.getTutorialSteps(user.userId),
        videoAdIntervalSeconds = abTestingService.getVideoOfferCoolDownSeconds(user.userId),
        showTimerInCoinGoalSection = showTimerInCoinGoalSection,
        threeDotMenuItems = buildThreeDotMenuConfig(locale, appVersion(), user.userId),
        useAmplitudeAnalytics = amplitudeEventEffectHandler.shouldUseAmplitudeAnalytics(user.userId),
        market = applicationConfig.justplayMarket,
        attestationRequired = !examinationService.wasSuccessfullyExamined(user.userId),
        initializeApplovin = !userService.useAndroidLazyApplovinInitialization(user.userId),
        consentedToAnalytics = !marketService.isGdprAppliesToCountry(user.countryCode) || user.isConsentedToAnalytics,
        coinGoal = coinGoal,
        coinGoalReached = coinGoalReached,
        giftBoxInsteadOfEarnings = hideCashoutAmountExperimentService.shouldShowGiftBox(user.userId, userHasCashouts, availableCashout.amountUsd),
        cashoutButtonStyle = CashoutButtonStyle.from(abTestingService.assignedVariationValue(user.userId, ANDROID_HIDE_EARNINGS)),
        cashoutTimerSubtext = translationService.translateOrDefault(CASHOUT_TIMER_SUBTEXT, locale, user.userId),
        paymentProviderSurvey = abTestingService.assignedVariationValue(user.userId, ANDROID_PAYMENT_PROVIDER_SURVEY)
          .takeIf { it is PaymentProviderSurveyVariation }
          ?.let { PaymentProviderSurvey.fromVariation(it as PaymentProviderSurveyVariation) },
        cashoutProgressBarMode = AndroidCashoutProgressBarMode.fromVariationOrNull(
          abTestingService.assignedVariationValue(
            user.userId,
            ANDROID_CASHOUT_PROGRESS_BAR
          )
        ),
        incompleteCashoutRestoringMode = AndroidIncompleteCashoutRestoringMode.fromVariationOrNull(
          abTestingService.assignedVariationValue(
            user.userId,
            ANDROID_INCOMPLETE_CASHOUT_RESTORING
          )
        ),
        cashoutBonusCoins = cashoutCoinsService.createApiDto(user.userId, userHasCashouts, ANDROID),
        faceScanPreScreen = faceScanPreScreenService.createApiDto(
          userId = user.userId,
          userIp = UserRequestMetadata.from(call.request, applicationConfig.isK8s).forwardedIp,
          appVersion = appVersion(),
          locale = locale,
        ),
        paymentProviderAvailable = paymentProvidersService.isPaymentProvidersAvailable(userHasCashouts, availableCashout.amountUsd),
        demoGamesLaunchMode = (abTestingService.assignedVariationValue(
          userId = user.userId,
          experiment = ANDROID_DEMO_GAMES
        ) as? AndroidDemoGamesVariation)?.let { DemoGameLaunchModeApiDto.fromVariation(it) },
        privacyRegulation = marketService.getPrivacyRegulation(user.countryCode),
        preGameMode = PreGameModeApiDto.from(abTestingService.assignedVariationValue(user.userId, ANDROID_TASKS_IN_PRE_GAME)),
        onboardingProgressBarMode = (abTestingService.assignedVariationValue(
          user.userId,
          ANDROID_ONBOARDING_PROGRESS_BAR
        ) as? AndroidOnboardingProgressBarVariation)?.getKey(),
        celebrateEarningsConfig = androidAnimationToCelebrateEarningsService.getCelebrateEarningsConfig(user.userId),
        showPayPalLogo = abTestingService.isUserExperimentParticipant(user.userId, ClientExperiment.ANDROID_SHOW_PAYPAL_LOGO).takeIf { it },
        cashStreakMode = AndroidCashStreakModeApiDto.fromVariation(abTestingService.assignedVariationValue(user.userId, ANDROID_CASH_STREAK)),
        gameStoriesMode = AndroidGameStoriesModeApiDto.from(abTestingService.assignedVariationValue(user.userId, ANDROID_GAME_STORIES)),
        coinGoalBarMode = coinGoalBarMode(user.userId),
        cashoutPeriodId = cashoutPeriod.id,
        milestonesConfig = getMilestonesConfig(cashoutPeriod.coinGoalMilestones, abInflatingCoinsMultiplier),
        coinsConversionRatioLabelText = getCoinsConversionRatioLabelText(user.userId, userHasCashouts),
        boostedMode = boostedMode?.let { bm ->
          UserBoostedModeApiDto(
            coinsAfter = (coinsBalance.coins * bm.coinsCoefficient).toLong(),
            endTime = bm.visibleEndTime,
            hintText = bm.uiConfig.mainScreenHintTranslation?.let { translationService.tryTranslate(it, locale, user.userId) },
            hintId = bm.presetId.takeIf { bm.uiConfig.mainScreenHintTranslation != null }, // used just to track hint icons state
            colorBadge = bm.uiConfig.colorBadge,
            colorBadgeEnd = bm.uiConfig.colorBadgeEnd,
            colorTextBadge = bm.uiConfig.colorTextBadge,
            colorBack = bm.uiConfig.colorBack,
            colorBackEnd = bm.uiConfig.colorBackEnd,
            colorTextCoinsBefore = bm.uiConfig.colorTextCoinsBefore,
            colorTextCoinsNow = bm.uiConfig.colorTextCoinsNow,
          )
        },
        bonusCashBarAvailable = (abTestingService.assignedVariationValue(user.userId, BONUS_BANK) == BONUS_CASH_BAR)
      )

      call.respond(result)
    }
  }

  private fun Route.userData() {
    post("/googleAdId") {
      val googleAdId = call.receive<GoogleAdIdApiDto>().id
      val userId = userId()
      userService.updateGoogleAdId(userId, googleAdId, appVersion())
      call.respond(OK)
    }
    post("/appSetId") {
      val appSetId = call.receive<AppSetIdApiDto>().id
      val userId = userId()
      userService.addUserTrackingData(userId, TrackingData(appSetId, IDFV, ANDROID))
      call.respond(OK)
    }
    post("/deviceToken") {
      val deviceToken = call.receive<DeviceTokenApiDto>().token
      if (deviceToken.isNotEmpty()) {
        userService.updateDeviceToken(userId(), deviceToken, ANDROID)
      } else {
        logger().warn("Received empty device token for user ${userId()}")
      }
      call.respond(OK)
    }
    post("/firebaseAppInstanceId") {
      userService.addOrUpdateFirebaseAppInstanceId(userId(), call.receive<FirebaseAppInstanceIdDto>().firebaseAppInstanceId)
      call.respond(OK)
    }
    post("/adjustId") {
      userService.updateAdjustId(userId(), call.receive<AdjustIdDto>().adjustId)
      call.respond(OK)
    }
    post("/consent") {
      userService.updateUserConsent(userId(), call.receive())
      call.respond(OK)
    }
    post("/delete") {
      if (isUserRequestAccountDeletionEnabled()) {
        userService.requestUserDeletion(userId())
        call.respond(OK)
      } else
        call.respond(NotFound)
    }
    post("/videoAdReward") {
      userService.storeVideoReward(userId(), call.receive())
      call.respond(OK)
    }
  }

  private fun Route.coins() {
    // used by ios client as well
    post("/welcome-coins-acceptation") {
      val appPlatform = appVersion().platform
      val userId = userId()

      welcomeCoinsService.acceptWelcomeCoins(userId, appPlatform)

      call.respond(OK)
    }
  }

  private fun Route.topRunningBarConfiguration() {
    get("/top-running-bar") {
      val barConfig = topRunningBarService.getTopRunningBarConfig(userId())
      if (barConfig == null) {
        call.respond(NotFound, "No bar configuration for user")
        return@get
      }
      call.respond(barConfig)
    }
  }

  private fun Route.promotionEventConfig() {
    get("/promotion-event-configuration") {
      val config = promotionEventService.getPromotionEventConfig(userId(), getLocale(logger()))
      call.respond(config)
    }
  }

  private fun Route.playersOnline() {
    get("/online") {
      call.respond(mapOf("online" to androidOnlineUsersService.getActiveUsers()))
    }
  }

  private fun Route.userGames() {
    post("/pre-game-screen-opened") {
      userGameService.onPreGameScreenOpened(userId())
      call.respond(OK)
    }
  }

  private fun Route.paymentProviderSurvey() {
    post("/provider-survey") {
      val survey = call.receive<PaymentProviderSurveyApiDto>()
      paymentProviderSurveyService.saveSurveyResult(userId(), survey.userChoice, survey.ownAnswer, ANDROID)
      call.respond(OK)
    }
  }

  private fun Route.highlightedGames() {
    get("/highlighted-games") {
      call.respond(androidHighlightedGamesService.loadHighlightedGames(userId(), getLocale(logger())))
    }
  }

  private fun Route.gameStories() {
    get("/game-stories") {
      call.respond(androidGameStoriesService.getStories(userId(), getLocale(logger())))
    }
  }

  private fun Route.cashout2xOffer() {
    post("/cashout-2x-offer-accepted") {
      cashout2xOfferService.trackOfferAccepted(userId())
      call.respond(OK)
    }
  }

  private suspend fun fillExpLabels(user: User, locale: Locale, boostedMode: BoostedMode?) =
    user.copy(
      expLabels = user.expLabels +
        tutorialService.getAdditionalTranslations(user.userId) +
        coinGoalTextExpLabels(user.userId) +
        earnPlayingGamesTextExpLabels(user.userId) +
        renameCoinsExpLabels(user.userId) +
        coinGoalVariationsExpService.getReachCoinGoalReplacements(user.userId) +
        boostedModeExpLabels(boostedMode, locale) +
        em3ExpLabels(user.userId)
    )

  private suspend fun coinGoalTextExpLabels(userId: String): Map<String, String> =
    when (abTestingService.assignedVariationValue(userId, ANDROID_COIN_GOAL_TEXT)) {
      LONG_AGGRESSIVE -> mapOf("mainCoinGoalLabel" to "Hit Goal by Countdown for Guaranteed High Payouts!")
      SHORTER_AGGRESSIVE -> mapOf("mainCoinGoalLabel" to "Hit Goal for Guaranteed High Payouts!")
      SHORTER -> mapOf("mainCoinGoalLabel" to "Hit goal before timer ends")
      SHORT_AGGRESSIVE -> mapOf("mainCoinGoalLabel" to "Guaranteed High Payouts!")
      else -> emptyMap()
    }


  private suspend fun earnPlayingGamesTextExpLabels(userId: String): Map<String, String> =
    when (abTestingService.assignedVariationValue(userId, ANDROID_EARN_PLAYING_GAMES_TEXT)) {
      LOYALTY_NORMAL -> mapOf("earn_playing_games" to "Loyalty Program Games:")
      LOYALTY_FIRE -> mapOf("earn_playing_games" to """🔥 Loyalty Program Games 🔥""")
      LOYALTY_DOLLARS -> mapOf("earn_playing_games" to """$$ Loyalty Program Games $$""")
      NO_TEXT -> mapOf("earn_playing_games" to "")
      else -> emptyMap()
    }

  private suspend fun renameCoinsExpLabels(userId: String): Map<String, String> =
    androidCoinsRenamingExpService.getReplacements(userId).filterNot { it.key.startsWith("\$_") }

  private suspend fun boostedModeExpLabels(boostedMode: BoostedMode?, locale: Locale): Map<String, String> =
    boostedMode
      ?.let { boostedModeTranslationService.getAppTranslations(locale, boostedMode) }
      ?: emptyMap()

  private suspend fun em3ExpLabels(userId: String): Map<String, String> =
    if (abTestingService.isEm3Participant(userId))
    // TODO: "5 minutes" in string is hardcoded for now, but should be synced with Rewarding:
    //  com.justplayapps.service.rewarding.earnings.em3.Em3CoinsService#CHECK_INTERVAL
      mapOf(
        "mainCoinGoalLabel" to EM3_GOAL_BAR_MAIN_LABEL,
        "mainCoinGoalReachedLabel" to EM3_GOAL_BAR_MAIN_LABEL,
      )
    else emptyMap()

  private suspend fun buildThreeDotMenuConfig(locale: Locale, appVersion: AppVersionDto, userId: String): List<ThreeDotMenuItem> {
    val items = mutableListOf(
      ThreeDotMenuItem(ThreeDotMenuActionApiDto.routeToRewards(), translationService.translateOrDefault(MENU_ITEM_MY_REWARDS, locale, userId)),
      ThreeDotMenuItem(ThreeDotMenuActionApiDto.contactUs(), translationService.translateOrDefault(MENU_ITEM_CONTACT_US, locale, userId)),
      ThreeDotMenuItem(
        openLinkInPopUp("https://justplayapps.com/privacy-policy/"),
        translationService.translateOrDefault(MENU_ITEM_PRIVACY_POLICY, locale, userId),
      ),
      ThreeDotMenuItem(
        openLinkInPopUp("https://justplayapps.com/loyalty-program-rules/"),
        translationService.translateOrDefault(MENU_ITEM_FAQ, locale, userId),
      ),
    )

    if (appVersion.platform == ANDROID) {
      items.add(
        ThreeDotMenuItem(
          tutorialHub(),
          translationService.translateOrDefault(MENU_ITEM_TUTORIAL_HUB, locale, userId),
        )
      )
      if (appVersion.version >= ANDROID_THREE_DOT_OPT_OUT_APP_VERSION) {
        items.add(
          ThreeDotMenuItem(
            optOut(),
            label = "Opt-out" // should be USA-only so translations not needed so far.
          )
        )
      }
      if (isUserRequestAccountDeletionEnabled()) {
        items.add(
          ThreeDotMenuItem(
            accountDeletion(),
            translationService.translateOrDefault(MENU_ITEM_ACCOUNT_DELETION, locale, userId),
          )
        )
      }
      userGameRankService.getThreeDotMenuItem(userId, locale)?.let { items.add(it) }
    }

    return items
  }

  private fun isUserRequestAccountDeletionEnabled(): Boolean =
    buildVariantProvider.get() != BuildVariant.PRODUCTION // https://app.asana.com/0/****************/****************

  private suspend fun coinGoalBarMode(userId: String): UserApiDto.CoinGoalBarMode? =
    if (abTestingService.isEm3Participant(userId)) UserApiDto.CoinGoalBarMode.EM3
    else null

  private suspend fun getCoinsConversionRatioLabelText(userId: String, userHasCashouts: Boolean): String? {
    return if (abTestingService.isUserExperimentParticipant(userId, ClientExperiment.ANDROID_SHOW_COINS_CONVERSION_RATIO)
      && !userHasCashouts && abTestingService.isEm2Participant(userId)
    ) {
      "<font color=\"0x828282\"><b>880,000c = $1</b> *Rates may vary based on real revenue.</font>"
    } else {
      null
    }
  }

  private fun getMilestonesConfig(userMilestones: List<Int>, abInflatingCoinsMultiplier: Int): List<UserApiDto.MilestoneDesc> =
    userMilestones
      // TODO: list (3, 3, 5) should not be hardcoded, but fetched from Rewarding.
      //  will do after merging of https://github.com/JustPlayApps/justplay-backend/pull/1744
      .zip(listOf(3, 3, 5))
      .map { (milestone, reward) ->
        UserApiDto.MilestoneDesc(
          milestone = milestone * abInflatingCoinsMultiplier,
          reward = reward * abInflatingCoinsMultiplier,
        )
      }

  companion object {
    const val DESIRED_APP_VERSION_HEADER = "X-Playtime-Desired-App-Version"
    private const val IOS_APP_ENV_HEADER = "X-iOS-Env"

    const val POPUP_MESSAGE_HEADER = "X-MessageToUser"
    const val EXAMINATION_COOLDOWN_MINUTES = 10L
    const val USER_ID_PARAMETER = "userId"
    const val PACKAGE_ID_PARAMETER = "packageId"
    const val SANITY_CHECK_REQUEST = "sanityCheckRequest"
    val userId: ParamExtractor = { call.parameters[USER_ID_PARAMETER]!! }

    val userAgent: ParamExtractor = { call.request.header(HttpHeaders.UserAgent) ?: "" }
    val iosExaminationEnv: PipelineContext<Unit, ApplicationCall>.() -> IosExaminationEnv = {
      val iosAppEnvHeaderValue = call.request.headers[IOS_APP_ENV_HEADER]

      IosExaminationEnv.fromHeader(iosAppEnvHeaderValue)
        ?: throw IllegalStateException("Unknown app Env: '$iosAppEnvHeaderValue'")
    }
  }
}
