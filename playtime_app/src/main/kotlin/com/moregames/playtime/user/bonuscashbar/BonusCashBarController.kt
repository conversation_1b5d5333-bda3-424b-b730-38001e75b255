package com.moregames.playtime.user.bonuscashbar


import com.justplayapps.playtime.rewarding.bonusbank.bonusBankClaim
import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.commands.CommandIdResponse
import com.moregames.base.messaging.commands.CommandsClient
import com.moregames.base.user.appVersion
import com.moregames.base.util.extractRequiredParameter
import com.moregames.base.util.toProto
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.UserController.Companion.USER_ID_PARAMETER
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import kotlinx.serialization.ExperimentalSerializationApi
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class BonusCashBarController @Inject constructor(
  private val rewardingFacade: RewardingFacade,
  private val commandsClient: CommandsClient,
  private val messageBus: MessageBus,
) {
  @OptIn(ExperimentalSerializationApi::class)
  fun startRouting(route: Route) {
    route.route("/bonus-cash-bar") {
      get("/widget") {
        val userId = extractRequiredParameter(USER_ID_PARAMETER)

        val bonusCashBar = rewardingFacade.getBonusCashBarState(userId, appVersion().platform)

        if (bonusCashBar != null) call.respond(bonusCashBar)
        else call.respond(HttpStatusCode.NotFound, "Bonus cash bar is not available for user.")
      }

      post("/claim") {
        val userId = extractRequiredParameter(USER_ID_PARAMETER)

        val commandId = commandsClient.createCommand()

        messageBus.publish(
          bonusBankClaim {
            this.userId = userId
            this.commandId = commandId
            this.platform = appVersion().platform.toProto()
          }
        )

        val response = CommandIdResponse(
          commandId = commandId
        )

        call.respond(response)
      }
    }
  }
}