package com.moregames.playtime.user.exception

import com.moregames.base.exceptions.BaseException
import com.moregames.base.exceptions.ErrorType
import com.moregames.base.exceptions.PlaytimeErrorCodes

class AttestationRequiredException(userId: String) : BaseException(
  internalMessage = "Attestation required for '$userId'"
) {
  override val errorCode = PlaytimeErrorCodes.ATTESTATION_REQUIRED
  override val errorType = ErrorType.AUTHENTICATION_ERROR
}
