package com.moregames.playtime.user.challenge.dto.config

import com.moregames.playtime.user.challenge.dto.ChallengeType
import kotlinx.serialization.Serializable

@Serializable
data class SpecialChallengeWidgetsDto(
  val mainScreen: List<SpecialChallengeMenuItemDto>?,
  val specialChallengeScreen: SpecialChallengeScreenDto?,
  val claimWidget: SpecialChallengeClaimWidgetDto?,
)

@Serializable
data class SpecialChallengeMenuItemDto(
  val challengeType: ChallengeType,
  val imageUrl: String?,
  val title: String?,
)

@Serializable
data class SpecialChallengeScreenDto(
  val treasureImageUrl: String?,
)
@Serializable
data class SpecialChallengeClaimWidgetDto(
  val title: String?,
  val description: String?,
  val image: String?,
)