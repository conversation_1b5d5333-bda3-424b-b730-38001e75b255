package com.justplayapps.service.rewarding.bonusbank

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import assertk.assertions.isEqualTo
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.mock
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.math.BigDecimal
import kotlin.test.assertFailsWith

class BonusBankTest {

  private val bonusBankStorage: BonusBankStorage = mock()
  private val USER_ID = "userId"
  private val PLATFORM = AppPlatform.ANDROID

  @Test
  fun `SHOULD add new coins to bank ON cutCoins FOR BonusCashBarBank`() {
    val bank = BonusCashBarBank(
      coinGoal = 1_000_000.toBigDecimal(),
      milestones = listOf(
        MilestoneWithReward(250_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(500_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(750_000.toBigDecimal(), EM2_COINS(25_000.toBigDecimal())),
      ),
      reward = SingleReward(EM2_COINS(200_000.toBigDecimal()))
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(10_000, 0)

    runBlocking {
      bank.cutCoins(10_000.toBigDecimal())
    }
    verifyNewBalance(
      bankBalance = 18_000,
      lastClaimedBalance = 0,
    )
  }

  @Test
  fun `SHOULD add em2 coins to user ON claimReward FOR BonusCashBarBank`() {
    val bank = BonusCashBarBank(
      coinGoal = 1_000_000.toBigDecimal(),
      milestones = listOf(
        MilestoneWithReward(250_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(500_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(750_000.toBigDecimal(), EM2_COINS(25_000.toBigDecimal())),
      ),
      reward = SingleReward(EM2_COINS(200_000.toBigDecimal()))
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(2_300_000, 700_000)

    runBlocking {
      bank.claimReward()
    }

    verifyNewBalance(
      bankBalance = 300_000,
      lastClaimedBalance = 300_000,
    )
    verifyEm2CoinsClaimReward(495_000)
  }

  @Test
  fun `SHOULD add em2 coins to user ON claimReward FOR BonusCashBarBank IF only milestones reached`() {
    val bank = BonusCashBarBank(
      coinGoal = 1_000_000.toBigDecimal(),
      milestones = listOf(
        MilestoneWithReward(250_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(500_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(750_000.toBigDecimal(), EM2_COINS(25_000.toBigDecimal())),
      ),
      reward = SingleReward(EM2_COINS(200_000.toBigDecimal()))
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(800_000, 400_000)

    runBlocking {
      bank.claimReward()
    }

    verifyNewBalance(
      bankBalance = 800_000,
      lastClaimedBalance = 800_000,
    )
    verifyEm2CoinsClaimReward(40_000)
  }

  @Test
  fun `SHOULD add em2 coins to user valuables storage ON claimReward FOR PiggyBank`() {
    val bank = PiggyBank(
      coinGoal = 1_000_000.toBigDecimal(),
      reward = SingleReward(EM2_COINS(250_000.toBigDecimal()))
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(2_300_000, 0)

    runBlocking {
      bank.claimReward()
    }

    verifyNewBalance(
      bankBalance = 300_000,
      lastClaimedBalance = 300_000,
    )
    verifyEm2AddedToStorage(500_000)
  }

  @Test
  fun `SHOULD add em2 coins to user ON claimReward FOR CarouselBank`() {
    val bank = CarouselBank(
      coinGoal = 1_000_000.toBigDecimal(),
      reward = ChanceBasedReward(
        listOf(
          ChanceBased(1.toBigDecimal(), EM2_COINS(25_000.toBigDecimal())),
        )
      )
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(3_200_000, 0)

    runBlocking {
      bank.claimReward()
    }

    verifyNewBalance(
      bankBalance = 200_000,
      lastClaimedBalance = 200_000,
    )
    verifyEm2CoinsClaimReward(75_000)
  }

  @Test
  fun `SHOULD throw exception ON claimReward FOR CarouselBank IF balance less than coinGoal`() {
    val bank = CarouselBank(
      coinGoal = 1_000_000.toBigDecimal(),
      reward = ChanceBasedReward(
        listOf(
          ChanceBased(1.toBigDecimal(), EM2_COINS(25_000.toBigDecimal())),
        )
      )
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(900_000, 0)

    assertFailsWith<IllegalStateException> {
      runBlocking { bank.claimReward() }
    }
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN zero balances`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal.ZERO,
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = false,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(0, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN one bar zero milestones no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("124"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = false,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(124, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN one bar one milestone no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("249"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = false,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(249, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN one bar two milestones no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("374"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = false,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(374, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN one full bar no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal.ZERO,
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(500, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN second bar no milestones no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("124"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(624, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN second bar one milestone no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("249"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(749, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN second bar two milestones no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("374"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(874, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN two full bars no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("0"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(1000, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN third bar no milestones no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("124"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(1124, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN third bar one milestone no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("249"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(1249, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }


  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN third bar two milestones no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("374"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(1374, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN three full bars no claims`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("0"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(1500, 0)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN first bar no milestones claim before first milestone`() {
    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal("124"),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = false,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(124, 120)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN first bar one milestone claim before first milestone`() {
    val bankBalance = 249

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal(bankBalance),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = false,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, 120)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN first bar one milestone claim after first milestone`() {
    val bankBalance = 249

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal(bankBalance),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = false,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.CLAIMED
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, 200)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN first bar two milestones claim before first milestone`() {
    val bankBalance = 374

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal(bankBalance),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = false,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, 120)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }


  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN first bar two milestones claim before second milestone`() {
    val bankBalance = 374

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal(bankBalance),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = false,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.CLAIMED
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, 200)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }


  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN first bar two milestones claim after second milestone`() {
    val bankBalance = 374

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal(bankBalance),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = false,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.CLAIMED
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, 200)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }


  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN full bar claim before first milestone`() {
    val bankBalance = 500

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal.ZERO,
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, 100)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN full bar claim before second milestone`() {
    val bankBalance = 500

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal.ZERO,
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, 200)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD calculate state ON getBonusCashBarState WHEN full bar claim before third milestone`() {
    val bankBalance = 500

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal.ZERO,
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, 300)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }


  @ParameterizedTest
  @ValueSource(ints = [120, 200, 300])
  fun `SHOULD calculate state ON getBonusCashBarState WHEN second bar no milestones`(lastClaimedBalance: Int) {
    val bankBalance = 624

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = 124.toBigDecimal(),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, lastClaimedBalance)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @ParameterizedTest
  @ValueSource(ints = [120, 200, 300])
  fun `SHOULD calculate state ON getBonusCashBarState WHEN second bar one milestone different last claim`(lastClaimedBalance: Int) {
    val bankBalance = 749

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = 249.toBigDecimal(),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, lastClaimedBalance)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @ParameterizedTest
  @ValueSource(ints = [120, 200, 300])
  fun `SHOULD calculate state ON getBonusCashBarState WHEN second bar two milestones different last claim`(lastClaimedBalance: Int) {
    val bankBalance = 874

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = 374.toBigDecimal(),
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.READY_TO_CLAIM
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, lastClaimedBalance)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @ParameterizedTest
  @ValueSource(ints = [120, 200, 300])
  fun `SHOULD calculate state ON getBonusCashBarState WHEN full second bar different last claim`(lastClaimedBalance: Int) {
    val bankBalance = 1000

    val expected = BonusBarWithMilestonesState(
      coinGoal = 500.toBigDecimal(),
      coins = BigDecimal.ZERO,
      reward = EM2_COINS(100.toBigDecimal()),
      readyToClaim = true,
      milestones = listOf(
        BonusBankMilestoneState(
          coinGoal = BigDecimal("125"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("250"),
          reward = EM2_COINS(BigDecimal("7.5")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        ),
        BonusBankMilestoneState(
          coinGoal = BigDecimal("375"),
          reward = EM2_COINS(BigDecimal("10")),
          status = BonusBankMilestoneStatus.IN_PROGRESS
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(bankBalance, lastClaimedBalance)

    val actual = runBlocking { bank.getBonusBarWithMilestonesState() }

    assertStatesEqual(actual, expected)
  }

  @Test
  fun `SHOULD throw exception ON claim WHEN zero balances`() {
    val bank = sampleBonusCashBar()

    mockBalance(0, 0)

    assertFailsWith<IllegalStateException> { runBlocking { bank.claim() } }.let { exception ->
      assertThat(exception.message).isEqualTo("Claim is not possible")
    }
  }

  @Test
  fun `SHOULD throw exception ON claim WHEN no milestones reached AND no last claims`() {
    val bank = sampleBonusCashBar()

    mockBalance(120, 0)

    assertFailsWith<IllegalStateException> { runBlocking { bank.claim() } }.let { exception ->
      assertThat(exception.message).isEqualTo("Claim is not possible")
    }
  }

  @Test
  fun `SHOULD throw exception ON claim WHEN milestone reached AND last claim after milestone`() {
    val bank = sampleBonusCashBar()

    mockBalance(249, 200)

    assertFailsWith<IllegalStateException> { runBlocking { bank.claim() } }.let { exception ->
      assertThat(exception.message).isEqualTo("Claim is not possible")
    }
  }

  @Test
  fun `SHOULD throw exception ON claim WHEN second milestone reached AND last claim after second milestone`() {
    val bank = sampleBonusCashBar()

    mockBalance(374, 300)

    assertFailsWith<IllegalStateException> { runBlocking { bank.claim() } }.let { exception ->
      assertThat(exception.message).isEqualTo("Claim is not possible")
    }
  }

  @Test
  fun `SHOULD return claim data ON claim WHEN one milestone reached AND no last claims`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = null,
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("125"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          )
        )
      )
    )
    val expectedRewards = listOf(
      EM2_COINS(BigDecimal("7.5")),
    )

    val bank = sampleBonusCashBar()

    mockBalance(249, 0)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)

    val actualRewards = argumentCaptor<List<BaseRewardType>>()

    verifyBlocking(bonusBankStorage) { claimReward(eq(USER_ID), eq(AppPlatform.ANDROID), actualRewards.capture(), eq(false)) }
    assertRewardListsEqual(actualRewards.firstValue, expectedRewards)

    verifyBlocking(bonusBankStorage) {
      saveBonusBank(
        BonusBank(
          userId = USER_ID,
          bankBalance = 249.toBigDecimal(),
          lastClaimedBalance = 249.toBigDecimal(),
        )
      )
    }
  }

  @Test
  fun `SHOULD return claim data ON claim WHEN one milestone reached AND last claim before first milestone`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = null,
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("125"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          )
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(249, 120)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)
  }

  @Test
  fun `SHOULD return claim data ON claim WHEN two milestone reached AND no last claims`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = null,
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("125"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          )
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(374, 0)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)
  }

  @Test
  fun `SHOULD return claim data ON claim WHEN two milestone reached AND last claims before first milestone`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = null,
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("125"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          )
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(374, 100)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)
  }

  @Test
  fun `SHOULD return claim data ON claim WHEN two milestone reached AND last claims before second milestone`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = null,
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          )
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(374, 200)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)
  }

  @Test
  fun `SHOULD return claim data ON claim WHEN full first bar claimed AND no last claims`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = EM2_COINS(BigDecimal("100")),
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("125"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("375"),
            reward = EM2_COINS(BigDecimal("10.0")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(500, 0)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)
  }


  @Test
  fun `SHOULD return claim data ON claim WHEN full first bar claimed AND last claims before first milestone`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = EM2_COINS(BigDecimal("100")),
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("125"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("375"),
            reward = EM2_COINS(BigDecimal("10.0")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(500, 100)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)
  }


  @Test
  fun `SHOULD return claim data ON claim WHEN full first bar claimed AND last claims before second milestone`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = EM2_COINS(BigDecimal("100")),
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("375"),
            reward = EM2_COINS(BigDecimal("10.0")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(500, 200)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)
  }


  @Test
  fun `SHOULD return claim data ON claim WHEN full first bar claimed AND last claims before third milestone`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = EM2_COINS(BigDecimal("100")),
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("375"),
            reward = EM2_COINS(BigDecimal("10.0")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(500, 300)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)
  }


  @Test
  fun `SHOULD return claim data ON claim WHEN second bar claimed AND no milestones AND no last claims`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = EM2_COINS(BigDecimal("100")),
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("125"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("375"),
            reward = EM2_COINS(BigDecimal("10.0")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(600, 0)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)
  }

  @Test
  fun `SHOULD return claim data ON claim WHEN second bar claimed AND no milestones AND last claims before second milestone`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = EM2_COINS(BigDecimal("100")),
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("375"),
            reward = EM2_COINS(BigDecimal("10.0")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(600, 200)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)
  }

  @Test
  fun `SHOULD return claim data ON claim WHEN second bar claimed AND two milestones AND last claims before second milestone`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = EM2_COINS(BigDecimal("100")),
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("375"),
            reward = EM2_COINS(BigDecimal("10.0")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      ),
      BonusBankWithMilestonesClaim(
        reward = null,
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("125"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      )
    )

    val bank = sampleBonusCashBar()

    mockBalance(700, 200)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)
  }

  @Test
  fun `SHOULD return claim data ON claim WHEN two full bars AND last claims before second milestone`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = EM2_COINS(BigDecimal("100")),
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("375"),
            reward = EM2_COINS(BigDecimal("10.0")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      ),
      BonusBankWithMilestonesClaim(
        reward = EM2_COINS(BigDecimal("100")),
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("125"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("375"),
            reward = EM2_COINS(BigDecimal("10.0")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      )
    )
    val expectedRewards = listOf(
      EM2_COINS(BigDecimal("242.5")),
    )

    val bank = sampleBonusCashBar()

    mockBalance(1000, 200)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)

    val actualRewards = argumentCaptor<List<BaseRewardType>>()

    verifyBlocking(bonusBankStorage) { claimReward(eq(USER_ID), eq(AppPlatform.ANDROID), actualRewards.capture(), eq(false)) }
    assertRewardListsEqual(actualRewards.firstValue, expectedRewards)

    verifyBlocking(bonusBankStorage) {
      saveBonusBank(
        BonusBank(
          userId = USER_ID,
          bankBalance = 0.toBigDecimal(),
          lastClaimedBalance = 0.toBigDecimal(),
        )
      )
    }
  }

  @Test
  fun `SHOULD return claim data ON claim WHEN third bar AND last claims before second milestone`() {
    val expected = listOf(
      BonusBankWithMilestonesClaim(
        reward = EM2_COINS(BigDecimal("100")),
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("375"),
            reward = EM2_COINS(BigDecimal("10.0")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      ),
      BonusBankWithMilestonesClaim(
        reward = EM2_COINS(BigDecimal("100")),
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("125"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("375"),
            reward = EM2_COINS(BigDecimal("10.0")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      ),
      BonusBankWithMilestonesClaim(
        reward = null,
        milestones = listOf(
          BonusBankMilestoneState(
            coinGoal = BigDecimal("125"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
          BonusBankMilestoneState(
            coinGoal = BigDecimal("250"),
            reward = EM2_COINS(BigDecimal("7.5")),
            status = BonusBankMilestoneStatus.CLAIMED
          ),
        )
      )
    )
    val expectedRewards = listOf(
      EM2_COINS(BigDecimal("257.5")),
    )

    val bank = sampleBonusCashBar()

    mockBalance(1360, 200)

    val actual = runBlocking { bank.claim() }

    assertClaimsEqual(actual, expected)

    val actualRewards = argumentCaptor<List<BaseRewardType>>()

    verifyBlocking(bonusBankStorage) { claimReward(eq(USER_ID), eq(AppPlatform.ANDROID), actualRewards.capture(), eq(false)) }
    assertRewardListsEqual(actualRewards.firstValue, expectedRewards)

    verifyBlocking(bonusBankStorage) {
      saveBonusBank(
        BonusBank(
          userId = USER_ID,
          bankBalance = 360.toBigDecimal(),
          lastClaimedBalance = 360.toBigDecimal(),
        )
      )
    }
  }

  private fun mockBalance(bankBalance: Int, lastClaimedBalance: Int) {
    bonusBankStorage.mock({ getBonusBank(USER_ID) }, BonusBank(USER_ID, bankBalance.toBigDecimal(), lastClaimedBalance.toBigDecimal()))
  }

  private fun verifyNewBalance(bankBalance: Int, lastClaimedBalance: Int) {
    verifyBlocking(bonusBankStorage) {
      saveBonusBank(argThat { bonusBank ->
        bonusBank.userId == USER_ID &&
          bonusBank.bankBalance.compareTo(bankBalance.toBigDecimal()) == 0 &&
          bonusBank.lastClaimedBalance.compareTo(lastClaimedBalance.toBigDecimal()) == 0
      })
    }
  }

  private fun verifyEm2CoinsClaimReward(reward: Int) {
    verifyBlocking(bonusBankStorage) {
      claimReward(
        eq(USER_ID),
        eq(PLATFORM),
        argThat { rewards ->
          rewards.map {
            when (it) {
              is EM2_COINS -> {
                it.coins.compareTo(reward.toBigDecimal()) == 0
              }

              else -> true
            }
          }.all { it }
        },
        eq(false),
      )
    }
  }

  private fun verifyEm2AddedToStorage(coins: Int) {
    verifyBlocking(bonusBankStorage) {
      storeUserValue(
        eq(USER_ID),
        argThat { reward ->
          reward.compareTo(coins.toBigDecimal()) == 0
        },
        eq(ValuableType.EM2_COINS)
      )
    }
  }

  private fun sampleBonusCashBar() = BonusCashBarBank(
    // numbers assume we have x2000 multiplier, though changing multiplier will not break arithmetic, could only affect UI
    coinGoal = BigDecimal("500"),
    reward = SingleReward(EM2_COINS(BigDecimal("100"))),
    milestones = listOf(
      MilestoneWithReward(BigDecimal("125"), EM2_COINS(BigDecimal("7.5"))),
      MilestoneWithReward(BigDecimal("250"), EM2_COINS(BigDecimal("7.5"))),
      MilestoneWithReward(BigDecimal("375"), EM2_COINS(BigDecimal("10"))),
    ),
  )
    .also { it.init(USER_ID, PLATFORM, bonusBankStorage) }

  private fun assertStatesEqual(actual: BonusBarWithMilestonesState, expected: BonusBarWithMilestonesState) {
    assertThat(actual.coinGoal).isEqualByComparingTo(expected.coinGoal)
    assertThat(actual.coins).isEqualByComparingTo(expected.coins)
    assertThat(actual.readyToClaim).isEqualTo(expected.readyToClaim)

    assertThat(actual.milestones.size).isEqualTo(expected.milestones.size)

    actual.milestones.zip(expected.milestones).forEach { (actualMilestone, expectedMilestone) ->
      assertClaimMilestonesEqual(actualMilestone, expectedMilestone)
    }
  }

  private fun assertClaimsEqual(actual: List<BonusBankWithMilestonesClaim>, expected: List<BonusBankWithMilestonesClaim>) {
    assertThat(actual.size).isEqualTo(expected.size)

    actual.zip(expected).forEach { (actualClaim, expectedClaim) ->
      assertThat(actualClaim.reward?.javaClass).isEqualTo(expectedClaim.reward?.javaClass)

      assertRewardsEqual(actualClaim.reward, expectedClaim.reward)

      assertThat(actualClaim.milestones.size).isEqualTo(expectedClaim.milestones.size)

      actualClaim.milestones.zip(expectedClaim.milestones).forEach { (actualMilestone, expectedMilestone) ->
        assertClaimMilestonesEqual(actualMilestone, expectedMilestone)
      }
    }
  }

  private fun assertClaimMilestonesEqual(actual: BonusBankMilestoneState, expected: BonusBankMilestoneState) {
    assertThat(actual.status).isEqualTo(expected.status)
    assertThat(actual.coinGoal).isEqualTo(expected.coinGoal)

    assertRewardsEqual(actual.reward, expected.reward)

    assertThat(actual.reward.javaClass).isEqualTo(expected.reward.javaClass)
  }

  private fun assertRewardsEqual(actual: BaseRewardType?, expected: BaseRewardType?) {
    if (actual == null && expected == null) return

    assertThat(actual?.javaClass).isEqualTo(expected?.javaClass)

    when {
      actual is EM2_COINS ->
        assertThat(actual.coins).isEqualByComparingTo((expected as EM2_COINS).coins)

      actual is EARNINGS ->
        assertThat(actual.earnings).isEqualByComparingTo((expected as EARNINGS).earnings)

      else -> throw IllegalStateException("unsupported assert: ${actual?.javaClass}")
    }
  }

  private fun assertRewardListsEqual(actual: List<BaseRewardType>, expected: List<BaseRewardType>) {
    assertThat(actual.size).isEqualTo(expected.size)

    actual.zip(expected).forEach { (actual, expected) -> assertRewardsEqual(actual, expected) }
  }
}