package com.justplayapps.service.rewarding.bonusbank

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isTrue
import com.moregames.base.abtesting.Variations
import org.junit.jupiter.api.Test

class BonusBankVariationTest {

  @Test
  fun `SHOULD return BonusCashBar variation ON toBonusBankVariation WHEN key is bonusCashBar `() {
    val actual = Variations.BONUS_CASH_BAR.toBonusBankVariation()
    val actualIsCheck = actual is BonusBankVariation.BonusCashBar

    assertThat(actual?.javaClass).isEqualTo(BonusBankVariation.BonusCashBar::class.java)
    assertThat(actualIsCheck).isTrue()
  }


}