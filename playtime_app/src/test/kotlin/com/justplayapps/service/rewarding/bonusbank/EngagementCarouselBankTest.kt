package com.justplayapps.service.rewarding.bonusbank

import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.mock
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.argThat
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal

class EngagementCarouselBankTest {

  private val bonusBankStorage: BonusBankStorage = mock()
  private val USER_ID = "userId"
  private val TASK_ID = "taskId"
  private val COMMAND_ID = "commandId"
  private val PLATFORM = AppPlatform.ANDROID

  @Test
  fun `SHOULD add revenue to bank ON cutRevenue`() {
    val bank = EngagementCarouselBank(
      firstReward = SingleReward(
        BankSplit(EARNINGS::class, 1.toBigDecimal(), 0.toBigDecimal())
      ),
      reward = ChanceBasedReward(
        listOf(
          ChanceBased(1.toBigDecimal(), BankSplit(EM2_COINS::class, 0.15.toBigDecimal(), 0.85.toBigDecimal())),
        )
      )
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(10.toBigDecimal())

    runBlocking {
      bank.cutRevenue(6.toBigDecimal(), TASK_ID)
    }
    verifyNewBalance(13.toBigDecimal())
  }

  @Test
  fun `SHOULD claimReward as LARGE_EARNINGS FOR first reward ever + calc diamond price`() {
    val bank = EngagementCarouselBank(
      firstReward = SingleReward(
        BankSplit(EARNINGS::class, 1.toBigDecimal(), 0.toBigDecimal())
      ),
      reward = ChanceBasedReward(
        listOf(
          ChanceBased(1.toBigDecimal(), BankSplit(EM2_COINS::class, 0.15.toBigDecimal(), 0.85.toBigDecimal())),
        )
      )
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(0.4.toBigDecimal())
    mockCfg(0.1.toBigDecimal(), false)

    runBlocking { bank.claimReward(TASK_ID, COMMAND_ID) }

    verifyNewCfg(0.1.toBigDecimal(), true)
    verifyClaimedReward(EARNINGS(0.4.toBigDecimal()))
    verifyStorageValue(0.toBigDecimal(), ValuableType.REVENUE)
    verifyNewBalance(0.4.toBigDecimal(), true)
  }

  @Test
  fun `SHOULD claimReward as DIAMOND IF user has enough revenue`() {
    val bank = EngagementCarouselBank(
      firstReward = SingleReward(
        BankSplit(EARNINGS::class, 1.toBigDecimal(), 0.toBigDecimal())
      ),
      reward = ChanceBasedReward(
        listOf(
          ChanceBased(1.toBigDecimal(), BankSplit(EM2_COINS::class, 0.15.toBigDecimal(), 0.85.toBigDecimal())),
        )
      )
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(0.4.toBigDecimal())
    mockCfg(0.5.toBigDecimal(), true)
    mockStorageValue(0.7.toBigDecimal(), ValuableType.REVENUE)
    mockStorageValue(2.toBigDecimal(), ValuableType.DIAMONDS)

    runBlocking { bank.claimReward(TASK_ID, COMMAND_ID) }

    verifyClaimedReward(DIAMOND())
    verifyStorageValue(3.toBigDecimal(), ValuableType.DIAMONDS)
    verifyStorageValue(0.2.toBigDecimal(), ValuableType.REVENUE)
  }

  @Test
  fun `SHOULD use post cap diamond price IF diamond price greater than 2`() {
    val bank = EngagementCarouselBank(
      firstReward = SingleReward(
        BankSplit(EARNINGS::class, 1.toBigDecimal(), 0.toBigDecimal())
      ),
      reward = ChanceBasedReward(
        listOf(
          ChanceBased(1.toBigDecimal(), BankSplit(EM2_COINS::class, 0.15.toBigDecimal(), 0.85.toBigDecimal())),
        )
      )
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(0.4.toBigDecimal())
    mockCfg(4.toBigDecimal(), true)
    mockStorageValue(4.3.toBigDecimal(), ValuableType.REVENUE)
    mockStorageValue(4.toBigDecimal(), ValuableType.DIAMONDS)

    runBlocking { bank.claimReward(TASK_ID, COMMAND_ID) }

    verifyClaimedReward(DIAMONDS_REWARD(20.toBigDecimal()))
    verifyStorageValue(0.toBigDecimal(), ValuableType.DIAMONDS)
    verifyStorageValue(0.3.toBigDecimal(), ValuableType.REVENUE)
    verifyNewCfg(10.toBigDecimal(), true)
  }

  @Test
  fun `SHOULD split task revenue between reward and bank ON claimReward`() {
    val bank = EngagementCarouselBank(
      firstReward = SingleReward(
        BankSplit(EARNINGS::class, 1.toBigDecimal(), 0.toBigDecimal())
      ),
      reward = ChanceBasedReward(
        listOf(
          ChanceBased(1.toBigDecimal(), BankSplit(EARNINGS::class, 0.15.toBigDecimal(), 0.85.toBigDecimal())),
        )
      )
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(0.4.toBigDecimal())
    mockCfg(1.toBigDecimal(), true)
    mockStorageValue(0.3.toBigDecimal(), ValuableType.REVENUE)
    mockStorageValue(2.toBigDecimal(), ValuableType.DIAMONDS)

    runBlocking { bank.claimReward(TASK_ID, COMMAND_ID) }

    verifyClaimedReward(EARNINGS(0.06.toBigDecimal()))
    verifyStorageValue(0.64.toBigDecimal(), ValuableType.REVENUE)
    verifyNewBalance(0.4.toBigDecimal(), true)
  }

  private fun mockBalance(bankBalance: BigDecimal, isClaimed: Boolean = false) {
    bonusBankStorage.mock({ getEngCarouselBank(USER_ID, TASK_ID) }, EngCarouselBank(USER_ID, TASK_ID, bankBalance, isClaimed))
  }

  private fun verifyNewBalance(bankBalance: BigDecimal, isClaimed: Boolean = false) {
    verifyBlocking(bonusBankStorage) {
      saveEngCarouselBank(argThat { bonusBank ->
        bonusBank.userId == USER_ID &&
          bonusBank.taskId == TASK_ID &&
          bonusBank.bankBalance.compareTo(bankBalance) == 0 &&
          bonusBank.isClaimed == isClaimed
      })
    }
  }

  private fun mockCfg(diamondPrice: BigDecimal, wasEverClaimed: Boolean) {
    bonusBankStorage.mock({ getEngCarouselCfg(USER_ID) }, EngCarouselConfig(USER_ID, wasEverClaimed, diamondPrice))
  }

  private fun verifyNewCfg(diamondPrice: BigDecimal, wasEverClaimed: Boolean) {
    verifyBlocking(bonusBankStorage) {
      saveEngCarouselCfg(argThat { cfg ->
        cfg.userId == USER_ID &&
          cfg.diamondPrice.compareTo(diamondPrice) == 0 &&
          cfg.wasEverClaimed == wasEverClaimed
      })
    }
  }

  private fun mockStorageValue(amount: BigDecimal, type: ValuableType) {
    bonusBankStorage.mock({ getUserValue(USER_ID, type) }, amount)
  }

  private fun verifyStorageValue(amount: BigDecimal, type: ValuableType) {
    verifyBlocking(bonusBankStorage) {
      storeUserValue(
        eq(USER_ID),
        argThat { reward ->
          reward.compareTo(amount) == 0
        },
        eq(type)
      )
    }
  }

  private fun verifyClaimedReward(reward: BaseRewardType) {
    verifyBlocking(bonusBankStorage) {
      claimReward(
        eq(USER_ID),
        eq(PLATFORM),
        argThat { rewards ->
          rewards.map {
            when (it) {
              is EM2_COINS -> {
                it.coins.compareTo((reward as EM2_COINS).coins) == 0
              }

              is EARNINGS -> {
                it.earnings.compareTo((reward as EARNINGS).earnings) == 0
              }

              is DIAMONDS_REWARD -> {
                it.earnings.compareTo((reward as DIAMONDS_REWARD).earnings) == 0
              }

              is DIAMOND -> true

              is BOOSTER -> true

              else -> throw RuntimeException("Invalid reward type")
            }
          }.all { it }
        },
        eq(false)
      )
    }
  }
}