package com.justplayapps.service.rewarding.bonusbank

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import assertk.assertions.isFalse
import assertk.assertions.isTrue
import com.moregames.base.table.DatabaseExtension
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.util.*

@ExtendWith(DatabaseExtension::class)
class BonusBankPersistenceServiceTest(
  database: Database
) {

  private val service = BonusBankPersistenceService(database)
  private val USER_ID = "userId"
  private val TASK_ID = "taskId"

  @Test
  fun `test bonus bank persists`() {
    val userId = UUID.randomUUID().toString()

    val bank = runBlocking { service.getBonusBank(userId) }
    assertThat(bank.bankBalance).isEqualByComparingTo(0.toBigDecimal())
    assertThat(bank.lastClaimedBalance).isEqualByComparingTo(0.toBigDecimal())

    runBlocking { service.saveBonusBank(bank.copy(bankBalance = 10.toBigDecimal(), lastClaimedBalance = 5.toBigDecimal())) }

    val updatedBank = runBlocking { service.getBonusBank(userId) }
    assertThat(updatedBank.bankBalance).isEqualByComparingTo(10.toBigDecimal())
    assertThat(updatedBank.lastClaimedBalance).isEqualByComparingTo(5.toBigDecimal())
  }

  @Test
  fun `SHOULD update existing value ON save`() {
    val userId = UUID.randomUUID().toString()

    val bank = runBlocking { service.getBonusBank(userId) }
    assertThat(bank.bankBalance).isEqualByComparingTo(0.toBigDecimal())
    assertThat(bank.lastClaimedBalance).isEqualByComparingTo(0.toBigDecimal())

    runBlocking { service.saveBonusBank(bank.copy(bankBalance = 10.toBigDecimal(), lastClaimedBalance = 5.toBigDecimal())) }
    runBlocking { service.saveBonusBank(bank.copy(bankBalance = 20.toBigDecimal(), lastClaimedBalance = 15.toBigDecimal())) }

    val updatedBank = runBlocking { service.getBonusBank(userId) }
    assertThat(updatedBank.bankBalance).isEqualByComparingTo(20.toBigDecimal())
    assertThat(updatedBank.lastClaimedBalance).isEqualByComparingTo(15.toBigDecimal())
  }

  @Test
  fun `test engagement carousel bank persists`() {
    val bank = runBlocking { service.getEngCarouselBank(USER_ID, TASK_ID) }
    assertThat(bank.bankBalance).isEqualByComparingTo(0.toBigDecimal())
    assertThat(bank.isClaimed).isFalse()

    runBlocking { service.saveEngCarouselBank(bank.copy(bankBalance = 10.toBigDecimal(), isClaimed = true)) }

    val updatedBank = runBlocking { service.getEngCarouselBank(USER_ID, TASK_ID) }
    assertThat(updatedBank.bankBalance).isEqualByComparingTo(10.toBigDecimal())
    assertThat(updatedBank.isClaimed).isTrue()
  }
}