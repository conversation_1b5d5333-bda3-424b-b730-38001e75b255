package com.justplayapps.service.rewarding.earnings

import com.google.protobuf.StringValue
import com.justplayapps.service.rewarding.bonusbank.BonusBank
import com.justplayapps.service.rewarding.bonusbank.BonusBankStorage
import com.justplayapps.service.rewarding.earnings.proto.AddUserCoinsMessageKt.addAdditionalOfferCoinsMessage
import com.justplayapps.service.rewarding.earnings.proto.AddUserCoinsMessageKt.addGameCoinsMessage
import com.justplayapps.service.rewarding.earnings.proto.AddUserCoinsMessageKt.addOfferWallCoinsMessage
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.additionalOfferCoins
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.offerWallCoins
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.userGameCoins
import com.justplayapps.service.rewarding.earnings.proto.addUserCoinsMessage
import com.justplayapps.service.rewarding.earnings.proto.userCoinsAddedEvent
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.moregames.base.abtesting.AbTestingService.IsExperimentParticipant.No
import com.moregames.base.abtesting.AbTestingService.IsExperimentParticipant.Yes
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.abtesting.Variations.EM2_REV_SHARE_50
import com.moregames.base.abtesting.Variations.EM3
import com.moregames.base.bus.MessageBus
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.mock
import com.moregames.base.util.toProto
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import java.math.BigDecimal

class AddUserCoinsMessageHandlerTest {

  private val userCurrentCoinsBalancePersistenceService: UserCurrentCoinsBalancePersistenceService = mock()
  private val emExperimentBaseService: EmExperimentBaseService = mock {
    onBlocking { inflatingCoinsMultiplier(USER_ID) } doReturn 200
  }
  private val abTestingFacade: AbTestingFacade = mock {
    onBlocking { isCoinsDoNotResetParticipant(USER_ID, PLATFORM) } doReturn true
    onBlocking { assignedVariation(USER_ID, ClientExperiment.BONUS_BANK) } doReturn DEFAULT
  }
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService = mock {
    onBlocking { getUserCurrentCoinsBalance(USER_ID, PLATFORM) } doReturn UserCurrentCoinsGoalBalance(42, 0, 0)
  }
  private val messageBus: MessageBus = mock()
  private val bonusBankStorage: BonusBankStorage = mock()

  private val handler = AddUserCoinsMessageHandler(
    userCurrentCoinsBalancePersistenceService = userCurrentCoinsBalancePersistenceService,
    abTestingFacade = abTestingFacade,
    userCurrentCoinsBalanceService = userCurrentCoinsBalanceService,
    emExperimentBaseService = emExperimentBaseService,
    messageBus = messageBus,
    bonusBankStorage = bonusBankStorage,
  )

  private companion object {
    val USER_ID = "userId"
    val PLATFORM = AppPlatform.ANDROID
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `handle additional offer coins`(isEm2: Boolean) {
    abTestingFacade.mock({ isEm2Participant(USER_ID) }, isEm2)

    runBlocking {
      handler.handle(
        addUserCoinsMessage {
          userId = USER_ID
          platform = PLATFORM.toProto()
          additionalOfferCoins = addAdditionalOfferCoinsMessage {
            coinsEarned = 123.toBigDecimal().toProto()
            nonInflatedCoinsEarned = 234.toProto()
            offerId = 1
          }
        }
      )
    }

    if (isEm2) {
      verifyBlocking(userCurrentCoinsBalancePersistenceService) {
        updateEm2AdditionalOfferCurrentCoinsBalance(USER_ID, 123.toBigDecimal(), true)
      }
    } else {
      verifyBlocking(userCurrentCoinsBalancePersistenceService) {
        updateAdditionalOfferCurrentCoinsBalance(USER_ID, 123, 234, true)
      }
    }

    verifyBlocking(messageBus) {
      publish(
        userCoinsAddedEvent {
          this.userId = USER_ID
          this.coins = 42
          this.coinsAdded = if (isEm2) 24600 else 123
          this.additionalOfferCoinsData = additionalOfferCoins {
            this.offerId = 1
          }
        }
      )
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `handle offerwall coins`(isEm2: Boolean) {
    abTestingFacade.mock({ isEm2Participant(USER_ID) }, isEm2)

    runBlocking {
      handler.handle(
        addUserCoinsMessage {
          userId = USER_ID
          platform = PLATFORM.toProto()
          offerWallCoins = addOfferWallCoinsMessage {
            coinsEarned = 123.toBigDecimal().toProto() // currently completely ignored (for em1)
            nonInflatedCoinsEarned = 234.toProto()
          }
        }
      )
    }

    if (isEm2) {
      verifyBlocking(userCurrentCoinsBalancePersistenceService) {
        updateEm2AdditionalOfferCurrentCoinsBalance(USER_ID, 123.toBigDecimal(), true)
      }
    } else {
      verifyBlocking(userCurrentCoinsBalancePersistenceService) {
        updateAdditionalOfferCurrentCoinsBalance(USER_ID, 46800, 234, true)
      }
    }

    verifyBlocking(messageBus) {
      publish(
        userCoinsAddedEvent {
          this.userId = USER_ID
          this.coins = 42
          this.coinsAdded = if (isEm2) 24600 else 46800
          this.offerWallCoinsData = offerWallCoins {}
        }
      )
    }
  }


  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD handle offerwall coins WHEN bonus bank share taken`(isEm2: Boolean) {
    abTestingFacade.mock({ isEm2Participant(USER_ID) }, isEm2)
    abTestingFacade.mock({ assignedVariation(USER_ID, ClientExperiment.BONUS_BANK) }, Variations.BONUS_CASH_BAR)
    bonusBankStorage.mock(
      { getBonusBank(USER_ID) },
      BonusBank(
        userId = USER_ID,
        bankBalance = 100.toBigDecimal(),
        lastClaimedBalance = 100.toBigDecimal(),
      )
    )

    runBlocking {
      handler.handle(
        addUserCoinsMessage {
          userId = USER_ID
          platform = PLATFORM.toProto()
          offerWallCoins = addOfferWallCoinsMessage {
            coinsEarned = 234.toBigDecimal().toProto() // currently completely ignored (for em1)
            nonInflatedCoinsEarned = 234.toProto()
          }
        }
      )
    }

    if (isEm2) {
      verifyBlocking(userCurrentCoinsBalancePersistenceService) {
        updateEm2AdditionalOfferCurrentCoinsBalance(USER_ID, BigDecimal("187.2"), true)
      }
    } else {
      verifyBlocking(userCurrentCoinsBalancePersistenceService) {
        updateAdditionalOfferCurrentCoinsBalance(USER_ID, 37440, 187, true)
      }
    }

    verifyBlocking(messageBus) {
      publish(
        userCoinsAddedEvent {
          this.userId = USER_ID
          this.coins = 42
          this.coinsAdded = if (isEm2) 37440 else 37440
          this.offerWallCoinsData = offerWallCoins {}
        }
      )
    }
    verifyBlocking(bonusBankStorage) {
      saveBonusBank(
        BonusBank(
          USER_ID, BigDecimal("287.2"), 100.toBigDecimal()
        )
      )
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `handle game coins`(isEm2: Boolean) {
    abTestingFacade.mock({ getEm2Participation(USER_ID) }, if (isEm2) Yes(EM2_REV_SHARE_50) else No)

    runBlocking {
      handler.handle(
        addUserCoinsMessage {
          userId = USER_ID
          platform = PLATFORM.toProto()
          gameCoins = addGameCoinsMessage {
            coinsEarned = 123.toBigDecimal().toProto()
            forceCommandNotification = true
            gameId = 1
            commandId = StringValue.of("commandId")
          }
        }
      )
    }

    if (isEm2) {
      verifyBlocking(userCurrentCoinsBalancePersistenceService) {
        updateEm2CurrentGamesCoinsBalance(USER_ID, 123.toBigDecimal(), true)
      }
    } else {
      verifyBlocking(userCurrentCoinsBalancePersistenceService) {
        updateCurrentGamesCoinsBalance(USER_ID, 123, true)
      }
    }

    verifyBlocking(messageBus) {
      publish(
        userCoinsAddedEvent {
          this.userId = USER_ID
          this.coins = 42
          this.coinsAdded = 24600
          this.gameCoinsData = userGameCoins {
            this.forceCommandNotification = true
            this.gameId = 1
            this.commandId = StringValue.of("commandId")
          }
        }
      )
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD handle game coins WHEN bonus bank share taken`(isEm2: Boolean) {
    abTestingFacade.mock({ getEm2Participation(USER_ID) }, if (isEm2) Yes(EM2_REV_SHARE_50) else No)
    abTestingFacade.mock({ assignedVariation(USER_ID, ClientExperiment.BONUS_BANK) }, Variations.BONUS_CASH_BAR)
    bonusBankStorage.mock(
      { getBonusBank(USER_ID) },
      BonusBank(
        userId = USER_ID,
        bankBalance = 100.toBigDecimal(),
        lastClaimedBalance = 100.toBigDecimal(),
      )
    )

    runBlocking {
      handler.handle(
        addUserCoinsMessage {
          userId = USER_ID
          platform = PLATFORM.toProto()
          gameCoins = addGameCoinsMessage {
            coinsEarned = 123.toBigDecimal().toProto()
            forceCommandNotification = true
            gameId = 1
            commandId = StringValue.of("commandId")
          }
        }
      )
    }

    if (isEm2) {
      verifyBlocking(userCurrentCoinsBalancePersistenceService) {
        updateEm2CurrentGamesCoinsBalance(USER_ID, BigDecimal("98.4"), true)
      }
    } else {
      verifyBlocking(userCurrentCoinsBalancePersistenceService) {
        updateCurrentGamesCoinsBalance(USER_ID, 98, true)
      }
    }

    verifyBlocking(messageBus) {
      publish(
        userCoinsAddedEvent {
          this.userId = USER_ID
          this.coins = 42
          this.coinsAdded = 19680
          this.gameCoinsData = userGameCoins {
            this.forceCommandNotification = true
            this.gameId = 1
            this.commandId = StringValue.of("commandId")
          }
        }
      )
    }
    verifyBlocking(bonusBankStorage) {
      saveBonusBank(
        BonusBank(
          USER_ID, BigDecimal("198.4"), 100.toBigDecimal()
        )
      )
    }
  }

  @Test
  fun `handle game coins WHEN coins = 0`() {
    runBlocking {
      handler.handle(
        addUserCoinsMessage {
          userId = USER_ID
          platform = PLATFORM.toProto()
          gameCoins = addGameCoinsMessage {
            coinsEarned = 0.toBigDecimal().toProto()
            forceCommandNotification = true
            gameId = 1
            commandId = StringValue.of("commandId")
          }
        }
      )
    }

    verifyNoInteractions(userCurrentCoinsBalancePersistenceService)
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `do nothing on EM3 game coins`() {
    abTestingFacade.mock({ getEm2Participation(USER_ID) }, Yes(EM3))

    runBlocking {
      handler.handle(
        addUserCoinsMessage {
          userId = USER_ID
          platform = PLATFORM.toProto()
          gameCoins = addGameCoinsMessage {
            coinsEarned = 123.toBigDecimal().toProto()
            forceCommandNotification = true
            gameId = 1
            commandId = StringValue.of("commandId")
          }
        }
      )
    }

    verifyNoInteractions(userCurrentCoinsBalancePersistenceService)
    verifyNoInteractions(messageBus)
  }
}