package com.moregames.playtime.service

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNotNull
import com.google.protobuf.Int32Value
import com.google.protobuf.Value
import com.google.protobuf.struct
import com.justplayapps.games.status.GameStatus.*
import com.justplayapps.games.status.challengeProgressTracking
import com.justplayapps.games.status.gameStatusRequest
import com.justplayapps.games.status.gameStatusResponse
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.GamesExperiment
import com.moregames.base.abtesting.variations.CommonGameVariation
import com.moregames.base.abtesting.variations.TmLevelBalanceVariation
import com.moregames.base.abtesting.variations.TmRotationSpeedVariation
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.GenericPayloadApiDto
import com.moregames.base.messaging.dto.WebAppVerificationGpsLocationCheckEventDto
import com.moregames.base.user.dto.WebUserAdditionalData.IDFA
import com.moregames.base.user.dto.WebUserStatusDto
import com.moregames.base.user.dto.WebUsersInGameVerification.JAIL_BREAK
import com.moregames.base.util.encodeToBase64
import com.moregames.base.util.fromBase64
import com.moregames.base.util.mock
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.games.examination.GameExaminationService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.dto.ChallengeProgressTrackingDto
import com.moregames.playtime.user.fraudscore.HighlyTrustedUsersService
import com.moregames.playtime.user.usergame.UserGameService
import com.moregames.playtime.util.defaultJsonConverter
import com.moregames.playtime.util.installDefaultContentNegotiation
import com.moregames.playtime.utils.Json
import com.moregames.playtime.web.WebUserService
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.routing.*
import io.ktor.server.testing.*
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal
import java.util.stream.Stream

class ExtGamesServiceControllerTest {
  private val gameExaminationService: GameExaminationService = mock()
  private val gamesService: GamesService = mock()
  private val userGameService: UserGameService = mock()
  private val abTestingService: AbTestingService = mock()
  private val userService: UserService = mock()
  private val challengeService: ChallengeService = mock()
  private val webUserService: WebUserService = mock()
  private val highlyTrustedUsersService: HighlyTrustedUsersService = mock()

  private fun controller(): Application.() -> Unit = {
    installDefaultContentNegotiation()
    install(IgnoreTrailingSlash)

    routing {
      ExtGamesServiceController(
        gameExaminationService = gameExaminationService,
        gamesService = gamesService,
        userGameService = userGameService,
        abTestingService = abTestingService,
        userService = userService,
        challengeService = challengeService,
        webUserService = webUserService,
        highlyTrustedUsersService = highlyTrustedUsersService
      ).startRouting(this)
    }
  }

  companion object {
    const val USER_ID = "some-user-id"

    @JvmStatic
    fun platformProvider(): Stream<Arguments> = Stream.of(
      Arguments.of(AppPlatform.ANDROID, "android"),
      Arguments.of(AppPlatform.IOS, "ios")
    )
  }

  @BeforeEach
  fun init() {
    highlyTrustedUsersService.mock({ isHighlyTrustedUser(USER_ID) }, false)
  }

  @Test
  fun `SHOULD return not_found ON GET _status WHEN package id unknown`() = withTestApplication(controller()) {
    gamesService.mock({ getGameId("some-package-id", AppPlatform.ANDROID) }, null)

    handleRequest(
      method = HttpMethod.Post,
      uri = "/ext/games/android/some-user-id/status"
    ) {
      addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
      setBody(generateBody(gameStatusRequest {
        packageId = "some-package-id"
      }))
    }.let { response ->
      assertThat(response.response.status()).isEqualTo(HttpStatusCode.NotFound)
    }
  }

  @Test
  fun `SHOULD request examination ON GET _status WHEN examination needed`() = withTestApplication(controller()) {
    gamesService.mock({ getGameId("some-package-id", AppPlatform.ANDROID) }, 42)
    gameExaminationService.mock({ examinationRequired(USER_ID, 42) }, true)
    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, AppPlatform.ANDROID, activate = false) }, false)
    abTestingService.mock({ getAssignedGameVariations(USER_ID, 42) }, emptyMap<GamesExperiment, CommonGameVariation>())
    highlyTrustedUsersService.mock({ isHighlyTrustedUser(USER_ID) }, true)

    handleRequest(
      method = HttpMethod.Post,
      uri = "/ext/games/android/some-user-id/status"
    ) {
      addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
      setBody(generateBody(gameStatusRequest {
        packageId = "some-package-id"
      }))
    }.let { response ->
      assertThat(response.response.status()).isEqualTo(OK)
      assertThat(response.response.content).isNotNull().transform {
        defaultJsonConverter.decodeFromString<GenericPayloadApiDto>(it).payload.fromBase64(GameStatusResponse.parser())
      }.isEqualTo(gameStatusResponse {
        this.userId = USER_ID
        this.examinationRequired = true
        this.progressTracking = ChallengeProgressTracking.getDefaultInstance()
        this.isHt = true
        this.gameVariables = struct {
          // Empty struct for game variables
        }
      })
    }
  }

  @Test
  fun `SHOULD NOT request examination ON GET _status WHEN examination not needed`() = withTestApplication(controller()) {
    gamesService.mock({ getGameId("some-package-id", AppPlatform.ANDROID) }, 42)
    gameExaminationService.mock({ examinationRequired(USER_ID, 42) }, false)
    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, AppPlatform.ANDROID, activate = false) }, false)
    abTestingService.mock({ getAssignedGameVariations(USER_ID, 42) }, emptyMap<GamesExperiment, CommonGameVariation>())

    handleRequest(
      method = HttpMethod.Post,
      uri = "/ext/games/android/some-user-id/status"
    ) {
      addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
      setBody(generateBody(gameStatusRequest {
        packageId = "some-package-id"
      }))
    }.let { response ->
      assertThat(response.response.status()).isEqualTo(OK)
      assertThat(response.response.content).isNotNull().transform {
        defaultJsonConverter.decodeFromString<GenericPayloadApiDto>(it).payload.fromBase64(GameStatusResponse.parser())
      }.isEqualTo(gameStatusResponse {
        this.userId = USER_ID
        this.examinationRequired = false
        this.progressTracking = ChallengeProgressTracking.getDefaultInstance()
        this.gameVariables = struct {
          // Empty struct for game variables
        }
      })
    }
  }

  @Test
  fun `SHOULD return ad variant ON GET _status WHEN user is a participant`() = withTestApplication(controller()) {
    gamesService.mock({ getGameId("some-package-id", AppPlatform.ANDROID) }, 42)
    gameExaminationService.mock({ examinationRequired(USER_ID, 42) }, false)
    userGameService.mock({ getGamesAdVariantValue(USER_ID, 42) }, "adVariant")
    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, AppPlatform.ANDROID, activate = false) }, false)
    abTestingService.mock({ getAssignedGameVariations(USER_ID, 42) }, emptyMap<GamesExperiment, CommonGameVariation>())

    handleRequest(
      method = HttpMethod.Post,
      uri = "/ext/games/android/some-user-id/status"
    ) {
      addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
      setBody(generateBody(gameStatusRequest {
        packageId = "some-package-id"
      }))
    }.let { response ->
      assertThat(response.response.status()).isEqualTo(OK)
      assertThat(response.response.content).isNotNull().transform {
        defaultJsonConverter.decodeFromString<GenericPayloadApiDto>(it).payload.fromBase64(GameStatusResponse.parser())
      }.isEqualTo(gameStatusResponse {
        this.userId = USER_ID
        this.examinationRequired = false
        this.adVariant = "adVariant"
        this.progressTracking = ChallengeProgressTracking.getDefaultInstance()
        this.gameVariables = struct {
          // Empty struct for game variables
        }
      })
    }
  }

  @Test
  fun `SHOULD return ad variant and inGameBalanceNotificationsEnabled ON GET _status WHEN user is a participant`() = withTestApplication(controller()) {
    gamesService.mock({ getGameId("some-package-id", AppPlatform.ANDROID) }, 42)
    gameExaminationService.mock({ examinationRequired(USER_ID, 42) }, false)
    userGameService.mock({ getGamesAdVariantValue(USER_ID, 42) }, "adVariant")
    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, AppPlatform.ANDROID, activate = false) }, true)
    abTestingService.mock({ getAssignedGameVariations(USER_ID, 42) }, emptyMap<GamesExperiment, CommonGameVariation>())

    handleRequest(
      method = HttpMethod.Post,
      uri = "/ext/games/android/some-user-id/status"
    ) {
      addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
      setBody(generateBody(gameStatusRequest {
        packageId = "some-package-id"
      }))
    }.let { response ->
      assertThat(response.response.status()).isEqualTo(OK)
      assertThat(response.response.content).isNotNull().transform {
        defaultJsonConverter.decodeFromString<GenericPayloadApiDto>(it).payload.fromBase64(GameStatusResponse.parser())
      }.isEqualTo(gameStatusResponse {
        this.userId = USER_ID
        this.examinationRequired = false
        this.adVariant = "adVariant"
        this.inGameBalanceNotificationsEnabled = true
        this.progressTracking = ChallengeProgressTracking.getDefaultInstance()
        this.gameVariables = struct {
          // Empty struct for game variables
        }
      })
    }
  }

  @Test
  fun `SHOULD include lft param in status response WHEN we have d0 revenue`() = withTestApplication(controller()) {
    gamesService.mock({ getGameId("some-package-id", AppPlatform.ANDROID) }, 42)
    gameExaminationService.mock({ examinationRequired(USER_ID, 42) }, false)
    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, AppPlatform.ANDROID, activate = false) }, true)
    userService.mock({ getDay0RevenueForUser(USER_ID) }, BigDecimal("10.12"))
    abTestingService.mock({ getAssignedGameVariations(USER_ID, 42) }, emptyMap<GamesExperiment, CommonGameVariation>())

    handleRequest(
      method = HttpMethod.Post,
      uri = "/ext/games/android/some-user-id/status"
    ) {
      addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
      setBody(generateBody(gameStatusRequest {
        packageId = "some-package-id"
      }))
    }.let { response ->
      assertThat(response.response.status()).isEqualTo(OK)
      assertThat(response.response.content).isNotNull().transform {
        defaultJsonConverter.decodeFromString<GenericPayloadApiDto>(it).payload.fromBase64(GameStatusResponse.parser())
      }.isEqualTo(gameStatusResponse {
        this.userId = USER_ID
        this.examinationRequired = false
        this.inGameBalanceNotificationsEnabled = true
        this.lft = "10.12"
        this.progressTracking = ChallengeProgressTracking.getDefaultInstance()
        this.gameVariables = struct {
          // Empty struct for game variables
        }
      })
    }
  }

  @Test
  fun `SHOULD include progressTracking param in status response WHEN we have challenge for this game`() = withTestApplication(controller()) {
    gamesService.mock({ getGameId("some-package-id", AppPlatform.ANDROID) }, 42)
    gameExaminationService.mock({ examinationRequired(USER_ID, 42) }, false)
    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, AppPlatform.ANDROID, activate = false) }, true)
    abTestingService.mock({ getAssignedGameVariations(USER_ID, 42) }, emptyMap<GamesExperiment, CommonGameVariation>())

    challengeService.mock({ getChallengeProgressTracking(USER_ID, 42) }, ChallengeProgressTrackingDto(100))

    handleRequest(
      method = HttpMethod.Post,
      uri = "/ext/games/android/some-user-id/status"
    ) {
      addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
      setBody(generateBody(gameStatusRequest {
        packageId = "some-package-id"
      }))
    }.let { response ->
      assertThat(response.response.status()).isEqualTo(OK)
      assertThat(response.response.content).isNotNull().transform {
        defaultJsonConverter.decodeFromString<GenericPayloadApiDto>(it).payload.fromBase64(GameStatusResponse.parser())
      }.isEqualTo(gameStatusResponse {
        this.userId = USER_ID
        this.examinationRequired = false
        this.inGameBalanceNotificationsEnabled = true
        this.progressTracking = challengeProgressTracking {
          limit = Int32Value.of(100)
        }
        this.gameVariables = struct {
          // Empty struct for game variables
        }
      })
    }
  }

  @Test
  fun `SHOULD NOT include progressTracking param in status response WHEN we have challenge for this game but it's completed`() =
    withTestApplication(controller()) {
      gamesService.mock({ getGameId("some-package-id", AppPlatform.ANDROID) }, 42)
      gameExaminationService.mock({ examinationRequired(USER_ID, 42) }, false)
      userGameService.mock({ getGamesAdVariantValue(USER_ID, 42) }, "adVariant")
      abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, AppPlatform.ANDROID, activate = false) }, true)
      abTestingService.mock({ getAssignedGameVariations(USER_ID, 42) }, emptyMap<GamesExperiment, CommonGameVariation>())

      challengeService.mock({ getChallengeProgressTracking(USER_ID, 42) }, null)

      handleRequest(
        method = HttpMethod.Post,
        uri = "/ext/games/android/some-user-id/status"
      ) {
        addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
        setBody(generateBody(gameStatusRequest {
          packageId = "some-package-id"
        }))
      }.let { response ->
        assertThat(response.response.status()).isEqualTo(OK)
        assertThat(response.response.content).isNotNull().transform {
          defaultJsonConverter.decodeFromString<GenericPayloadApiDto>(it).payload.fromBase64(GameStatusResponse.parser())
        }.isEqualTo(gameStatusResponse {
          this.userId = USER_ID
          this.examinationRequired = false
          this.adVariant = "adVariant"
          this.inGameBalanceNotificationsEnabled = true
          this.progressTracking = ChallengeProgressTracking.getDefaultInstance()
          this.gameVariables = struct {
            // Empty struct for game variables
          }
        })
      }
    }

  @Test
  fun `SHOULD include gameVariables in status response for Android WHEN user has game variations`() =
    withTestApplication(controller()) {
      gamesService.mock({ getGameId("some-package-id", AppPlatform.ANDROID) }, 42)
      gameExaminationService.mock({ examinationRequired(USER_ID, 42) }, false)
      abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, AppPlatform.ANDROID, activate = false) }, false)

      // Mock game variations
      val gameVariations = mapOf(
        GamesExperiment.TM_LEVEL_BALANCE to TmLevelBalanceVariation.HardBoss,
        GamesExperiment.TM_ROTATION_SPEED to TmRotationSpeedVariation.Faster25Percent
      )
      abTestingService.mock({ getAssignedGameVariations(USER_ID, 42) }, gameVariations)

      handleRequest(
        method = HttpMethod.Post,
        uri = "/ext/games/android/some-user-id/status"
      ) {
        addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
        setBody(generateBody(gameStatusRequest {
          packageId = "some-package-id"
        }))
      }.let { response ->
        assertThat(response.response.status()).isEqualTo(OK)
        assertThat(response.response.content).isNotNull().transform {
          defaultJsonConverter.decodeFromString<GenericPayloadApiDto>(it).payload.fromBase64(GameStatusResponse.parser())
        }.isEqualTo(gameStatusResponse {
          this.userId = USER_ID
          this.examinationRequired = false
          this.progressTracking = ChallengeProgressTracking.getDefaultInstance()
          this.gameVariables = struct {
            fields.put("level_balance_id", Value.newBuilder().setStringValue("peaks_boss").build())
            fields.put("global_rotation_speed_coef", Value.newBuilder().setStringValue("1.25").build())
          }
        })
      }
    }

  @Test
  fun `SHOULD include gameVariables in status response for iOS WHEN user has game variations`() =
    withTestApplication(controller()) {
      gamesService.mock({ getGameId("some-package-id", AppPlatform.IOS) }, 42)
      gameExaminationService.mock({ examinationRequired(USER_ID, 42) }, false)
      abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, AppPlatform.IOS, activate = false) }, false)

      // Mock game variations
      val gameVariations = mapOf(
        GamesExperiment.TM_LEVEL_BALANCE to TmLevelBalanceVariation.ControlHardBoss,
        GamesExperiment.TM_ROTATION_SPEED to TmRotationSpeedVariation.Faster35Percent
      )
      abTestingService.mock({ getAssignedGameVariations(USER_ID, 42) }, gameVariations)

      handleRequest(
        method = HttpMethod.Post,
        uri = "/ext/games/ios/some-user-id/status"
      ) {
        addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
        setBody(generateBody(gameStatusRequest {
          packageId = "some-package-id"
        }))
      }.let { response ->
        assertThat(response.response.status()).isEqualTo(OK)
        assertThat(response.response.content).isNotNull().transform {
          defaultJsonConverter.decodeFromString<GenericPayloadApiDto>(it).payload.fromBase64(GameStatusResponse.parser())
        }.isEqualTo(gameStatusResponse {
          this.userId = USER_ID
          this.examinationRequired = false
          this.progressTracking = ChallengeProgressTracking.getDefaultInstance()
          this.gameVariables = struct {
            fields.put("level_balance_id", Value.newBuilder().setStringValue("peaks_boss").build())
            fields.put("global_rotation_speed_coef", Value.newBuilder().setStringValue("1.35").build())
          }
        })
      }
    }

  @Test
  fun `SHOULD provide user status ON GET webclient status call`() = withTestApplication(controller()) {
    val expected = WebUserStatusDto(listOf(IDFA), listOf(JAIL_BREAK))
    webUserService.mock({ getUserStatus("userId") }, expected)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/ext/games/webclient/userId/status"
    )

    assertThat(response.response.status()).isEqualTo(OK)
    assertThat(response.response.content).isEqualTo(Json.defaultJsonConverter.encodeToString(expected))
  }

  @ParameterizedTest
  @MethodSource("platformProvider")
  fun `SHOULD return not_found ON POST first-launch WHEN package id unknown`(platform: AppPlatform, platformPath: String) = withTestApplication(controller()) {
    gamesService.mock({ getGameId("some-package-id", platform) }, null)

    handleRequest(
      method = HttpMethod.Post,
      uri = "/ext/games/$platformPath/some-user-id/first-launch"
    ) {
      addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
      setBody(generateFirstLaunchBody(FirstLaunchRequest.newBuilder().apply {
        packageId = "some-package-id"
        gameVersion = "1.0.0"
      }.build()))
    }.let { response ->
      assertThat(response.response.status()).isEqualTo(HttpStatusCode.NotFound)
    }
  }

  @ParameterizedTest
  @MethodSource("platformProvider")
  fun `SHOULD assign active game experiments ON POST first-launch WHEN package id is known`(platform: AppPlatform, platformPath: String) =
    withTestApplication(controller()) {
      gamesService.mock({ getGameId("some-package-id", platform) }, 42)

    handleRequest(
      method = HttpMethod.Post,
      uri = "/ext/games/$platformPath/some-user-id/first-launch"
    ) {
      addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
      setBody(generateFirstLaunchBody(FirstLaunchRequest.newBuilder().apply {
        packageId = "some-package-id"
        gameVersion = "1.0.0"
      }.build()))
    }.let { response ->
      assertThat(response.response.status()).isEqualTo(OK)
    }

    verifyBlocking(abTestingService) { assignActiveGameExperiments("some-user-id", 42, "1.0.0") }
  }

  @ParameterizedTest
  @MethodSource("platformProvider")
  fun `SHOULD assign active game experiments with different game version ON POST first-launch`(platform: AppPlatform, platformPath: String) =
    withTestApplication(controller()) {
      gamesService.mock({ getGameId("another-package-id", platform) }, 123)

    handleRequest(
      method = HttpMethod.Post,
      uri = "/ext/games/$platformPath/test-user/first-launch"
    ) {
      addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
      setBody(generateFirstLaunchBody(FirstLaunchRequest.newBuilder().apply {
        packageId = "another-package-id"
        gameVersion = "2.1.5"
      }.build()))
    }.let { response ->
      assertThat(response.response.status()).isEqualTo(OK)
    }

    verifyBlocking(abTestingService) { assignActiveGameExperiments("test-user", 123, "2.1.5") }
  }

  @Test
  fun `SHOULD trigger gps location check ON post gps location call`() =
    withTestApplication(controller()) {
      val incomingData = WebAppVerificationGpsLocationCheckEventDto(
        sessionId = "sessionId",
        applicationId = "applicationId",
        provided = true,
        location = "US",
        isMocked = true
      )

      handleRequest(
        method = HttpMethod.Post,
        uri = "/ext/games/webclient/userId/gps-location"
      ) {
        addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
        setBody(Json.defaultJsonConverter.encodeToString(incomingData))
      }.let { response ->
        assertThat(response.response.status()).isEqualTo(OK)
      }

      verifyBlocking(webUserService) { gpsLocationCheck("userId", incomingData) }
    }

  private fun generateBody(response: GameStatusRequest): String {
    return GenericPayloadApiDto(response.encodeToBase64()).let { defaultJsonConverter.encodeToString(it) }
  }

  private fun generateFirstLaunchBody(request: FirstLaunchRequest): String {
    return GenericPayloadApiDto(request.encodeToBase64()).let { defaultJsonConverter.encodeToString(it) }
  }
}
