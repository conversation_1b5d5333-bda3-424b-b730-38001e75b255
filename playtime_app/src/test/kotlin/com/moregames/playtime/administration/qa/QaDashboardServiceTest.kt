package com.moregames.playtime.administration.qa

import assertk.assertThat
import assertk.assertions.isEqualToIgnoringGivenProperties
import assertk.assertions.isGreaterThan
import assertk.assertions.isInstanceOf
import assertk.assertions.isNotEmpty
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService
import com.moregames.base.abtesting.AbTestingPersistenceService
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.Variations
import com.moregames.base.abtesting.dto.Experiment
import com.moregames.base.abtesting.dto.UserVariation
import com.moregames.base.app.OfferWallType
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.messaging.dto.CashoutPeriodEndedEventDto
import com.moregames.base.messaging.dto.MessageDto
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.base.util.throwException
import com.moregames.playtime.administration.blacklist.BlacklistService
import com.moregames.playtime.administration.dto.AssignedVariationsResponse
import com.moregames.playtime.administration.qa.QaDashboardController.*
import com.moregames.playtime.buseffects.InvalidateUserCacheEffect
import com.moregames.playtime.checks.ExaminationService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.notifications.MassPushNotificationService
import com.moregames.playtime.notifications.NotificationsFacade
import com.moregames.playtime.revenue.fyber.FyberReportingManager
import com.moregames.playtime.revenue.tapjoy.TapjoyReportingManager
import com.moregames.playtime.subscribers.CashoutPeriodEndedMessagePushSubscriber
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.attestation.UserAttestationPersistenceService
import com.moregames.playtime.user.cashout.CashoutStatusService
import com.moregames.playtime.user.fraudscore.FraudScoreChangeReason
import com.moregames.playtime.user.fraudscore.FraudScorePersistenceService
import com.moregames.playtime.user.fraudscore.FraudScorePersistenceService.FraudScoreTransaction
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.offer.AndroidOfferwallService
import com.moregames.playtime.user.offer.AndroidOfferwallTypeService
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.user.tracking.TrackingService
import com.moregames.playtime.user.verification.VerificationPersistenceService
import com.moregames.playtime.user.verification.VerificationService
import com.moregames.playtime.user.verification.dto.VerificationSessionDto
import com.moregames.playtime.utils.customNotificationStub
import com.moregames.playtime.utils.emailNotificationEventStub
import com.moregames.playtime.utils.userDtoStub
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.client.features.json.*
import io.ktor.client.features.json.serializer.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertNull

class QaDashboardServiceTest {
  private val cashoutPeriodEndedMessagePushSubscriber: CashoutPeriodEndedMessagePushSubscriber = mock()
  private val notificationsFacade: NotificationsFacade = mock()
  private val testUserId: String = UUID.randomUUID().toString()
  private val now: Instant = Instant.now()
  private val timeService: TimeService = mock { on { now() } doReturn now }
  private val userService: UserService = mock {
    onBlocking { fetchUserId(any()) } doReturn testUserId
  }
  private val encryptionService: EncryptionService = mock {
    onBlocking { encrypt(any()) } doAnswer { "encrypted" + (it.arguments[0] as String) }
  }
  private val massPushNotificationService: MassPushNotificationService = mock()

  private val trackingService: TrackingService = mock()
  private val fraudScorePersistenceService: FraudScorePersistenceService = mock()
  private val blacklistService: BlacklistService = mock()
  private val verificationPersistenceService: VerificationPersistenceService = mock()
  private val marketService: MarketService = mock()
  private val userPersistenceService: UserPersistenceService = mock()
  private val verificationService: VerificationService = mock()
  private val userEarningsPersistenceService: UserEarningsPersistenceService = mock()
  private val httpClient = mockHttpClient()
  private val cashoutStatusService: CashoutStatusService = mock()
  private val androidOfferwallService: AndroidOfferwallService = mock()
  private val androidOfferwallTypeService: AndroidOfferwallTypeService = mock()
  private val tapjoyReportingManager: TapjoyReportingManager = mock()
  private val fyberReportingManager: FyberReportingManager = mock()
  private val fraudScoreService: FraudScoreService = mock()
  private val userAttestationPersistenceService: UserAttestationPersistenceService = mock()
  private val qaUserSettingsService: QaUserSettingsService = mock()
  private val examinationService: ExaminationService = mock()
  private val abTestingPersistenceService: AbTestingPersistenceService = mock()
  private val abTestingService: AbTestingService = mock()
  private val messageBus: MessageBus = mock()


  private companion object {
    const val USER_ID = "userId"
    const val TRACKING_ID = "trackingId"
    const val IOS_IDFV = "iosIdfv"
    val testRequest = AddCoinsRequestDto("com.relaxingbraintraining.numbermerge", 1, 593, true)
  }

  private val underTest: QaDashboardService = QaDashboardService(
    cashoutPeriodEndedMessagePushSubscriber = cashoutPeriodEndedMessagePushSubscriber,
    notificationsFacade = notificationsFacade,
    timeService = timeService,
    userService = userService,
    encryptionService = encryptionService,
    massPushNotificationService = massPushNotificationService,
    trackingService = trackingService,
    fraudScorePersistenceService = fraudScorePersistenceService,
    blacklistService = blacklistService,
    verificationPersistenceService = verificationPersistenceService,
    marketService = marketService,
    verificationService = verificationService,
    userPersistenceService = userPersistenceService,
    cashoutStatusService = cashoutStatusService,
    userEarningsPersistenceService = userEarningsPersistenceService,
    httpClient = httpClient,
    androidOfferwallService = androidOfferwallService,
    androidOfferwallTypeService = androidOfferwallTypeService,
    tapjoyReportingManager = tapjoyReportingManager,
    fyberReportingManager = fyberReportingManager,
    fraudScoreService = fraudScoreService,
    userAttestationPersistenceService = userAttestationPersistenceService,
    qaUserSettingsService = qaUserSettingsService,
    examinationService = examinationService,
    abTestingPersistenceService = abTestingPersistenceService,
    abTestingService = abTestingService,
    messageBus = messageBus,
  )

  @BeforeEach
  fun before() {
    userService.mock({ fetchUserId(TRACKING_ID) }, USER_ID)
  }

  @Test
  fun `SHOULD hit CP subscriber handler ON endCashoutPeriod`() {
    runBlocking {
      underTest.endCashoutPeriod(testUserId)
    }

    verifyBlocking(cashoutPeriodEndedMessagePushSubscriber) {
      handle(
        CashoutPeriodEndedEventDto(
          userId = testUserId,
          periodStart = now,
          periodEnd = now,
        )
      )
    }
  }

  @Test
  fun `SHOULD send notification ON sendQaFreeFormCustomNotification`() {
    val notification = QaFreeFormCustomNotificationApiDto(data = mapOf("key1" to "value1", "key2" to "value2"))

    runBlocking {
      underTest.sendQaFreeFormCustomNotification(testUserId, notification)
    }

    verifyBlocking(notificationsFacade) { sendMessage(testUserId, notification) }
  }

  @Test
  fun `SHOULD send notifications ON sendCustomNotifications`() {
    runBlocking {
      underTest.sendCustomNotifications(USER_ID, customNotificationStub)
    }
    verifyBlocking(massPushNotificationService) { sendCustomPushNotifications(mapOf(USER_ID to customNotificationStub)) }
  }

  @Test
  fun `SHOULD send email ON sendEmailNotification`() {
    val request = QaEmailNotificationRequest(name = "Name", email = "Email", emailNotificationEventStub.sendGridTemplateId)

    runBlocking {
      underTest.sendEmailNotification(USER_ID, request)
    }

    verifyBlocking(messageBus) { publish(emailNotificationEventStub.copy(userId = USER_ID)) }
  }

  @Test
  fun `SHOULD remove cache for the user ON drop-user-cache call by userId`() {
    runBlocking {
      underTest.dropUserCache(userId = USER_ID)
    }
    verifyBlocking(messageBus) { publish(InvalidateUserCacheEffect(USER_ID)) }
  }

  @Test
  fun `SHOULD trigger user whitelisting ON whitelist-user POST call`() {
    runBlocking {
      underTest.whitelistUser(USER_ID, false)
    }
    verifyBlocking(userService) { whitelistUser(USER_ID) }
    verifyNoInteractions(fraudScorePersistenceService)
  }

  @Test
  fun `SHOULD trigger user whitelisting and FS drop ON whitelist-user POST call with flag`() {
    runBlocking {
      underTest.whitelistUser(USER_ID, true)
    }

    verifyBlocking(userService) { whitelistUser(USER_ID) }
    verifyBlocking(fraudScorePersistenceService) { dropTransactions(USER_ID) }
  }

  @Test
  fun `SHOULD trigger tracking id whitelisting ON whitelist-trackingId POST call WHEN android user`() {
    userService.throwException({ whitelistTrackingData(any()) }, UserRecordNotFoundException(""))

    runBlocking {
      underTest.whitelistTrackingData(TRACKING_ID)
    }
    verifyBlocking(userService) { whitelistGoogleAdId(TRACKING_ID) }
    verifyBlocking(userService, never()) { whitelistTrackingData(any()) }
  }

  @Test
  fun `SHOULD trigger tracking data whitelisting ON whitelist-trackingId call WHEN ios user`() {
    userService.throwException({ whitelistGoogleAdId(any()) }, UserRecordNotFoundException(""))

    runBlocking {
      underTest.whitelistTrackingData(TRACKING_ID)
    }
    verifyBlocking(userService) { whitelistGoogleAdId(TRACKING_ID) }
    verifyBlocking(userService) { whitelistTrackingData(TrackingData(TRACKING_ID, IDFV, IOS)) }
  }

  @Test
  fun `SHOULD return exception ON whitelist-trackingId call WHEN user not found`() {
    userService.throwException({ whitelistGoogleAdId(any()) }, UserRecordNotFoundException(""))
    userService.throwException({ whitelistTrackingData(any()) }, UserRecordNotFoundException(TRACKING_ID, "current_tracking_id"))

    assertThrows<UserRecordNotFoundException> {
      runBlocking {
        underTest.whitelistTrackingData(TRACKING_ID)
      }
    }.also {
      assertEquals("Could not find user record with current_tracking_id $TRACKING_ID", it.message)
    }

    verifyBlocking(userService) { whitelistGoogleAdId(TRACKING_ID) }
    verifyBlocking(userService) { whitelistTrackingData(TrackingData(TRACKING_ID, IDFV, IOS)) }
  }

  @Test
  fun `SHOULD trigger tracking id unWhitelisting ON remove-trackingId-from-whitelist POST call `() {

    runBlocking {
      underTest.removeTrackingIdFromWhitelist(TRACKING_ID)
    }

    verifyBlocking(trackingService) { removeGoogleAdIdsFromWhitelist(setOf(TRACKING_ID)) }
    verifyBlocking(trackingService) { removeTrackingDataSetFromWhitelist(setOf(TrackingData(TRACKING_ID, IDFV, IOS))) }
  }

  @Test
  fun `SHOULD trigger tracking id blacklisting ON blacklist-trackingId POST call`() {
    blacklistService.mock({ blacklistGoogleAdIds(setOf(TRACKING_ID), "some reason", false) }, setOf("userId1", "userId2"))
    blacklistService.mock({ blacklistTrackingData(setOf(TrackingData(TRACKING_ID, IDFV, IOS)), "some reason", false) }, setOf("userId3", "userId4"))

    val result = runBlocking {
      underTest.blacklistTrackingId(TRACKING_ID, "some reason")
    }

    assertEquals(setOf("userId1", "userId2", "userId3", "userId4"), result)
  }

  @Test
  fun `SHOULD auto-verify user ON verify-face call`() {
    runBlocking { underTest.verifyFace(USER_ID) }

    val verificationSession = argumentCaptor<VerificationSessionDto>()
    verifyBlocking(verificationPersistenceService) {
      createSession(verificationSession.capture())
      assertThat(verificationSession.firstValue).isEqualToIgnoringGivenProperties(
        VerificationSessionDto(
          sessionId = "any",
          userId = USER_ID,
          verification = listOf(
            VerificationSessionDto.VerificationStep(
              type = VerificationSessionDto.VerificationType.FACE,
              status = VerificationSessionDto.VerificationStatus.VERIFIED,
              order = 1
            )
          ),
          expiredAt = Instant.now()
        ),
        VerificationSessionDto::sessionId,
        VerificationSessionDto::expiredAt
      )
      assertThat(verificationSession.firstValue.expiredAt).isGreaterThan(Instant.now())
      assertThat(verificationSession.firstValue.sessionId).isNotEmpty()
    }
  }

  @Test
  fun `SHOULD auto-verify user ON verify-ios-specific-steps call`() {
    marketService.mock({ getAllowedCountries() }, setOf("AU", "NZ"))

    runBlocking { underTest.verifyIosSpecificSteps(USER_ID) }

    val verificationSession = argumentCaptor<VerificationSessionDto>()
    verifyBlocking(verificationPersistenceService) {
      createSession(verificationSession.capture())
      assertThat(verificationSession.firstValue).isEqualToIgnoringGivenProperties(
        VerificationSessionDto(
          sessionId = "any",
          userId = USER_ID,
          verification = listOf(
            VerificationSessionDto.VerificationStep(
              type = VerificationSessionDto.VerificationType.FACE,
              status = VerificationSessionDto.VerificationStatus.VERIFIED,
              order = 1
            ),
            VerificationSessionDto.VerificationStep(
              type = VerificationSessionDto.VerificationType.EXAMINATION,
              status = VerificationSessionDto.VerificationStatus.VERIFIED,
              order = 2
            ),
            VerificationSessionDto.VerificationStep(
              type = VerificationSessionDto.VerificationType.JAIL_BREAK,
              status = VerificationSessionDto.VerificationStatus.VERIFIED,
              order = 3
            ),
            VerificationSessionDto.VerificationStep(
              type = VerificationSessionDto.VerificationType.LOCATION,
              status = VerificationSessionDto.VerificationStatus.VERIFIED,
              order = 4
            ),
          ),
          expiredAt = Instant.now()
        ),
        VerificationSessionDto::sessionId,
        VerificationSessionDto::expiredAt
      )
      assertThat(verificationSession.firstValue.expiredAt).isGreaterThan(Instant.now())
      assertThat(verificationSession.firstValue.sessionId).isNotEmpty()
      verifyBlocking(userService) { updateGpsLocationCountry(USER_ID, "AU") }
    }
  }

  @Test
  fun `SHOULD initiate generic notification ON send-generic-notification POST call`() {
    val notificationText = "notificationText"

    runBlocking { underTest.sendGenericNotification(USER_ID, notificationText) }

    verifyBlocking(massPushNotificationService) { sendGenericPushNotifications(setOf(USER_ID), text = notificationText) }
  }

  @Test
  fun `SHOULD initiate custom notification ON send-free-form-custom-notification POST call`() {
    val request = QaFreeFormCustomNotificationApiDto(data = mapOf("key1" to "value1", "key2" to "value2"))

    runBlocking { underTest.sendQaFreeFormCustomNotification(USER_ID, request) }
    verifyBlocking(notificationsFacade) { sendMessage(USER_ID, request) }
  }

  @Test
  fun `SHOULD initiate custom notification ON send-free-form-custom-notification POST call WHEN notification type defined`() {
    val request = QaFreeFormCustomNotificationApiDto(notificationType = "someType", data = mapOf("key1" to "value1", "key2" to "value2"))

    runBlocking { underTest.sendQaFreeFormCustomNotification(USER_ID, request) }
    verifyBlocking(notificationsFacade) { sendMessage(USER_ID, request) }
  }

  @Test
  fun `SHOULD trigger user creation timestamp change ON change-creation-timestamp POST call`() {
    val newCreationTimestamp = Instant.now()

    runBlocking { underTest.changeUserCreationTimestamp(USER_ID, newCreationTimestamp) }

    verifyBlocking(userPersistenceService) { updateUserCreatedAt(USER_ID, newCreationTimestamp) }
  }

  @Test
  fun `SHOULD trigger user country code change ON change-country-code POST call`() {
    val newCountryCode = "CA"

    runBlocking { underTest.changeUserCountryCode(USER_ID, newCountryCode) }

    verifyBlocking(userPersistenceService) { updateUserCountryCode(USER_ID, newCountryCode) }
  }

  @Test
  fun `SHOULD trigger user locale change ON change-user-locale POST call`() {
    val newCountryCode = "de-DE"

    runBlocking { underTest.changeUserLocale(USER_ID, newCountryCode) }

    verifyBlocking(userPersistenceService) { updateDeviceLocale(USER_ID, Locale.of("de", "DE")) }
  }

  @Test
  fun `SHOULD add generic revenue and emit related event ON add-generic-revenue POST call`() {
    val revenueAmount = BigDecimal("5.43")

    runBlocking { underTest.addGenericRevenue(USER_ID, RevenueSource.APPLOVIN, revenueAmount, 200039) }

    val captor = argumentCaptor<Array<MessageDto>>()
    verifyBlocking(messageBus) { publish(*captor.capture()) }
    assertThat(captor.firstValue).transform { it.first() }.isInstanceOf(RevenueReceivedEventDto::class).isEqualToIgnoringGivenProperties(
      RevenueReceivedEventDto(
        eventId = UUID.randomUUID().toString(),
        userId = USER_ID,
        timestamp = Instant.now(),
        source = RevenueSource.APPLOVIN,
        amount = revenueAmount,
        networkId = -1,
        gameId = 200039,
        createdAt = Instant.now()
      ),
      RevenueReceivedEventDto::eventId,
      RevenueReceivedEventDto::timestamp,
      RevenueReceivedEventDto::createdAt
    )
  }

  @Test
  fun `SHOULD add earnings and allow cashout ON add-earnings POST call`() {
    val testAmount = BigDecimal("5.43")

    runBlocking {
      underTest.addEarnings(
        USER_ID,
        AddEarningsRequestDto(amountUSD = testAmount)
      )
    }

    verifyBlocking(userEarningsPersistenceService) { qaAddEarnings(USER_ID, testAmount, testAmount, "USD") }
    verifyBlocking(cashoutStatusService) { enableCashout(USER_ID) }
  }

  @Test
  fun `SHOULD trigger request to orchestrator ON add-coins POST call WHEN appPlatform is ANDROID`() {
    userService.mock({ getUser(USER_ID) }, userDtoStub)
    runBlocking { underTest.addCoins(USER_ID, testRequest) }
  }

  @Test
  fun `SHOULD trigger request to orchestrator ON add-coins POST call WHEN appPlatform is IOS`() {
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(trackingData = TrackingData(id = IOS_IDFV, type = IDFV, platform = IOS)))
    runBlocking { underTest.addCoins(USER_ID, testRequest) }
  }

  private fun mockHttpClient() = HttpClient(MockEngine) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
      serializer = KotlinxSerializer(json)
    }
    engine {
      addHandler { request ->
        when (request.url.toString()) {
          "https://test-dot-orchestrator-dot-playspot-server-dev.appspot.com/webhooks/games/progress" +
            "?game_id=com.relaxingbraintraining.numbermerge" +
            "&user_id=userId" +
            "&idfv=" +
            "&score=1" +
            "&amount=593" +
            "&is_new_record=true" -> {
            respondOk()
          }

          "https://test-dot-orchestrator-dot-playspot-server-dev.appspot.com/webhooks/games/progress" +
            "?game_id=com.relaxingbraintraining.numbermerge" +
            "&user_id=userId" +
            "&idfv=iosIdfv" +
            "&score=1" +
            "&amount=593" +
            "&is_new_record=true" -> {
            respondOk()
          }

          else -> error("Unhandled ${request.url.fullPath}")
        }
      }
    }
  }

  @Test
  fun `SHOULD trigger onTapjoyCoinsReport ON add-offerwall-offer-completion-coins`() {
    androidOfferwallService.mock({ shouldShowOfferwalls("userId1") }, true)
    androidOfferwallTypeService.mock({ getOfferwallTypes("userId1") }, listOf(OfferWallType.TAPJOY))

    runBlocking {
      underTest.addOfferwallOfferCompletionCoins(
        "userId1",
        QaTapjoyCoinsReport(
          10500,
          BigDecimal(1.5),
          BigDecimal(1.5)
        )
      )
    }

    verifyBlocking(tapjoyReportingManager) { onTapjoyCoinsReport(any()) }
  }

  @Test
  fun `SHOULD trigger onIronSourceCoinsReport ON add-offerwall-offer-completion-coins`() {
    androidOfferwallService.mock({ shouldShowOfferwalls("userId1") }, true)
    androidOfferwallTypeService.mock({ getOfferwallTypes("userId1") }, listOf(OfferWallType.FYBER))
    val request = QaTapjoyCoinsReport(
      10500,
      BigDecimal(1.5),
      BigDecimal(1.5)
    )
    runBlocking { underTest.addFyberOfferwallOfferCompletionCoins("userId1", request) }

    verifyBlocking(fyberReportingManager) { onIncomingReport(any()) }
  }

  @Test
  fun `SHOULD insert fraud score transaction ON fraudScore`() {
    val curFraudScore = 1345.0
    val targetFraudScore = 99

    fraudScorePersistenceService.mock({ getUserFraudScore(USER_ID) }, curFraudScore)

    runBlocking { underTest.setFraudScore(USER_ID, targetFraudScore) }

    val transListCapt = argumentCaptor<List<FraudScoreTransaction>>()
    verifyBlocking(fraudScoreService) {
      addTransactions(transListCapt.capture())
    }
    assertThat(transListCapt.firstValue[0])
      .isEqualToIgnoringGivenProperties(
        FraudScoreTransaction(
          userId = USER_ID,
          amount = targetFraudScore - curFraudScore,
          reasonType = FraudScoreChangeReason.MANUAL_FS,
          reasonUniqueId = "uuid1",
          description = "makes total FS == $targetFraudScore",
        ), FraudScoreTransaction::reasonUniqueId
      )
  }

  @Test
  fun `SHOULD set frozen fraud score for user ON frozenFraudScore call`() {
    runBlocking { underTest.setFrozenFraudScore(USER_ID, 100) }
    verifyBlocking(fraudScorePersistenceService) { setFrozenFraudScore("userId", 100.0) }
  }

  @Test
  fun `SHOULD add user to passed strong attestation users list ON pass-strong-attestation call`() {
    runBlocking { underTest.passStrongAttestation(USER_ID) }
    verifyBlocking(userAttestationPersistenceService) { saveUserPassedStrongAttestation(USER_ID) }
  }

  @Test
  fun `SHOULD set usual cashout period qa setting ON set-usual-cashout-periods`() {
    runBlocking { underTest.setUsualCashoutPeriods(USER_ID, false) }

    verifyBlocking(qaUserSettingsService) { setUserSettings(USER_ID, listOf(QaUserSetting.USE_USUAL_CASHOUT_PERIODS)) }
  }

  @Test
  fun `SHOULD set usual cashout period + manual cashout periods run ON set-usual-cashout-periods WHEN a special flag was set`() {
    runBlocking { underTest.setUsualCashoutPeriods(USER_ID, true) }

    verifyBlocking(qaUserSettingsService) {
      setUserSettings(USER_ID, listOf(QaUserSetting.USE_USUAL_CASHOUT_PERIODS, QaUserSetting.SPEED_UP_CASHOUT_PERIOD_END))
    }
  }

  @Test
  fun `SHOULD call markUserAsPassedDeviceAttestation ON pass-device-attestation call`() {
    runBlocking { underTest.passDeviceAttestation(USER_ID) }
    verifyBlocking(examinationService) {
      markUserAsPassedDeviceAttestation(USER_ID)
    }
  }

  @Test
  fun `SHOULD save force HT flag for user ON forceHighlyTrusted call by userId`() {
    runBlocking {
      underTest.forceHighlyTrusted(userId = USER_ID)
    }
    verifyBlocking(qaUserSettingsService) { forceHighlyTrusted(USER_ID) }
  }

  @Test
  fun `SHOULD return assigned variations WHEN userId is provided`() {
    val now = Instant.now()
    val variations = listOf(
      UserVariation(
        id = 1,
        key = "key1",
        allocation = BigDecimal(0.5),
        experiment = Experiment(
          id = 1,
          key = "key1",
          isActive = true,
          minimumAppVersion = 63,
          startedAt = now,
          finishedAt = now,
          gameVersion = null,
        ),
        isUserActivatedForVariation = true
      )
    )
    abTestingPersistenceService.mock({ loadActiveAssignedVariationsFor(USER_ID) }, variations)
    val experiments = variations.map {
      AssignedVariationsResponse.AssignedVariationsExperiment(
        experiment = ExperimentDto.from(it.experiment),
        variationKey = it.key,
        activated = it.isUserActivatedForVariation
      )
    }

    val response = AssignedVariationsResponse(
      userId = USER_ID,
      experiments = experiments
    )

    val result = runBlocking {
      underTest.getAssignedVariations(USER_ID)
    }

    verifyBlocking(abTestingPersistenceService) { loadActiveAssignedVariationsFor(USER_ID) }
    assertEquals(response, result)
  }

  @Test
  fun `SHOULD return false FOR reassignVariationAndReturnSuccess WHEN incorrect experiment key`() {
    val request = ReassignUserRequestApiDto(
      experimentKey = "experimentKey",
      variationKey = Variations.GROUP_A.variationKey
    )

    val result = runBlocking {
      underTest.reassignVariationAndReturnSuccess(USER_ID, request)
    }

    verifyNoInteractions(abTestingService)
    assertEquals(false, result)
  }

  @Test
  fun `SHOULD return false FOR reassignVariationAndReturnSuccess WHEN incorrect variation key`() {
    val request = ReassignUserRequestApiDto(
      experimentKey = ClientExperiment.ENABLE_VENMO.key,
      variationKey = "variationKey"
    )

    val result = runBlocking {
      underTest.reassignVariationAndReturnSuccess(USER_ID, request)
    }

    verifyNoInteractions(abTestingService)

    assertEquals(false, result)
  }

  @Test
  fun `SHOULD return true FOR reassignVariationAndReturnSuccess WHEN all is ok`() {
    val request = ReassignUserRequestApiDto(
      experimentKey = ClientExperiment.AA_TEST.key,
      variationKey = Variations.GROUP_A.variationKey
    )

    val result = runBlocking {
      underTest.reassignVariationAndReturnSuccess(USER_ID, request)
    }

    verifyBlocking(abTestingService) { reassignUserToVariation(USER_ID, ClientExperiment.AA_TEST, Variations.GROUP_A) }

    assertEquals(true, result)
  }

  @Test
  fun `SHOULD return userId FOR resolveUserId WHEN userId is provided`() {
    val params = UserOrTrackingIdParam(userId = USER_ID, trackingId = null)

    val result = runBlocking {
      underTest.resolveUserId(params)
    }

    verifyNoInteractions(userService)
    assertEquals(USER_ID, result)
  }

  @Test
  fun `SHOULD return userId FOR resolveUserId WHEN trackingId is provided`() {
    val params = UserOrTrackingIdParam(userId = null, trackingId = TRACKING_ID)

    val result = runBlocking {
      underTest.resolveUserId(params)
    }

    verifyBlocking(userService) { fetchUserId(TRACKING_ID) }
    assertEquals(USER_ID, result)
  }

  @Test
  fun `SHOULD return userId FOR resolveUserId WHEN both id's are provided`() {
    val params = UserOrTrackingIdParam(userId = USER_ID, trackingId = TRACKING_ID)

    val result = runBlocking {
      underTest.resolveUserId(params)
    }

    verifyBlocking(userService, never()) { fetchUserId(TRACKING_ID) }
    assertEquals(USER_ID, result)
  }

  @Test
  fun `SHOULD return null FOR resolveUserId WHEN both id's are not provided`() {
    val params = UserOrTrackingIdParam(userId = null, trackingId = null)

    val result = runBlocking {
      underTest.resolveUserId(params)
    }

    verifyBlocking(userService, never()) { fetchUserId(TRACKING_ID) }
    assertNull(result)
  }

  @Test
  fun `SHOULD remove face FOR removeFaceByUserId`() {
    runBlocking {
      underTest.removeFaceByUserId(USER_ID)
    }

    verifyBlocking(verificationService) { removeFaces(USER_ID) }
  }

  @Test
  fun `SHOULD track jail break false for the user ON passJailBreakCheck`() {
    runBlocking {
      underTest.passJailBreakCheck(USER_ID)
    }

    verifyBlocking(userPersistenceService) { trackJailBreakCheck(USER_ID, false) }
  }

}