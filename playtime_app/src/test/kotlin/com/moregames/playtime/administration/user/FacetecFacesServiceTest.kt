package com.moregames.playtime.administration.user

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.secret.SecretService
import com.moregames.base.util.mock
import com.moregames.playtime.administration.dto.FacetecFacesRequestDto
import com.moregames.playtime.administration.dto.FacetecFacesResponseDto
import com.moregames.playtime.app.PlaytimeSecrets
import com.moregames.playtime.user.UserPersistenceService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock

class FacetecFacesServiceTest {
  private val userPersistenceService: UserPersistenceService = mock()
  private val mongodbApiClient: MongodbApiClient = mock()
  private val secretService: SecretService = mock()

  private val service = FacetecFacesService(userPersistenceService, mongodbApiClient, secretService)

  private val metabaseLink = "https://metabase.link"
  private val imageUrlTemplate =
    "https://playtime.us.justplay-internal.com/admin/facetec-dashboard/api/liveness/image?tid="

  @BeforeEach
  fun before() {
    secretService.mock({ secretValue(PlaytimeSecrets.METABASE_USER_DASHBOARD_LINK) }, metabaseLink)
  }

  @Test
  fun `SHOULD generate links for user ON getUsersInfo`() {
    userPersistenceService.mock({ fetchAllUserIds("gaid3") }, listOf("userId3"))

    mongodbApiClient.mock({ getUserImageTids("userId1") }, listOf("tid11", "tid12"))
    mongodbApiClient.mock({ getUserImageTids("userId2") }, listOf("tid21", "tid22"))
    mongodbApiClient.mock({ getUserImageTids("userId3") }, listOf("tid31", "tid32"))

    val request = FacetecFacesRequestDto(
      userIds = listOf("userId1", "userId2"),
      googleAdIds = listOf("gaid3")
    )
    val expected = FacetecFacesResponseDto(
      listOf(
        FacetecFacesResponseDto.UserRecord(
          metabaseUserDashboardLink = "$metabaseLink?user_id=userId1",
          listOfFaces = listOf(imageUrlTemplate + "tid11", imageUrlTemplate + "tid12")
        ),
        FacetecFacesResponseDto.UserRecord(
          metabaseUserDashboardLink = "$metabaseLink?user_id=userId2",
          listOfFaces = listOf(imageUrlTemplate + "tid21", imageUrlTemplate + "tid22")
        ),
        FacetecFacesResponseDto.UserRecord(
          metabaseUserDashboardLink = "$metabaseLink?user_id=userId3",
          listOfFaces = listOf(imageUrlTemplate + "tid31", imageUrlTemplate + "tid32")
        ),
      )
    )

    runBlocking { service.getUsersInfo(request) }
      .also {
        assertThat(it).isEqualTo(expected)
      }
  }

  @Test
  fun `SHOULD delete face ON deleteFace`() {
    mongodbApiClient.mock({ deleteFace("sessionId1") }, 1)
    runBlocking { service.deleteFace("sessionId1") }
      .also {
        assertThat(it).isEqualTo(1)
      }
  }
}