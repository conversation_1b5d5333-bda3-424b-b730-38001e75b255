package com.moregames.playtime.web

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.AppPlatform.IOS_WEB
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.dto.PlatformDeviceTokenDto
import com.moregames.base.dto.TrackingDataType
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.dto.WebAppUserAdditionalDataEventDto
import com.moregames.base.messaging.dto.WebAppUserJailBreakCheckEventDto
import com.moregames.base.messaging.dto.WebAppUserRegistrationEventDto
import com.moregames.base.messaging.dto.WebAppVerificationGpsLocationCheckEventDto
import com.moregames.base.user.dto.WebUserAdditionalData
import com.moregames.base.user.dto.WebUserStatusDto
import com.moregames.base.user.dto.WebUsersInGameVerification.DEVICE_EXAMINATION
import com.moregames.base.user.dto.WebUsersInGameVerification.JAIL_BREAK
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.WebAppGpsLocationCheckEventEffect
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.ios.examination.IosExaminationService
import com.moregames.playtime.notifications.status.UserNotificationStatusService
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.dto.UserExternalIds
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import org.mockito.kotlin.verifyNoMoreInteractions
import kotlin.test.assertFailsWith

@ExtendWith(MockExtension::class)
class WebUserServiceTest(
  private val userService: UserService,
  private val notificationStatusService: UserNotificationStatusService,
  private val userPersistenceService: UserPersistenceService,
  private val iosExaminationService: IosExaminationService,
  private val fraudScoreService: FraudScoreService,
  private val gamesService: GamesService,
  private val messageBus: MessageBus,
) {

  private val service = WebUserService(
    userService = userService,
    notificationStatusService = notificationStatusService,
    userPersistenceService = userPersistenceService,
    iosExaminationService = iosExaminationService,
    fraudScoreService = fraudScoreService,
    gamesService = gamesService,
    messageBus = messageBus,

    )

  companion object {
    private const val USER_ID = "userId"
    val testMessage = WebAppUserRegistrationEventDto(
      projectName = "projectName",
      userId = USER_ID,
      idfv = "idfv",
      deviceToken = "deviceToken",
      notificationEnabled = false
    )
    val testMessageData =
      WebAppUserAdditionalDataEventDto(
        projectName = "projectName",
        userId = USER_ID,
        idfv = "idfv",
        deviceToken = "deviceToken",
        notificationEnabled = false,
        idfa = "idfa",
        firebaseAppInstanceId = "firebaseAppInstanceId",
        adjustId = "adjustId"
      )
    val trackingData = TrackingData("idfv", IDFV, IOS_WEB)
    val trackingIdfaData = TrackingData("idfa", TrackingDataType.IDFA, IOS_WEB)
    val appVersion = AppVersionDto(IOS_WEB, 1)
    val mUserDtoStub = userDtoStub.copy(
      appPlatform = IOS_WEB,
      appVersion = 1
    )
    val externalIds = UserExternalIds(
      userId = USER_ID,
      googleAdId = "googleAdId",
      idfa = "idfa",
      adjustId = "adjustId",
      firebaseAppId = "firebaseAppId",
      trackingData = trackingData
    )
    val gpsCheckEvent = WebAppVerificationGpsLocationCheckEventDto(
      sessionId = "sessionId",
      applicationId = "applicationId",
      provided = true,
      location = "US",
      isMocked = true

    )
  }

  @BeforeEach
  fun init() {
    userService.mock({ getUser(USER_ID, includingDeleted = true) }, mUserDtoStub)
  }

  @Test
  fun `SHOULD track IDFV ON registerUser`() {
    runBlocking {
      service.registerUser(testMessage.copy(deviceToken = null, notificationEnabled = null))
    }

    verifyBlocking(userService) { getUser(USER_ID, includingDeleted = true) }
    verifyBlocking(userService) { updateTrackingData(USER_ID, trackingData, appVersion) }
    verifyNoMoreInteractions(userService)
    verifyNoInteractions(notificationStatusService)
  }

  @Test
  fun `SHOULD also track device token and notification status ON registerUser IF it was provided`() {
    runBlocking {
      service.registerUser(testMessage)
    }

    verifyBlocking(userService) { updateDeviceToken(USER_ID, "deviceToken", IOS_WEB) }
    verifyBlocking(notificationStatusService) { setUserNotificationsStatus(USER_ID, areEnabled = false) }
  }

  @Test
  fun `SHOULD return only JAIL BREAK verification ON getUserStatus WHEN we have everything`() {
    userService.mock({ fetchExternalIds(USER_ID) }, externalIds)
    notificationStatusService.mock({ isNotificationsStatusTracked(USER_ID) }, true)
    userPersistenceService.mock(
      { loadDeviceTokensForUsers(listOf(USER_ID)) },
      mapOf(USER_ID to PlatformDeviceTokenDto("someToken", IOS_WEB))
    )
    iosExaminationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)

    runBlocking {
      service.getUserStatus(USER_ID)
    }.let { assertThat(it).isEqualTo(WebUserStatusDto(requiredDataFields = emptyList(), verifications = listOf(JAIL_BREAK))) }
  }

  @Test
  fun `SHOULD return IDFV ON getUserStatus WHEN we have no IDFV`() {
    userService.mock({ fetchExternalIds(USER_ID) }, externalIds.copy(trackingData = null))
    notificationStatusService.mock({ isNotificationsStatusTracked(USER_ID) }, true)
    userPersistenceService.mock(
      { loadDeviceTokensForUsers(listOf(USER_ID)) },
      mapOf(USER_ID to PlatformDeviceTokenDto("someToken", IOS_WEB))
    )
    iosExaminationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)

    runBlocking {
      service.getUserStatus(USER_ID)
    }.let {
      assertThat(it).isEqualTo(
        WebUserStatusDto(
          requiredDataFields = listOf(WebUserAdditionalData.IDFV),
          verifications = listOf(JAIL_BREAK)
        )
      )
    }
  }

  @Test
  fun `SHOULD return IDFA ON getUserStatus WHEN we have no IDFA`() {
    userService.mock({ fetchExternalIds(USER_ID) }, externalIds.copy(trackingData = null, idfa = null))
    notificationStatusService.mock({ isNotificationsStatusTracked(USER_ID) }, true)
    userPersistenceService.mock(
      { loadDeviceTokensForUsers(listOf(USER_ID)) },
      mapOf(USER_ID to PlatformDeviceTokenDto("someToken", IOS_WEB))
    )
    iosExaminationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)

    runBlocking {
      service.getUserStatus(USER_ID)
    }.let {
      assertThat(it).isEqualTo(
        WebUserStatusDto(
          requiredDataFields = listOf(WebUserAdditionalData.IDFV, WebUserAdditionalData.IDFA),
          verifications = listOf(JAIL_BREAK)
        )
      )
    }
  }

  @Test
  fun `SHOULD return device token ON getUserStatus WHEN we have no device token`() {
    userService.mock({ fetchExternalIds(USER_ID) }, externalIds.copy(trackingData = null, idfa = null))
    notificationStatusService.mock({ isNotificationsStatusTracked(USER_ID) }, true)
    userPersistenceService.mock({ loadDeviceTokensForUsers(listOf(USER_ID)) }, emptyMap())
    iosExaminationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)

    runBlocking {
      service.getUserStatus(USER_ID)
    }.let {
      assertThat(it).isEqualTo(
        WebUserStatusDto(
          requiredDataFields = listOf(WebUserAdditionalData.IDFV, WebUserAdditionalData.IDFA, WebUserAdditionalData.FIREBASE_DEVICE_TOKEN),
          verifications = listOf(JAIL_BREAK)
        )
      )
    }
  }

  @Test
  fun `SHOULD return firebase app instance id ON getUserStatus WHEN we have no firebase app instance id`() {
    userService.mock({ fetchExternalIds(USER_ID) }, externalIds.copy(trackingData = null, idfa = null, firebaseAppId = null))
    notificationStatusService.mock({ isNotificationsStatusTracked(USER_ID) }, true)
    userPersistenceService.mock({ loadDeviceTokensForUsers(listOf(USER_ID)) }, emptyMap())
    iosExaminationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)

    runBlocking {
      service.getUserStatus(USER_ID)
    }.let {
      assertThat(it).isEqualTo(
        WebUserStatusDto(
          requiredDataFields = listOf(
            WebUserAdditionalData.IDFV, WebUserAdditionalData.IDFA, WebUserAdditionalData.FIREBASE_DEVICE_TOKEN,
            WebUserAdditionalData.FIREBASE_APP_INSTANCE_ID
          ),
          verifications = listOf(JAIL_BREAK)
        )
      )
    }
  }

  @Test
  fun `SHOULD return adjust id ON getUserStatus WHEN we have no adjust id`() {
    userService.mock({ fetchExternalIds(USER_ID) }, externalIds.copy(trackingData = null, idfa = null, firebaseAppId = null, adjustId = null))
    notificationStatusService.mock({ isNotificationsStatusTracked(USER_ID) }, true)
    userPersistenceService.mock({ loadDeviceTokensForUsers(listOf(USER_ID)) }, emptyMap())
    iosExaminationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)

    runBlocking {
      service.getUserStatus(USER_ID)
    }.let {
      assertThat(it).isEqualTo(
        WebUserStatusDto(
          requiredDataFields = listOf(
            WebUserAdditionalData.IDFV, WebUserAdditionalData.IDFA, WebUserAdditionalData.FIREBASE_DEVICE_TOKEN,
            WebUserAdditionalData.FIREBASE_APP_INSTANCE_ID, WebUserAdditionalData.ADJUST_ID
          ),
          verifications = listOf(JAIL_BREAK)
        )
      )
    }
  }

  @Test
  fun `SHOULD return all external ids ON getUserStatus WHEN we have nothing tracked`() {
    userService.mock({ fetchExternalIds(USER_ID) }, null)
    notificationStatusService.mock({ isNotificationsStatusTracked(USER_ID) }, true)
    userPersistenceService.mock({ loadDeviceTokensForUsers(listOf(USER_ID)) }, emptyMap())
    iosExaminationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)

    runBlocking {
      service.getUserStatus(USER_ID)
    }.let {
      assertThat(it).isEqualTo(
        WebUserStatusDto(
          requiredDataFields = listOf(
            WebUserAdditionalData.IDFV, WebUserAdditionalData.IDFA, WebUserAdditionalData.FIREBASE_DEVICE_TOKEN,
            WebUserAdditionalData.FIREBASE_APP_INSTANCE_ID, WebUserAdditionalData.ADJUST_ID
          ),
          verifications = listOf(JAIL_BREAK)
        )
      )
    }
  }

  @Test
  fun `SHOULD add device examination ON getUserStatus WHEN user has not passed it`() {
    userService.mock({ fetchExternalIds(USER_ID) }, null)
    notificationStatusService.mock({ isNotificationsStatusTracked(USER_ID) }, true)
    userPersistenceService.mock({ loadDeviceTokensForUsers(listOf(USER_ID)) }, emptyMap())
    iosExaminationService.mock({ wasSuccessfullyExamined(USER_ID) }, false)

    runBlocking {
      service.getUserStatus(USER_ID)
    }.let {
      assertThat(it).isEqualTo(
        WebUserStatusDto(
          requiredDataFields = listOf(
            WebUserAdditionalData.IDFV, WebUserAdditionalData.IDFA, WebUserAdditionalData.FIREBASE_DEVICE_TOKEN,
            WebUserAdditionalData.FIREBASE_APP_INSTANCE_ID, WebUserAdditionalData.ADJUST_ID
          ),
          verifications = listOf(DEVICE_EXAMINATION, JAIL_BREAK)
        )
      )
    }
  }

  @Test
  fun `SHOULD try to update all identifiers ON updateAdditionalData WHEN they were provided`() {
    runBlocking {
      service.updateAdditionalData(testMessageData)
    }

    verifyBlocking(userService) { getUser(USER_ID, includingDeleted = true) }
    verifyBlocking(userService) { updateTrackingData(USER_ID, trackingData, appVersion) }
    verifyBlocking(userService) { addUserTrackingData(USER_ID, trackingIdfaData) }
    verifyBlocking(userService) { updateDeviceToken(USER_ID, "deviceToken", IOS_WEB) }
    verifyBlocking(userService) { addOrUpdateFirebaseAppInstanceId(USER_ID, "firebaseAppInstanceId") }
    verifyBlocking(userService) { updateAdjustId(USER_ID, "adjustId") }
    verifyBlocking(notificationStatusService) { setUserNotificationsStatus(USER_ID, areEnabled = false) }
  }

  @Test
  fun `SHOULD try to update some identifiers ON updateAdditionalData WHEN they were provided`() {
    runBlocking {
      service.updateAdditionalData(testMessageData.copy(firebaseAppInstanceId = null, idfv = null, notificationEnabled = null))
    }

    verifyBlocking(userService) { getUser(USER_ID, includingDeleted = true) }
    verifyBlocking(userService) { addUserTrackingData(USER_ID, trackingIdfaData) }
    verifyBlocking(userService) { updateDeviceToken(USER_ID, "deviceToken", IOS_WEB) }
    verifyBlocking(userService) { updateAdjustId(USER_ID, "adjustId") }
  }

  @Test
  fun `SHOULD update nothing ON updateAdditionalData WHEN no identifiers were provided`() {
    runBlocking {
      service.updateAdditionalData(
        testMessageData.copy(
          firebaseAppInstanceId = null,
          idfv = null,
          notificationEnabled = null,
          idfa = null,
          deviceToken = null,
          adjustId = null
        )
      )
    }

    verifyBlocking(userService) { getUser(USER_ID, includingDeleted = true) }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD track jail break check result ON onJailBreakCheck`(jailBreak: Boolean) = runBlocking {
    val message = WebAppUserJailBreakCheckEventDto("projectName", USER_ID, jailBreak = jailBreak)

    runBlocking {
      service.onJailBreakCheck(message)
    }

    verifyBlocking(userPersistenceService) { trackJailBreakCheck(USER_ID, jailBreak) }
    if (jailBreak)
      verifyBlocking(fraudScoreService) { blockUserOnJailBreakUsageDetected(USER_ID) }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD provide response from persistence layer ON jailBreakCheckPassed`(jailBreak: Boolean) {
    userPersistenceService.mock({ jailBreakCheckPassed(USER_ID) }, jailBreak)

    runBlocking {
      service.jailBreakCheckPassed(USER_ID)
    }.let { assertThat(it).isEqualTo(jailBreak) }
  }

  @Test
  fun `SHOULD fall ungracefully on gpsLocationCheck WHEN we failed to define game id by application id for IOS platform`() {
    gamesService.mock({ getGameId("applicationId", IOS) }, null)

    assertFailsWith<IllegalArgumentException> {
      runBlocking {
        service.gpsLocationCheck(USER_ID, gpsCheckEvent)
      }
    }
  }

  @Test
  fun `SHOULD trigger gps check fraud control effect ON gpsLocationCheck WHEN we have real location`() {
    gamesService.mock({ getGameId("applicationId", IOS) }, 1)

    runBlocking {
      service.gpsLocationCheck(USER_ID, gpsCheckEvent)
    }

    verifyBlocking(userPersistenceService) { trackGpsLocationCheckAskedGame(USER_ID, 1, gpsCheckEvent.provided) }
    verifyBlocking(messageBus) {
      publish(
        WebAppGpsLocationCheckEventEffect(
          userId = USER_ID,
          sessionId = gpsCheckEvent.sessionId,
          location = gpsCheckEvent.location!!,
          isMocked = true
        )
      )
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["notProvided", "noLocation"])
  fun `SHOULD only track gps location check triggered fact ON gpsLocationCheck WHEN location was not provided or provided without location`(option: String) {
    gamesService.mock({ getGameId("applicationId", IOS) }, 1)

    val mGpsCheckEvent = gpsCheckEvent.copy(provided = false).takeIf { option == "notProvided" }
      ?: gpsCheckEvent.copy(location = null)
    runBlocking {
      service.gpsLocationCheck(USER_ID, mGpsCheckEvent)
    }

    verifyBlocking(userPersistenceService) { trackGpsLocationCheckAskedGame(USER_ID, 1, mGpsCheckEvent.provided) }
    verifyNoInteractions(messageBus)
  }

}