package com.moregames.playtime.games

import assertk.assertThat
import assertk.assertions.containsExactly
import assertk.assertions.isEqualTo
import com.moregames.base.app.BuildVariant
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.util.ApplicationId.BALL_BOUNCE_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_APP_ID
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_APP_ID
import com.moregames.base.util.ClientVersionsSupport.IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE
import com.moregames.base.util.TEST_IO_SCOPE
import com.moregames.base.util.TimeService
import com.moregames.base.util.answer
import com.moregames.base.util.mock
import com.moregames.playtime.app.IMAGES_ROOT
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.ios.dto.GameApiDto
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.UserPersistenceService.GamePlayStatusDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.utils.iosGameOfferStub
import com.moregames.playtime.utils.iosPreGameScreenStub
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit.HOURS
import java.time.temporal.ChronoUnit.MINUTES
import java.util.*
import kotlin.test.assertNull
import kotlin.test.assertTrue

@OptIn(ExperimentalCoroutinesApi::class)
class IosGameServiceTest {
  private val gamePersistenceService: GamePersistenceService = mock()
  private val userService: UserService = mock()
  private val imageService: ImageService = mock()
  private val translationService: UserTranslationService = mock()
  private val timeService: TimeService = mock()

  private val underTest: IosGameService = IosGameService(
    gamePersistenceService = gamePersistenceService,
    userService = userService,
    imageService = imageService,
    translationService = translationService,
    buildVariant = BuildVariant.TEST,
    coroutineScope = { TEST_IO_SCOPE },
    timeService = timeService
  )

  @BeforeEach
  fun init() {
    Dispatchers.setMain(StandardTestDispatcher())
    gamePersistenceService.mock({ loadVisibleIosGamesByIds(any()) }, emptyList())
    gamePersistenceService.mock({ loadVisibleIosGamesExcludeIds(any()) }, emptyList())
    translationService.mock({ tryTranslate(eq("description"), eq(Locale.ENGLISH), any()) }, "descriptionTranslation")
    translationService.mock({ tryTranslate(eq("infoTextInstallTop"), eq(Locale.ENGLISH), any()) }, "infoTextInstallTopTranslation")
    userService.mock({ loadUserGameCoins(any()) }, emptyMap())
    imageService.answer({ toUrl(any()) }, { IMAGES_ROOT + it.getArgument(0) as String })
    timeService.mock({ today() }, today)
    userService.mock({ getUser(any(), any()) }, userDtoStub.copy(appPlatform = IOS, appVersion = IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE + 1))
  }

  @Test
  fun `SHOULD return new games ON loadNewGames`() {
    val userId = UUID.randomUUID().toString()
    userService.mock({ loadUserGameCoins(userId) }, mapOf(1 to null, 2 to null))
    //it should not work for new app versions
    gamePersistenceService.mock(
      { loadVisibleIosBoostedGame(today) },
      iosGameOfferStub.copy(
        id = 5,
        applicationId = BALL_BOUNCE_APP_ID,
        iosApplicationId = "id1667518216",
        iosGameUrl = "ballbounce:LaunchBallBounce",
        webglGame = IosGameOffer.Webgl("TBD")
      )
    )
    gamePersistenceService.mock(
      { loadVisibleIosGamesExcludeIds(setOf(1, 2)) },
      listOf(
        iosGameOfferStub.copy(
          id = 1,
          applicationId = TREASURE_MASTER_APP_ID,
          iosApplicationId = "id6444407009",
          iosGameUrl = "treasuremaster:LaunchTreasureMaster"
        ),
        iosGameOfferStub.copy(
          id = 2,
          applicationId = SOLITAIRE_VERSE_APP_ID,
          iosApplicationId = "id1667538256",
          iosGameUrl = "solitaireverse:LaunchSolitaireVerse",
          webglGame = IosGameOffer.Webgl("TBD")
        ),
      )
    )

    translationService.mock({ tryTranslate("\$_ios_treasuremaster_exp_description", Locale.ENGLISH, userId) }, "Defeat monsters and collect the treasure!")
    translationService.mock({ tryTranslate("\$_ios_solitaireverse_exp_description", Locale.ENGLISH, userId) }, "The most beloved card game of all times!")

    val actual = runBlocking { underTest.loadNewGames(userId, Locale.ENGLISH, null) }

    assertThat(actual).containsExactly(
      GameApiDto(
        id = 1,
        applicationId = "com.gimica.treasuremaster",
        name = "name",
        description = "descriptionTranslation",
        iconFilename = IMAGES_ROOT + "iconFilename",
        imageFilename = IMAGES_ROOT + "imageFilename",
        infoTextInstall = "infoTextInstallTopTranslation",
        iosApplicationId = "id6444407009",
        iosGameUrl = "treasuremaster:LaunchTreasureMaster",
        trendingIcon = "https://storage.googleapis.com/public-playtime/images/ios_treasuremaster_trending_icon.jpg",
        testplayDescription = "Defeat monsters and collect the treasure!",
        //showBadge = true,
      ),
      GameApiDto(
        id = 2,
        applicationId = "com.gimica.solitaireverse",
        name = "name",
        description = "descriptionTranslation",
        iconFilename = IMAGES_ROOT + "iconFilename",
        imageFilename = IMAGES_ROOT + "imageFilename",
        infoTextInstall = "infoTextInstallTopTranslation",
        iosApplicationId = "id1667538256",
        iosGameUrl = "solitaireverse:LaunchSolitaireVerse",
        trendingIcon = "https://storage.googleapis.com/public-playtime/images/ios_solitaireverse_trending_icon.jpg",
        testplayDescription = "The most beloved card game of all times!"
      ),
    )
    verifyBlocking(gamePersistenceService, never()) { loadVisibleIosBoostedGame(today) }
  }

  @Test
  fun `SHOULD return new games with first boosted game ON loadNewGames when there is a boosted game and old version`() {
    val userId = UUID.randomUUID().toString()
    userService.mock({ getUser(userId, true) }, userDtoStub.copy(appPlatform = IOS, appVersion = IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE))
    userService.mock({ loadUserGameCoins(userId) }, emptyMap())
    gamePersistenceService.mock(
      { loadVisibleIosBoostedGame(today) },
      iosGameOfferStub.copy(
        id = 5,
        applicationId = BALL_BOUNCE_APP_ID,
        iosApplicationId = "id1667518216",
        iosGameUrl = "ballbounce:LaunchBallBounce",
        webglGame = IosGameOffer.Webgl("TBD"),
        imageFilename = "boosted_image_file_name.jpg"
      )
    )
    gamePersistenceService.mock(
      { loadVisibleIosGamesExcludeIds(emptySet()) },
      listOf(
        iosGameOfferStub.copy(
          id = 1,
          applicationId = TREASURE_MASTER_APP_ID,
          iosApplicationId = "id6444407009",
          iosGameUrl = "treasuremaster:LaunchTreasureMaster"
        ),
        iosGameOfferStub.copy(
          id = 2,
          applicationId = SOLITAIRE_VERSE_APP_ID,
          iosApplicationId = "id1667538256",
          iosGameUrl = "solitaireverse:LaunchSolitaireVerse",
          webglGame = IosGameOffer.Webgl("TBD")
        ),
      )
    )

    translationService.mock({ tryTranslate("\$_ios_treasuremaster_exp_description", Locale.ENGLISH, userId) }, "Defeat monsters and collect the treasure!")
    translationService.mock({ tryTranslate("\$_ios_solitaireverse_exp_description", Locale.ENGLISH, userId) }, "The most beloved card game of all times!")
    translationService.mock({ tryTranslate("\$_ios_ballbounce_exp_description", Locale.ENGLISH, userId) }, "Some translated description")


    val actual = runBlocking { underTest.loadNewGames(userId, Locale.ENGLISH, null) }

    assertThat(actual).containsExactly(
      *listOf(
        GameApiDto(
          id = 5,
          applicationId = "com.gimica.ballbounce",
          name = "name",
          description = "descriptionTranslation",
          iconFilename = IMAGES_ROOT + "iconFilename",
          imageFilename = IMAGES_ROOT + "boosted_image_file_name.jpg",
          infoTextInstall = "infoTextInstallTopTranslation",
          iosApplicationId = "id1667518216",
          iosGameUrl = "ballbounce:LaunchBallBounce",
          trendingIcon = "https://storage.googleapis.com/public-playtime/images/ios_ballbounce_trending_icon.jpg",
          testplayDescription = "Some translated description",
        ),
        GameApiDto(
          id = 1,
          applicationId = "com.gimica.treasuremaster",
          name = "name",
          description = "descriptionTranslation",
          iconFilename = IMAGES_ROOT + "iconFilename",
          imageFilename = IMAGES_ROOT + "ios_promo_2025_04_16/TreasureMaster_badge.png",
          infoTextInstall = "infoTextInstallTopTranslation",
          iosApplicationId = "id6444407009",
          iosGameUrl = "treasuremaster:LaunchTreasureMaster",
          trendingIcon = "https://storage.googleapis.com/public-playtime/images/ios_treasuremaster_trending_icon.jpg",
          testplayDescription = "Defeat monsters and collect the treasure!",
          //showBadge = true,
        ),
        GameApiDto(
          id = 2,
          applicationId = "com.gimica.solitaireverse",
          name = "name",
          description = "descriptionTranslation",
          iconFilename = IMAGES_ROOT + "iconFilename",
          imageFilename = IMAGES_ROOT + "ios_promo_2025_04_16/SolitaireVerse_badge.png",
          infoTextInstall = "infoTextInstallTopTranslation",
          iosApplicationId = "id1667538256",
          iosGameUrl = "solitaireverse:LaunchSolitaireVerse",
          trendingIcon = "https://storage.googleapis.com/public-playtime/images/ios_solitaireverse_trending_icon.jpg",
          testplayDescription = "The most beloved card game of all times!"
        ),
      ).toTypedArray()
    )
  }

  @Test
  fun `SHOULD return correct amount of games ON loadNewGames`() {
    val userId = UUID.randomUUID().toString()
    userService.mock({ loadUserGameCoins(userId) }, mapOf(1 to null, 2 to null))
    gamePersistenceService.mock(
      { loadVisibleIosGamesExcludeIds(setOf(1, 2)) },
      listOf(
        iosGameOfferStub.copy(id = 1, iosApplicationId = "id6444407009", iosGameUrl = "treasuremaster:LaunchTreasureMaster"),
        iosGameOfferStub.copy(id = 2, iosApplicationId = "id1667538256", iosGameUrl = "solitaireverse:LaunchSolitaireVerse1"),
        iosGameOfferStub.copy(id = 3, iosApplicationId = "id1667538257", iosGameUrl = "solitaireverse:LaunchSolitaireVerse2"),
        iosGameOfferStub.copy(id = 4, iosApplicationId = "id1667538258", iosGameUrl = "solitaireverse:LaunchSolitaireVerse3"),
        iosGameOfferStub.copy(id = 5, iosApplicationId = "id1667538259", iosGameUrl = "solitaireverse:LaunchSolitaireVerse4"),
        iosGameOfferStub.copy(id = 6, iosApplicationId = "id1667538260", iosGameUrl = "solitaireverse:LaunchSolitaireVerse5"),
        iosGameOfferStub.copy(id = 7, iosApplicationId = "id1667538261", iosGameUrl = "solitaireverse:LaunchSolitaireVerse6"),
        iosGameOfferStub.copy(id = 8, iosApplicationId = "id1667538262", iosGameUrl = "solitaireverse:LaunchSolitaireVerse7"),
        iosGameOfferStub.copy(id = 9, iosApplicationId = "id1667538263", iosGameUrl = "solitaireverse:LaunchSolitaireVerse8"),
      )
    )

    val actual = runBlocking { underTest.loadNewGames(userId, Locale.ENGLISH, null) }

    val result = listOf(
      GameApiDto(
        id = 1,
        applicationId = "applicationId",
        name = "name",
        description = "descriptionTranslation",
        iconFilename = IMAGES_ROOT + "iconFilename",
        imageFilename = IMAGES_ROOT + "imageFilename",
        infoTextInstall = "infoTextInstallTopTranslation",
        iosApplicationId = "id6444407009",
        iosGameUrl = "treasuremaster:LaunchTreasureMaster",
        //showBadge = true,
      ),
      GameApiDto(
        id = 2,
        applicationId = "applicationId",
        name = "name",
        description = "descriptionTranslation",
        iconFilename = IMAGES_ROOT + "iconFilename",
        imageFilename = IMAGES_ROOT + "imageFilename",
        infoTextInstall = "infoTextInstallTopTranslation",
        iosApplicationId = "id1667538256",
        iosGameUrl = "solitaireverse:LaunchSolitaireVerse1"
      ),
      GameApiDto(
        id = 3,
        applicationId = "applicationId",
        name = "name",
        description = "descriptionTranslation",
        iconFilename = IMAGES_ROOT + "iconFilename",
        imageFilename = IMAGES_ROOT + "imageFilename",
        infoTextInstall = "infoTextInstallTopTranslation",
        iosApplicationId = "id1667538257",
        iosGameUrl = "solitaireverse:LaunchSolitaireVerse2"
      ),
      GameApiDto(
        id = 4,
        applicationId = "applicationId",
        name = "name",
        description = "descriptionTranslation",
        iconFilename = IMAGES_ROOT + "iconFilename",
        imageFilename = IMAGES_ROOT + "imageFilename",
        infoTextInstall = "infoTextInstallTopTranslation",
        iosApplicationId = "id1667538258",
        iosGameUrl = "solitaireverse:LaunchSolitaireVerse3"
      ),
      GameApiDto(
        id = 5,
        applicationId = "applicationId",
        name = "name",
        description = "descriptionTranslation",
        iconFilename = IMAGES_ROOT + "iconFilename",
        imageFilename = IMAGES_ROOT + "imageFilename",
        infoTextInstall = "infoTextInstallTopTranslation",
        iosApplicationId = "id1667538259",
        iosGameUrl = "solitaireverse:LaunchSolitaireVerse4"
      ),
    )

    assertThat(actual).containsExactly(*result.toTypedArray())
  }

  @Test
  fun `SHOULD return played games ON loadNewGames WHEN new games list is empty`() {
    val userId = UUID.randomUUID().toString()
    userService.mock(
      { loadUserGameCoins(userId) },
      mapOf(
        1 to GamePlayStatusDto(1, 2000, true, Instant.now(), Instant.now().minus(1, HOURS)),
        2 to GamePlayStatusDto(2, 2000, true, Instant.now(), Instant.now()),
        3 to GamePlayStatusDto(3, 2000, true, Instant.now(), Instant.now().minus(20, MINUTES)),
        4 to GamePlayStatusDto(4, 2000, true, Instant.now(), Instant.now().minus(25, MINUTES)),
        5 to GamePlayStatusDto(5, 2000, true, Instant.now(), Instant.now().minus(15, MINUTES)),
        6 to GamePlayStatusDto(6, 2000, true, Instant.now(), Instant.now().minus(2, HOURS)),
      )
    )
    gamePersistenceService.mock(
      { loadVisibleIosGamesByIds(setOf(1, 2, 3, 4, 5, 6)) },
      listOf(
        iosGameOfferStub.copy(id = 1, orderKey = 6, iosApplicationId = "id6444407009", iosGameUrl = "LaunchTreasureMaster:"),
        iosGameOfferStub.copy(id = 2, orderKey = 2, iosApplicationId = "id1667538256", iosGameUrl = "LaunchSolitaireVerse:"),
        iosGameOfferStub.copy(id = 3, orderKey = 3, iosApplicationId = "id1667538111", iosGameUrl = "playMe:"),
        iosGameOfferStub.copy(id = 4, orderKey = 4, iosApplicationId = "id1667538222", iosGameUrl = "andMe:"),
        iosGameOfferStub.copy(id = 5, orderKey = 5, iosApplicationId = "id1667538333", iosGameUrl = "iAmTheBestGame:"),
        iosGameOfferStub.copy(id = 6, orderKey = 1, iosApplicationId = "id1667538444", iosGameUrl = "coinsWaiting:"),
      )
    )
    gamePersistenceService.mock({ loadVisibleIosGamesExcludeIds(setOf(1, 2, 3, 4, 5, 6)) }, emptyList())

    val actual = runBlocking { underTest.loadNewGames(userId, Locale.ENGLISH, null) }

    //game 6 less recent that 1, but have higher order; then 6 should be first by order
    assertThat(actual.map { it.id }).containsExactly(6, 1)
  }

  @Test
  fun `SHOULD return played games and boosted game on the first place ON loadNewGames WHEN new games list is empty and old version`() {
    val userId = UUID.randomUUID().toString()
    userService.mock({ getUser(userId, true) }, userDtoStub.copy(appPlatform = IOS, appVersion = IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE))
    gamePersistenceService.mock(
      { loadVisibleIosBoostedGame(today) },
      iosGameOfferStub.copy(
        id = 5,
        applicationId = BALL_BOUNCE_APP_ID,
        iosApplicationId = "id1667518216",
        iosGameUrl = "ballbounce:LaunchBallBounce",
        webglGame = IosGameOffer.Webgl("TBD")
      )
    )
    userService.mock(
      { loadUserGameCoins(userId) },
      mapOf(
        1 to GamePlayStatusDto(1, 2000, true, Instant.now(), Instant.now().minus(1, HOURS)),
        2 to GamePlayStatusDto(2, 2000, true, Instant.now(), Instant.now()),
        3 to GamePlayStatusDto(3, 2000, true, Instant.now(), Instant.now().minus(20, MINUTES)),
        4 to GamePlayStatusDto(4, 2000, true, Instant.now(), Instant.now().minus(25, MINUTES)),
        5 to GamePlayStatusDto(5, 2000, true, Instant.now(), Instant.now().minus(15, MINUTES)),
        6 to GamePlayStatusDto(6, 2000, true, Instant.now(), Instant.now().minus(2, HOURS)),
      )
    )
    gamePersistenceService.mock(
      { loadVisibleIosGamesByIds(setOf(1, 2, 3, 4, 5, 6)) },
      listOf(
        iosGameOfferStub.copy(id = 1, orderKey = 6, iosApplicationId = "id6444407009", iosGameUrl = "LaunchTreasureMaster:"),
        iosGameOfferStub.copy(id = 2, orderKey = 2, iosApplicationId = "id1667538256", iosGameUrl = "LaunchSolitaireVerse:"),
        iosGameOfferStub.copy(id = 3, orderKey = 3, iosApplicationId = "id1667538111", iosGameUrl = "playMe:"),
        iosGameOfferStub.copy(id = 4, orderKey = 4, iosApplicationId = "id1667538222", iosGameUrl = "andMe:"),
        iosGameOfferStub.copy(id = 5, orderKey = 5, iosApplicationId = "id1667538333", iosGameUrl = "iAmTheBestGame:"),
        iosGameOfferStub.copy(id = 6, orderKey = 1, iosApplicationId = "id1667538444", iosGameUrl = "coinsWaiting:"),
      )
    )
    gamePersistenceService.mock({ loadVisibleIosGamesExcludeIds(setOf(1, 2, 3, 4, 5, 6)) }, emptyList())

    val actual = runBlocking { underTest.loadNewGames(userId, Locale.ENGLISH, null) }

    assertThat(actual.map { it.id }).containsExactly(5, 6, 1)
  }

  @Test
  fun `SHOULD return emptyList ON loadNewGames WHEN new games list is empty AND played games list is small`() {
    val userId = UUID.randomUUID().toString()
    userService.mock(
      { loadUserGameCoins(userId) },
      mapOf(
        1 to GamePlayStatusDto(1, 2000, true, Instant.now(), Instant.now().minus(1, HOURS)),
      )
    )
    gamePersistenceService.mock(
      { loadVisibleIosGamesByIds(setOf(1)) },
      listOf(
        iosGameOfferStub.copy(id = 1, iosApplicationId = "id6444407009", iosGameUrl = "LaunchTreasureMaster:"),
      )
    )
    gamePersistenceService.mock({ loadVisibleIosGamesExcludeIds(setOf(1)) }, emptyList())

    val actual = runBlocking { underTest.loadNewGames(userId, Locale.ENGLISH, null) }

    assertTrue(actual.isEmpty())
  }

  @Test
  fun `SHOULD return played games AND made correct conversion to dto ON loadPlayedIosGames`() {
    val userId = UUID.randomUUID().toString()
    userService.mock({ loadUserGameCoins(userId) }, mapOf(1 to null, 2 to null))
    gamePersistenceService.mock(
      { loadVisibleIosGamesByIds(setOf(1, 2)) },
      listOf(
        iosGameOfferStub.copy(
          id = 1,
          applicationId = TREASURE_MASTER_APP_ID,
          iosApplicationId = "id6444407009",
          iosGameUrl = "treasuremaster:LaunchTreasureMaster",
        ),
        iosGameOfferStub.copy(
          id = 2,
          applicationId = SOLITAIRE_VERSE_APP_ID,
          iosApplicationId = "id1667538256",
          iosGameUrl = "solitaireverse:LaunchSolitaireVerse"
        ),
      )
    )

    val actual = runBlocking { underTest.loadPlayedGames(userId, Locale.ENGLISH) }

    assertThat(actual).isEqualTo(
      listOf(
        GameApiDto(
          id = 1,
          applicationId = "com.gimica.treasuremaster",
          name = "name",
          description = "descriptionTranslation",
          iconFilename = IMAGES_ROOT + "iconFilename",
          imageFilename = IMAGES_ROOT + "imageFilename",
          infoTextInstall = "infoTextInstallTopTranslation",
          iosApplicationId = "id6444407009",
          iosGameUrl = "treasuremaster:LaunchTreasureMaster",
          trendingIcon = "https://storage.googleapis.com/public-playtime/images/ios_treasuremaster_trending_icon.jpg",
        ),
        GameApiDto(
          id = 2,
          applicationId = "com.gimica.solitaireverse",
          name = "name",
          description = "descriptionTranslation",
          iconFilename = IMAGES_ROOT + "iconFilename",
          imageFilename = IMAGES_ROOT + "imageFilename",
          infoTextInstall = "infoTextInstallTopTranslation",
          iosApplicationId = "id1667538256",
          iosGameUrl = "solitaireverse:LaunchSolitaireVerse",
          trendingIcon = "https://storage.googleapis.com/public-playtime/images/ios_solitaireverse_trending_icon.jpg"
        )
      )
    )
  }

  @Test
  fun `SHOULD return played games AND made correct conversion to dto ON loadPlayedIosGames WHEN old users`() {
    val userId = UUID.randomUUID().toString()
    userService.mock({ getUser(userId, true) }, userDtoStub.copy(appPlatform = IOS, appVersion = IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE))
    userService.mock({ loadUserGameCoins(userId) }, mapOf(1 to null, 2 to null))
    gamePersistenceService.mock(
      { loadVisibleIosGamesByIds(setOf(1, 2)) },
      listOf(
        iosGameOfferStub.copy(
          id = 1,
          applicationId = TREASURE_MASTER_APP_ID,
          iosApplicationId = "id6444407009",
          iosGameUrl = "treasuremaster:LaunchTreasureMaster",
        ),
        iosGameOfferStub.copy(
          id = 2,
          applicationId = SOLITAIRE_VERSE_APP_ID,
          iosApplicationId = "id1667538256",
          iosGameUrl = "solitaireverse:LaunchSolitaireVerse"
        ),
      )
    )

    val actual = runBlocking { underTest.loadPlayedGames(userId, Locale.ENGLISH) }

    assertThat(actual).isEqualTo(
      listOf(
        GameApiDto(
          id = 1,
          applicationId = "com.gimica.treasuremaster",
          name = "name",
          description = "descriptionTranslation",
          iconFilename = IMAGES_ROOT + "iconFilename",
          imageFilename = IMAGES_ROOT + "ios_promo_2025_04_16/TreasureMaster_badge.png",
          infoTextInstall = "infoTextInstallTopTranslation",
          iosApplicationId = "id6444407009",
          iosGameUrl = "treasuremaster:LaunchTreasureMaster",
          trendingIcon = "https://storage.googleapis.com/public-playtime/images/ios_treasuremaster_trending_icon.jpg",
        ),
        GameApiDto(
          id = 2,
          applicationId = "com.gimica.solitaireverse",
          name = "name",
          description = "descriptionTranslation",
          iconFilename = IMAGES_ROOT + "iconFilename",
          imageFilename = IMAGES_ROOT + "ios_promo_2025_04_16/SolitaireVerse_badge.png",
          infoTextInstall = "infoTextInstallTopTranslation",
          iosApplicationId = "id1667538256",
          iosGameUrl = "solitaireverse:LaunchSolitaireVerse",
          trendingIcon = "https://storage.googleapis.com/public-playtime/images/ios_solitaireverse_trending_icon.jpg"
        )
      )
    )
  }

  @Test
  fun `SHOULD return game offers ON loadNewsRelatedGameOffers`() {
    val userId = UUID.randomUUID().toString()
    userService.mock({ loadUserGameCoins(userId) }, mapOf(1 to null, 2 to null))
    gamePersistenceService.mock(
      { loadVisibleIosGamesByApplicationIds(setOf("com.gimica.treasuremaster", "com.gimica.solitaireverse")) },
      listOf(
        iosGameOfferStub.copy(
          id = 1,
          applicationId = "com.gimica.treasuremaster",
          iosApplicationId = "id6444407009",
          iosGameUrl = "treasuremaster:LaunchTreasureMaster",
          orderKey = 1
        ),
        iosGameOfferStub.copy(
          id = 2,
          applicationId = "com.gimica.solitaireverse",
          iosApplicationId = "id1667538256",
          iosGameUrl = "solitaireverse:LaunchSolitaireVerse",
          orderKey = 2
        ),
      )
    )

    val actual = runBlocking { underTest.loadNewsRelatedGameOffers(setOf("com.gimica.treasuremaster", "com.gimica.solitaireverse")) }

    assertThat(actual).isEqualTo(
      listOf(
        IosGameOffer(
          id = 1,
          applicationId = "com.gimica.treasuremaster",
          name = "name",
          description = "description",
          iconFilename = "iconFilename",
          imageFilename = "imageFilename",
          infoTextInstall = "infoTextInstallTop",
          iosApplicationId = "id6444407009",
          iosGameUrl = "treasuremaster:LaunchTreasureMaster",
          orderKey = 1,
        ),
        IosGameOffer(
          id = 2,
          applicationId = "com.gimica.solitaireverse",
          name = "name",
          description = "description",
          iconFilename = "iconFilename",
          imageFilename = "imageFilename",
          infoTextInstall = "infoTextInstallTop",
          iosApplicationId = "id1667538256",
          iosGameUrl = "solitaireverse:LaunchSolitaireVerse",
          orderKey = 2
        )
      )
    )
  }

  @Test
  fun `SHOULD return sorted and cut played games ON loadPlayedIosGames`() {
    val userId = UUID.randomUUID().toString()
    userService.mock(
      { loadUserGameCoins(userId) },
      mapOf(
        1 to GamePlayStatusDto(1, 2000, true, Instant.now(), Instant.now().minus(1, HOURS)),
        2 to GamePlayStatusDto(2, 2000, true, Instant.now(), Instant.now()),
        3 to GamePlayStatusDto(3, 2000, true, Instant.now(), Instant.now().minus(20, MINUTES)),
        4 to GamePlayStatusDto(4, 2000, true, Instant.now(), Instant.now().minus(25, MINUTES)),
        5 to GamePlayStatusDto(5, 2000, true, Instant.now(), Instant.now().minus(15, MINUTES)),
      )
    )
    gamePersistenceService.mock(
      { loadVisibleIosGamesByIds(setOf(1, 2, 3, 4, 5)) },
      listOf(
        iosGameOfferStub.copy(id = 1, iosApplicationId = "id6444407009", iosGameUrl = "LaunchTreasureMaster:"),
        iosGameOfferStub.copy(id = 2, iosApplicationId = "id1667538256", iosGameUrl = "LaunchSolitaireVerse:"),
        iosGameOfferStub.copy(id = 3, iosApplicationId = "id1667538111", iosGameUrl = "playMe:"),
        iosGameOfferStub.copy(id = 4, iosApplicationId = "id1667538222", iosGameUrl = "andMe:"),
        iosGameOfferStub.copy(id = 5, iosApplicationId = "id1667538333", iosGameUrl = "iAmTheBestGame:"),
      )
    )

    val actual = runBlocking { underTest.loadPlayedGames(userId, Locale.ENGLISH) }

    assertThat(actual.map { it.id }).containsExactly(2, 5, 3, 4)
  }

  @Test
  fun `SHOULD return sorted and cut played games ON loadPlayedIosGames WHEN a little of played games`() {
    val userId = UUID.randomUUID().toString()
    userService.mock(
      { loadUserGameCoins(userId) },
      mapOf(
        2 to GamePlayStatusDto(2, 2000, true, Instant.now(), Instant.now()),
        5 to GamePlayStatusDto(5, 2000, true, Instant.now(), Instant.now().minus(15, MINUTES)),
      )
    )
    gamePersistenceService.mock(
      { loadVisibleIosGamesByIds(setOf(2, 5)) },
      listOf(
        iosGameOfferStub.copy(id = 2, iosApplicationId = "id1667538256", iosGameUrl = "LaunchSolitaireVerse:"),
        iosGameOfferStub.copy(id = 5, iosApplicationId = "id1667538333", iosGameUrl = "iAmTheBestGame:"),
      )
    )

    val actual = runBlocking { underTest.loadPlayedGames(userId, Locale.ENGLISH) }

    assertThat(actual.map { it.id }).containsExactly(2, 5)
  }

  @Test
  fun `SHOULD not enrich game list ON loadNewGames WHEN user is not IOS_PRE_GAME_SCREEN participant`() {
    val userId = UUID.randomUUID().toString()
    userService.mock({ loadUserGameCoins(userId) }, mapOf(1 to null, 2 to null))
    gamePersistenceService.mock(
      { loadVisibleIosGamesExcludeIds(setOf(1, 2)) },
      listOf(
        iosGameOfferStub.copy(id = 1, iosApplicationId = "id6444407009", iosGameUrl = "treasuremaster:LaunchTreasureMaster"),
        iosGameOfferStub.copy(id = 2, iosApplicationId = "id1667538256", iosGameUrl = "solitaireverse:LaunchSolitaireVerse"),
      )
    )

    runBlocking { underTest.loadNewGames(userId, Locale.ENGLISH, null) }.let {
      assertNull(it[0].preGameScreen)
      assertNull(it[1].preGameScreen)
    }
  }

  @Test
  fun `SHOULD enrich game list ON loadNewGames WHEN user is IOS_PRE_GAME_SCREEN participant`() = runTest {
    val userId = UUID.randomUUID().toString()
    userService.mock({ loadUserGameCoins(userId) }, mapOf(1 to null, 2 to null))
    gamePersistenceService.mock(
      { loadVisibleIosGamesExcludeIds(setOf(1, 2)) },
      listOf(
        iosGameOfferStub.copy(id = 500001, iosApplicationId = "id6444407009", iosGameUrl = "LaunchTreasureMasters:"),
        iosGameOfferStub.copy(id = 500002, iosApplicationId = "id1667538256", iosGameUrl = "LaunchSolitaireVerse:"),
        iosGameOfferStub.copy(id = 500006, iosApplicationId = "id6446891888", iosGameUrl = "LaunchChefsQuestMatchSensation:"),
        iosGameOfferStub.copy(id = 500012, iosApplicationId = "id6471265311", iosGameUrl = "LaunchWaterSorter:"),
        iosGameOfferStub.copy(id = 500013, iosApplicationId = "id6478022829", iosGameUrl = "LaunchFairytaleMansion:"),
        iosGameOfferStub.copy(id = 500014, iosApplicationId = "id6499209581", iosGameUrl = "LaunchPuzzlePopBlaster:"),
        iosGameOfferStub.copy(id = 500015, iosApplicationId = "id6499209500", iosGameUrl = "LaunchTileMatchUltimate:"),
      )
    )
    gamePersistenceService.mock({ loadIosPreGameScreens() }, iosPreGameScreenStub)

    underTest.loadNewGames(userId, Locale.ENGLISH, 17).let {
      assertEquals(iosPreGameScreenStub[500001]?.toApiDto(imageService), it[0].preGameScreen)
      assertEquals(iosPreGameScreenStub[500002]?.toApiDto(imageService), it[1].preGameScreen)
    }
  }

  companion object {
    val today: LocalDate = LocalDate.now()
  }
}
