package com.moregames.playtime.games.addwidget

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.ClientExperiment.ANDROID_BEGINNER_FRIENDLY_GAMES
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.variations.AndroidNewOfferButtonVariation
import com.moregames.base.offers.dto.OffersConfig
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_APP_ID
import com.moregames.base.util.answer
import com.moregames.base.util.mock
import com.moregames.playtime.app.IMAGES_ROOT
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.AndroidOnlineUsersService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import com.moregames.playtime.user.cashout.dto.CashoutPeriodDto
import com.moregames.playtime.user.offer.GameAdditionalWidgets
import com.moregames.playtime.user.offer.GameStyle
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import java.time.Instant

class GameAdditionalWidgetServiceTest {
  private val imageService: ImageService = mock()
  private val abTestingService: AbTestingService = mock {
    onBlocking { isUserExperimentParticipant(any(), any()) } doReturn false
    onBlocking { isEm2Participant(any()) } doReturn false
    onBlocking { assignedVariationValue(userId = USER_ID, ClientExperiment.ANDROID_NEW_OFFER_BUTTON) } doReturn DEFAULT
  }
  private val cashoutPeriodsService: CashoutPeriodsService = mock()
  private val rewardingFacade: RewardingFacade = mock()
  private val userService: UserService = mock()
  private lateinit var underTest: GameAdditionalWidgetService
  private val offersConfig = OffersConfig(
    buttonText = "button_text",
    unlockText = "unlock_text",
    additionalOfferSubtext = "additional_offer_subtext"
  )

  private val androidOnlineUsersService: AndroidOnlineUsersService = mock {
    onBlocking { getActiveUsers() } doReturn 65601

  }

  companion object {
    const val USER_ID = "userId"

    private const val MONEY_0 = 11932779L
    private const val MONEY_1 = 10739501L

    const val MONEY_BADGE_URL = "https://storage.googleapis.com/public-playtime/images/game_widget_money_paid_out_en.png"
  }

  @BeforeEach
  fun init() {
    imageService.answer({ toUrl(any()) }, { IMAGES_ROOT + it.getArgument(0) as String })
    underTest = GameAdditionalWidgetService(imageService, abTestingService, cashoutPeriodsService, rewardingFacade, userService, androidOnlineUsersService)
  }

  @Test
  fun `SHOULD return gameAdditionalWidgets ON createAdditionalWidgets WHEN first game`() {
    val expected = ExtendedGameAdditionalWidget(
      widgets = GameAdditionalWidgets(
        moneyBadgeUrl = MONEY_BADGE_URL,
        moneyPaidOut = MONEY_0
      ),
      style = GameStyle.LARGE_IMAGE,
      subText = offersConfig.buttonText
    )

    val actual = runBlocking {
      underTest.createExtendedAdditionalWidget(
        index = 0,
        userId = USER_ID,
        applicationId = TREASURE_MASTER_APP_ID,
        offersConfig = offersConfig
      )
    }
    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return gameAdditionalWidgets ON createAdditionalWidgets WHEN second game`() {
    val expected = ExtendedGameAdditionalWidget(
      widgets = GameAdditionalWidgets(
        moneyBadgeUrl = MONEY_BADGE_URL,
        moneyPaidOut = MONEY_1
      ),
      style = GameStyle.LARGE_IMAGE,
      subText = offersConfig.buttonText
    )

    val actual = runBlocking {
      underTest.createExtendedAdditionalWidget(
        index = 1,
        userId = USER_ID,
        applicationId = TREASURE_MASTER_APP_ID,
        offersConfig = offersConfig
      )
    }
    assertThat(actual).isEqualTo(expected)
  }

  @ParameterizedTest
  @CsvSource(
    //first item, first CP - show badge
    "0,true,1,false,,true",
    //not the first item,  first CP - null
    "1,true,1,false,,",
    //not exp participant - null
    "0,false,1,false,,",
    //first CP, has game earnings - show badge
    "0,true,1,true,2025-02-12T00:00:00Z,true",
    //not the first CP,has earnings but not played game - show badge
    "0,true,2,true,,true",
    //not the first CP, user played game, but no earnings - show badge
    "0,true,2,false,2025-02-12T00:00:00Z,true",
    //not the first CP, user played game, has earnings - null
    "0,true,2,true,2025-02-12T00:00:00Z,",
  )
  fun `SHOULD return beginnerFriendly ON createAdditionalWidgets`(
    index: Int,
    expParticipant: Boolean,
    currentCpCounter: Int,
    hasEarnings: Boolean,
    lastGameCoinsDate: Instant?,
    expectedResult: Boolean?
  ) {
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, ANDROID_BEGINNER_FRIENDLY_GAMES) }, expParticipant)
    cashoutPeriodsService.mock(
      { getCurrentCashoutPeriod(USER_ID) }, CashoutPeriodDto(
        userId = USER_ID,
        periodStart = Instant.parse("2025-02-12T00:00:00Z"),
        periodEnd = Instant.parse("2025-02-12T00:00:00Z").plusSeconds(180),
        coinGoal = 42,
        counter = currentCpCounter,
        noEarningsCounter = 3,
        coinGoalMilestones = listOf(1, 2, 3),
      )
    )
    rewardingFacade.mock({ userEverHadEarnings(USER_ID) }, hasEarnings)
    userService.mock({ getUserLastGameCoinsDate(USER_ID) }, lastGameCoinsDate)

    val actual = runBlocking { underTest.createExtendedAdditionalWidget(index, USER_ID, TREASURE_MASTER_APP_ID, offersConfig) }

    assertThat(actual.widgets.beginnerFriendly).isEqualTo(expectedResult)
  }

  @ParameterizedTest
  @CsvSource(
    //first item, first CP - show badge
    "0,true,1,false,,true",
    //not the first item,  first CP - null
    "1,true,1,false,,",
    //not exp participant - null
    "0,false,1,false,,",
    //first CP, has game earnings - show badge
    "0,true,1,true,2025-02-12T00:00:00Z,true",
    //not the first CP,has earnings but not played game - show badge
    "0,true,2,true,,true",
    //not the first CP, user played game, but no earnings - show badge
    "0,true,2,false,2025-02-12T00:00:00Z,true",
    //not the first CP, user played game, has earnings - null
    "0,true,2,true,2025-02-12T00:00:00Z,",
  )
  fun `SHOULD return beginnerFriendly ON createAdditionalWidgets WHEN EM2`(
    index: Int,
    expParticipant: Boolean,
    currentCpCounter: Int,
    hasEarnings: Boolean,
    lastGameCoinsDate: Instant?,
    expectedResult: Boolean?
  ) {
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, ANDROID_BEGINNER_FRIENDLY_GAMES) }, expParticipant)
    cashoutPeriodsService.mock(
      { getCurrentCashoutPeriod(USER_ID) }, CashoutPeriodDto(
        userId = USER_ID,
        periodStart = Instant.parse("2025-02-12T00:00:00Z"),
        periodEnd = Instant.parse("2025-02-12T00:00:00Z").plusSeconds(180),
        coinGoal = 42,
        counter = currentCpCounter,
        noEarningsCounter = 3,
        coinGoalMilestones = listOf(1, 2, 3),
      )
    )
    rewardingFacade.mock({ userEverHadEarnings(USER_ID) }, hasEarnings)
    userService.mock({ getUserLastGameEm2CoinsDate(USER_ID) }, lastGameCoinsDate)
    abTestingService.mock({ isEm2Participant(any()) }, true)

    val actual = runBlocking { underTest.createExtendedAdditionalWidget(index, USER_ID, TREASURE_MASTER_APP_ID, offersConfig) }

    assertThat(actual.widgets.beginnerFriendly).isEqualTo(expectedResult)
  }

  @Test
  fun `SHOULD return gameAdditionalWidgets ON createAdditionalWidgets WHEN first game AND AddCTAOldPayout`() {
    abTestingService.mock(
      { assignedVariationValue(userId = USER_ID, ClientExperiment.ANDROID_NEW_OFFER_BUTTON) },
      AndroidNewOfferButtonVariation.AddCTAOldPayout
    )
    val expected = ExtendedGameAdditionalWidget(
      widgets = GameAdditionalWidgets(
        moneyBadgeUrl = MONEY_BADGE_URL,
        moneyPaidOut = MONEY_0
      ),
      style = GameStyle.WIDGET_REVAMPED_V1,
      subText = "Play & Earn \$243"
    )

    val actual = runBlocking {
      underTest.createExtendedAdditionalWidget(
        index = 0,
        userId = USER_ID,
        applicationId = TREASURE_MASTER_APP_ID,
        offersConfig = offersConfig
      )
    }
    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return gameAdditionalWidgets ON createAdditionalWidgets WHEN first game AND AddCTANewPayout`() {
    abTestingService.mock(
      { assignedVariationValue(userId = USER_ID, ClientExperiment.ANDROID_NEW_OFFER_BUTTON) },
      AndroidNewOfferButtonVariation.AddCTANewPayout
    )
    val expected = ExtendedGameAdditionalWidget(
      widgets = GameAdditionalWidgets(
        moneyBadgeUrl = null,
        moneyPaidOut = MONEY_0
      ),
      style = GameStyle.WIDGET_REVAMPED_V1,
      subText = "Play & Earn \$243"
    )

    val actual = runBlocking {
      underTest.createExtendedAdditionalWidget(
        index = 0,
        userId = USER_ID,
        applicationId = TREASURE_MASTER_APP_ID,
        offersConfig = offersConfig
      )
    }
    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return gameAdditionalWidgets ON createAdditionalWidgets WHEN first game AND FullSet`() {
    abTestingService.mock(
      { assignedVariationValue(userId = USER_ID, ClientExperiment.ANDROID_NEW_OFFER_BUTTON) },
      AndroidNewOfferButtonVariation.FullSet
    )
    val expected = ExtendedGameAdditionalWidget(
      widgets = GameAdditionalWidgets(
        moneyBadgeUrl = null,
        moneyPaidOut = MONEY_0,
        teaserLabelUrl = "https://storage.googleapis.com/public-playtime/images/game_widget_tm_teaser.png",
        storeRating = "4.5",
        playersLive = "52k Playing"
      ),
      style = GameStyle.WIDGET_REVAMPED_V1,
      subText = "Play & Earn \$243"
    )

    val actual = runBlocking {
      underTest.createExtendedAdditionalWidget(
        index = 0,
        userId = USER_ID,
        applicationId = TREASURE_MASTER_APP_ID,
        offersConfig = offersConfig
      )
    }
    assertThat(actual).isEqualTo(expected)
  }

  @ParameterizedTest
  @CsvSource(
    "0,com.gimica.treasuremaster,4.5,52k Playing,Play & Earn $243,https://storage.googleapis.com/public-playtime/images/game_widget_tm_teaser.png",
    "1,com.gimica.solitaireverse,4.5,49k Playing,Play & Earn $218,https://storage.googleapis.com/public-playtime/images/game_widget_solitaire_teaser.png",
    "2,com.gimica.mergeblast,4.5,51k Playing,Play & Earn $223,https://storage.googleapis.com/public-playtime/images/game_widget_mb_bb_teaser.png",
    "3,com.gimica.marblemadness,4.6,47k Playing,Play & Earn $204,https://storage.googleapis.com/public-playtime/images/game_widget_default_teaser.png",
    "4,com.gimica.ballbounce,4.5,44k Playing,Play & Earn $198,https://storage.googleapis.com/public-playtime/images/game_widget_ball_bounce_teaser.png",
    "5,com.gimica.sugarmatch,4.5,41k Playing,Play & Earn $201,https://storage.googleapis.com/public-playtime/images/game_widget_default_teaser.png",
    "6,com.gimica.puzzlepopblaster,4.6,38k Playing,Play & Earn $193,https://storage.googleapis.com/public-playtime/images/game_widget_default_teaser.png",
    "7,com.gimica.idlemergefun,4.5,35k Playing,Play & Earn $184,https://storage.googleapis.com/public-playtime/images/game_widget_default_teaser.png",
    "8,com.gimica.helixdash,4.6,33k Playing,Play & Earn $176,https://storage.googleapis.com/public-playtime/images/game_widget_default_teaser.png",
    "9,com.pinmaster.screwpuzzle,4.5,30k Playing,Play & Earn $168,https://storage.googleapis.com/public-playtime/images/game_widget_default_teaser.png",
    "10,some.app.id,4.5,28k Playing,Play & Earn $160,https://storage.googleapis.com/public-playtime/images/game_widget_default_teaser.png",
  )
  fun `SHOULD return gameAdditionalWidgets ON createAdditionalWidgets WHEN first 11 games AND FullSet`(
    gameIndex: Int,
    applicationId: String,
    storeRating: String,
    playersLive: String,
    subText: String,
    teaserLabelUrl: String,
  ) {
    abTestingService.mock(
      { assignedVariationValue(userId = USER_ID, ClientExperiment.ANDROID_NEW_OFFER_BUTTON) },
      AndroidNewOfferButtonVariation.FullSet
    )

    val actual = runBlocking {
      underTest.createExtendedAdditionalWidget(
        index = gameIndex,
        userId = USER_ID,
        applicationId = applicationId,
        offersConfig = offersConfig
      )
    }
    assertThat(actual.subText).isEqualTo(subText)
    assertThat(actual.style).isEqualTo(GameStyle.WIDGET_REVAMPED_V1)
    assertThat(actual.widgets.playersLive).isEqualTo(playersLive)
    assertThat(actual.widgets.storeRating).isEqualTo(storeRating)
    assertThat(actual.widgets.teaserLabelUrl).isEqualTo(teaserLabelUrl)
  }

}