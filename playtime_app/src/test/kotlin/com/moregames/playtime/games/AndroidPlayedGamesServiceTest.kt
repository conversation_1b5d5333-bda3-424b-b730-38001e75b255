package com.moregames.playtime.games

import com.moregames.playtime.games.AndroidPlayedGamesService.NonPlayedAndPlayedGames
import com.moregames.playtime.games.viewed.ViewedGameDto
import com.moregames.playtime.utils.androidGameOfferStub
import kotlinx.coroutines.runBlocking
import org.apache.commons.lang3.RandomStringUtils
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verifyBlocking
import java.time.Instant
import java.util.*


class AndroidPlayedGamesServiceTest {
  private val gamePersistenceService: GamePersistenceService = mock()
  private val underTest: AndroidPlayedGamesService = AndroidPlayedGamesService(
    gamePersistenceService = gamePersistenceService,
  )
  private lateinit var userId: String
  private val now = Instant.now()

  @BeforeEach
  fun init() {
    userId = UUID.randomUUID().toString()
  }

  @Test
  fun `SHOULD make correct call of gamePersistenceService ON trackViewedGames`() {
    val viewedGames = generateViewedGames(userId)

    runBlocking {
      underTest.trackViewedGames(viewedGames)
    }

    verifyBlocking(gamePersistenceService, times(1)) { trackViewedGames(viewedGames) }
  }

  @Test
  fun `SHOULD split games for played ON splitPlayedGames`() {
    val games = listOf(
      androidGameOfferStub,
      androidGameOfferStub.copy(id = 50, lastPlayedAt = now.minusSeconds(10)),
      androidGameOfferStub.copy(id = 42, lastPlayedAt = now),
      androidGameOfferStub.copy(id = 47, lastPlayedAt = now.minusSeconds(5)),
    )
    val expectedNonPlayedGames = listOf(
      androidGameOfferStub,
    )
    val expectedPlayedGames = listOf(
      androidGameOfferStub.copy(id = 42, lastPlayedAt = now),
      androidGameOfferStub.copy(id = 47, lastPlayedAt = now.minusSeconds(5)),
      androidGameOfferStub.copy(id = 50, lastPlayedAt = now.minusSeconds(10)),
    )
    val expected = NonPlayedAndPlayedGames(expectedNonPlayedGames, expectedPlayedGames)

    val actual = underTest.splitPlayedGames(games)

    assertEquals(actual, expected)
  }

  private fun generateViewedGames(userId: String, size: Int = 3) =
    (1..size).map { ViewedGameDto(userId, RandomStringUtils.randomNumeric(2).toInt()) }.toSet()

}