package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.*
import com.google.inject.Provider
import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalance
import com.justplayapps.service.rewarding.earnings.table.UserLastLowEarningsCashoutPeriodTable
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.OfferWallType
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.*
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.dto.PlatformDeviceTokenDto
import com.moregames.base.dto.TrackingDataType
import com.moregames.base.dto.TrackingDataType.IDFA
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource
import com.moregames.base.table.*
import com.moregames.base.table.UserIpsTable.updatedAt
import com.moregames.base.user.dto.CreateUserRequestDto
import com.moregames.base.user.dto.SimInfo
import com.moregames.base.user.dto.UserDeviceSpecificationApiDto
import com.moregames.base.util.*
import com.moregames.playtime.buseffects.InvalidateUserCacheEffect
import com.moregames.playtime.buseffects.InvalidateUserExternalIDsCacheEffect
import com.moregames.playtime.earnings.table.CountryLimitsTierTable
import com.moregames.playtime.earnings.table.LimitTierTable
import com.moregames.playtime.notifications.NotificationType
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.UserPersistenceService.GamePlayStatusDto
import com.moregames.playtime.user.UserPersistenceService.UserIdAndTrackingId
import com.moregames.playtime.user.cashout.prepareUserCashoutTransaction
import com.moregames.playtime.user.dto.*
import com.moregames.playtime.user.fraudscore.FraudScoreChangeReason
import com.moregames.playtime.user.fraudscore.table.UserFraudScoreFrozenScoreTable
import com.moregames.playtime.user.fraudscore.table.UserFraudScoreTable
import com.moregames.playtime.user.fraudscore.table.UserFraudScoreTransactionTable
import com.moregames.playtime.user.table.*
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.util.DEFAULT_USER_LOCALE
import com.moregames.playtime.utils.ES_LOCALE
import com.moregames.playtime.utils.FR_LOCALE
import com.moregames.playtime.utils.cashoutTransactionStub
import com.moregames.playtime.utils.createUserDataStub
import kotlinx.coroutines.runBlocking
import org.apache.commons.lang3.RandomStringUtils.randomAlphabetic
import org.apache.commons.lang3.RandomStringUtils.randomAlphanumeric
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.util.*
import java.util.Random
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.assertNull

@Suppress("DEPRECATION")
@ExtendWith(DatabaseExtension::class)
class UserPersistenceServiceTest(
  private val database: Database,
) {

  private lateinit var service: UserPersistenceService

  private val abTestingService: AbTestingService = mock()
  private val rewardingFacade: RewardingFacade = mock()
  private val timeService: TimeService = mock()
  private val buildVariantProvider: Provider<BuildVariant> = mock()
  private val encryptionService: EncryptionService = mock()
  private val messageBus: MessageBus = mock()

  private val userStub = User(
    userId = "userId",
    googleAdId = null,
    deviceToken = null,
    coinsGoal = 1,
    createdAt = Instant.now(),
    appPlatform = ANDROID,
    appVersion = 0,
    countryCode = "US",
    isConsentedToAnalytics = false,
  )

  companion object {
    private val now = Instant.now()
  }

  @BeforeEach
  fun before() {
    TimeZone.setDefault(TimeZone.getTimeZone("GMT"))
    whenever(buildVariantProvider.get()).thenReturn(BuildVariant.PRODUCTION)
    whenever(timeService.now()).thenReturn(now)
    rewardingFacade.mock({ inflatingCoinsMultiplier(any()) }, 1)

    service = UserPersistenceService(
      database = database,
      rewardingFacade = rewardingFacade,
      timeService = timeService,
      buildVariantProvider = buildVariantProvider,
      encryptionService = encryptionService,
      messageBus = messageBus
    )

    transaction(database) {
      UserAppVersionInfoTable.deleteAll()
    }
  }

  @Test
  fun `SHOULD return true banned flag ON is user banned call WHEN user was banned`() {
    runBlocking {
      val userId = database.prepareUser()

      service.banUser(userId, BanReason.HIGH_FRAUD_SCORE, "reason")

      assertThat(isUserBanned(userId)).isTrue()
    }
  }

  @Test
  fun `SHOULD return false banned flag ON is user banned call WHEN user is banned but whitelisted`() {
    runBlocking {
      val userId = database.prepareUser(isBanned = true)
      transaction(database) {
        UserWhitelistTable.insert { it[UserWhitelistTable.userId] = userId }
      }

      assertThat(isUserBanned(userId)).isFalse()
    }
  }

  @Test
  fun `SHOULD throw exception ON isUserBanned call WHEN no user`() {
    val userId = UUID.randomUUID().toString()

    assertThrows<UserRecordNotFoundException> {
      runBlocking { isUserBanned(userId) }
    }
  }

  @Test
  fun `SHOULD add user to whitelist ON whitelistUser`() {
    val userId = database.prepareUser()

    runBlocking { service.whitelistUser(userId) }

    val result = transaction(database) {
      UserWhitelistTable
        .select { UserWhitelistTable.userId eq userId }
        .map { it[UserWhitelistTable.userId] }
        .first()
    }

    assertThat(result).isEqualTo(userId)
    verifyUserCacheInvalidationEffectWasPublished(userId)
  }

  @Test
  fun `SHOULD return true ON isUserWhitelisted WHEN user is whitelisted`() {
    val userId = database.prepareUser()

    val actual = runBlocking {
      service.whitelistUser(userId)
      service.isUserWhitelisted(userId)
    }

    verifyUserCacheInvalidationEffectWasPublished(userId)
    assertThat(actual).isTrue()
  }

  @Test
  fun `SHOULD return false ON isUserWhitelisted WHEN user is NOT whitelisted`() {
    val userId = database.prepareUser()

    val actual = runBlocking {
      service.isUserWhitelisted(userId)
    }

    assertThat(actual).isFalse()
  }

  @Test
  fun `SHOULD track ban reason ON banUser call`() {
    val userId = database.prepareUser()

    runBlocking {
      service.banUser(userId, BanReason.HIGH_FRAUD_SCORE, "reason")
    }

    val (reason, description) = transaction(database) {
      UserBanInfoTable
        .slice(UserBanInfoTable.reason, UserBanInfoTable.description)
        .select { UserBanInfoTable.userId eq userId }
        .map { it[UserBanInfoTable.reason] to it[UserBanInfoTable.description] }
        .first()
    }

    assertThat(reason).isEqualTo(BanReason.HIGH_FRAUD_SCORE.name)
    assertThat(description).isEqualTo("reason")
  }

  @Test
  fun `SHOULD have idempotent behavior ON banUser call`() {
    val userId = database.prepareUser()

    runBlocking {
      service.banUser(userId, BanReason.HIGH_FRAUD_SCORE, "reason")
      service.banUser(userId, BanReason.HIGH_FRAUD_SCORE, "emailReason")
    }

    val (reason, description) = transaction(database) {
      UserBanInfoTable
        .slice(UserBanInfoTable.reason, UserBanInfoTable.description)
        .select { UserBanInfoTable.userId eq userId }
        .map { it[UserBanInfoTable.reason] to it[UserBanInfoTable.description] }
        .first()
    }

    assertThat(reason).isEqualTo(BanReason.HIGH_FRAUD_SCORE.name)
    assertThat(description).isEqualTo("reason")
  }

  @Test
  fun `SHOULD return error ON ban user WHEN user doesn't exist`() {
    assertFailsWith(UserRecordNotFoundException::class) {
      runBlocking {
        service.banUser("anotherUserId", BanReason.HIGH_FRAUD_SCORE, "reason")
      }
    }
  }

  @Test
  fun `SHOULD return user ids ON loadUserIdsForIp WHEN user IP match the request`() {
    runBlocking {
      val userId1 = database.prepareUser()
      val userId2 = database.prepareUser()
      val userId3 = database.prepareUser()
      val userId4 = database.prepareUser()
      transaction(database) {
        UserIpsTable.replace { it[userId] = userId1; it[ip] = "***********" }
        UserIpsTable.replace { it[userId] = userId2; it[ip] = "***********" }
        UserIpsTable.replace { it[userId] = userId3; it[ip] = "***********" }

        UserIpsTable.replace { it[userId] = userId4; it[ip] = "***********" } // deleted user
        UserTable.update({ UserTable.id eq userId4 }) { it[isDeleted] = true }
      }

      val actual = service.loadUserIdsForIps(setOf("***********"))

      assertThat(actual.size).isEqualTo(2)
      assertThat(actual).containsOnly(userId1 to "***********", userId3 to "***********")
    }
  }

  @Test
  fun `SHOULD return user ids ON loadUserIdsForGaids`() {
    runBlocking {
      database.prepareUser(googleAdId = "gaId1", isDeleted = true)
      val userId2 = database.prepareUser(googleAdId = "gaId2")
      database.prepareUser(googleAdId = "gaId3")
      val userId4 = database.prepareUser(googleAdId = "gaId1")

      val actual = service.loadUserIdsForGaids(setOf("gaId1", "gaId2"))

      assertThat(actual.size).isEqualTo(2)
      assertThat(actual).containsOnly(userId2 to "gaId2", userId4 to "gaId1")
    }
  }

  @Test
  fun `SHOULD return user ids ON loadUserIdsForTrackingDataSet`() {
    val firstTrackingData = generateTrackingData()
    val secondTrackingData = generateTrackingData()
    val thirdTrackingData = generateTrackingData()
    val userId = database.prepareUser(trackingData = firstTrackingData)
    val userId2 = database.prepareUser(trackingData = secondTrackingData)
    database.prepareUser(trackingData = thirdTrackingData, isDeleted = true)

    runBlocking {
      service.loadUserIdsForTrackingDataSet(setOf(firstTrackingData, secondTrackingData, thirdTrackingData))
    }.let {
      assertThat(it.size).isEqualTo(2)
      assertThat(it).containsOnly(UserTrackingData(userId, firstTrackingData), UserTrackingData(userId2, secondTrackingData))
    }
  }

  @Test
  fun `SHOULD mark user as LAT ON setLimitedTrackingUser`() {
    val userId = database.prepareUser()

    val before = transaction(database) {
      UserLimitedTrackingTable
        .slice(UserLimitedTrackingTable.userId)
        .select { UserLimitedTrackingTable.userId eq userId }
        .map { it[UserLimitedTrackingTable.userId] }
        .firstOrNull()
    }
    assertThat(before).isNull()

    runBlocking {
      service.setLimitedTrackingUser(userId)
    }

    val after = transaction(database) {
      UserLimitedTrackingTable
        .slice(UserLimitedTrackingTable.userId)
        .select { UserLimitedTrackingTable.userId eq userId }
        .map { it[UserLimitedTrackingTable.userId] }
        .firstOrNull()
    }
    assertThat(after).isEqualTo(userId)
    verifyUserCacheInvalidationEffectWasPublished(userId)
  }

  @Test
  fun `SHOULD update app version ON updateUserAppVersion`() {
    val userId = database.prepareUser()

    val appVersion = AppVersionDto(IOS, 5)
    runBlocking {
      service.updateUserAppVersion(userId, appVersion)
    }

    val actual = transaction(database) {
      UserTable
        .select { UserTable.id eq userId }
        .map { it[UserTable.appPlatform] to it[UserTable.appVersion] }
        .first()
    }

    assertThat(actual).isEqualTo(Pair(appVersion.platform.name, appVersion.version))
    verifyUserCacheInvalidationEffectWasPublished(userId)
  }

  @Test
  fun `SHOULD log app version change ON logUserAppVersionChange`() {
    val userId = database.prepareUser()
    val appVersion = AppVersionDto(IOS, 10500)

    runBlocking {
      service.logUserAppVersionChange(userId, appVersion)
    }

    val actual = transaction(database) {
      UserAppVersionInfoTable
        .select { UserAppVersionInfoTable.userId eq userId }
        .map {
          it[UserAppVersionInfoTable.appPlatform] to it[UserAppVersionInfoTable.appVersion]
        }
        .first()
    }

    assertThat(actual).isEqualTo(Pair(appVersion.platform.name, appVersion.version))
  }

  @Test
  fun `SHOULD return app version ON loadUserAppVersionAndLastActivityDay`() {
    val userId = database.prepareUser()

    val appVersion = AppVersionDto(ANDROID, 5)
    runBlocking {
      service.updateUserAppVersion(userId, appVersion)
    }

    val actual = runBlocking {
      service.loadUserAppVersionAndLastActivityDay(userId)
    }
    assertThat(actual.first).isEqualTo(appVersion)
    assertThat(actual.second).isEqualTo(LocalDate.now())
  }

  @Test
  fun `SHOULD return appVersion = 0 ON loadUserAppVersionAndLastActivityDay WHEN appVersion in DB is null`() {
    val userId = database.prepareUser()

    val actual = runBlocking {
      service.loadUserAppVersionAndLastActivityDay(userId)
    }
    assertThat(actual.first).isEqualTo(AppVersionDto(ANDROID, 0))
    assertThat(actual.second).isEqualTo(LocalDate.now())
  }

  @Test
  fun `SHOULD update last activity at day on updateUserLastActivityDay`() {
    val yesterday = LocalDate.now().minusDays(1)
    val userId = database.prepareUser()

    runBlocking {
      service.updateUserLastActivityDay(userId, yesterday)
    }

    val actual = runBlocking {
      service.loadUserAppVersionAndLastActivityDay(userId)
    }

    assertThat(actual.second).isEqualTo(yesterday)
    verifyUserCacheInvalidationEffectWasPublished(userId)
  }

  @Test
  fun `SHOULD be tolerant to insert with same unique key ON insertOrUpdate`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserIpsTable.insertOrUpdate(updatedAt) {
        it[UserIpsTable.userId] = userId
        it[ip] = "**********"
      }
      UserIpsTable.insertOrUpdate(updatedAt) {
        it[UserIpsTable.userId] = userId
        it[ip] = "**********"
      }
    }
  }

  @Test
  fun `SHOULD track users ip and country ON saveUserIpReturnNew WHEN ip is not tracked yet`() {
    val userId = database.prepareUser()
    val ip = "usertrack.0.0.1"

    runBlocking {
      service.saveUserIpReturnNew(userId, ip, "US")
    }

    val actual = transaction(database) {
      UserIpsTable
        .select { (UserIpsTable.userId eq userId) and (UserIpsTable.ip eq ip) }
        .first()
    }

    assertThat(actual[UserIpsTable.ip]).isEqualTo(ip)
    assertThat(actual[UserIpsTable.countryCode]).isEqualTo("US")
  }

  @Test
  fun `SHOULD return all recent tracked countries ON loadUserCountryCodes`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val oldUpdatedAt = Instant.now().minus(31, ChronoUnit.DAYS).truncatedTo(ChronoUnit.SECONDS)
    transaction(database) {
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "cc.0.0.1"; it[countryCode] = "US" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "cc.0.0.2"; it[countryCode] = "CA" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "cc.0.0.3"; it[countryCode] = "US" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "cc.0.0.3"; it[countryCode] = "GB"; it[updatedAt] = oldUpdatedAt }

      UserIpsTable.replace { it[userId] = userId2; it[ip] = "cc.0.0.1"; it[countryCode] = "US" }
      UserIpsTable.replace { it[userId] = userId2; it[ip] = "cc.0.0.2"; it[countryCode] = "MX" }
    }

    runBlocking {
      assertThat(service.loadRecentCountryCodes(userId1)).containsExactly("US", "CA")
      assertThat(service.loadRecentCountryCodes(userId2)).containsExactly("US", "MX")
    }
  }

  @Test
  fun `SHOULD update updated_at value ON saveUserIpReturnNew WHEN update_at is older than 30 days`() {
    val userId = database.prepareUser()
    val ip = "*********"
    val oldUpdatedAt = Instant.now().minus(31, ChronoUnit.DAYS).truncatedTo(ChronoUnit.SECONDS)

    transaction(database) {
      UserIpsTable.insert {
        it[UserIpsTable.userId] = userId
        it[UserIpsTable.ip] = ip
        it[updatedAt] = oldUpdatedAt
      }
    }

    runBlocking {
      service.saveUserIpReturnNew(userId, ip, "ZZ")
    }

    val newUpdatedAt = transaction(database) {
      UserIpsTable
        .select { (UserIpsTable.userId eq userId) and (UserIpsTable.ip eq ip) }
        .first()[updatedAt]
    }

    assertThat(newUpdatedAt).isNotEqualTo(oldUpdatedAt)
    assertThat(Duration.between(oldUpdatedAt, newUpdatedAt)).isGreaterThan(Duration.of(30, ChronoUnit.DAYS))
  }

  @Test
  fun `SHOULD return amount of unique IP associated to user ON getUserFraudScore`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val userId3 = database.prepareUser()
    val userId4 = database.prepareUser()
    transaction(database) {
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "uipt.0.0.1" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "uipt.0.0.2" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "uipt.0.0.3" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "uipt.0.0.5" } // unique

      UserIpsTable.replace { it[userId] = userId2; it[ip] = "uipt.0.0.1" }
      UserIpsTable.replace { it[userId] = userId2; it[ip] = "uipt.0.0.3" }

      UserIpsTable.replace { it[userId] = userId3; it[ip] = "uipt.0.0.2" }
      UserIpsTable.replace { it[userId] = userId3; it[ip] = "uipt.0.0.3" }
      UserIpsTable.replace { it[userId] = userId3; it[ip] = "uipt.0.0.4" }

      UserIpsTable.replace { it[userId] = userId4; it[ip] = "uipt.0.0.4" }
      UserIpsTable.replace { it[userId] = userId4; it[ip] = "uipt.0.0.10" } // unique
      UserIpsTable.replace { it[userId] = userId4; it[ip] = "uipt.0.0.11" } // unique
      UserIpsTable.replace { it[userId] = userId4; it[ip] = "uipt.0.0.12" } // unique
    }

    runBlocking {
      assertThat(service.countUniqueIps(userId1)).isEqualTo(1)
      assertThat(service.countUniqueIps(userId2)).isEqualTo(0)
      assertThat(service.countUniqueIps(userId3)).isEqualTo(0)
      assertThat(service.countUniqueIps(userId4)).isEqualTo(3)
    }
  }

  @Test
  fun `SHOULD return list of user ips ON getIpsByUserId`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()

    transaction(database) {
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "gipbuid.0.0.1" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "gipbuid.0.0.2" }
      UserIpsTable.replace { it[userId] = userId2; it[ip] = "gipbuid.0.0.3" }
      UserIpsTable.replace { it[userId] = userId2; it[ip] = "gipbuid.0.0.1" }
    }

    val userId1Ips = runBlocking {
      service.getIpsByUserId(userId1)
    }

    assertThat(userId1Ips).containsOnly("gipbuid.0.0.1", "gipbuid.0.0.2")

    val userId2Ips = runBlocking {
      service.getIpsByUserId(userId2)
    }

    assertThat(userId2Ips).containsOnly("gipbuid.0.0.1", "gipbuid.0.0.3")
  }

  @Test
  internal fun `SHOULD return Google Ad id ON fetchGoogleAdId`() {
    val expected = "gaid"
    val userId = database.prepareUser(googleAdId = expected)

    val actual = runBlocking {
      service.fetchGoogleAdId(userId)
    }

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  internal fun `SHOULD return null ON fetchGoogleAdId WHEN user exists but googleAdId is not defined`() {
    val userId = database.prepareUser(googleAdId = null)

    val actual = runBlocking {
      service.fetchGoogleAdId(userId)
    }

    assertThat(actual).isNull()
  }

  @Test
  internal fun `SHOULD throw UserRecordNotFoundException ON fetchGoogleAdId WHEN user doesn't exist`() {
    assertFailsWith(UserRecordNotFoundException::class) {
      runBlocking {
        service.fetchGoogleAdId("anotherUserId")
      }
    }
  }

  @Test
  internal fun `SHOULD return trackingData ON fetchTrackingData`() {
    val expected = generateTrackingData()
    val userId = database.prepareUser(trackingData = expected)

    val actual = runBlocking {
      service.fetchTrackingData(userId)
    }

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  internal fun `SHOULD return null ON fetchTrackingData WHEN user exists but trackingData is not defined`() {
    val userId = database.prepareUser(trackingData = null)

    val actual = runBlocking {
      service.fetchTrackingData(userId)
    }

    assertThat(actual).isNull()
  }

  @Test
  internal fun `SHOULD throw UserRecordNotFoundException ON fetchTrackingData WHEN user doesn't exist`() {
    assertFailsWith(UserRecordNotFoundException::class) {
      runBlocking {
        service.fetchTrackingData("anotherUserId")
      }
    }
  }

  @Test
  fun `SHOULD return users list that are associated by recent ips with their ban and country status ON loadAssociatedUsers`() {
    val monthEarlier = Instant.now().minus(31, ChronoUnit.DAYS)
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val userId3 = database.prepareUser(isBanned = true)
    val userId4 = database.prepareUser(isBanned = true)
    val userId5 = database.prepareUser()//wrong sim country
    val userId6 = database.prepareUser()//wrong network country
    val userId5a = database.prepareUser()//null sim country
    val userId6a = database.prepareUser()//null network country
    val userId7 = database.prepareUser()//good countries, empty network operator
    val userId8 = database.prepareUser()//good countries, null network operator
    val userId9 = database.prepareUser()//good countries, empty sim operator
    val userId10 = database.prepareUser()//good countries, null sim operator
    val userId11 = database.prepareUser(countryCode = "EG")//good additional data, wrong user country
    val userId12 = database.prepareUser()//good countries, field network and sim operator name but no strong attestation
    val userIdX = database.prepareUser()//good countries, field network and sim operator name + strong attestation *

    transaction(database) {
      UserTable.update({ UserTable.id eq userId4 }) { it[isDeleted] = true }

      UserIpsTable.replace { it[userId] = userId1; it[ip] = "lau.0.0.1" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "lau.0.0.2" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "lau.0.0.3" }

      UserIpsTable.replace { it[userId] = userId2; it[ip] = "lau.0.0.1" }
      UserIpsTable.replace { it[userId] = userId2; it[ip] = "lau.0.0.3" }
      UserIpsTable.replace { it[userId] = userId2; it[ip] = "lau.0.0.2"; it[updatedAt] = monthEarlier } // not recent
      UserIpsTable.replace { it[userId] = userId2; it[ip] = "lau.0.0.5"; it[updatedAt] = monthEarlier } // not recent

      UserIpsTable.replace { it[userId] = userId3; it[ip] = "lau.0.0.2" }
      UserIpsTable.replace { it[userId] = userId3; it[ip] = "lau.0.0.3" }
      UserIpsTable.replace { it[userId] = userId3; it[ip] = "lau.0.0.4" }

      UserIpsTable.replace { it[userId] = userId4; it[ip] = "lau.0.0.4" }
      UserIpsTable.replace { it[userId] = userId4; it[ip] = "lau.0.0.5" }
      UserIpsTable.replace { it[userId] = userId4; it[ip] = "lau.0.0.1"; it[updatedAt] = monthEarlier } // not recent
      UserIpsTable.replace { it[userId] = userId4; it[ip] = "lau.0.0.2"; it[updatedAt] = monthEarlier } // not recent

      UserFraudScoreTransactionTable.replace {
        it[userId] = userId1
        it[amount] = -1.0
        it[reasonType] = FraudScoreChangeReason.USER_IP_IS_UNIQUE.name
        it[reasonUniqueId] = "GH"
        it[description] = ""
      }

      UserFraudScoreTransactionTable.replace {
        it[userId] = userId2
        it[amount] = 1.0
        it[reasonType] = FraudScoreChangeReason.USER_SHARE_GOOGLE_AD_ID_WITH_OTHER_USERS.name
        it[reasonUniqueId] = "googleAdId"
        it[description] = ""
      }

      UserFraudScoreTransactionTable.replace {
        it[userId] = userId3
        it[amount] = 200.0
        it[reasonType] = FraudScoreChangeReason.USER_IS_CONNECTED_FROM_NON_ALLOWED_COUNTRY.name
        it[reasonUniqueId] = "GH"
        it[description] = ""
      }

      UserFraudScoreTransactionTable.replace {
        it[userId] = userId4
        it[amount] = 200.0
        it[reasonType] = FraudScoreChangeReason.USER_IS_CONNECTED_FROM_NON_ALLOWED_COUNTRY.name
        it[reasonUniqueId] = "GH"
        it[description] = ""
      }

      UserFraudScoreFrozenScoreTable.insert {
        it[userId] = userId4
        it[amount] = 100.0
      }

      listOf(userId5, userId5a, userId6, userId6a, userId7, userId8, userId9, userId10, userId11, userId12, userIdX).forEach { curUser ->

        UserIpsTable.insert { it[userId] = curUser; it[ip] = "lau.0.0.4" }

        UserAdditionalDataTable.insert {
          it[userId] = curUser
          it[networkCountry] = when (curUser) {
            userId6 -> "eg"; userId6a -> null; else -> "us"
          }
          it[simCountry] = when (curUser) {
            userId5 -> "EG"; userId5a -> null; else -> "US"
          }
          it[networkOperatorName] = when (curUser) {
            userId7 -> "  "; userId8 -> null; else -> "networkOperatorName"
          }
          it[simOperatorName] = when (curUser) {
            userId9 -> ""; userId10 -> null; else -> "simOperatorName"
          }
        }

        if (curUser != userId12) {
          UserPassedStrongAttestationCheckTable.insert {
            it[userId] = curUser
          }
        }
      }
    }

    val associatedUser = AssociatedUser("userId", isDeleted = false, isBanned = false, allowedCountriesOnly = true, hasFrozenFraudScore = false)

    runBlocking {
      assertThat(service.loadRecentUsersAssociatedByIp(userId1, "lau.0.0.1", setOf("US"))).containsOnly(
        AssociatedUser(userId2, isDeleted = false, isBanned = false, allowedCountriesOnly = true, hasFrozenFraudScore = false)
      )
      assertThat(service.loadRecentUsersAssociatedByIp(userId2, "lau.0.0.3", setOf("US"))).containsOnly(
        AssociatedUser(userId1, isDeleted = false, isBanned = false, allowedCountriesOnly = true, hasFrozenFraudScore = false),
        AssociatedUser(userId3, isDeleted = false, isBanned = true, allowedCountriesOnly = false, hasFrozenFraudScore = false)
      )
      assertThat(service.loadRecentUsersAssociatedByIp(userId3, "lau.0.0.2", setOf("US"))).containsOnly(
        AssociatedUser(userId1, isDeleted = false, isBanned = false, allowedCountriesOnly = true, hasFrozenFraudScore = false)
      )
      assertThat(service.loadRecentUsersAssociatedByIp(userId3, "lau.0.0.4", setOf("US"))).containsOnly(
        AssociatedUser(userId4, isDeleted = true, isBanned = true, allowedCountriesOnly = false, hasFrozenFraudScore = true),
        associatedUser.copy(userId = userId5), associatedUser.copy(userId = userId5a), associatedUser.copy(userId = userId6),
        associatedUser.copy(userId = userId6a), associatedUser.copy(userId = userId7), associatedUser.copy(userId = userId8),
        associatedUser.copy(userId = userId9), associatedUser.copy(userId = userId10), associatedUser.copy(userId = userId11),
        associatedUser.copy(userId = userId12)
      )
      assertThat(service.loadRecentUsersAssociatedByIp(userId4, "lau.0.0.4", setOf("US"))).containsOnly(
        AssociatedUser(userId3, isDeleted = false, isBanned = true, allowedCountriesOnly = false, hasFrozenFraudScore = false),
        associatedUser.copy(userId = userId5), associatedUser.copy(userId = userId5a), associatedUser.copy(userId = userId6),
        associatedUser.copy(userId = userId6a), associatedUser.copy(userId = userId7), associatedUser.copy(userId = userId8),
        associatedUser.copy(userId = userId9), associatedUser.copy(userId = userId10), associatedUser.copy(userId = userId11),
        associatedUser.copy(userId = userId12)
      )
      assertThat(service.loadRecentUsersAssociatedByIp(userId4, "lau.0.0.5", setOf("US"))).isEmpty()
    }
  }

  @Test
  fun `SHOULD return set of user countries (except associated with current ip) ON loadUserCountries`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    transaction(database) {
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "luc.0.0.1"; it[countryCode] = "US" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "luc.0.0.2"; it[countryCode] = "GB" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "luc.0.0.3"; it[countryCode] = "GB" }
      UserIpsTable.replace { it[userId] = userId1; it[ip] = "luc.0.0.4"; it[countryCode] = "CN" }
      UserIpsTable.replace { it[userId] = userId2; it[ip] = "luc.0.0.5"; it[countryCode] = "ZZ" }
    }

    val actual = runBlocking {
      service.loadUserCountries(userId1, "luc.0.0.1")
    }

    assertThat(actual).isEqualTo(setOf("GB", "CN"))
  }

  @Test
  fun `SHOULD throw error ON updateUserCreatedAt WHEN build variant is production`() = runBlocking {
    val userId = database.prepareUser()

    assertFailsWith(IllegalStateException::class) {
      service.updateUserCreatedAt(userId, Instant.now())
    }.let {
      assertThat(it.message).isEqualTo("Not allowed in production mode")
    }
  }

  @Test
  fun `SHOULD change user creation ON updateUserCreatedAt WHEN build variant is NOT production`() = runBlocking {
    whenever(buildVariantProvider.get()).thenReturn(BuildVariant.TEST)
    val userId = database.prepareUser()
    val newUserCreationTimestamp = service.loadUserCreationDate(userId).minus(5, ChronoUnit.HOURS)

    service.updateUserCreatedAt(userId, newUserCreationTimestamp)
    assertThat(service.loadUserCreationDate(userId)).isEqualTo(newUserCreationTimestamp)
    verifyUserCacheInvalidationEffectWasPublished(userId)
  }


  @Test
  fun `SHOULD return all user ids (include deleted) ON fetchAllUserIds`() {
    runBlocking {
      val userId1 = database.prepareUser(googleAdId = "fauid1")
      val userId2 = database.prepareUser(googleAdId = "fauid1", isBanned = true)
      val userId3 = database.prepareUser(googleAdId = "fauid1", isDeleted = true)
      database.prepareUser(googleAdId = "fauid2") // another google ad id

      val actual = service.fetchAllUserIds("fauid1")

      assertThat(actual).containsOnly(userId1, userId2, userId3)
    }
  }

  @Test
  internal fun `SHOULD return userId to deviceToken map ON loadDeviceTokensForUsers WHEN user has device token AND NOT user is deleted AND NOT user is banned AND NOT user is whitelisted`() {
    val deviceToken = UUID.randomUUID().toString()
    val userId = database.prepareUser(deviceToken = deviceToken, isBanned = false, isDeleted = false)

    val actual = runBlocking {
      service.loadDeviceTokensForUsers(listOf(userId))
    }

    assertThat(actual).hasSize(1)
    assertThat(actual[userId]).isEqualTo(PlatformDeviceTokenDto(deviceToken, ANDROID))
  }

  @Test
  internal fun `SHOULD return empty userId to deviceToken map ON loadDeviceTokensForUsers WHEN NOT user has device token AND NOT user is deleted AND NOT user is banned AND NOT user is whitelisted`() {
    val userId = database.prepareUser(deviceToken = null, isBanned = false, isDeleted = false)

    val actual = runBlocking {
      service.loadDeviceTokensForUsers(listOf(userId))
    }

    assertThat(actual).isEmpty()
  }

  @Test
  internal fun `SHOULD return empty userId to deviceToken map ON loadDeviceTokensForUsers WHEN user has device token AND user is deleted AND NOT user is banned AND NOT user is whitelisted`() {
    val userId = database.prepareUser(deviceToken = UUID.randomUUID().toString(), isBanned = false, isDeleted = true)

    val actual = runBlocking {
      service.loadDeviceTokensForUsers(listOf(userId))
    }

    assertThat(actual).isEmpty()
  }

  @Test
  internal fun `SHOULD return userId to deviceToken map ON loadDeviceTokensForUsers WHEN user has device token AND NOT user is deleted AND user is banned AND NOT user is whitelisted`() {
    val deviceToken1 = UUID.randomUUID().toString()
    val deviceToken2 = UUID.randomUUID().toString()
    val userId1 = database.prepareUser(
      deviceToken = deviceToken1,
      isBanned = true,
      isDeleted = false,
      trackingData = TrackingData(UUID.randomUUID().toString(), IDFA, ANDROID)
    )
    val userId2 =
      database.prepareUser(deviceToken = deviceToken2, isBanned = true, isDeleted = false, trackingData = TrackingData(UUID.randomUUID().toString(), IDFA, IOS))

    val actual = runBlocking {
      service.loadDeviceTokensForUsers(listOf(userId1, userId2))
    }

    assertThat(actual).hasSize(2)
    assertThat(actual[userId1]).isEqualTo(PlatformDeviceTokenDto(deviceToken1, ANDROID))
    assertThat(actual[userId2]).isEqualTo(PlatformDeviceTokenDto(deviceToken2, IOS))
  }

  @Test
  internal fun `SHOULD return userId to deviceToken map ON loadDeviceTokensForUsers WHEN user has device token AND NOT user is deleted AND user is banned AND user is whitelisted`() {
    val deviceToken = UUID.randomUUID().toString()
    val userId = database.prepareUser(deviceToken = deviceToken, isBanned = true, isDeleted = false)
    transaction(database) { UserWhitelistTable.insert { it[UserWhitelistTable.userId] = userId } }

    val actual = runBlocking {
      service.loadDeviceTokensForUsers(listOf(userId))
    }

    assertThat(actual).hasSize(1)
    assertThat(actual[userId]).isEqualTo(PlatformDeviceTokenDto(deviceToken, ANDROID))
  }

  @Test
  fun `SHOULD save phone data ON saveFirebaseData`() {
    val firebaseUid = "firebaseUid"
    val userId = database.prepareUser()
    val now = Instant.now()
    whenever(timeService.now()).thenReturn(now)

    runBlocking {
      service.saveFirebaseData(userId, firebaseUid)
    }

    transaction(database) {
      UserFirebaseAuthTable
        .select { UserFirebaseAuthTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserFirebaseAuthTable.firebaseUid]).isEqualTo(firebaseUid)
        }
    }
  }

  @Test
  fun `SHOULD update phone data ON saveFirebaseData WHEN phone is already tracked for user`() {
    val firebaseUid = "firebaseUid"
    val userId = database.prepareUser()
    val now = Instant.now()
    whenever(timeService.now()).thenReturn(now)

    runBlocking {
      service.saveFirebaseData(userId, "initialFbUid")
      service.saveFirebaseData(userId, firebaseUid)
    }

    transaction(database) {
      UserFirebaseAuthTable
        .select { UserFirebaseAuthTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserFirebaseAuthTable.firebaseUid]).isEqualTo(firebaseUid)
        }
    }
  }

  @Test
  fun `SHOULD return user game coins info ON loadPerGameCoinsForUser WHEN there are balance totals`() {
    val now = Instant.now()
    val userId1 = database.prepareUser()
    val game1CoinsCreatedAt = now.minusSeconds(30).truncatedTo(ChronoUnit.SECONDS)
    val game2CoinsCreatedAt = now.minus(2, ChronoUnit.DAYS).truncatedTo(ChronoUnit.SECONDS)
    val game1 = database.prepareGame()
    val game2 = database.prepareGame()
    val game3 = database.prepareGame()
    whenever(timeService.now()).thenReturn(now)
    database.addUserTotalCoinsByGameId(userId1, game1, 100, game1CoinsCreatedAt)
    database.addUserTotalCoinsByGameId(userId1, game2, 100, game2CoinsCreatedAt)
    database.addUserTotalCoinsByGameId(userId1, game3, 0)
    database.addUserFirstPlayedByGameId(userId1, game1, game1CoinsCreatedAt)

    val actual = runBlocking {
      service.loadPerGameCoinsForUser(userId1)
    }

    assertThat(actual).isEqualTo(
      listOf(
        GamePlayStatusDto(
          gameId = game1,
          coins = 100,
          playedRecently = true,
          firstPlayedAt = game1CoinsCreatedAt,
          lastPlayedAt = game1CoinsCreatedAt,
        ),
        GamePlayStatusDto(
          gameId = game2,
          coins = 100,
          playedRecently = false,
          firstPlayedAt = null,
          lastPlayedAt = game2CoinsCreatedAt,
        )
      )
    )
  }

  @Test
  fun `SHOULD return user game coins info ON loadRoundedPerGameCoinsForEm2User WHEN there are em2 balance totals`() {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId1 = database.prepareUser()
    val game1CoinsCreatedAt = now.minusSeconds(30).truncatedTo(ChronoUnit.SECONDS)
    val game1 = database.prepareGame()
    val game2 = database.prepareGame()
    val game3 = database.prepareGame()
    whenever(timeService.now()).thenReturn(now)
    database.addUserEm2TotalCoinsByGameId(userId1, game1, BigDecimal("100.5"), firstPlayedAt = game1CoinsCreatedAt, lastPlayedAt = now)
    database.addUserEm2TotalCoinsByGameId(userId1, game2, BigDecimal("100.75"), firstPlayedAt = now, lastPlayedAt = now.minus(2, ChronoUnit.DAYS))
    database.addUserEm2TotalCoinsByGameId(userId1, game3, BigDecimal.ZERO)

    val actual = runBlocking {
      service.loadRoundedPerGameCoinsForEm2User(userId1)
    }

    assertThat(actual).isEqualTo(
      listOf(
        GamePlayStatusDto(
          gameId = game1,
          coins = 100,
          playedRecently = true,
          firstPlayedAt = game1CoinsCreatedAt,
          lastPlayedAt = now,
        ),
        GamePlayStatusDto(
          gameId = game2,
          coins = 100,
          playedRecently = false,
          firstPlayedAt = now,
          lastPlayedAt = now.minus(2, ChronoUnit.DAYS),
        )
      )
    )
  }

  @Test
  fun `SHOULD create user and bonus tracking ON createUser`() {
    val actual = runBlocking {
      service.createUser("us", createUserDataStub)
    }

    transaction(database) {
      UserTable
        .select { UserTable.id eq actual }
        .first()
        .let {
          assertThat(it[UserTable.countryCode]).isEqualTo("us")
          assertThat(it[UserTable.lastActiveAtDay]).isEqualTo(LocalDate.now())
          assertThat(it[UserTable.appPlatform]).isEqualTo(createUserDataStub.appVersion.platform.name)
          assertThat(it[UserTable.appVersion]).isEqualTo(createUserDataStub.appVersion.version)
        }
      UserAppVersionInfoTable
        .select { UserAppVersionInfoTable.userId eq actual }
        .first()
        .let {
          assertThat(it[UserAppVersionInfoTable.appPlatform]).isEqualTo(createUserDataStub.appVersion.platform.name)
          assertThat(it[UserAppVersionInfoTable.appVersion]).isEqualTo(createUserDataStub.appVersion.version)
        }
      ActiveUsersTable
        .select { ActiveUsersTable.userId eq actual }
        .first()
        .let { assertThat(it[ActiveUsersTable.lastActiveAtDay]).isEqualTo(LocalDate.now()) }
    }
  }

  @Test
  fun `SHOULD create user and bonus tracking and additional data ON createUser WHEN additional data is provided`() {
    val additionalData = CreateUserRequestDto(
      networkCountry = "us",
      networkOperatorName = "verizon",
      simCountry = "ca",
      simOperatorName = "Verizon Canada",
      deviceLocale = "en",
      deviceLanguageTag = "en-US",
      installedFromStore = true,
      timeZone = "Europe/Berlin",
    )
    val appVersion = AppVersionDto(IOS, 1)

    val actual = runBlocking {
      service.createUser("us", createUserDataStub.copy(userRequestDto = additionalData, appVersion = appVersion, isReviewer = true))
    }

    transaction(database) {
      UserTable
        .select { UserTable.id eq actual }
        .first()
        .let {
          assertThat(it[UserTable.countryCode]).isEqualTo("us")
          assertThat(it[UserTable.appPlatform]).isEqualTo(appVersion.platform.name)
          assertThat(it[UserTable.appVersion]).isEqualTo(appVersion.version)
        }
      UserAdditionalDataTable
        .select { UserAdditionalDataTable.userId eq actual }
        .first()
        .let {
          assertThat(it[UserAdditionalDataTable.networkCountry]).isEqualTo(additionalData.networkCountry?.toUpperCase())
          assertThat(it[UserAdditionalDataTable.networkOperatorName]).isEqualTo(additionalData.networkOperatorName)
          assertThat(it[UserAdditionalDataTable.simCountry]).isEqualTo(additionalData.simCountry?.toUpperCase())
          assertThat(it[UserAdditionalDataTable.simOperatorName]).isEqualTo(additionalData.simOperatorName)
          assertThat(it[UserAdditionalDataTable.deviceLocale]).isEqualTo(additionalData.deviceLocale)
          assertThat(it[UserAdditionalDataTable.timeZone]).isEqualTo("Europe/Berlin")
          assertThat(it[UserAdditionalDataTable.isReviewer]).isEqualTo(true)
        }
    }
  }

  @Test
  fun `SHOULD create user and track sims info list ON createUser WHEN we have some items`() {
    val additionalData = CreateUserRequestDto(
      networkCountry = "us",
      networkOperatorName = "verizon",
      simCountry = "ca",
      simOperatorName = "Verizon Canada",
      deviceLocale = "en",
      deviceLanguageTag = "en-US",
      installedFromStore = true,
      timeZone = "Europe/Berlin",
      simInfoList = listOf(
        SimInfo("us", "opName", "us", "opName2", 0),
        SimInfo("mx", "opName3", "mx", "opName3", 1),
      )
    )
    val appVersion = AppVersionDto(ANDROID, 68)
    val expectedSimsInfo = additionalData.simInfoList!!.map {
      it.copy(
        networkCountry = it.networkCountry.uppercase(),
        simCountry = it.simCountry.uppercase(),
      )
    }.toSet()

    val actual = runBlocking {
      service.createUser("us", createUserDataStub.copy(userRequestDto = additionalData, appVersion = appVersion))
    }

    transaction(database) {
      UserTable
        .select { UserTable.id eq actual }
        .first()
        .let {
          assertThat(it[UserTable.countryCode]).isEqualTo("us")
          assertThat(it[UserTable.appPlatform]).isEqualTo(appVersion.platform.name)
          assertThat(it[UserTable.appVersion]).isEqualTo(appVersion.version)
        }
      UserMultiSimDataTable
        .select { UserMultiSimDataTable.userId eq actual }
        .map {
          SimInfo(
            networkCountry = it[UserMultiSimDataTable.networkCountry],
            networkOperatorName = it[UserMultiSimDataTable.networkOperatorName],
            simCountry = it[UserMultiSimDataTable.simCountry],
            simOperatorName = it[UserMultiSimDataTable.simOperatorName],
            simSlotIndex = it[UserMultiSimDataTable.simSlotIndex],
          )
        }.toSet()
        .let { assertThat(it).isEqualTo(expectedSimsInfo) }
    }
  }

  @Test
  fun `SHOULD add totals rows for new user ON createUser`() {
    val actual = runBlocking {
      service.createUser("us", createUserDataStub.copy(userRequestDto = null))
    }

    transaction(database) {
      UserFraudScoreTable
        .select { UserFraudScoreTable.userId eq actual }
        .first()[UserFraudScoreTable.score]
        .let { assertThat(it).isEqualTo(0.0) }
    }
  }

  @Test
  fun `SHOULD add inters empty row for new user ON createUser`() {
    val actual = runBlocking {
      service.createUser("us", createUserDataStub.copy(userRequestDto = null))
    }

    transaction(database) {
      UserLastEcpmRevenueTable
        .select { UserLastEcpmRevenueTable.userId eq actual }
        .first()
        .let {
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd1]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd2]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd3]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd4]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd5]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd6]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd7]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd8]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd9]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd10]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst10Avg]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.countryCode]).isEqualTo("us")
        }
    }
  }

  @Test
  fun `SHOULD save device specification ON createUser`() {
    val actual = runBlocking {
      service.createUser("us", createUserDataStub)
    }

    transaction(database) {
      UserDeviceTable
        .select { UserDeviceTable.userId eq actual }
        .first()
        .let {
          assertEquals(actual, it[UserDeviceTable.userId])
          assertEquals("42", it[UserDeviceTable.osVersion])
          assertEquals("Pixel 4 XL", it[UserDeviceTable.modelName])
          assertEquals(6144, it[UserDeviceTable.ramSize])
          assertEquals(BigDecimal.valueOf(12.121), it[UserDeviceTable.fontScale])
          assertEquals(10, it[UserDeviceTable.density])
          assertEquals(BigDecimal.valueOf(15.151), it[UserDeviceTable.densityScaleFactor])
        }
    }
  }

  @Test
  fun `SHOULD skip save device specification ON createUser WHEN no device specification provided`() {
    val actual = runBlocking {
      service.createUser("us", createUserDataStub.copy(userRequestDto = createUserDataStub.userRequestDto?.copy(deviceSpecification = null)))
    }

    transaction(database) {
      UserDeviceTable
        .select { UserDeviceTable.userId eq actual }
        .count()
        .let {
          assertEquals(0, it)
        }
    }
  }

  @Test
  fun `SHOULD return user data ON loadCoinGoalUser`() {
    val userId = database.prepareUser()
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 100,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD return user data with exp labels ON loadCoinGoalUser when user is a participant`() {
    val userId = database.prepareUser()
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 100,
      createdAt = Instant.now(),
      expLabels = emptyMap(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD return user data with coin goal from tracked period ON loadCoinGoalUser WHEN user is on ICP experiment`() {
    val userId = database.prepareUser()
    transaction(database) {
      CashoutPeriodTable
        .insert {
          it[CashoutPeriodTable.userId] = userId
          it[periodStart] = Instant.now()
          it[periodEnd] = Instant.now().plus(24, ChronoUnit.HOURS)
          it[counter] = 5
          it[coinGoal] = 2500
          it[noEarningsCounter] = 0
        }
    }
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 2500,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD return user data with coin goal fallback ON loadCoinGoalUser WHEN user is on ICP experiment but period is not defined (migrated user)`() {
    val userId = database.prepareUser()
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 100,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD return user data with unchanged coinGoal ON loadCoinGoalUser WHEN there were no previous coin goal reset AND NOT welcome coins defined`() {
    val userId = database.prepareUser()
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 100,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    runBlocking { service.loadCoinGoalUser(userId) }.let { actual ->
      assertThat(actual).isEqualToIgnoringGivenProperties(
        expected,
        User::createdAt
      )
    }
  }

  @ParameterizedTest
  @ValueSource(ints = [10, 100])
  fun `SHOULD return user data with multiplied coins ON loadCoinGoalUser when user is INFLATING_COINS_X variation of the experiment`(multiplier: Int) {
    val userId = database.prepareUser()
    rewardingFacade.mock({ inflatingCoinsMultiplier(userId) }, multiplier)
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 100 * multiplier,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @ParameterizedTest
  @ValueSource(ints = [10, 100])
  fun `SHOULD return user data with coinGoal shifted by welcome coins ON loadCoinGoalUser WHEN ios AND there were no previous coin goal reset AND welcome coins defined`(
    multiplier: Int
  ) {
    val userId = database.prepareUser(appPlatform = IOS)
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 100 * multiplier,
      createdAt = Instant.now(),
      appPlatform = IOS,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    abTestingService.mock({ isEm2Participant(userId) }, true)
    rewardingFacade.mock({ inflatingCoinsMultiplier(userId) }, multiplier)

    runBlocking { service.loadCoinGoalUser(userId) }.let { actual ->
      assertThat(actual).isEqualToIgnoringGivenProperties(
        expected,
        User::createdAt
      )
    }
  }

  @ParameterizedTest
  @ValueSource(ints = [10, 100])
  fun `SHOULD return user data with multiplied coins ON loadCoinGoalUser when user is em2 participant and welcome coins not defined`(multiplier: Int) {
    val userId = database.prepareUser()
    rewardingFacade.mock({ inflatingCoinsMultiplier(userId) }, multiplier)
    abTestingService.mock({ isEm2Participant(userId) }, true)

    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 100 * multiplier,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD return firebaseAppInstanceId ON getFirebaseAppInstanceId`() {
    val firebaseAppInstanceId = "firebaseAppInstanceId_GUIAFPIIBG"
    val userId = database.prepareUser()
    transaction(database) {
      UserFirebaseAppInstanceTable.insert {
        it[UserFirebaseAppInstanceTable.userId] = userId
        it[UserFirebaseAppInstanceTable.firebaseAppInstanceId] = firebaseAppInstanceId
      }
    }

    runBlocking {
      val actual = service.getFirebaseAppInstanceId(userId)

      assertThat(actual).isEqualTo(firebaseAppInstanceId)
      assertThat(service.getFirebaseAppInstanceId("nonExistUserId")).isNull()
    }
  }

  @Test
  fun `SHOULD idempotently save firebaseAppInstanceId ON addOrUpdateFirebaseAppInstanceId`() {
    val firebaseAppInstanceId = "firebaseAppInstanceId_AOUFAII"
    val userId = database.prepareUser()

    runBlocking {
      service.addOrUpdateFirebaseAppInstanceId(userId, firebaseAppInstanceId)
      service.addOrUpdateFirebaseAppInstanceId(userId, firebaseAppInstanceId)
    }

    val actual = transaction(database) {
      UserFirebaseAppInstanceTable
        .slice(UserFirebaseAppInstanceTable.firebaseAppInstanceId)
        .select { UserFirebaseAppInstanceTable.userId eq userId }
        .first()
        .let { it[UserFirebaseAppInstanceTable.firebaseAppInstanceId] }
    }
    assertThat(actual).isEqualTo(firebaseAppInstanceId)
  }

  @Test
  fun `SHOULD update firebaseAppInstanceId ON addOrUpdateFirebaseAppInstanceId WHEN id was already stored`() {
    val firebaseAppInstanceId = "firebaseAppInstanceId_UPD"
    val userId = database.prepareUser()
    transaction(database) {
      UserFirebaseAppInstanceTable.insert {
        it[UserFirebaseAppInstanceTable.userId] = userId
        it[UserFirebaseAppInstanceTable.firebaseAppInstanceId] = "firebaseAppInstanceId_test"
      }
    }

    runBlocking {
      service.addOrUpdateFirebaseAppInstanceId(userId, firebaseAppInstanceId)
    }

    val actual = transaction(database) {
      UserFirebaseAppInstanceTable
        .slice(UserFirebaseAppInstanceTable.firebaseAppInstanceId)
        .select { UserFirebaseAppInstanceTable.userId eq userId }
        .first()
        .let { it[UserFirebaseAppInstanceTable.firebaseAppInstanceId] }
    }
    assertThat(actual).isEqualTo(firebaseAppInstanceId)
    verifyBlocking(messageBus) { publishAsync(InvalidateUserExternalIDsCacheEffect(userId)) }
  }

  @Test
  fun `SHOULD return false banned flag ON is user banned call WHEN user was unbanned`() {
    runBlocking {
      val userId = database.prepareUser()

      service.unbanUser(userId, "reason")

      assertThat(isUserBanned(userId)).isFalse()
    }
  }

  @Test
  fun `SHOULD load users created before some date ON loadNewUserIdsAndCreationDates`() {
    val now = Instant.now()

    val userId = database.prepareUser(createdAt = now.minusSeconds(15 * 60L))
    database.prepareUser(createdAt = now.minusSeconds(24 * 60 * 60L))

    val result = runBlocking {
      service.loadNewUserIdsAndCreationDates(now.minusSeconds(30 * 60L))
    }

    assertThat(result.any { it.first == userId }).isTrue()
    assertThat(result.none { it.second < now.minusSeconds(30 * 60L) }).isTrue()
  }

  @Test
  fun `SHOULD load user with mainCoinGoalReachedLabel ON loadCoinGoalUser WHEN a user reached coin goal`() {
    val userId = database.prepareUser()
    val expected =
      userStub.copy(
        userId = userId,
        coinsGoal = 100,
      )

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD load user with mainCoinGoalLabel ON loadCoinGoalUser WHEN a user does not reach coin goal`() {
    val userId = database.prepareUser()
    val expected =
      userStub.copy(userId = userId, coinsGoal = 100)

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD NOT update libraries consent ON updateUserConsent WHEN no libraries to consent`() {
    val userId = database.prepareUser()

    runBlocking {
      service.updateUserConsent(userId, ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = null))
    }

    transaction(database) {
      UserPrivacyLibraryConsentTable
        .select { UserPrivacyLibraryConsentTable.userId eq userId }
        .firstOrNull()
        ?.let { it[UserPrivacyLibraryConsentTable.libraryName] }
    }
      ?.let { result ->
        assertThat(result).isNull()
      }
  }

  @Test
  fun `SHOULD update libraries consent ON updateUserConsent`() {
    val userId = database.prepareUser()
    val consents = mapOf(
      "lib 1" to true,
      "lib 2" to false,
    )

    runBlocking {
      service.updateUserConsent(userId, ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = consents))
    }

    transaction(database) {
      UserPrivacyLibraryConsentTable
        .select { UserPrivacyLibraryConsentTable.userId eq userId }
        .orderBy(UserPrivacyLibraryConsentTable.libraryName)
        .map { Pair(it[UserPrivacyLibraryConsentTable.libraryName], it[UserPrivacyLibraryConsentTable.isConsented]) }
    }
      .let { result ->
        assertThat(result).isEqualTo(
          listOf(
            Pair("lib 1", true),
            Pair("lib 2", false)
          )
        )
      }
  }

  @Test
  fun `SHOULD update libraries consent ON updateUserConsent WHEN consequtive calls`() {
    val userId = database.prepareUser()
    val consents1 = mapOf(
      "lib 1" to true,
      "lib 2" to false,
    )

    val consents2 = mapOf(
      "lib 2" to true,
      "lib 3" to false,
    )

    runBlocking {
      service.updateUserConsent(userId, ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = consents1))
      service.updateUserConsent(userId, ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = consents2))
    }

    transaction(database) {
      UserPrivacyLibraryConsentTable
        .select { UserPrivacyLibraryConsentTable.userId eq userId }
        .orderBy(UserPrivacyLibraryConsentTable.libraryName)
        .map { Pair(it[UserPrivacyLibraryConsentTable.libraryName], it[UserPrivacyLibraryConsentTable.isConsented]) }
    }
      .let { result ->
        assertThat(result).isEqualTo(
          listOf(
            Pair("lib 1", true),
            Pair("lib 2", true),
            Pair("lib 3", false)
          )
        )
      }
  }

  @Test
  fun `SHOULD return user data with isConsentedToAnalytics flag when consent was given ON loadCoinGoalUser WHEN user consented`() {
    val userId = database.prepareUser()
    val expected = userStub.copy(
      userId = userId, coinsGoal = 100,
      countryCode = "US", isConsentedToAnalytics = true
    )

    val actual = runBlocking {
      service.updateUserConsent(userId, ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = null))
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD fill goal coins from goal_coins field ON loadCoinGoalUser WHEN goal_coins defined AND em1`() {
    val userId = database.prepareUser()
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 100,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD fill goal coins from goal_coins field ON loadCoinGoalUser WHEN goal_coins defined AND em2`() {
    val userId = database.prepareUser()
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 100,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    abTestingService.mock({ isEm2Participant(any()) }, true)

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD fill coins from visible coins table ON loadCoinGoalUser WHEN visible coins defined AND coinsDoNotReset participant`() {
    val userId = database.prepareUser()
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 100,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    abTestingService.mock({ isCoinsDoNotResetParticipant(userId, ANDROID) }, true)

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD NOT fill coins from visible coins table ON loadCoinGoalUser WHEN visible coins defined AND NOT coinsDoNotReset participant`() {
    val userId = database.prepareUser()
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 100,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @Test
  fun `SHOULD fill goal coins from truncated visible coins table ON loadCoinGoalUser WHEN NOT goal coins defined in db AND visible coins defined AND coinsDoNotReset participant AND coins are bigger than int32`() {
    val userId = database.prepareUser()
    val expected = User(
      userId = userId,
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 200000,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )

    rewardingFacade.mock({ inflatingCoinsMultiplier(userId) }, 2000)
    abTestingService.mock({ isCoinsDoNotResetParticipant(userId, ANDROID) }, true)

    val actual = runBlocking {
      service.loadCoinGoalUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expected, User::createdAt)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD load user data ON getUser`(isWhitelisted: Boolean) {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser(
      googleAdId = "google123",
      createdAt = now,
      appVersion = 31,
      isBanned = true,
      trackingData = TrackingData("google123", IDFA, ANDROID),
    )
    if (isWhitelisted) {
      transaction(database) { UserWhitelistTable.insert { it[UserWhitelistTable.userId] = userId } }
    }
    val expectedData = UserDto(
      id = userId,
      googleAdId = "google123",
      createdAt = now,
      countryCode = "US",
      limitedTrackingInfo = LimitedTrackingInfo(false, "US", false),
      appVersion = 31,
      isBanned = !isWhitelisted,
      isWhitelisted = isWhitelisted,
      trackingData = TrackingData("google123", IDFA, ANDROID),
      isDeleted = false,
      appPlatform = ANDROID,
      lastActiveAtDay = LocalDate.now(),
      locale = DEFAULT_USER_LOCALE
    )
    transaction(database) {
      UserOfferwallTypeTable.insert { it[UserOfferwallTypeTable.userId] = userId; it[type] = OfferWallType.ADJOE.name }
      UserOfferwallTypeTable.insert { it[UserOfferwallTypeTable.userId] = userId; it[type] = OfferWallType.TAPJOY.name }
    }
    val actual = runBlocking {
      service.getUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expectedData, UserDto::offerWallTypes)
    assertThat(actual.offerWallTypes!!).containsExactly(OfferWallType.TAPJOY, OfferWallType.ADJOE)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD load user data ON getUser with specified locale`(isWhitelisted: Boolean) {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser(
      googleAdId = "google123",
      createdAt = now,
      appVersion = 31,
      isBanned = true,
      trackingData = TrackingData("google123", IDFA, ANDROID),
    )
    if (isWhitelisted) {
      transaction(database) { UserWhitelistTable.insert { it[UserWhitelistTable.userId] = userId } }
    }
    val expectedData = UserDto(
      id = userId,
      googleAdId = "google123",
      createdAt = now,
      countryCode = "US",
      limitedTrackingInfo = LimitedTrackingInfo(false, "US", false),
      appVersion = 31,
      isBanned = !isWhitelisted,
      isWhitelisted = isWhitelisted,
      trackingData = TrackingData("google123", IDFA, ANDROID),
      isDeleted = false,
      appPlatform = ANDROID,
      lastActiveAtDay = LocalDate.now(),
      locale = FR_LOCALE
    )
    transaction(database) {
      UserOfferwallTypeTable.insert { it[UserOfferwallTypeTable.userId] = userId; it[type] = OfferWallType.ADJOE.name }
      UserOfferwallTypeTable.insert { it[UserOfferwallTypeTable.userId] = userId; it[type] = OfferWallType.TAPJOY.name }
    }
    transaction(database) {
      UserAdditionalDataTable.insert {
        it[UserAdditionalDataTable.userId] = userId
        it[deviceLocale] = "fr"
        it[deviceLanguageTag] = null
      }
    }
    val actual = runBlocking {
      service.getUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expectedData, UserDto::offerWallTypes)
    assertThat(actual.offerWallTypes!!).containsExactly(OfferWallType.TAPJOY, OfferWallType.ADJOE)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD load user data ON getUser with specified locale AND Language Tag`(isWhitelisted: Boolean) {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser(
      googleAdId = "google123",
      createdAt = now,
      appVersion = 31,
      isBanned = true,
      trackingData = TrackingData("google123", IDFA, ANDROID),
    )
    if (isWhitelisted) {
      transaction(database) { UserWhitelistTable.insert { it[UserWhitelistTable.userId] = userId } }
    }
    val expectedData = UserDto(
      id = userId,
      googleAdId = "google123",
      createdAt = now,
      countryCode = "US",
      limitedTrackingInfo = LimitedTrackingInfo(false, "US", false),
      appVersion = 31,
      isBanned = !isWhitelisted,
      isWhitelisted = isWhitelisted,
      trackingData = TrackingData("google123", IDFA, ANDROID),
      isDeleted = false,
      appPlatform = ANDROID,
      lastActiveAtDay = LocalDate.now(),
      locale = Locale.CANADA_FRENCH
    )
    transaction(database) {
      UserOfferwallTypeTable.insert { it[UserOfferwallTypeTable.userId] = userId; it[type] = OfferWallType.ADJOE.name }
      UserOfferwallTypeTable.insert { it[UserOfferwallTypeTable.userId] = userId; it[type] = OfferWallType.TAPJOY.name }
    }
    transaction(database) {
      UserAdditionalDataTable.insert {
        it[UserAdditionalDataTable.userId] = userId
        it[deviceLocale] = "fr"
        it[deviceLanguageTag] = "fr-CA"
      }
    }
    val actual = runBlocking {
      service.getUser(userId)
    }

    assertThat(actual).isEqualToIgnoringGivenProperties(expectedData, UserDto::offerWallTypes)
    assertThat(actual.offerWallTypes!!).containsExactly(OfferWallType.TAPJOY, OfferWallType.ADJOE)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD load user data ON getUser WHEN appPlatform is iOS`(isDeleted: Boolean) {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser(
      googleAdId = null,
      createdAt = now,
      appVersion = 42,
      isBanned = true,
      trackingData = TrackingData("some_id", IDFV, IOS),
      isDeleted = isDeleted
    )
    val expectedData = UserDto(
      id = userId,
      googleAdId = null,
      createdAt = now,
      countryCode = "US",
      limitedTrackingInfo = LimitedTrackingInfo(false, "US", false),
      appVersion = 42,
      isBanned = true,
      trackingData = TrackingData("some_id", IDFV, IOS),
      appPlatform = IOS,
      isDeleted = isDeleted,
      lastActiveAtDay = LocalDate.now()
    )

    val actual = runBlocking {
      service.getUser(userId, includingDeleted = isDeleted)
    }

    assertThat(actual).isEqualTo(expectedData)
  }

  @Test
  fun `SHOULD load user data on getUser WHEN user is limited tracking`() {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser(googleAdId = "google123", createdAt = now)
    database.addUserToLimitedTracking(userId)
    val expectedData = UserDto(
      id = userId,
      googleAdId = "google123",
      createdAt = now,
      countryCode = "US",
      limitedTrackingInfo = LimitedTrackingInfo(true, "US", false),
      trackingData = null,
      isDeleted = false,
      appPlatform = ANDROID,
      lastActiveAtDay = LocalDate.now(),
      offerWallTypes = listOf(OfferWallType.TAPJOY)
    )
    transaction(database) {
      UserOfferwallTypeTable.insert { it[UserOfferwallTypeTable.userId] = userId; it[type] = OfferWallType.TAPJOY.name }
    }

    val actual = runBlocking {
      service.getUser(userId)
    }

    assertThat(actual).isEqualTo(expectedData)
  }

  @Test
  fun `SHOULD load user data with consent on getUser WHEN user given consent`() {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser(googleAdId = "google123", countryCode = "GB", createdAt = now)
    val expectedData = UserDto(
      id = userId,
      googleAdId = "google123",
      createdAt = now,
      countryCode = "GB",
      limitedTrackingInfo = LimitedTrackingInfo(false, "GB", true),
      trackingData = null,
      isDeleted = false,
      appPlatform = ANDROID,
      lastActiveAtDay = LocalDate.now()
    )

    val actual = runBlocking {
      service.updateUserConsent(userId, ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = null))
      service.getUser(userId)
    }

    assertThat(actual).isEqualTo(expectedData)
    verifyUserCacheInvalidationEffectWasPublished(userId)
  }

  @Test
  fun `SHOULD load user data ON getUser WHEN user is deleted`() {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser(
      googleAdId = "google123",
      createdAt = now,
      appVersion = 31,
      isBanned = true,
      trackingData = TrackingData("google123", IDFA, ANDROID),
      isDeleted = true,
      lastActiveDay = null
    )
    val expectedData = UserDto(
      id = userId,
      googleAdId = "google123",
      createdAt = now,
      countryCode = "US",
      limitedTrackingInfo = LimitedTrackingInfo(false, "US", false),
      appVersion = 31,
      isBanned = true,
      trackingData = TrackingData("google123", IDFA, ANDROID),
      isDeleted = true,
      appPlatform = ANDROID,
      lastActiveAtDay = null
    )

    val actual = runBlocking {
      service.getUser(userId, true)
    }

    assertThat(actual).isEqualTo(expectedData)
  }

  @Test
  fun `SHOULD throw an exception ON getUser when there is no such user`() {
    assertFailsWith<UserRecordNotFoundException> {
      runBlocking {
        service.getUser("someUnexpectedUserId")
      }
    }
  }

  @Test
  fun `SHOULD throw an exception ON getUser when user is deleted and includingDeleted is false`() {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser(googleAdId = "google123", createdAt = now, isDeleted = true)

    assertFailsWith<UserRecordNotFoundException> {
      runBlocking {
        service.getUser(userId)
      }
    }
  }

  @Test
  fun `SHOULD load active user id ON loadActiveUserId`() {
    val googleAdId = UUID.randomUUID().toString()
    val userId = database.prepareUser(googleAdId = googleAdId)

    val result = runBlocking {
      service.loadActiveUserId(googleAdId, LocalDate.now().minusDays(45))
    }

    assertThat(result).isEqualTo(userId)
  }

  @Test
  fun `SHOULD NOT load active user id ON loadActiveUserId when user last active day is NULL`() {
    val googleAdId = UUID.randomUUID().toString()

    val result = runBlocking {
      service.loadActiveUserId(googleAdId, LocalDate.now().minusDays(45))
    }

    assertThat(result).isEqualTo(null)
  }

  @Test
  fun `SHOULD NOT load active user id ON loadActiveUserId when user is inactive for a long time`() {
    val googleAdId = UUID.randomUUID().toString()
    database.prepareUser(googleAdId = googleAdId, lastActiveDay = LocalDate.now().minusDays(46))

    val result = runBlocking {
      service.loadActiveUserId(googleAdId, LocalDate.now().minusDays(45))
    }

    assertThat(result).isEqualTo(null)
  }

  @Test
  fun `SHOULD load active user id ON loadActiveUserId when user is inactive for a long time but no activity check needed`() {
    val googleAdId = UUID.randomUUID().toString()
    val userId = database.prepareUser(googleAdId = googleAdId, lastActiveDay = LocalDate.now().minusDays(100))

    val result = runBlocking {
      service.loadActiveUserId(googleAdId)
    }

    assertThat(result).isEqualTo(userId)
  }

  @Test
  fun `SHOULD delete device tokens ON deleteDeviceTokens`() {
    val userId1 = database.prepareUser(deviceToken = "ddt1")
    val userId2 = database.prepareUser(deviceToken = "ddt2")
    val userId3 = database.prepareUser(deviceToken = "ddt3")
    val userId4 = database.prepareUser(deviceToken = null)

    runBlocking {
      service.deleteDeviceTokens(listOf("ddt1", "ddt2"))

      assertThat(service.loadDeviceTokensForUsers(listOf(userId1, userId2, userId3, userId4))).isEqualTo(
        mapOf(userId3 to PlatformDeviceTokenDto("ddt3", ANDROID))
      )
    }
  }

  @Test
  fun `SHOULD check if user is limited tracking ON getLimitedTrackingInfo`() {
    val user = database.prepareUser()
    val limitedTrackingUser = database.prepareUser()
    database.addUserToLimitedTracking(limitedTrackingUser)

    runBlocking {
      service.getLimitedTrackingInfo(user)
    }.let { assertThat(it.isLimitedTracking).isFalse() }

    runBlocking {
      service.getLimitedTrackingInfo(limitedTrackingUser)
    }.let { assertThat(it.isLimitedTracking).isTrue() }
  }

  @Test
  fun `SHOULD check if user is consented ON getUserConsentInfo`() {
    val user = database.prepareUser()
    val consentedUser = database.prepareUser()

    runBlocking {
      service.getLimitedTrackingInfo(user)
    }.let { assertThat(it.isConsentedToAnalytics).isFalse() }

    runBlocking {
      service.getUserConsentInfo(user)
    }.let { assertThat(it.isConsentedToAnalytics).isFalse() }

    runBlocking {
      service.updateUserConsent(
        consentedUser,
        ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = null)
      )
    }

    runBlocking {
      service.getLimitedTrackingInfo(consentedUser)
    }.let { assertThat(it.isConsentedToAnalytics).isTrue() }

    runBlocking {
      service.getUserConsentInfo(consentedUser)
    }.let { assertThat(it.isConsentedToAnalytics).isTrue() }

    verifyUserCacheInvalidationEffectWasPublished(consentedUser)
  }

  @Test
  fun `SHOULD return user country ON getUserCountry WHEN user is on multiCurrency experiment`() {
    val userId = database.prepareUser(countryCode = "CA")

    val actual = runBlocking {
      service.getUserCountryCode(userId)
    }

    assertThat(actual).isEqualTo("CA")
  }

  @Test
  fun `SHOULD throw error ON updateUserCountryCode WHEN build variant is production`() = runBlocking {
    val userId = database.prepareUser()

    assertFailsWith(IllegalStateException::class) {
      service.updateUserCountryCode(userId, "CA")
    }.let {
      assertThat(it.message).isEqualTo("Not allowed in production mode")
    }
  }

  @Test
  fun `SHOULD change user country code ON updateUserCountryCode WHEN build variant is NOT production`() = runBlocking {
    whenever(buildVariantProvider.get()).thenReturn(BuildVariant.TEST)
    val userId = database.prepareUser(countryCode = "US")

    service.updateUserCountryCode(userId, "CA")

    assertThat(service.getUserCountryCode(userId)).isEqualTo("CA")
    verifyUserCacheInvalidationEffectWasPublished(userId)
  }

  @Test
  fun `SHOULD return null user sim and operator country ON getUserAdditionalCountryInfo`() {
    val userId = database.prepareUser()

    val actual = runBlocking {
      service.getUserAdditionalCountryInfo(userId)
    }

    assertThat(actual).isNull()
  }

  @Test
  fun `SHOULD return user sim and operator country ON getUserAdditionalCountryInfo WHEN sim and operator country is null`() {
    val userId = database.prepareUser()
    transaction(database) {
      UserAdditionalDataTable.insert {
        it[UserAdditionalDataTable.userId] = userId
        it[networkCountry] = null
        it[networkOperatorName] = null
        it[simCountry] = null
        it[simOperatorName] = null
        it[deviceLocale] = null
      }
    }

    val actual = runBlocking {
      service.getUserAdditionalCountryInfo(userId)
    }

    assertThat(actual).isEqualTo(
      AdditionalCountryInfo(
        userId = userId,
        simCountry = null,
        networkCountry = null
      )
    )
  }

  @Test
  fun `SHOULD return user sim and operator country ON getUserAdditionalCountryInfo`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserAdditionalDataTable.insert {
        it[UserAdditionalDataTable.userId] = userId
        it[networkCountry] = "US"
        it[networkOperatorName] = null
        it[simCountry] = "CA"
        it[simOperatorName] = null
        it[deviceLocale] = null
      }
    }

    val actual = runBlocking {
      service.getUserAdditionalCountryInfo(userId)
    }

    assertThat(actual).isEqualTo(
      AdditionalCountryInfo(
        userId = userId,
        networkCountry = "US",
        simCountry = "CA"
      )
    )
  }

  @Test
  fun `SHOULD check domains for existence ON isTopLevelDomainExists`() {
    transaction(database) {
      TopLevelDomainsTable.deleteAll()
      TopLevelDomainsTable.insert { it[name] = "com" }
      TopLevelDomainsTable.insert { it[name] = "ru" }
      TopLevelDomainsTable.insert { it[name] = "gov" }
    }

    assertThat(runBlocking { service.isTopLevelDomainExists("ru") }).isEqualTo(true)
    assertThat(runBlocking { service.isTopLevelDomainExists("RU") }).isEqualTo(true)
    assertThat(runBlocking { service.isTopLevelDomainExists("io") }).isEqualTo(false)
  }

  @ParameterizedTest
  @ValueSource(strings = ["com", "ru", "net"])
  fun `SHOULD check domains for existence ON isTopLevelDomainExists with default loaded data`(domain: String) {
    assertThat(runBlocking { service.isTopLevelDomainExists(domain) }).isEqualTo(true)
  }

  @Test
  fun `SHOULD add user connected via vpn table ON markUserAsConnectedViaVpn`() {
    val userId = database.prepareUser()

    runBlocking {
      service.markUserAsConnectedViaVpn(userId)
      service.markUserAsConnectedViaVpn(userId) // idempotency
    }

    transaction(database) {
      UserConnectedViaVpnTable
        .select { UserConnectedViaVpnTable.userId eq userId }
        .count() == 1L
    }.let { assertThat(it).isTrue() }
  }

  @Test
  fun `SHOULD return flag for user connected via vpn ON wasUserEverConnectedViaVpn`() {
    val userId = database.prepareUser()

    runBlocking {
      assertThat(service.wasUserEverConnectedViaVpn(userId)).isFalse()

      service.markUserAsConnectedViaVpn(userId)

      assertThat(service.wasUserEverConnectedViaVpn(userId)).isTrue()
    }
  }

  @Test
  fun `SHOULD mark isDeleted ON markUserAsDeleted`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserTable
        .select(UserTable.id eq userId)
        .first()[UserTable.isDeleted]
        .also { assertThat(it).isEqualTo(false) }
    }

    runBlocking {
      service.markUserAsDeleted(userId)
    }

    transaction(database) {
      UserTable
        .select(UserTable.id eq userId)
        .first()[UserTable.isDeleted]
        .also { assertThat(it).isEqualTo(true) }
    }
    verifyUserCacheInvalidationEffectWasPublished(userId)
  }


  @Test
  fun `SHOULD obfuscate user personal data ON obfuscateUserPersonals`() {
    val userId = database.prepareUser()
    // check removing safety
    database.prepareUser()

    val actual = runBlocking {
      service.obfuscateUserPersonals(userId)
    }

    assertThat(actual).isEqualTo(1)

    transaction(database) {
      UserTable
        .select(UserTable.id eq userId)
        .first()
        .also { row ->
          assertThat(row[UserTable.googleAdId]).isEqualTo("personals deleted")
          assertThat(row[UserTable.deviceToken]).isEqualTo("")
        }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["save email", "update email"])
  fun `SHOULD save email on saveUserEmail`(option: String) {
    val userId = database.prepareUser()
    val email = randomAlphanumeric(240) + "@" + randomAlphabetic(10) + "." + randomAlphabetic(3)
    encryptionService.mock({ encrypt(email) }, "encryptedEmail")

    if (option == "update email") {
      transaction(database) {
        UserEmailsTable.insert {
          it[UserEmailsTable.userId] = userId
          it[encryptedEmail] = "<EMAIL>"
        }
      }
    }

    runBlocking {
      service.saveUserEmail(userId, email)
    }

    transaction(database) {
      UserEmailsTable
        .select { UserEmailsTable.userId eq userId }
        .map {
          assertThat(it[UserEmailsTable.encryptedEmail]).isEqualTo("encryptedEmail")
        }
    }.count()
      .let { assertThat(it).isEqualTo(1) }
  }

  @Test
  fun `SHOULD return null ON getUserFirstAppVersion WHEN no history`() {
    val userId = database.prepareUser()

    runBlocking { service.getUserFirstAppVersion(userId) }
      .also { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD return first user app version ON getUserFirstAppVersion`() {
    val userId = database.prepareUser()

    runBlocking {
      service.logUserAppVersionChange(userId, AppVersionDto(ANDROID, 40))
      service.logUserAppVersionChange(userId, AppVersionDto(ANDROID, 20))
      service.logUserAppVersionChange(userId, AppVersionDto(ANDROID, 30))

      service.getUserFirstAppVersion(userId)
    }
      .also { assertThat(it).isEqualTo(40) }
  }

  @Test
  fun `SHOULD get last notification date ON getLastNotificationDateByType`() {
    val userId = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)

    transaction(database) {
      (1L..5L).map { times ->
        database.prepareNotificationTrack(userId, NotificationType.THIRTY_MINUTES_TO_CASHOUT, now.minus(times, ChronoUnit.HOURS))
      }
    }
    database.prepareNotificationTrack(userId, NotificationType.DAILY_EMAIL, now)
    val anotherUser = database.prepareUser()
    database.prepareNotificationTrack(anotherUser, NotificationType.THIRTY_MINUTES_TO_CASHOUT, now)

    runBlocking {
      service.getLastNotificationDateByType(userId, NotificationType.THIRTY_MINUTES_TO_CASHOUT)
    }.let { assertThat(it).isEqualTo(now.minus(1L, ChronoUnit.HOURS)) }
  }

  @Test
  fun `SHOULD return null ON getLastNotificationDateByType WHEN user has no previous notifications`() {
    val userId = database.prepareUser()

    runBlocking {
      service.getLastNotificationDateByType(userId, NotificationType.THIRTY_MINUTES_TO_CASHOUT)
    }.let { assertThat(it).isEqualTo(null) }
  }

  @Test
  fun `SHOULD update notification date ON trackUserNotification WHEN notification tracked not for the first time`() {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser()
    database.prepareNotificationTrack(userId, NotificationType.THIRTY_MINUTES_TO_CASHOUT, now.minus(20, ChronoUnit.DAYS))

    runBlocking {
      service.trackUserNotification(userId, NotificationType.THIRTY_MINUTES_TO_CASHOUT, now)
    }

    transaction(database) {
      UserLastNotificationTable
        .select {
          (UserLastNotificationTable.userId eq userId) and
            (UserLastNotificationTable.notificationType eq "THIRTY_MINUTES_TO_CASHOUT")
        }
        .map { assertThat(it[UserLastNotificationTable.notifiedAt]).isEqualTo(now) }
        .count()
        .let { assertThat(it).isEqualTo(1) }
    }
  }

  @Test
  fun `SHOULD save notification date with type ON trackUserNotification`() {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser()

    runBlocking {
      service.trackUserNotification(userId, NotificationType.THIRTY_MINUTES_TO_CASHOUT, now)
      service.trackUserNotification(userId, NotificationType.THIRTY_MINUTES_TO_CASHOUT, now)
    }

    transaction(database) {
      UserLastNotificationTable
        .select {
          (UserLastNotificationTable.userId eq userId) and
            (UserLastNotificationTable.notificationType eq "THIRTY_MINUTES_TO_CASHOUT")
        }
        .map { assertThat(it[UserLastNotificationTable.notifiedAt]).isEqualTo(now) }
        .count()
        .let { assertThat(it).isEqualTo(1) }
    }
  }

  @Test
  fun `SHOULD set user adjustId ON updateAdjustId`() {
    val adjustId = "adjustId"
    val userId = database.prepareUser()

    val before = transaction(database) {
      UserAdjustIdTable
        .slice(UserAdjustIdTable.adjustId)
        .select { UserAdjustIdTable.userId eq userId }
        .firstOrNull()
        ?.get(UserAdjustIdTable.adjustId)
    }
    assertThat(before).isNull()

    runBlocking {
      service.updateAdjustId(userId, adjustId)
    }

    val after = transaction(database) {
      UserAdjustIdTable
        .slice(UserAdjustIdTable.adjustId)
        .select { UserAdjustIdTable.userId eq userId }
        .firstOrNull()
        ?.get(UserAdjustIdTable.adjustId)
    }
    assertThat(after).isEqualTo(adjustId)
  }

  @Test
  fun `SHOULD return adjustId ON getAdjustId`() {
    val adjustId1 = "adjustId1"
    val adjustId2 = "adjustId2"
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val userId3 = database.prepareUser()
    database.addAdjustId(userId = userId1, adjustId = adjustId1)
    database.addAdjustId(userId = userId2, adjustId = adjustId2)
    database.addAdjustId(userId = userId3, adjustId = adjustId1)

    mapOf(
      userId1 to adjustId1,
      userId2 to adjustId2,
      userId3 to adjustId1
    ).forEach { (userId, adjustId) ->
      runBlocking { service.getAdjustId(userId) }
        .also { assertThat(it).isEqualTo(adjustId) }
    }

    runBlocking { service.getAdjustId(UUID.randomUUID().toString()) }
      .also { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD update user adjustId ON updateAdjustId WHEN value is already set`() {
    val adjustId = "adjustId"
    val userId = database.prepareUser()

    runBlocking {
      service.updateAdjustId(userId, adjustId)
      service.updateAdjustId(userId, "anotherAdjustId")
    }

    val actual = transaction(database) {
      UserAdjustIdTable.slice(UserAdjustIdTable.adjustId).select { UserAdjustIdTable.userId eq userId }.firstOrNull()?.get(UserAdjustIdTable.adjustId)
    }
    assertThat(actual).isEqualTo("anotherAdjustId")
    verifyBlocking(messageBus, times(2)) { publishAsync(InvalidateUserExternalIDsCacheEffect(userId)) }
  }

  @Test
  fun `SHOULD remove some empty game totals on removeBatchOfZeroGameTotals`() {
    transaction(database) {
      UserGameBalanceTotalsTable.deleteAll()
    }
    val user1 = database.prepareUser("f" + UUID.randomUUID().toString().takeLast(35))
    val user2 = database.prepareUser("a" + UUID.randomUUID().toString().takeLast(35))
    val games = (1..5).map { database.prepareGame() }
    listOf(user1, user2).forEach { user ->
      database.addUserTotalCoinsByGameId(user, games.first(), 1)
      games.takeLast(4).forEach { game -> database.addUserTotalCoinsByGameId(user, game, 0) }
    }

    runBlocking {
      service.removeBatchOfZeroGameTotals(3, "f")
    }

    transaction(database) {
      UserGameBalanceTotalsTable
        .select { UserGameBalanceTotalsTable.userId eq user1 }
        .count()
        .let { assertThat(it).isEqualTo(2) }
      UserGameBalanceTotalsTable
        .select { UserGameBalanceTotalsTable.userId eq user2 }
        .count()
        .let { assertThat(it).isEqualTo(5) }
    }

    runBlocking {
      service.removeBatchOfZeroGameTotals(3, "f")
      service.removeBatchOfZeroGameTotals(3, "a")
      service.removeBatchOfZeroGameTotals(3, "a")
    }

    transaction(database) {
      UserGameBalanceTotalsTable
        .slice(UserGameBalanceTotalsTable.coins)
        .selectAll()
        .map { assertThat(it[UserGameBalanceTotalsTable.coins]).isEqualTo(1) }
        .count().let { assertThat(it).isEqualTo(2) }
    }
  }


  @Test
  fun `SHOULD fill created_at_day by trigger ON new user creation`() {
    val now = Instant.now()
    val userId = database.prepareUser(createdAt = now)

    transaction(database) {
      UserTable.slice(UserTable.createdAtDay)
        .select { UserTable.id eq userId }
        .map { assertThat(it[UserTable.createdAtDay]).isEqualTo(now.truncatedTo(ChronoUnit.DAYS).localUtcDate()) }
    }
  }

  @ParameterizedTest
  @CsvSource(
    "false,false,false,false,false",
    "true,false,false,false,false",
    "true,true,false,false,false",
    "true,true,true,false,false",
    "true,true,true,true,false",
    "true,true,true,true,true"
  )
  fun `SHOULD fetch user external ids ON fetchExternalIds`(
    userExists: Boolean, googleAdIdDefined: Boolean, adjustIdDefined: Boolean, firebaseAppIdDefined: Boolean, trackingDataDefined: Boolean
  ) {
    val googleAdId = "user-google-ad-id"
    val adjustId = "user-adjust-id"
    val firebaseAppInstance = "firebase-app-instance"
    val trackingData = TrackingData("idfv", IDFV, IOS)

    val userId = if (userExists) {
      val userId = database.prepareUser(
        googleAdId = if (googleAdIdDefined) googleAdId else null,
        trackingData = if (trackingDataDefined) trackingData else null
      )
      if (adjustIdDefined) {
        runBlocking { service.updateAdjustId(userId, adjustId) }
      }
      if (firebaseAppIdDefined) {
        runBlocking { service.addOrUpdateFirebaseAppInstanceId(userId, firebaseAppInstance) }
      }

      userId
    } else {
      "non-existing-user-id"
    }

    val expected =
      if (userExists) {
        UserExternalIds(
          userId,
          if (googleAdIdDefined) googleAdId else null,
          null, // todo: to do or not to do?
          if (adjustIdDefined) adjustId else null,
          if (firebaseAppIdDefined) firebaseAppInstance else null,
          if (trackingDataDefined) trackingData else null,
        )
      } else null

    runBlocking { service.fetchExternalIds(userId) }
      .also { assertThat(it).isEqualTo(expected) }

  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD fetch the latest IDFA ON fetchExternalIds WHEN it is IOS`(appPlatform: AppPlatform) {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser(
      googleAdId = "user-google-ad-id",
      trackingData = TrackingData("idfv", IDFV, appPlatform)
    )
    transaction(database) {
      TrackingDataTable.insert {
        it[TrackingDataTable.userId] = userId
        it[trackingId] = "tt1"
        it[trackingType] = IDFA.name
        it[TrackingDataTable.appPlatform] = appPlatform.name
        it[createdAt] = now
      }
      TrackingDataTable.insert {
        it[TrackingDataTable.userId] = userId
        it[trackingId] = "tt2"
        it[trackingType] = IDFA.name
        it[TrackingDataTable.appPlatform] = appPlatform.name
        it[createdAt] = now.plusSeconds(5)
      }
      TrackingDataTable.insert {
        it[TrackingDataTable.userId] = userId
        it[trackingId] = "tt3"
        it[trackingType] = IDFA.name
        it[TrackingDataTable.appPlatform] = appPlatform.name
        it[createdAt] = now.minusSeconds(5)
      }
    }

    runBlocking { service.fetchExternalIds(userId) }
      .also {
        if (appPlatform == IOS || appPlatform == IOS_WEB)
          assertThat(it!!.idfa).isEqualTo("tt2")
        else
          assertThat(it!!.idfa).isNull()
      }

  }

  @Test
  fun `SHOULD update device locale on updateDeviceLocale`() {
    val userId = database.prepareUser()
    transaction(database) {
      UserAdditionalDataTable.insert {
        it[UserAdditionalDataTable.userId] = userId
        it[deviceLocale] = "fr"
      }
    }

    runBlocking { service.updateDeviceLocale(userId, ES_LOCALE) }

    transaction(database) {
      UserAdditionalDataTable
        .slice(UserAdditionalDataTable.deviceLocale, UserAdditionalDataTable.deviceLanguageTag)
        .select { UserAdditionalDataTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserAdditionalDataTable.deviceLocale]).isEqualTo("es")
          assertThat(it[UserAdditionalDataTable.deviceLanguageTag]).isEqualTo("es")
        }
    }
    verifyUserCacheInvalidationEffectWasPublished(userId)
  }

  @Test
  fun `SHOULD update device locale on updateDeviceLocale WHEN locale is country specific`() {
    val userId = database.prepareUser()
    transaction(database) {
      UserAdditionalDataTable.insert {
        it[UserAdditionalDataTable.userId] = userId
        it[deviceLocale] = "es"
      }
    }

    runBlocking { service.updateDeviceLocale(userId, Locale.CANADA_FRENCH) }

    transaction(database) {
      UserAdditionalDataTable
        .slice(UserAdditionalDataTable.deviceLocale, UserAdditionalDataTable.deviceLanguageTag)
        .select { UserAdditionalDataTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserAdditionalDataTable.deviceLocale]).isEqualTo("fr")
          assertThat(it[UserAdditionalDataTable.deviceLanguageTag]).isEqualTo("fr-ca")
        }
    }
    verifyUserCacheInvalidationEffectWasPublished(userId)
  }

  @Test
  fun `SHOULD update gps_location_country on updateGpsLocationCountry`() {
    val userId = database.prepareUser()
    transaction(database) {
      UserAdditionalDataTable.insert {
        it[UserAdditionalDataTable.userId] = userId
      }
    }

    runBlocking { service.updateGpsLocationCountry(userId, "DE") }

    transaction(database) {
      UserAdditionalDataTable
        .slice(UserAdditionalDataTable.gpsLocationCountry)
        .select { UserAdditionalDataTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserAdditionalDataTable.gpsLocationCountry]).isEqualTo("DE")
        }
    }
  }

  @Test
  fun `SHOULD get gps_location_country on findGpsLocationCountry WHEN location is not filled yet`() {
    val userId = database.prepareUser()
    transaction(database) {
      UserAdditionalDataTable.insert {
        it[UserAdditionalDataTable.userId] = userId
      }
    }

    assertNull(runBlocking { service.findGpsLocationCountry(userId) })
  }

  @Test
  fun `SHOULD get gps_location_country on findGpsLocationCountry WHEN location is already filled`() {
    val userId = database.prepareUser()
    transaction(database) {
      UserAdditionalDataTable.insert {
        it[UserAdditionalDataTable.userId] = userId
        it[gpsLocationCountry] = "TR"
      }
    }

    assertEquals("TR", runBlocking { service.findGpsLocationCountry(userId) })
  }

  @ParameterizedTest
  @ValueSource(strings = ["UserGameCoinsBalanceByPeriodsTable"])
  fun `SHOULD remove obsolete data from coins by periods or revenue by periods table`(option: String) {
    val (table, testingMethod) = when (option) {
      "UserGameCoinsBalanceByPeriodsTable" -> UserGameCoinsBalanceByPeriodsTable to service::removeBatchOfCoinsTrackedByPeriods
      else -> throw AssertionError("unexpected variation of test")
    }
    transaction(database) {
      table.deleteAll()
    }
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    timeService.mock({ now() }, now)

    val obsoletePeriod = now.minus(20, ChronoUnit.DAYS)
    val actualPeriod = now.minus(14, ChronoUnit.DAYS)
    val user1 = database.prepareUser("f" + UUID.randomUUID().toString().takeLast(35))
    val user2 = database.prepareUser("a" + UUID.randomUUID().toString().takeLast(35))
    val games = (1..5).map { database.prepareGame() }
    listOf(user1, user2).forEach { user ->
      games.forEach { gameId ->
        database.addGameCoinsByPeriods(user, gameId, obsoletePeriod)
        database.addGameCoinsByPeriods(user, gameId, actualPeriod)
      }
    }

    runBlocking {
      testingMethod.invoke(3, "f")
    }
    @Suppress("UNCHECKED_CAST")
    val userIdColumn = table.columns.first { it.name == "user_id" } as Column<String>

    transaction(database) {
      table
        .select { userIdColumn.eq(user1) }
        .count()
        .let { assertThat(it).isEqualTo(7) }
      table
        .select { userIdColumn eq user2 }
        .count()
        .let { assertThat(it).isEqualTo(10) }
    }

    runBlocking {
      testingMethod.invoke(3, "f")
      testingMethod.invoke(3, "a")
      testingMethod.invoke(3, "a")
    }

    @Suppress("UNCHECKED_CAST")
    val periodStartColumn = table.columns.first { it.name == "period_start" } as Column<Instant>
    val counterColumn = when (option) {
      "UserGameCoinsBalanceByPeriodsTable" -> UserGameCoinsBalanceByPeriodsTable.coinsTransactionsCount
      else -> throw AssertionError("unexpected variation of test")
    }

    transaction(database) {
      table
        .slice(counterColumn, periodStartColumn)
        .selectAll()
        .map {
          assertThat(it[counterColumn]).isEqualTo(1)
          assertThat(it[periodStartColumn]).isEqualTo(actualPeriod)
        }
        .count().let { assertThat(it).isEqualTo(10) }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["the first time", "not the first time"])
  fun `SHOULD add some notification amounts left ON updateUserOnEmptyCPNotificationTable for new user and old user`(option: String) {
    val userId = database.prepareUser()
    if (option == "not the first time") {
      transaction(database) {
        UserOnEmptyCPNotificationTable.insert { it[UserOnEmptyCPNotificationTable.userId] = userId; it[notificationsLeft] = 0 }
      }
    }

    runBlocking { service.updateUserOnEmptyCPNotificationTable(userId, 5) }

    transaction(database) {
      UserOnEmptyCPNotificationTable
        .select { UserOnEmptyCPNotificationTable.userId eq userId }
        .first()
        .let { assertThat(it[UserOnEmptyCPNotificationTable.notificationsLeft]).isEqualTo(5) }
    }
  }

  @Test
  fun `SHOULD reduce notifications amount by 1 and return 1 row updated if we have some ON reduceAmountOfNotificationsOnEmptyCPByOneReturnRowsUpdated`() {
    val userId = database.prepareUser()
    transaction(database) {
      UserOnEmptyCPNotificationTable.insert { it[UserOnEmptyCPNotificationTable.userId] = userId; it[notificationsLeft] = 5 }
    }

    runBlocking { service.reduceAmountOfNotificationsOnEmptyCPByOneReturnRowsUpdated(userId) }
      .let { assertThat(it).isEqualTo(1) }

    transaction(database) {
      UserOnEmptyCPNotificationTable
        .select { UserOnEmptyCPNotificationTable.userId eq userId }
        .first()
        .let { assertThat(it[UserOnEmptyCPNotificationTable.notificationsLeft]).isEqualTo(4) }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["no notifications scheduled ever", "all notifications were done"])
  fun `SHOULD not reduce notifications amount by 1 ON reduceAmountOfNotificationsOnEmptyCPByOneReturnRowsUpdated if we have no notifications left`(option: String) {
    val userId = database.prepareUser()
    if (option == "all notifications were done") {
      transaction(database) {
        UserOnEmptyCPNotificationTable.insert { it[UserOnEmptyCPNotificationTable.userId] = userId; it[notificationsLeft] = 0 }
      }
    }

    runBlocking { service.reduceAmountOfNotificationsOnEmptyCPByOneReturnRowsUpdated(userId) }
      .let { assertThat(it).isEqualTo(0) }

    if (option == "all notifications were done") {
      transaction(database) {
        UserOnEmptyCPNotificationTable
          .select { UserOnEmptyCPNotificationTable.userId eq userId }
          .first()
          .let { assertThat(it[UserOnEmptyCPNotificationTable.notificationsLeft]).isEqualTo(0) }
      }
    }
  }

  @Test
  fun `SHOULD return last time user got coins ON getUserLastGameCoinsDate`() {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val userId3 = database.prepareUser()
    val gameId1 = database.prepareGame()
    val gameId2 = database.prepareGame()

    database.addUserTotalCoinsByGameId(userId1, gameId1, 100, now.minusSeconds(5))
    database.addUserTotalCoinsByGameId(userId1, gameId2, 100, now)
    database.addUserTotalCoinsByGameId(userId2, gameId1, 100, now.minusSeconds(10))
    database.addUserTotalCoinsByGameId(userId2, gameId2, 100, now.minusSeconds(15))

    runBlocking { service.getUserLastGameCoinsDate(userId1) }
      .let { assertThat(it).isEqualTo(now) }
    runBlocking { service.getUserLastGameCoinsDate(userId2) }
      .let { assertThat(it).isEqualTo(now.minusSeconds(10)) }
    runBlocking { service.getUserLastGameCoinsDate(userId3) }
      .let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD return last time user got coins ON getUserLastGameCoinsDate em2`() {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val userId3 = database.prepareUser()
    val gameId1 = database.prepareGame()
    val gameId2 = database.prepareGame()

    database.addUserTotalEm2CoinsByGameId(userId1, gameId1, BigDecimal.TEN, now.minusSeconds(5))
    database.addUserTotalEm2CoinsByGameId(userId1, gameId2, BigDecimal.TEN, now)
    database.addUserTotalEm2CoinsByGameId(userId2, gameId1, BigDecimal.TEN, now.minusSeconds(10))
    database.addUserTotalEm2CoinsByGameId(userId2, gameId2, BigDecimal.TEN, now.minusSeconds(15))

    runBlocking { service.getUserLastGameEm2CoinsDate(userId1) }
      .let { assertThat(it).isEqualTo(now) }
    runBlocking { service.getUserLastGameEm2CoinsDate(userId2) }
      .let { assertThat(it).isEqualTo(now.minusSeconds(10)) }
    runBlocking { service.getUserLastGameEm2CoinsDate(userId3) }
      .let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD calculate user coins totals for period ON getUserCoinsByPeriod`() {
    transaction(database) { UserGameCoinsBalanceByPeriodsTable.deleteAll() }
    val periodStart = Instant.now().minus(18, ChronoUnit.DAYS)
    val periodEnd = periodStart.plus(3, ChronoUnit.HOURS)
    val periodIn = periodStart.plus(5, ChronoUnit.MINUTES)
    val periodIn2 = periodStart.plus(4, ChronoUnit.MINUTES)
    val periodOut = periodStart.minus(5, ChronoUnit.MINUTES)

    val gameId = database.prepareGame()
    val userId = database.prepareUser(countryCode = "US")
    val userId2 = database.prepareUser(countryCode = "ZZ")
    val userId3 = database.prepareUser(countryCode = "ZZ")

    database.addGameCoinsByPeriods(userId, gameId, periodIn)
    database.addGameCoinsByPeriods(userId2, gameId, periodIn)
    database.addGameCoinsByPeriods(userId3, gameId, periodIn)
    database.addGameCoinsByPeriods(userId2, gameId, periodIn2)
    database.addGameCoinsByPeriods(userId3, gameId, periodIn2)
    database.addGameCoinsByPeriods(userId2, gameId, periodOut)
    database.addGameCoinsByPeriods(userId3, gameId, periodOut)

    runBlocking {
      service.getUserCoinsByPeriod(userId2, periodStart, periodEnd)
    }.let { assertThat(it).isEqualByComparingTo(2L) }
  }

  @Test
  fun `SHOULD return 0 coins ON getUserCoinsByPeriod WHEN no coins`() {
    transaction(database) { UserGameCoinsBalanceByPeriodsTable.deleteAll() }

    runBlocking {
      service.getUserCoinsByPeriod(UUID.randomUUID().toString(), Instant.now(), Instant.now())
    }.let { assertThat(it).isEqualByComparingTo(0L) }
  }

  @Test
  fun `SHOULD return CountryTierSettingsON getUserCountryTierSettings WHEN user country is in supported countries and it has some tier`() {
    val userId = database.prepareUser(countryCode = "TT")
    val expectedQuotas = listOf(
      BigDecimal("0.03"),
      BigDecimal("0.07"),
      BigDecimal("0.15"),
      BigDecimal("0.25"),
      BigDecimal("0.50"),
      BigDecimal("1.00"),
      BigDecimal("1.00"),
      BigDecimal("2.00")
    )
    val countryTierId = database.addCountryTier(BigDecimal(150), "0.03,0.07,0.15,0.25,0.50,1.00,1.00,2.00")
    database.addSupportedCountry("TT", countryTierId)

    runBlocking { service.getUserCountryTierSettings(userId) }
      .let { assertThat(it).isEqualTo(CountryTierSettings(BigDecimal("1.50"), expectedQuotas)) }
  }

  @ParameterizedTest
  @ValueSource(strings = ["0,0", "0,0,0,0,0,0,0,0,0"])
  fun `SHOULD fail with error ON getUserCountryTierSettings WHEN quotas list size does not match`(quotasStr: String) {
    val userId = database.prepareUser(countryCode = "TY")
    val countryTierId = database.addCountryTier(BigDecimal(150), quotasStr)
    database.addSupportedCountry("TY", countryTierId)

    assertFailsWith<java.lang.IllegalStateException> {
      runBlocking { service.getUserCountryTierSettings(userId) }
    }
  }

  @Test
  fun `SHOULD return null ON getUserCountryTierSettings WHEN user country is not in supported countries and it has some tier`() {
    val userId = database.prepareUser(countryCode = "T1")

    runBlocking { service.getUserCountryTierSettings(userId) }
      .let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD throw an exception ON getUserCountryTierSettings WHEN tier quotas list can not be converted to big decimals`() {
    val userId = database.prepareUser(countryCode = "T2")
    val countryTierId = database.addCountryTier(BigDecimal(150), "0.03_0.07,0.15,0.25,0.50,1.00,1.00,2.00")
    database.addSupportedCountry("T2", countryTierId)

    assertFailsWith<NumberFormatException> {
      runBlocking { service.getUserCountryTierSettings(userId) }
    }
  }

  @Test
  fun `SHOULD parse first 4 tiers quotas lists right ON split and toBigDecimal transform`() {
    val quotasList = transaction(database) {
      LimitTierTable
        .select { LimitTierTable.id less 5 }
        .associate { row ->
          row[LimitTierTable.id] to row[LimitTierTable.dailyEarningsQuotas].split(",").map { it.toBigDecimal() }
        }
    }
    assertThat(quotasList[1]).isEqualTo(
      listOf(
        BigDecimal("0.03"), BigDecimal("0.07"), BigDecimal("0.15"),
        BigDecimal("0.25"), BigDecimal("0.50"), BigDecimal("1.00"),
        BigDecimal("1.00"), BigDecimal("2.00")
      )
    )
    assertThat(quotasList[2]).isEqualTo(
      listOf(
        BigDecimal("0.02"), BigDecimal("0.04"), BigDecimal("0.06"),
        BigDecimal("0.13"), BigDecimal("0.25"), BigDecimal("0.50"),
        BigDecimal("1.00"), BigDecimal("2.00")
      )
    )
    assertThat(quotasList[3]).isEqualTo(
      listOf(
        BigDecimal("0.02"), BigDecimal("0.04"), BigDecimal("0.06"),
        BigDecimal("0.13"), BigDecimal("0.25"), BigDecimal("0.50"),
        BigDecimal("1.00"), BigDecimal("1.00")
      )
    )
    assertThat(quotasList[4]).isEqualTo(
      listOf(
        BigDecimal("0.01"), BigDecimal("0.02"), BigDecimal("0.03"),
        BigDecimal("0.06"), BigDecimal("0.13"), BigDecimal("0.25"),
        BigDecimal("0.50"), BigDecimal("1.00")
      )
    )
  }

  @Test
  fun `SHOULD return empty map ON getGamesCoinsByPeriod WHEN no data`() {
    val userId = database.prepareUser()
    val gameId = database.prepareGame()
    val periodEnd = now
    val periodStart = now.minus(1L, ChronoUnit.HOURS)

    transaction(database) { UserGameCoinsBalanceByPeriodsTable.deleteAll() }

    // too before
    database.addGameCoinsByPeriods(userId, gameId, periodStart.minusSeconds(600))
    // too after
    database.addGameCoinsByPeriods(userId, gameId, periodEnd.plusSeconds(600))

    val expected = emptyMap<String, Long>()

    runBlocking { service.getGamesCoinsByPeriod(periodStart, periodEnd) }
      .let { assertThat(it).isEqualTo(expected) }

  }

  @Test
  fun `SHOULD get all game coins in period ON getGamesCoinsByPeriod`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val gameId1 = database.prepareGame(applicationId = "gameId1a")
    val gameId2 = database.prepareGame(applicationId = "gameId2a")
    val periodEnd = now
    val periodStart = now.minus(1L, ChronoUnit.HOURS)

    transaction(database) { UserGameCoinsBalanceByPeriodsTable.deleteAll() }

    database.addGameCoinsByPeriods(userId1, gameId1, periodStart.plusSeconds(1))
    database.addGameCoinsByPeriods(userId1, gameId1, periodStart.plusSeconds(60))
    database.addGameCoinsByPeriods(userId1, gameId2, periodStart.plusSeconds(60))
    database.addGameCoinsByPeriods(userId2, gameId1, periodStart.plusSeconds(1))

    val expected = mapOf(
      "gameId1a" to 3L,
      "gameId2a" to 1L,
    )

    runBlocking { service.getGamesCoinsByPeriod(periodStart, periodEnd) }
      .let { assertThat(it).isEqualTo(expected) }
  }

  @Test
  fun `SHOULD return empty map ON getUserGamesCoinsByPeriod WHEN no data`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val gameId = database.prepareGame()
    val periodEnd = now
    val periodStart = now.minus(1L, ChronoUnit.HOURS)

    // too before
    database.addGameCoinsByPeriods(userId1, gameId, periodStart.minusSeconds(600))
    // too after
    database.addGameCoinsByPeriods(userId1, gameId, periodEnd.plusSeconds(600))
    // another user
    database.addGameCoinsByPeriods(userId2, gameId, periodStart)

    val expected = emptyMap<String, Long>()

    runBlocking { service.getUserGamesCoinsByPeriod(userId1, periodStart, periodEnd) }
      .let { assertThat(it).isEqualTo(expected) }
  }

  @Test
  fun `SHOULD return user coins ON getUserGamesCoinsByPeriod`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val gameId1 = database.prepareGame(applicationId = "gameId1")
    val gameId2 = database.prepareGame(applicationId = "gameId2")
    val periodEnd = now
    val periodStart = now.minus(1L, ChronoUnit.HOURS)

    database.addGameCoinsByPeriods(userId1, gameId1, periodStart.plusSeconds(1))
    database.addGameCoinsByPeriods(userId1, gameId1, periodStart.plusSeconds(60))
    database.addGameCoinsByPeriods(userId1, gameId2, periodStart.plusSeconds(1))
    database.addGameCoinsByPeriods(userId2, gameId1, periodStart.plusSeconds(1)) // other user

    val expected = mapOf(
      "gameId1" to 2L,
      "gameId2" to 1L,
    )

    runBlocking {
      service.getUserGamesCoinsByPeriod(userId1, periodStart, periodEnd)
    }
      .let { assertThat(it).isEqualTo(expected) }
  }

  @Test
  fun `SHOULD correct count users ON countUsersWithSameGoogleAdId`() {
    val uniqueGoogleAdId = UUID.randomUUID().toString()
    val duplicateGoogleAdId = UUID.randomUUID().toString()
    val userId = database.prepareUser(googleAdId = uniqueGoogleAdId)
    val userId2 = database.prepareUser(googleAdId = duplicateGoogleAdId)
    val userId3 = database.prepareUser(googleAdId = duplicateGoogleAdId)

    assertEquals(0, runBlocking { service.countUsersWithSameGoogleAdId(userId, uniqueGoogleAdId) })
    assertEquals(1, runBlocking { service.countUsersWithSameGoogleAdId(userId2, uniqueGoogleAdId) })
    assertEquals(2, runBlocking { service.countUsersWithSameGoogleAdId(userId, duplicateGoogleAdId) })
    assertEquals(1, runBlocking { service.countUsersWithSameGoogleAdId(userId2, duplicateGoogleAdId) })
    assertEquals(1, runBlocking { service.countUsersWithSameGoogleAdId(userId3, duplicateGoogleAdId) })
  }

  @Test
  fun `SHOULD correct count users ON countUsersWithSameTrackingData`() {
    val uniqueTrackingData = generateTrackingData()
    val duplicatedTrackingData = generateTrackingData()
    val userId = database.prepareUser(trackingData = uniqueTrackingData)
    val userId2 = database.prepareUser(trackingData = duplicatedTrackingData)
    val userId3 = database.prepareUser(trackingData = duplicatedTrackingData)

    assertEquals(0, runBlocking { service.countUsersWithSameTrackingData(userId, uniqueTrackingData) })
    assertEquals(1, runBlocking { service.countUsersWithSameTrackingData(userId2, uniqueTrackingData) })
    assertEquals(2, runBlocking { service.countUsersWithSameTrackingData(userId, duplicatedTrackingData) })
    assertEquals(1, runBlocking { service.countUsersWithSameTrackingData(userId2, duplicatedTrackingData) })
    assertEquals(1, runBlocking { service.countUsersWithSameTrackingData(userId3, duplicatedTrackingData) })
  }

  @Test
  fun `SHOULD correct count users ON countUsersWithSameGoogleAdId by userId WHEN user not exists`() {
    val nonExistentUser = UUID.randomUUID().toString()
    assertEquals(0, runBlocking { service.countUsersWithSameGoogleAdId(nonExistentUser) })
  }

  @Test
  fun `SHOULD correct count users ON countUsersWithSameGoogleAdId by userId WHEN user without gaid`() {
    val user = database.prepareUser()
    assertEquals(0, runBlocking { service.countUsersWithSameGoogleAdId(user) })
  }

  @Test
  fun `SHOULD correct count users ON countUsersWithSameGoogleAdId by userId WHEN unique gaid`() {
    val user = database.prepareUser()
    database.updateGoogleAdId(user, UUID.randomUUID().toString())
    assertEquals(0, runBlocking { service.countUsersWithSameGoogleAdId(user) })
  }

  @Test
  fun `SHOULD correct count users ON countUsersWithSameGoogleAdId by userId WHEN user changed gaid`() {
    val user = database.prepareUser()
    database.updateGoogleAdId(user, UUID.randomUUID().toString())
    database.updateGoogleAdId(user, UUID.randomUUID().toString())
    assertEquals(0, runBlocking { service.countUsersWithSameGoogleAdId(user) })
  }

  @Test
  fun `SHOULD correct count users ON countUsersWithSameGoogleAdId by userId WHEN user changed gaid and back`() {
    val user = database.prepareUser()
    val gaid1 = UUID.randomUUID().toString()
    val gaid2 = UUID.randomUUID().toString()
    database.updateGoogleAdId(user, gaid1)
    database.updateGoogleAdId(user, gaid2)
    database.updateGoogleAdId(user, gaid1)
    assertEquals(0, runBlocking { service.countUsersWithSameGoogleAdId(user) })
  }

  @Test
  fun `SHOULD correct count users ON countUsersWithSameGoogleAdId by userId WHEN two users with same gaid`() {
    val user1 = database.prepareUser()
    val user2 = database.prepareUser()
    val gaid = UUID.randomUUID().toString()

    database.updateGoogleAdId(user1, gaid)
    database.updateGoogleAdId(user2, gaid)

    assertEquals(1, runBlocking { service.countUsersWithSameGoogleAdId(user1) })
    assertEquals(1, runBlocking { service.countUsersWithSameGoogleAdId(user2) })
  }


  @Test
  fun `SHOULD correct count users ON countUsersWithSameGoogleAdId by userId WHEN two users and second changes gaid to same`() {
    val user1 = database.prepareUser()
    val user2 = database.prepareUser()
    val gaid11 = UUID.randomUUID().toString()
    val gaid21 = UUID.randomUUID().toString()

    database.updateGoogleAdId(user1, gaid11)

    database.updateGoogleAdId(user2, gaid21)
    database.updateGoogleAdId(user2, gaid11)

    assertEquals(1, runBlocking { service.countUsersWithSameGoogleAdId(user1) })
    assertEquals(1, runBlocking { service.countUsersWithSameGoogleAdId(user2) })
  }

  @Test
  fun `SHOULD correct count users ON countUsersWithSameGoogleAdId by userId WHEN two users start same gaid and both change`() {
    val user1 = database.prepareUser()
    val user2 = database.prepareUser()
    val gaid0 = UUID.randomUUID().toString()
    val gaid11 = UUID.randomUUID().toString()
    val gaid21 = UUID.randomUUID().toString()

    database.updateGoogleAdId(user1, gaid0)
    database.updateGoogleAdId(user1, gaid11)

    database.updateGoogleAdId(user2, gaid0)
    database.updateGoogleAdId(user2, gaid21)

    assertEquals(0, runBlocking { service.countUsersWithSameGoogleAdId(user1) })
    assertEquals(0, runBlocking { service.countUsersWithSameGoogleAdId(user2) })
  }

  @Test
  fun `SHOULD correct count users ON countUsersWithSameGoogleAdId by userId WHEN two users both changes second ends up what first has previously`() {
    val user1 = database.prepareUser()
    val user2 = database.prepareUser()
    val gaid11 = UUID.randomUUID().toString()
    val gaid12 = UUID.randomUUID().toString()
    val gaid21 = UUID.randomUUID().toString()
    val gaid22 = UUID.randomUUID().toString()

    database.updateGoogleAdId(user1, gaid11)
    database.updateGoogleAdId(user1, gaid12)

    database.updateGoogleAdId(user2, gaid21)
    database.updateGoogleAdId(user2, gaid22)
    database.updateGoogleAdId(user2, gaid11)

    assertEquals(0, runBlocking { service.countUsersWithSameGoogleAdId(user1) })
    assertEquals(1, runBlocking { service.countUsersWithSameGoogleAdId(user2) })
  }

  @Test
  fun `SHOULD correct count users ON countUsersWithSameGoogleAdId by userId WHEN 3 users with same gaid`() {
    val user1 = database.prepareUser()
    val user2 = database.prepareUser()
    val user3 = database.prepareUser()
    val gaid = UUID.randomUUID().toString()

    database.updateGoogleAdId(user1, gaid)
    database.updateGoogleAdId(user2, gaid)
    database.updateGoogleAdId(user3, gaid)

    assertEquals(2, runBlocking { service.countUsersWithSameGoogleAdId(user1) })
    assertEquals(2, runBlocking { service.countUsersWithSameGoogleAdId(user2) })
    assertEquals(2, runBlocking { service.countUsersWithSameGoogleAdId(user3) })
  }

  @Test
  fun `SHOULD get userId ON fetchUserId by tracking id`() {
    val firstTrackingData = generateTrackingData()
    val secondTrackingData = generateTrackingData()
    database.prepareUser(trackingData = firstTrackingData, isDeleted = true) // deleted user with same tracking data
    val userId = database.prepareUser(trackingData = firstTrackingData)
    database.prepareUser(trackingData = secondTrackingData)

    runBlocking { service.fetchUserId(firstTrackingData.id) }.let { actual ->
      assertThat(actual).isEqualTo(userId)
    }
  }

  @Test
  fun `SHOULD throw ON fetchUserId by tracking id WHEN no user`() {
    val trackingId = UUID.randomUUID().toString()
    assertThrows<UserRecordNotFoundException> { runBlocking { service.fetchUserId(trackingId) } }
      .let { exception ->
        assertThat(exception.message).isEqualTo("Could not find user record with current_tracking_id $trackingId")
      }
  }

  @Test
  fun `SHOULD throw ON fetchUserId by tracking id WHEN user deleted`() {
    val trackingData = generateTrackingData()
    database.prepareUser(trackingData = trackingData, isDeleted = true)

    assertThrows<UserRecordNotFoundException> { runBlocking { service.fetchUserId(trackingData.id) } }
      .let { exception ->
        assertThat(exception.message).isEqualTo("Could not find user record with current_tracking_id ${trackingData.id}")
      }
  }

  @Test
  fun `SHOULD return empty list ON fetchUserIds WHEN empty inputs list`() {
    runBlocking { service.fetchUserIds(emptyList()) }.let { actual ->
      assertThat(actual).isEmpty()
    }
  }

  @Test
  fun `SHOULD get trackingId to userId pairs ON fetchUserIds`() {
    val trackingData1 = generateTrackingData()
    val trackingData2 = generateTrackingData()
    val trackingData3 = generateTrackingData()
    database.prepareUser(trackingData = trackingData1, isDeleted = true) // deleted user with same tracking data
    val userId2 = database.prepareUser(trackingData = trackingData1)
    database.prepareUser(trackingData = trackingData2)
    val userId4 = database.prepareUser(trackingData = trackingData3)

    runBlocking { service.fetchUserIds(listOf(trackingData1.id, trackingData3.id)) }.let { actual ->
      assertThat(actual.toSet()).isEqualTo(
        setOf(
          UserIdAndTrackingId(trackingData1.id, userId2),
          UserIdAndTrackingId(trackingData3.id, userId4),
        )
      )
    }
  }

  @Test
  fun `SHOULD not return user ON fetchUserId by tracking id WHEN user deleted`() {
    val trackingData = generateTrackingData()
    database.prepareUser(trackingData = trackingData, isDeleted = true)

    runBlocking { service.fetchUserIds(listOf(trackingData.id)) }.let { actual ->
      assertThat(actual).isEmpty()
    }
  }

  @Test
  fun `SHOULD get userId ON fetchUserId by trackingData`() {
    val firstTrackingData = generateTrackingData()
    val secondTrackingData = generateTrackingData()
    database.prepareUser(trackingData = firstTrackingData, isDeleted = true) // deleted user with same tracking data
    val userId = database.prepareUser(trackingData = firstTrackingData)
    database.prepareUser(trackingData = secondTrackingData)

    assertEquals(userId, runBlocking { service.fetchUserId(firstTrackingData) })
  }

  @Test
  fun `SHOULD get userId list ON fetchAllUserIds by trackingData`() {
    val trackingData = generateTrackingData()
    val userId = database.prepareUser(trackingData = trackingData)
    val userId2 = database.prepareUser(trackingData = trackingData)

    assertThat(runBlocking { service.fetchAllUserIds(trackingData) }).containsOnly(userId, userId2)
  }

  @Test
  fun `SHOULD calculate total property for UserCurrentCoinsBalance`() {
    assertThat(
      UserCurrentCoinsBalance(
        gameCoins = BigDecimal("1.001"),
        offerCoins = BigDecimal("2.002"),
        bonusCoins = BigDecimal("3.003")
      )
        .total
    ).isEqualTo(BigDecimal("6.006"))
  }

  @Test
  fun `SHOULD add users shared email ON markUsersAsSharedEmail`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()

    runBlocking {
      service.markUsersAsSharedEmail(listOf(userId1))
      service.markUsersAsSharedEmail(listOf(userId1, userId2)) // idempotency
    }

    transaction(database) {
      UserSharedEmailTable
        .select { UserSharedEmailTable.userId eq userId1 }
        .count()
    }.let { assertThat(it).isEqualTo(1) }
    transaction(database) {
      UserSharedEmailTable
        .select { UserSharedEmailTable.userId eq userId2 }
        .count()
    }.let { assertThat(it).isEqualTo(1) }
  }

  @Test
  fun `SHOULD return flag for user shared email ON isUserSharedEmail`() {
    val userId = database.prepareUser()

    runBlocking {
      assertThat(service.isUserSharedEmail(userId)).isFalse()

      service.markUsersAsSharedEmail(listOf(userId))

      assertThat(service.isUserSharedEmail(userId)).isTrue()
    }
  }

  @Test
  fun `SHOULD return null ON getLastIpv4 when there is none`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserIpsTable.insert {
        it[UserIpsTable.userId] = userId
        it[ip] = "2a02:908:1582:8720:4d1c:2c02:a0c6:78ec"
      }
    }

    runBlocking {
      service.getLastIpV4ByUserId(userId).let {
        assertThat(it).isNull()
      }
    }
  }

  @Test
  fun `SHOULD return last ipv4 ON getLastIpv4`() {
    val userId = database.prepareUser()
    transaction(database) {
      UserIpsTable.insert {
        it[UserIpsTable.userId] = userId
        it[ip] = "**********"
      }
      UserIpsTable.insert {
        it[UserIpsTable.userId] = userId
        it[ip] = "2a02:908:1582:8720:4d1c:2c02:a0c6:78ec"
      }

      UserIpsTable.insert {
        it[UserIpsTable.userId] = userId
        it[ip] = "***********"
      }
    }
    runBlocking {
      service.getLastIpV4ByUserId(userId).let {
        assertThat(it).isEqualTo("***********")
      }
    }

  }

  @Test
  fun `SHOULD generate single account deletion row with cash-out flag 0 WHEN requestAccountDeletion two times and no cash-out`() {
    val userId = database.prepareUser()

    runBlocking {
      service.requestUserDeletion(userId = userId, hasCashoutData = false)
      service.requestUserDeletion(userId = userId, hasCashoutData = false)
    }
    transaction(database) {
      UserAccountDeletionRequestTable.select { UserAccountDeletionRequestTable.userId eq userId }
        .count().let { assertThat(it).isEqualTo(1) }
    }
  }

  @Test
  fun `SHOULD generate account deletion row with cash-out flag 1 WHEN requestAccountDeletion and has cash-out`() {
    val userId = database.prepareUser()

    runBlocking { service.requestUserDeletion(userId = userId, hasCashoutData = true) }
    transaction(database) {
      UserAccountDeletionRequestTable.select { UserAccountDeletionRequestTable.userId eq userId }
        .count().let { assertThat(it).isEqualTo(1) }
      UserAccountDeletionRequestTable.select { UserAccountDeletionRequestTable.userId eq userId }
        .firstOrNull()?.get(UserAccountDeletionRequestTable.hasCashoutData).let { assertThat(it).isEqualTo(true) }
    }
  }

  @Test
  fun `SHOULD return true on isUserUnique WHEN there is no other user with the same tracking id`() {
    val userId = database.prepareUser()
    val uuid = UUID.randomUUID().toString()
    transaction(database) {
      UserTable.update({ UserTable.id eq userId }) {
        it[currentTrackingId] = uuid
      }
    }

    runBlocking {
      assertThat(service.isUserUnique(userId)).isTrue()
    }
  }

  @Test
  fun `SHOULD return true on isUserUnique WHEN there is no tracking id for the user`() {
    val userId = database.prepareUser()

    runBlocking {
      assertThat(service.isUserUnique(userId)).isTrue()
    }
  }

  @Test
  fun `SHOULD return false on isUserUnique WHEN there is another user with the same tracking id`() {
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    val uuid = UUID.randomUUID().toString()
    transaction(database) {
      UserTable.update({ UserTable.id inList (listOf(userId, userId2)) }) {
        it[currentTrackingId] = uuid
      }
    }

    runBlocking {
      assertThat(service.isUserUnique(userId)).isFalse()
    }
  }

  @Test
  fun `SHOULD return user device specs WHEN it was saved previously`() {
    val deviceSpecs = UserDeviceSpecificationApiDto(
      modelName = "sdk_gphone_x64",
      ramSize = 8192,
    )
    val userId = runBlocking {
      service.createUser("us", createUserDataStub.copy(userRequestDto = createUserDataStub.userRequestDto?.copy(deviceSpecification = deviceSpecs)))
    }

    val actual = runBlocking {
      service.loadUserDevice(userId)
    }

    assertEquals(expected = deviceSpecs.modelName, actual = actual?.modelName)
    assertEquals(expected = deviceSpecs.ramSize, actual = actual?.ramSize)
  }

  @Test
  fun `SHOULD track encrypted name email and cashout id ON trackUserLastCashoutEmailData`() {
    val userId = database.prepareUser()
    val trnId1 = database.prepareUserCashoutTransaction(userId = userId)
    val trnId2 = database.prepareUserCashoutTransaction(userId = userId)
    val mCashoutTransactionStub = cashoutTransactionStub.copy(userId = userId)

    runBlocking {
      service.trackUserLastCashoutData(mCashoutTransactionStub.copy(cashoutTransactionId = trnId1))
    }

    transaction(database) {
      UserLastCashoutDataTable.select { UserLastCashoutDataTable.userId eq userId }.first()
    }.let {
      assertThat(it[UserLastCashoutDataTable.encryptedEmail]).isEqualTo("<EMAIL>")
      assertThat(it[UserLastCashoutDataTable.encryptedName]).isEqualTo("Encrypted Barbara")
      assertThat(it[UserLastCashoutDataTable.cashoutTransactionId]).isEqualTo(trnId1)
    }

    runBlocking {
      service.trackUserLastCashoutData(
        mCashoutTransactionStub.copy(
          cashoutTransactionId = trnId2,
          encryptedEmail = "encryptedEmailX",
          encryptedUserName = "encryptedNameX",
        )
      )
    }

    transaction(database) {
      UserLastCashoutDataTable.select { UserLastCashoutDataTable.userId eq userId }.first()
    }.let {
      assertThat(it[UserLastCashoutDataTable.encryptedEmail]).isEqualTo("encryptedEmailX")
      assertThat(it[UserLastCashoutDataTable.encryptedName]).isEqualTo("encryptedNameX")
      assertThat(it[UserLastCashoutDataTable.cashoutTransactionId]).isEqualTo(trnId2)
    }
  }

  @Test
  fun `SHOULD store first video reward ON storeFirstVideoReward`() {
    val userId = database.prepareUser()

    val reward = VideoAdReward(revenue = 0.123456789123456)

    runBlocking {
      service.storeFirstVideoReward(userId, reward)
    }.let { assertThat(it).isTrue() }

    transaction(database) {
      UserFirstVideoRewardTable.select { UserFirstVideoRewardTable.userId eq userId }.first()
    }.let {
      assertThat(it[UserFirstVideoRewardTable.reward]).isEqualTo(BigDecimal("0.12345679")) // 8 digits precision in DB
    }

    // ignoring update

    val reward2 = VideoAdReward(revenue = 3.14)

    runBlocking {
      service.storeFirstVideoReward(userId, reward2)
    }.let { assertThat(it).isFalse() }

    transaction(database) {
      UserFirstVideoRewardTable.select { UserFirstVideoRewardTable.userId eq userId }.first()
    }.let {
      assertThat(it[UserFirstVideoRewardTable.reward]).isEqualTo(BigDecimal("0.12345679"))
    }
  }

  @Test
  fun `SHOULD write to DB ON writeCurrentEcpmGroupsThresholds`() {
    val ecpmThresholds = mapOf(
      0 to BigDecimal("0.70469806"),
      1 to BigDecimal("0.60469806"),
      2 to BigDecimal("0.47652368"),
      3 to BigDecimal("0.42652368"),
      4 to BigDecimal("0.35710806"),
      5 to BigDecimal("0.31710806"),
      6 to BigDecimal("0.27672577"),
      7 to BigDecimal("0.23672577"),
      8 to BigDecimal("0.20137254"),
      9 to BigDecimal("0.17137254"),
      10 to BigDecimal("0.1317917"),
      11 to BigDecimal("0.0917917"),
      12 to BigDecimal("0.07472855"),
      13 to BigDecimal("0.04472855"),
      14 to BigDecimal("0.03768203"),
      15 to BigDecimal("0.02768203"),
      16 to BigDecimal("0.01525086"),
      17 to BigDecimal("0.01025086"),
      18 to BigDecimal("0.00525086"),
      19 to BigDecimal("0"),
    )

    runBlocking {
      service.writeCurrentEcpmGroupsThresholds(ecpmThresholds)
    }

    transaction(database) {
      FirstVideoRewardEcpmGroupsThresholdsTable
        .select {
          FirstVideoRewardEcpmGroupsThresholdsTable.forDay eq LocalDate.now()
        }
        .first()
        .let {
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.forDay]).isEqualTo(LocalDate.now())
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group0]).isEqualTo(BigDecimal("0.70469806"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group1]).isEqualTo(BigDecimal("0.60469806"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group2]).isEqualTo(BigDecimal("0.47652368"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group3]).isEqualTo(BigDecimal("0.42652368"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group4]).isEqualTo(BigDecimal("0.35710806"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group5]).isEqualTo(BigDecimal("0.31710806"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group6]).isEqualTo(BigDecimal("0.27672577"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group7]).isEqualTo(BigDecimal("0.23672577"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group8]).isEqualTo(BigDecimal("0.20137254"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group9]).isEqualTo(BigDecimal("0.17137254"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group10]).isEqualTo(BigDecimal("0.13179170"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group11]).isEqualTo(BigDecimal("0.09179170"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group12]).isEqualTo(BigDecimal("0.07472855"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group13]).isEqualTo(BigDecimal("0.04472855"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group14]).isEqualTo(BigDecimal("0.03768203"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group15]).isEqualTo(BigDecimal("0.02768203"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group16]).isEqualTo(BigDecimal("0.01525086"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group17]).isEqualTo(BigDecimal("0.01025086"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group18]).isEqualTo(BigDecimal("0.00525086"))
          assertThat(it[FirstVideoRewardEcpmGroupsThresholdsTable.group19]).isEqualTo(BigDecimal("0.00000000"))
        }
    }
  }

  @Test
  fun `SHOULD return a list of first rewards for users created for the last X days ON getFirstRewardsForUsersCreatedDuringPreviousDaysOrdered`() {
    val userIds =
      (1..10).map { database.prepareUser() } +
        (1..10).map { database.prepareUser(countryCode = "CA") } +
        (1..2).map { database.prepareUser(createdAt = Instant.now().minus(3, ChronoUnit.DAYS)) }
    val rewards = (1..22).map { Random().nextDouble().toBigDecimal().setScale(8, RoundingMode.FLOOR) }

    transaction(database) {
      UserFirstVideoRewardTable.batchInsert(userIds) { userId ->
        this[UserFirstVideoRewardTable.userId] = userId
        this[UserFirstVideoRewardTable.reward] = rewards[userIds.indexOf(userId)]
      }
    }

    runBlocking {
      service.getFirstRewardsForUsersCreatedDuringPreviousDaysOrdered(2)
    }.let {
      assertThat(it).isEqualTo(rewards.take(10).sortedDescending())
    }
  }

  @Test
  fun `SHOULD return last tracked set of ecpm groups thresholds ON getCurrentEcpmGroupsThresholds`() {
    transaction(database) {
      FirstVideoRewardEcpmGroupsThresholdsTable.deleteAll()
      FirstVideoRewardEcpmGroupsThresholdsTable.insert {
        it[forDay] = LocalDate.now().minusDays(2)
        it[group0] = BigDecimal("0.70469822")//22
        it[group1] = BigDecimal("0.70369822")
        it[group2] = BigDecimal("0.47652322")
        it[group3] = BigDecimal("0.47552322")
        it[group4] = BigDecimal("0.35710822")
        it[group5] = BigDecimal("0.35610822")
        it[group6] = BigDecimal("0.27672522")
        it[group7] = BigDecimal("0.27572522")
        it[group8] = BigDecimal("0.20137222")
        it[group9] = BigDecimal("0.20037222")
        it[group10] = BigDecimal("0.13179122")
        it[group11] = BigDecimal("0.13079122")
        it[group12] = BigDecimal("0.07472822")
        it[group13] = BigDecimal("0.07372822")
        it[group14] = BigDecimal("0.03768222")
        it[group15] = BigDecimal("0.03668222")
        it[group16] = BigDecimal("0.01525022")
        it[group17] = BigDecimal("0.01425022")
        it[group18] = BigDecimal("0.00100022")
        it[group19] = BigDecimal("0.00000022")
      }
      FirstVideoRewardEcpmGroupsThresholdsTable.insert {
        it[forDay] = LocalDate.now().minusDays(1)
        it[group0] = BigDecimal("0.70469811")//11
        it[group1] = BigDecimal("0.70369811")
        it[group2] = BigDecimal("0.47652311")
        it[group3] = BigDecimal("0.47552311")
        it[group4] = BigDecimal("0.35710811")
        it[group5] = BigDecimal("0.35610811")
        it[group6] = BigDecimal("0.27672511")
        it[group7] = BigDecimal("0.27572511")
        it[group8] = BigDecimal("0.20137211")
        it[group9] = BigDecimal("0.20037211")
        it[group10] = BigDecimal("0.13179111")
        it[group11] = BigDecimal("0.13079111")
        it[group12] = BigDecimal("0.07472811")
        it[group13] = BigDecimal("0.07372811")
        it[group14] = BigDecimal("0.03768211")
        it[group15] = BigDecimal("0.03668211")
        it[group16] = BigDecimal("0.01525011")
        it[group17] = BigDecimal("0.01425011")
        it[group18] = BigDecimal("0.00100011")
        it[group19] = BigDecimal("0.00000011")
      }
      FirstVideoRewardEcpmGroupsThresholdsTable.insert {
        it[forDay] = LocalDate.now()
        it[group0] = BigDecimal("0.70469800")//00
        it[group1] = BigDecimal("0.70369800")
        it[group2] = BigDecimal("0.47652300")
        it[group3] = BigDecimal("0.47552300")
        it[group4] = BigDecimal("0.35710800")
        it[group5] = BigDecimal("0.35610800")
        it[group6] = BigDecimal("0.27672500")
        it[group7] = BigDecimal("0.27572500")
        it[group8] = BigDecimal("0.20137200")
        it[group9] = BigDecimal("0.20037200")
        it[group10] = BigDecimal("0.13179100")
        it[group11] = BigDecimal("0.13079100")
        it[group12] = BigDecimal("0.07472800")
        it[group13] = BigDecimal("0.07372800")
        it[group14] = BigDecimal("0.03768200")
        it[group15] = BigDecimal("0.03668200")
        it[group16] = BigDecimal("0.01525000")
        it[group17] = BigDecimal("0.01425000")
        it[group18] = BigDecimal("0.00100000")
        it[group19] = BigDecimal("0.00000000")
      }
    }

    runBlocking {
      service.getCurrentEcpmGroupsThresholds()
    }.let {
      assertThat(it).isEqualTo(
        listOf(
          0 to BigDecimal("0.70469800"),
          1 to BigDecimal("0.70369800"),
          2 to BigDecimal("0.47652300"),
          3 to BigDecimal("0.47552300"),
          4 to BigDecimal("0.35710800"),
          5 to BigDecimal("0.35610800"),
          6 to BigDecimal("0.27672500"),
          7 to BigDecimal("0.27572500"),
          8 to BigDecimal("0.20137200"),
          9 to BigDecimal("0.20037200"),
          10 to BigDecimal("0.13179100"),
          11 to BigDecimal("0.13079100"),
          12 to BigDecimal("0.07472800"),
          13 to BigDecimal("0.07372800"),
          14 to BigDecimal("0.03768200"),
          15 to BigDecimal("0.03668200"),
          16 to BigDecimal("0.01525000"),
          17 to BigDecimal("0.01425000"),
          18 to BigDecimal("0.00100000"),
          19 to BigDecimal("0.00000000"),
        )
      )
    }
  }

  @Test
  fun `SHOULD write ecpm group once per user ON trackUserEcpmGroup`() {
    val userId = database.prepareUser()
    runBlocking {
      service.trackUserEcpmGroup(userId, 5)
      service.trackUserEcpmGroup(userId, 1)
    }

    transaction(database) {
      UserEcpmGroupTable
        .select { UserEcpmGroupTable.userId eq userId }
        .first()
        .let { assertThat(it[UserEcpmGroupTable.ecpmGroup]).isEqualTo(5) }
    }
  }

  @Test
  fun `SHOULD return tracked ecpm group ON getUserEcpmGroup if some`() {
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    transaction(database) {
      UserEcpmGroupTable
        .insert {
          it[UserEcpmGroupTable.userId] = userId
          it[ecpmGroup] = 7
        }
    }

    runBlocking {
      service.getUserEcpmGroup(userId)
    }.let { assertThat(it).isEqualTo(7) }
    runBlocking {
      service.getUserEcpmGroup(userId2)
    }.let { assertThat(it).isNull() }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD track jail break check result on trackJailBreakCheck`(jailBreak: Boolean) {
    val userId = database.prepareUser()

    runBlocking {
      service.jailBreakCheckPassed(userId)
    }.let { assertThat(it).isFalse() }

    runBlocking {
      service.trackJailBreakCheck(userId, jailBreak)
    }

    runBlocking {
      service.jailBreakCheckPassed(userId)
    }.let { assertThat(it).isEqualTo(!jailBreak) }
  }

  @Test
  fun `SHOULD track gps location check with a game ON`() {
    val userId = database.prepareUser()
    val gameId = database.prepareGame()
    val gameId2 = database.prepareGame()

    runBlocking {
      service.trackGpsLocationCheckAskedGame(userId, gameId, provided = true)
    }
    runBlocking {
      service.trackGpsLocationCheckAskedGame(userId, gameId2, provided = false)
    }

    runBlocking {
      service.getGpsLocationCheckAskedGames(userId)
    }.let { assertThat(it).containsExactlyInAnyOrder(gameId, gameId2) }
  }


  private fun generateTrackingData(
    trackingId: String = UUID.randomUUID().toString(),
    trackingType: TrackingDataType = IDFA,
    appPlatform: AppPlatform = ANDROID
  ): TrackingData = TrackingData(trackingId, trackingType, appPlatform)

  private fun verifyUserCacheInvalidationEffectWasPublished(userId: String) {
    verifyBlocking(messageBus) { publish(InvalidateUserCacheEffect(userId)) }
  }

  private fun isUserBanned(userId: String): Boolean =
    transaction(database) {
      val isBanned = UserTable
        .slice(UserTable.isBanned)
        .select { UserTable.id eq userId }
        .firstOrNull()
        ?.let { row -> row[UserTable.isBanned] }
        ?: throw UserRecordNotFoundException(userId)

      if (isBanned) {
        val isWhitelisted = UserWhitelistTable.select { UserWhitelistTable.userId eq userId }.firstOrNull() != null
        !isWhitelisted
      } else {
        false
      }
    }

}

fun Database.addSupportedCountry(countryCode: String, tierId: Int) =
  transaction(this) {
    CountryLimitsTierTable
      .insertOrUpdate(CountryLimitsTierTable.tierId) {
        it[CountryLimitsTierTable.countryCode] = countryCode
        it[CountryLimitsTierTable.tierId] = tierId
      }
  }

fun Database.addCountryTier(maxCashoutAmountPercentage: BigDecimal, dailyEarningsQuotasStr: String): Int =
  transaction(this) {
    val id = (LimitTierTable.slice(LimitTierTable.id.max()).selectAll().map { it.getOrNull(LimitTierTable.id.max()) }.firstOrNull() ?: 0) + 1
    LimitTierTable
      .insert {
        it[LimitTierTable.id] = id
        it[dailyEarningsQuotas] = dailyEarningsQuotasStr
        it[LimitTierTable.maxCashoutAmountPercentage] = maxCashoutAmountPercentage
      }
    return@transaction id
  }


fun Database.addRevenueByPeriods(userId: String, gameId: Int, periodStart: Instant) =
  transaction(this) {
    UserApplovinRevenueByPeriodsTable.insert {
      it[UserApplovinRevenueByPeriodsTable.userId] = userId
      it[UserApplovinRevenueByPeriodsTable.gameId] = gameId
      it[UserApplovinRevenueByPeriodsTable.periodStart] = periodStart
      it[revenue] = BigDecimal.ONE
      it[revenueTransactionsCount] = 1
    }
  }

fun Database.addCurrentGenericRevenue(userId: String, gameId: Int, periodStart: Instant, source: RevenueSource) =
  transaction(this) {
    CurrentGenericRevenueTable.insert {
      it[id] = UUID.randomUUID().toString()
      it[CurrentGenericRevenueTable.userId] = userId
      it[CurrentGenericRevenueTable.gameId] = gameId
      it[timestamp] = periodStart
      it[revenueAmount] = BigDecimal.ONE
      it[revenueSource] = source.name
      it[networkId] = -1
    }
  }

fun Database.addGameCoinsByPeriods(userId: String, gameId: Int, periodStart: Instant) =
  this.addGameCoinsByPeriods(userId, gameId, periodStart, null)

fun Database.addGameCoinsByPeriods(userId: String, gameId: Int, periodStart: Instant, coinsAmount: Int?) =
  transaction(this) {
    UserGameCoinsBalanceByPeriodsTable.insert {
      it[UserGameCoinsBalanceByPeriodsTable.userId] = userId
      it[UserGameCoinsBalanceByPeriodsTable.gameId] = gameId
      it[UserGameCoinsBalanceByPeriodsTable.periodStart] = periodStart
      it[coins] = coinsAmount ?: 1
      it[coinsTransactionsCount] = 1
    }
  }

fun Database.prepareNotificationTrack(userId: String, type: NotificationType, notifyAt: Instant = Instant.now()) =
  transaction(this) {
    UserLastNotificationTable.insertIgnore {
      it[UserLastNotificationTable.userId] = userId
      it[notificationType] = type.name
      it[notifiedAt] = notifyAt
    }
  }

/**
 * Creates mock user and return user's id
 */
fun Database.prepareUser(
  userId: String = UUID.randomUUID().toString(),
  googleAdId: String? = null,
  isBanned: Boolean = false,
  isDeleted: Boolean = false,
  deviceToken: String? = null,
  createdAt: Instant? = null,
  countryCode: String = "US",
  lastActiveDay: LocalDate? = LocalDate.now(),
  appVersion: Int = 0,
  createdAtDay: LocalDate = LocalDate.now(),
  trackingData: TrackingData? = null,
  appPlatform: AppPlatform = ANDROID,
): String {
  transaction(this) {
    UserTable.insert {
      it[id] = userId
      it[UserTable.googleAdId] = googleAdId
      it[UserTable.countryCode] = countryCode
      it[UserTable.isBanned] = isBanned
      it[UserTable.isDeleted] = isDeleted
      it[UserTable.deviceToken] = deviceToken
      it[UserTable.createdAt] = createdAt ?: Instant.now()
      it[lastActiveAtDay] = lastActiveDay
      it[UserTable.appVersion] = appVersion
      it[UserTable.createdAtDay] = createdAtDay
      it[currentTrackingId] = trackingData?.id
      it[currentTrackingType] = trackingData?.type?.name
      it[UserTable.appPlatform] = trackingData?.platform?.name ?: appPlatform.name
    }
    UserFraudScoreTable.insert {
      it[UserFraudScoreTable.userId] = userId
      it[score] = 0.0
    }
  }
  return userId
}

fun Database.addUserEm2TotalCoinsByGameId(
  userId: String,
  gameId: Int,
  coinsAmount: BigDecimal,
  lastPlayedAt: Instant = Instant.now(),
  firstPlayedAt: Instant = Instant.now()
) =
  transaction(this) {
    UserGameBalanceTotalsEm2Table.insert {
      it[UserGameBalanceTotalsEm2Table.userId] = userId
      it[UserGameBalanceTotalsEm2Table.gameId] = gameId
      it[coins] = coinsAmount
      it[calculatedCoins] = 10
      it[blockedCoins] = 0
      it[UserGameBalanceTotalsEm2Table.firstPlayedAt] = firstPlayedAt
      it[UserGameBalanceTotalsEm2Table.lastPlayedAt] = lastPlayedAt
    }
  }

fun Database.addUserTotalCoinsByGameId(userId: String, gameId: Int, coinsAmount: Int, lastPlayedAt: Instant = Instant.now()) =
  transaction(this) {
    UserGameBalanceTotalsTable.insert {
      it[UserGameBalanceTotalsTable.userId] = userId
      it[UserGameBalanceTotalsTable.gameId] = gameId
      it[coins] = coinsAmount
      it[UserGameBalanceTotalsTable.lastPlayedAt] = lastPlayedAt
    }
  }

fun Database.addUserTotalEm2CoinsByGameId(userId: String, gameId: Int, coinsAmount: BigDecimal, lastPlayedAt: Instant = Instant.now()) =
  transaction(this) {
    UserGameBalanceTotalsEm2Table.insert {
      it[UserGameBalanceTotalsEm2Table.userId] = userId
      it[UserGameBalanceTotalsEm2Table.gameId] = gameId
      it[coins] = coinsAmount
      it[calculatedCoins] = 1
      it[blockedCoins] = 0
      it[UserGameBalanceTotalsEm2Table.lastPlayedAt] = lastPlayedAt
    }
  }

fun Database.addUserFirstPlayedByGameId(userId: String, gameId: Int, firstPlayedAt: Instant) =
  transaction(this) {
    UserGameFirstPlayedTable.insert {
      it[UserGameFirstPlayedTable.userId] = userId
      it[UserGameFirstPlayedTable.gameId] = gameId
      it[UserGameFirstPlayedTable.firstPlayedAt] = firstPlayedAt
    }
  }

fun Database.addUserToLimitedTracking(userId: String) =
  transaction(this) {
    UserLimitedTrackingTable.insert {
      it[UserLimitedTrackingTable.userId] = userId
    }
  }

fun Database.trackLowEarningsCashoutPeriod(userId: String, periodEnd: Instant) =
  transaction(this) {
    UserLastLowEarningsCashoutPeriodTable.insert {
      it[UserLastLowEarningsCashoutPeriodTable.userId] = userId
      it[UserLastLowEarningsCashoutPeriodTable.periodEnd] = periodEnd
    }
  }

fun Database.updateGoogleAdId(userId: String, googleAdId: String) =
  transaction(this) {
    UserTable.update({ UserTable.id eq userId }) {
      it[UserTable.googleAdId] = googleAdId
    }

    UserGoogleAdIdsTable.insert {
      it[UserGoogleAdIdsTable.userId] = userId
      it[UserGoogleAdIdsTable.googleAdId] = googleAdId
    }
  }

fun Database.addAdjustId(userId: String, adjustId: String) {
  transaction(this) {
    UserAdjustIdTable.insert {
      it[UserAdjustIdTable.userId] = userId
      it[UserAdjustIdTable.adjustId] = adjustId
    }
  }
}