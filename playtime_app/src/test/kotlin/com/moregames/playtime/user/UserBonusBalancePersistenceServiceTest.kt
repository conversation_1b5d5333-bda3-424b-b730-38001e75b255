package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import assertk.assertions.isEqualTo
import assertk.assertions.isFalse
import assertk.assertions.isTrue
import com.justplayapps.service.rewarding.bonus.UserBonusBalancePersistenceService
import com.justplayapps.service.rewarding.bonus.UserBonusesBalanceTable
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.user.UserBonusBalanceType
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertNull

@ExtendWith(DatabaseExtension::class)
internal class UserBonusBalancePersistenceServiceTest(
  private val database: Database,
) {

  private lateinit var service: UserBonusBalancePersistenceService

  @BeforeEach
  fun before() {
    service = UserBonusBalancePersistenceService(database)
  }

  @Test
  fun `SHOULD add bonus coins to user ON addBonusCoins`() {
    val userId = database.prepareUser()

    runBlocking {
      service.addBonusCoins(userId, 100.toBigDecimal(), UserBonusBalanceType.AB_COIN_REWARD_AFTER_X_MINUTES)
      service.addBonusCoins(userId, 100.toBigDecimal(), UserBonusBalanceType.AB_COIN_REWARD_AFTER_X_MINUTES)
    }

    val recordCount = transaction(database) {
      UserBonusesBalanceTable
        .select {
          (UserBonusesBalanceTable.userId eq userId) and
            (UserBonusesBalanceTable.bonusType eq "AB_COIN_REWARD_AFTER_X_MINUTES")
        }.count()
    }

    assertThat(recordCount).isEqualTo(1)
  }

  @Test
  fun `SHOULD return false ON addBonusCoins WHEN user already have this bonus`() {
    val userId = database.prepareUser()

    val result = runBlocking {
      service.addBonusCoins(userId, 100.toBigDecimal(), UserBonusBalanceType.AB_COIN_REWARD_AFTER_X_MINUTES)
      service.addBonusCoins(userId, 100.toBigDecimal(), UserBonusBalanceType.AB_COIN_REWARD_AFTER_X_MINUTES)
    }

    assertThat(result).isFalse()
  }

  @Test
  fun `SHOULD return null for user ON findLastWelcomeCoinsDate WHEN no welcome coins`() {
    val userId = database.prepareUser()

    runBlocking {
      service.findLastWelcomeCoinsDate(userId)
    }.let {
      assertNull(it)
    }
  }

  @Test
  fun `SHOULD return last welcome coins for user ON findLastWelcomeCoinsDate`() {
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)

    transaction(database) {
      //too old
      UserBonusesBalanceTable.insert {
        it[UserBonusesBalanceTable.userId] = userId
        it[coins] = 100
        it[bonusType] = UserBonusBalanceType.WELCOME_COINS.name
        it[uniqueBonusKey] = UUID.randomUUID().toString()
        it[createdAt] = now.minusSeconds(24 * 60 * 60L)
      }
      //expected
      UserBonusesBalanceTable.insert {
        it[UserBonusesBalanceTable.userId] = userId
        it[coins] = 100
        it[bonusType] = UserBonusBalanceType.WELCOME_COINS.name
        it[uniqueBonusKey] = UUID.randomUUID().toString()
        it[createdAt] = now.minusSeconds(60 * 60L)
      }
      //fresh, but belong to another user
      UserBonusesBalanceTable.insert {
        it[UserBonusesBalanceTable.userId] = userId2
        it[coins] = 100
        it[bonusType] = UserBonusBalanceType.WELCOME_COINS.name
        it[uniqueBonusKey] = UUID.randomUUID().toString()
        it[createdAt] = now.minusSeconds(15L)
      }
    }

    runBlocking {
      service.findLastWelcomeCoinsDate(userId)
    }.let {
      assertEquals(now.minusSeconds(60 * 60L), it)
    }
  }

  @Test
  fun `SHOULD respond correctly ON hasBonusFor`() {
    val userId = database.prepareUser()
    runBlocking {
      service.addBonusCoins(userId, 1.toBigDecimal(), UserBonusBalanceType.COINS_AFTER_CASHOUT, userId)
      service.hasBonusFor(userId, UserBonusBalanceType.COINS_AFTER_CASHOUT)
        .let { assertThat(it).isTrue() }
      service.hasBonusFor(userId, UserBonusBalanceType.COINS_AFTER_CASHOUT, userId)
        .let { assertThat(it).isTrue() }
      service.hasBonusFor(userId, UserBonusBalanceType.WELCOME_COINS, userId)
        .let { assertThat(it).isFalse() }
    }
  }

  @Test
  fun `TEST bonus bank balance`() {
    val userId = database.prepareUser()
    runBlocking {
      service.getBonusBankUnpaidCoins(userId).let { assertThat(it).isEqualByComparingTo(0.toBigDecimal()) }

      service.addBonusCoins(userId, 5.toBigDecimal(), UserBonusBalanceType.COINS_MILESTONE, "abc")
      service.addBonusCoins(userId, 6.toBigDecimal(), UserBonusBalanceType.BONUS_BANK_REWARD, "fgh")
      service.addBonusCoins(userId, 7.toBigDecimal(), UserBonusBalanceType.BONUS_BANK_REWARD, "hjk")

      service.getBonusBankUnpaidCoins(userId).let { assertThat(it).isEqualByComparingTo(13.toBigDecimal()) }

      service.setCashoutTransactionForBonusBankCoins(userId, "transactionId")
      service.getBonusBankUnpaidCoins(userId).let { assertThat(it).isEqualByComparingTo(0.toBigDecimal()) }

      service.resetCashoutTransactionForBonusBankCoins(userId, "transactionId")
      service.getBonusBankUnpaidCoins(userId).let { assertThat(it).isEqualByComparingTo(13.toBigDecimal()) }
    }
  }
}