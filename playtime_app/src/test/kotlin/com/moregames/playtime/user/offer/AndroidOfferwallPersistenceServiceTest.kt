package com.moregames.playtime.user.offer

import assertk.assertThat
import assertk.assertions.*
import com.moregames.base.app.OfferWallType
import com.moregames.base.app.OfferWallType.*
import com.moregames.base.offers.ConfigMissingException
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.util.TimeService
import com.moregames.playtime.notifications.table.PromotionsEmailSendingTable
import com.moregames.playtime.notifications.table.PromotionsPushNotificationTable
import com.moregames.playtime.user.prepareUser
import com.moregames.playtime.user.table.UserOfferwallTypeTable
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.deleteAll
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.test.assertFailsWith

@ExtendWith(DatabaseExtension::class)
class AndroidOfferwallPersistenceServiceTest(
  private val database: Database
) {

  private val timeService: TimeService = mock()

  private val underTest: AndroidOfferwallPersistenceService = AndroidOfferwallPersistenceService(database, timeService)

  private val cfgOfferwallRule =
    OfferwallConfig.Promotion(
      id = 1,
      name = "name",
      description = "Offerwall Rule",
      buttonText = "Play and Earn Rule",
      imageFilename = "offerwall_rule.jpg",
      iconFilename = "dunk_offer_icon_rule.jpg",
      orderKey = 1,
      backGroundColor = "#FF0000",
      isHighlighted = true
    )

  private val now = Instant.now()

  @BeforeEach
  fun before() {
    transaction(database) {
      CfgOfferwallTable.deleteAll()
    }
    whenever(timeService.now()).thenReturn(now)
  }

  @Test
  fun `SHOULD load default offerwall config ON loadDefaultOfferWallConfig`() {
    transaction(database) {
      CfgOfferwallTable.insert {
        it[id] = 1
        it[name] = "Offerwall"
        it[description] = "Offerwall"
        it[buttonText] = "Play and Earn"
        it[imageFilename] = "offerwall.jpg"
        it[iconFilename] = "dunk_offer_icon.jpg"
        it[orderKey] = 1
        it[backGroundColor] = "#FF0000"
      }
    }
    val expectedCfg = OfferwallConfig.Regular(
      id = 1,
      name = "Offerwall",
      description = "Offerwall",
      buttonText = "Play and Earn",
      imageFilename = "offerwall.jpg",
      iconFilename = "dunk_offer_icon.jpg",
      orderKey = 1,
      backGroundColor = "#FF0000",
    )

    val cfg = runBlocking {
      underTest.loadDefaultOfferWallConfig()
    }
    assertThat(cfg).isEqualToIgnoringGivenProperties(expectedCfg, OfferwallConfig::id)

  }

  @Test
  fun `SHOULD throw exception ON loadDefaultOfferWallConfig when there is no config`() {
    assertFailsWith(ConfigMissingException::class) {
      runBlocking {
        underTest.loadDefaultOfferWallConfig()
      }
    }
  }

  @Test
  fun `SHOULD load offerwall config ON loadOfferWallConfig WHEN default config requested`() {
    transaction(database) {
      CfgOfferwallTable.insert {
        it[id] = 1
        it[name] = "Offerwall"
        it[description] = "Offerwall"
        it[buttonText] = "Play and Earn"
        it[imageFilename] = "offerwall.jpg"
        it[iconFilename] = "dunk_offer_icon.jpg"
        it[orderKey] = 1
        it[backGroundColor] = "#FF0000"
      }
    }
    val expectedCfg = OfferwallConfig.Regular(
      id = 1,
      name = "Offerwall",
      description = "Offerwall",
      buttonText = "Play and Earn",
      imageFilename = "offerwall.jpg",
      iconFilename = "dunk_offer_icon.jpg",
      orderKey = 1,
      backGroundColor = "#FF0000",
    )

    val cfg = runBlocking {
      underTest.loadOfferWallConfig(offerWallType = null)
    }
    assertThat(cfg).isNotNull().isEqualToIgnoringGivenProperties(expectedCfg, OfferwallConfig::id)
  }

  @Test
  fun `SHOULD load offerwall config ON loadOfferWallConfig WHEN typed config requested`() {
    transaction(database) {
      CfgOfferwallTable.insert {
        it[id] = 1
        it[name] = "Offerwall"
        it[description] = "Offerwall"
        it[buttonText] = "Play and Earn"
        it[imageFilename] = "offerwall.jpg"
        it[iconFilename] = "dunk_offer_icon.jpg"
        it[orderKey] = 1
        it[backGroundColor] = "#FF0000"
        it[offerWallType] = TAPJOY
      }
    }
    val expectedCfg = OfferwallConfig.Regular(
      id = 1,
      name = "Offerwall",
      description = "Offerwall",
      buttonText = "Play and Earn",
      imageFilename = "offerwall.jpg",
      iconFilename = "dunk_offer_icon.jpg",
      orderKey = 1,
      backGroundColor = "#FF0000",
    )

    val cfg = runBlocking {
      underTest.loadOfferWallConfig(offerWallType = TAPJOY)
    }
    assertThat(cfg).isNotNull().isEqualToIgnoringGivenProperties(expectedCfg, OfferwallConfig::id)
  }

  @Test
  fun `SHOULD return null ON loadOfferWallConfig WHEN NOT config defined`() {
    val cfg = runBlocking {
      underTest.loadOfferWallConfig(offerWallType = TAPJOY)
    }
    assertThat(cfg).isNull()
  }

  @ParameterizedTest
  @EnumSource(OfferWallType::class, names = ["TAPJOY", "FYBER"])
  fun `SHOULD return no rows from CfgOfferwallTable ON loadOfferwallConfigRule`(offerWallType: OfferWallType) {
    prepareInactiveCfgOfferwallRules(offerWallType)

    runBlocking { underTest.loadOfferwallPromotionsConfigOrNull(offerWallType) }
      .let {
        assertThat(it).isNull()
      }
  }

  @ParameterizedTest
  @EnumSource(OfferWallType::class, names = ["TAPJOY", "FYBER", "ADJOE"])
  fun `SHOULD return offerwall config ON loadOfferwallConfigRule`(offerWallType: OfferWallType) {
    prepareInactiveCfgOfferwallRules(TAPJOY)
    prepareInactiveCfgOfferwallRules(FYBER)

    val ruleName = UUID.randomUUID().toString().take(30)

    val expectedCfg = cfgOfferwallRule.copy(name = ruleName + offerWallType.name.take(1))

    transaction(database) {
      insertCfgOfferwallRule(ruleName + "T", now.minusSeconds(100), now.plusSeconds(100), TAPJOY)
      insertCfgOfferwallRule(ruleName + "F", now.minusSeconds(100), now.plusSeconds(100), FYBER)
    }

    runBlocking { underTest.loadOfferwallPromotionsConfigOrNull(offerWallType) }
      .let {
        if (offerWallType == ADJOE)
          assertThat(it).isNull()
        else
          assertThat(it).isNotNull().isInstanceOf(OfferwallConfig.Promotion::class).isEqualToIgnoringGivenProperties(expectedCfg, OfferwallConfig.Promotion::id)
      }
  }

  @Test
  fun `SHOULD return offerwall config ON loadOfferwallConfigRule WHEN open-ended`() {
    prepareInactiveCfgOfferwallRules(TAPJOY)

    val ruleName = UUID.randomUUID().toString().take(30)

    val expectedCfg = cfgOfferwallRule.copy(name = ruleName)

    transaction(database) {
      insertCfgOfferwallRule(ruleName, now.minusSeconds(100), null, TAPJOY)
    }

    runBlocking { underTest.loadOfferwallPromotionsConfigOrNull(TAPJOY) }!!
      .let {
        assertThat(it).isInstanceOf(OfferwallConfig.Promotion::class).isEqualToIgnoringGivenProperties(expectedCfg, OfferwallConfig.Promotion::id)
      }
  }

  @Test
  fun `SHOULD return no offerwall config ON loadOfferwallConfigRule WHEN we have active tapjoy promotions but no fyber promotions set`() {
    prepareInactiveCfgOfferwallRules(TAPJOY)
    prepareInactiveCfgOfferwallRules(FYBER)

    val ruleName = UUID.randomUUID().toString().take(30)

    transaction(database) {
      insertCfgOfferwallRule(ruleName, now.minusSeconds(100), now.plusSeconds(100), TAPJOY)
    }

    runBlocking { underTest.loadOfferwallPromotionsConfigOrNull(FYBER) }
      .let {
        assertThat(it).isNull()
      }
  }

  @Test
  fun `SHOULD track offerwall types ON trackOfferwallTypes`() {
    val userId = database.prepareUser()
    val types = setOf(FYBER, ADJOE)

    runBlocking {
      underTest.trackOfferwallTypesReturnResult(userId, types)
    }.let { assertThat(it).isTrue() }
    transaction(database) {
      UserOfferwallTypeTable
        .select { UserOfferwallTypeTable.userId eq userId }
        .map { OfferWallType.valueOf(it[UserOfferwallTypeTable.type]) }
    }.let { assertThat(it.toSet()).isEqualTo(types) }
  }

  @Test
  fun `SHOULD not track offerwall types ON trackOfferwallTypes WHEN it was already tracked`() {
    val userId = database.prepareUser()
    val types = setOf(FYBER)

    runBlocking {
      underTest.trackOfferwallTypesReturnResult(userId, types)
    }
    runBlocking {
      underTest.trackOfferwallTypesReturnResult(userId, types + TAPJOY)
    }.let { assertThat(it).isFalse() }
    transaction(database) {
      UserOfferwallTypeTable
        .select { UserOfferwallTypeTable.userId eq userId }
        .map { OfferWallType.valueOf(it[UserOfferwallTypeTable.type]) }
    }.let { assertThat(it.toSet()).isEqualTo(types) }
  }

  private fun prepareInactiveCfgOfferwallRules(offerWallType: OfferWallType) {
    transaction(database) {
      PromotionsPushNotificationTable.deleteAll()
      PromotionsEmailSendingTable.deleteAll()
      CfgOfferwallRuleTable.deleteAll()

      listOf(
        now.minus(10, ChronoUnit.DAYS) to now.minus(5, ChronoUnit.DAYS),
        now.plus(5, ChronoUnit.DAYS) to now.plus(10, ChronoUnit.DAYS),
        now.plus(7, ChronoUnit.DAYS) to null,
      ).forEach { (from, to) ->
        insertCfgOfferwallRule("Rule ${from.epochSecond} to ${to?.epochSecond}", from, to, offerWallType)
      }
    }
  }

  private fun insertCfgOfferwallRule(name: String, from: Instant, to: Instant?, offerWallType: OfferWallType) =
    CfgOfferwallRuleTable.insert {
      it[CfgOfferwallRuleTable.name] = name
      it[description] = "Offerwall Rule"
      it[buttonText] = "Play and Earn Rule"
      it[imageFilename] = "offerwall_rule.jpg"
      it[iconFilename] = "dunk_offer_icon_rule.jpg"
      it[orderKey] = 1
      it[backGroundColor] = "#FF0000"
      it[dateFrom] = from
      it[dateUntil] = to
      it[offerwallType] = offerWallType
      it[isHighlighted] = true
    }
}
