package com.moregames.playtime.user.cashout.dto

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.config.CashoutConfig
import com.moregames.playtime.util.roundDownToSecondDigit
import com.moregames.playtime.utils.CAD
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.Instant

class CashoutStatusTest {

  @Test
  fun `SHOULD return expected content ON disabled constructor`() {
    val someWhen = Instant.now()
    val cashOutConfig = CashoutConfig(
      headerTextCashoutAvailable = "cashOut available",
      headerTextCashoutUnavailable = "cashOut {{coins}}unavailable",
      headerTextCashoutWithPeriod = "During the last {{period_duration_description}} earned",
      iconFilenameCashoutAvailable = "icon1",
      iconFilenameCashoutUnavailable = "icon2",
      cashoutMinutesAfterMidnightUtc = 60,
      lastPossibleUserCreationMinutesBeforeMidnightUtc = 60,
      fyberCoinsToUsdConversionRatio = BigDecimal(0.5),
      userEarningsCalculationFactor = BigDecimal(0.8),
      disclaimer = "disclaimer",
      ironSourceCoinsToUsdConversionRatio = BigDecimal("220"),
      maxCashoutAmount = BigDecimal("25"),
      tapjoyCoinsToUsdConversionRatio = BigDecimal("220"),
      offerwallCoinsToUsdConversionRatio = BigDecimal("220")
    )

    val expected = CashoutStatus(
      isEnabled = false,
      headerText = "cashOut {{coins}}unavailable",
      iconFilename = "icon2",
      nextCashout = someWhen,
      currency = CAD,
      amount = BigDecimal.ZERO.roundDownToSecondDigit(),
      bonusAmount = null,
      amountBefore = null,
      amountUsd = BigDecimal.ZERO.roundDownToSecondDigit(),
      providers = emptyList(),
      disclaimer = "disclaimer",
      userHasCashouts = true,
    )

    val actual = CashoutStatus.disabled(cashOutConfig, CAD, nextCashout = someWhen, userHasCashouts = true)

    assertThat(actual).isEqualTo(expected)
  }

}