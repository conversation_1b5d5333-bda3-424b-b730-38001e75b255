package com.moregames.playtime.user.iosgameattconsent

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment.IOS_GAMES_ATT_CONSENT
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.util.mock
import com.moregames.playtime.utils.iosGameAttConsentConfigApiDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import kotlin.test.assertEquals

class IosGameAttConsentServiceTest {
  private val abTestingService: AbTestingService = mock {
    onBlocking { assignedVariationValue(any(), eq(IOS_GAMES_ATT_CONSENT), any()) } doReturn DEFAULT
  }
  private val underTest = IosGameAttConsentService(abTestingService)

  @Test
  fun `SHOULD return configuration ON getConsentConfiguration WHEN user is participant`() {
    abTestingService.mock({ assignedVariationValue(any(), eq(IOS_GAMES_ATT_CONSENT), any()) }, Variations.CONSENT_VARIANT_1)

    runBlocking {
      underTest.getConsentConfiguration("userIdValue")
    }.let {
      assertEquals(iosGameAttConsentConfigApiDtoStub, it)
    }
  }

  @Test
  fun `SHOULD return empty configuration ON getConsentConfiguration WHEN user is not participant`() {
    runBlocking {
      underTest.getConsentConfiguration("userIdValue")
    }.let {
      assertEquals(iosGameAttConsentConfigApiDtoStub.copy(configurationId = null), it)
    }
  }
}