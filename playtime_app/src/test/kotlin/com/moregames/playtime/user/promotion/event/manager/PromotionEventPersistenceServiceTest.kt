package com.moregames.playtime.user.promotion.event.manager

import assertk.assertThat
import assertk.assertions.*
import ch.qos.logback.classic.Level
import ch.qos.logback.classic.Logger
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.Variations
import com.moregames.base.messaging.customnotification.ButtonAction
import com.moregames.base.messaging.customnotification.ButtonActionName
import com.moregames.base.messaging.customnotification.OnClickActionApiDto
import com.moregames.base.messaging.customnotification.OnClickActionName
import com.moregames.base.table.DatabaseExtension
import com.moregames.playtime.user.promotion.event.manager.api.client.BadgeConfigApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.CountDownInfoSectionDto
import com.moregames.playtime.user.promotion.event.manager.api.client.OfferModifierConfigApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.TopConfigApiDto
import com.moregames.playtime.user.promotion.event.manager.dto.CountDownBannersDto
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEvent
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEventType
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEventUiConfiguration
import com.moregames.playtime.user.promotion.event.manager.ftue.FtuePromoId
import com.moregames.playtime.user.promotion.event.manager.table.PromotionEventCfgTable
import com.moregames.playtime.user.promotion.event.manager.table.PromotionEventCfgTable.useUserTimeZone
import com.moregames.playtime.util.defaultJsonConverter
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.decodeFromString
import org.jetbrains.exposed.exceptions.ExposedSQLException
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.slf4j.LoggerFactory
import java.time.Instant

@ExtendWith(DatabaseExtension::class)
class PromotionEventPersistenceServiceTest(
  private val database: Database,
) {

  companion object {
    val promotionEventUiConfig = PromotionEventUiConfiguration(
      top = TopConfigApiDto(
        backgroundImage = "Christmas_back.jpg",
        foregroundImage = "Christmas_fore.png",
        gradientTop = "#FF0000",
        gradientBottom = "FF0033",
        cashoutButtonColor = "#FF0057",
        durationInMillis = 3000
      ),
      mainTop = listOf(
        TopConfigApiDto(
          backgroundImage = "Christmas_back.jpg",
          imageClickAction = OnClickActionApiDto(
            name = OnClickActionName.ROUTE_TO_MAIN,
            parameters = listOf("p1", "p2")
          ),
          foregroundImage = "Christmas_fore.png",
          gradientTop = "#FF0000",
          gradientBottom = "FF0033",
          cashoutButtonColor = "#FF0057",
          durationInMillis = 3000,
          order = 1
        )
      ),
      challengesTop = listOf(
        TopConfigApiDto(
          backgroundImage = "Christmas_back.jpg",
          foregroundImage = "Christmas_fore.png",
          gradientTop = "#FF0000",
          gradientBottom = "FF0033",
          cashoutButtonColor = "#FF0057",
          durationInMillis = 3000,
          order = 1
        )
      ),
      offerModifier = listOf(
        OfferModifierConfigApiDto(
          offerId = "2000089",
          offerImage = "treasure_master_override.jpg",
          badge = BadgeConfigApiDto(
            color = "#FFFF00",
            displaySpecialBadge = true,
            text = "<strikethrough>$5 daily</strikethrough><br><font color=\"0x00FF00\">Now!</font><br>"
          ),
          highlight = null,
        )
      ),
      countDownBanners = CountDownBannersDto(
        startPosition = 1,
        step = 3,
        max = 2,
        title = "Banner title",
        backgroundImage = "background_image.jpg",
        infoImages = listOf("image1.jgp", "image2.jpg"),
        infoTitle = "Info title",
        infoSections = listOf(
          CountDownInfoSectionDto(
            subTitle = "SubTitle",
            subText = "SubText",
          )
        ),
        infoButtonClickAction = ButtonAction(
          name = ButtonActionName.OPEN_FIRST_FOUND_OFFERWALL,
          parameters = listOf("parameter1", "parameter2")
        ),
        infoButtonText = "Info button text"
      )
    )
  }

  private lateinit var service: PromotionEventPersistenceService

  @BeforeEach
  fun before() {
    service = PromotionEventPersistenceService(database)
  }

  @AfterEach
  fun after() {
    transaction(database) {
      PromotionEventCfgTable.deleteWhere {
        PromotionEventCfgTable.id like "TEST_%"
      }
    }
  }

  @ParameterizedTest
  @EnumSource(FtuePromoId::class)
  fun `SHOUD exist promotions for FTUE`(promoId: FtuePromoId) = runTest {
    val result = service.findByKey(promoId.name)
    assertThat(result).isNotNull()
    val json = defaultJsonConverter
    val uiConfig = json.decodeFromString<PromotionEventUiConfiguration>(result?.uiConfiguration!!)
    assertThat(uiConfig).isNotNull()
  }

  @Test
  fun `SHOULD find promotion by key WHEN there is promotion by key`() {
    prepareEvent(id = "TEST_1")
    prepareEvent(id = "TEST_2")
    val result = runBlocking {  service.findByKey("TEST_2") }
    assertThat(result).isNotNull()
    assertThat(result).isEqualTo(
      PromotionEvent(
        id = "TEST_2",
        dateFrom = Instant.parse("2024-01-01T00:00:00Z"),
        dateTo =  Instant.parse("2024-01-03T00:00:00Z"),
        uiConfiguration = "{}",
        useUserTimeZone = false,
        experimentKey = "aaTest",
        variationKey = "groupA",
        priorityKey = 0,
        eventType = PromotionEventType.GLOBAL,
      )
    )
  }

  @Test
  fun `SHOULD return null ON promotion by key WHEN there is no promotion by key`() {
    prepareEvent(id = "TEST_1")
    prepareEvent(id = "TEST_2")
    val result = runBlocking {  service.findByKey("TEST_3") }
    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD save promotion by savePromotion`() {
    val id = "TEST_PROMOTION"
    val dateFrom = Instant.parse("2025-12-03T10:15:30.00Z")
    val dateTo = Instant.parse("2025-12-05T10:15:30.00Z")
    val experimentKey = ClientExperiment.AA_TEST.key
    val variationKey = Variations.GROUP_A.variationKey
    val priorityKey = 10
    val eventType = PromotionEventType.GLOBAL
    //language=json
    val cfg = """
      {"backgroundImage": "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png"}
    """.trimIndent()
    runBlocking { service.savePromotion(PromotionEvent(id, dateFrom, dateTo, false, cfg, experimentKey, variationKey, priorityKey, eventType)) }
    assertEvent(id, dateFrom, dateTo, cfg, experimentKey, variationKey, priorityKey)

    //language=json
    val cfg2 = """
      {"foregroundImage": "https://storage.googleapis.com/public-playtime/images/offer_haloween_foregrounground.png"}
    """.trimIndent()
    runBlocking {
      service.savePromotion(
        PromotionEvent(
          id = id,
          dateFrom = dateFrom.plusSeconds(100),
          dateTo = dateTo.plusSeconds(100),
          useUserTimeZone = false,
          uiConfiguration = cfg2,
          experimentKey = ClientExperiment.ANDROID_GAMES_ORDER.key,
          variationKey = Variations.ANDROID_FIXED_FIRST_11_ORDER.variationKey,
          priorityKey = priorityKey + 1,
          eventType = eventType,
        )
      )
    }
    assertEvent(
      id = id,
      dateFrom = dateFrom.plusSeconds(100),
      dateTo = dateTo.plusSeconds(100),
      cfg = cfg2,
      experimentKey = ClientExperiment.ANDROID_GAMES_ORDER.key,
      variationKey = Variations.ANDROID_FIXED_FIRST_11_ORDER.variationKey,
      priorityKey = priorityKey + 1
    )
  }

  private fun prepareEvents() {
    val cfg = javaClass.getResource("/user/promotion/promotionConfigPersistence.json")!!.readText()
    prepareEvent(id = "TEST_1")
    prepareEvent(id = "TEST_2", dateTo = Instant.parse("2024-05-06T00:00:00Z"))
    prepareEvent(id = "TEST_3", dateTo = Instant.parse("2024-08-06T00:00:00Z"), useUserTimeZone = true, cfg = cfg)
    prepareEvent(id = "TEST_4", dateTo = Instant.parse("2024-09-06T00:00:00Z"))
    prepareEvent(id = "TEST_5", dateTo = Instant.parse("2024-09-06T00:00:00Z"), dateFrom = Instant.parse("2024-09-02T00:00:00Z"))
    prepareEvent(id = "TEST_6", dateTo = Instant.parse("2024-09-06T00:00:00Z"), enabled = false, useUserTimeZone = true)
    prepareEvent(id = "TEST_7", dateTo = Instant.parse("2024-09-06T00:00:00Z"), priorityKey = -1)
    prepareEvent(id = "TEST_8", dateTo = Instant.parse("2024-09-06T00:00:00Z"), priorityKey = -1)
    prepareEvent(id = "TEST_9", dateTo = Instant.parse("2024-09-06T00:00:00Z"),  promotionEventType = PromotionEventType.FTUE)
  }

  @Test
  fun `SHOULD return list of enabled EventDto ON getPromotionEventList`() {
    //given
    prepareEvents()
    //when
    val result = runBlocking {
      service.getPromotionEventList(
        dateFrom = Instant.parse("2024-08-01T00:00:00Z"),
        dateTo = Instant.parse("2024-09-01T00:00:00Z"),
        enabled = true,
        eventType = PromotionEventType.GLOBAL,
      )
    }
    //then
    val filteredResult = result.filter { it.id.startsWith("TEST") }
    assertThat(filteredResult).hasSize(4)
    val ids = filteredResult.map { it.id }
    assertThat(ids).containsExactly("TEST_7", "TEST_8", "TEST_3", "TEST_4")
    val test3 = filteredResult.find { it.id == "TEST_3" }!!
    assertThat(test3.dateTo).isEqualTo(Instant.parse("2024-08-06T00:00:00Z"))
    assertThat(test3.dateFrom).isEqualTo(Instant.parse("2024-01-01T00:00:00Z"))
    assertThat(test3.useUserTimeZone).isTrue()
    val exampleDto = defaultJsonConverter.decodeFromString<PromotionEventUiConfiguration>(test3.uiConfiguration)
    assertThat(exampleDto).isEqualTo(promotionEventUiConfig)
  }

  @Test
  fun `SHOULD return list of EventDto ON getPromotionEventList WHEN eventType is not specified`() {
    //given
    prepareEvents()
    //when
    val result = runBlocking {
      service.getPromotionEventList(
        dateFrom = Instant.parse("2024-08-01T00:00:00Z"),
        dateTo = Instant.parse("2024-09-01T00:00:00Z"),
        enabled = null,
      )
    }
    //then
    val filteredResult = result.filter { it.id.startsWith("TEST") }
    assertThat(filteredResult).hasSize(6)
    val ids = filteredResult.map { it.id }
    assertThat(ids).containsExactly("TEST_7", "TEST_8", "TEST_3", "TEST_4", "TEST_6", "TEST_9")
  }

  @Test
  fun `SHOULD return list of EventDto ON getPromotionEventList WHEN enabled is null`() {
    //given
    prepareEvents()
    //when
    val result = runBlocking {
      service.getPromotionEventList(
        dateFrom = Instant.parse("2024-08-01T00:00:00Z"),
        dateTo = Instant.parse("2024-09-01T00:00:00Z"),
        enabled = null,
        eventType = PromotionEventType.GLOBAL,
      )
    }
    //then
    val filteredResult = result.filter { it.id.startsWith("TEST") }
    assertThat(filteredResult).hasSize(5)
    val ids = filteredResult.map { it.id }
    assertThat(ids).containsExactly("TEST_7", "TEST_8", "TEST_3", "TEST_4", "TEST_6")
    val test3 = filteredResult.find { it.id == "TEST_3" }!!
    assertThat(test3.dateTo).isEqualTo(Instant.parse("2024-08-06T00:00:00Z"))
    assertThat(test3.dateFrom).isEqualTo(Instant.parse("2024-01-01T00:00:00Z"))
    assertThat(test3.useUserTimeZone).isTrue()
    val exampleDto = defaultJsonConverter.decodeFromString<PromotionEventUiConfiguration>(test3.uiConfiguration)
    assertThat(exampleDto).isEqualTo(promotionEventUiConfig)
    val test6 = filteredResult.find { it.id == "TEST_6" }!!
    assertThat(test6.useUserTimeZone).isTrue()
  }


  @Test
  fun `SHOULD return list of all EventDto ON getPromotionEventList WHEN enabled is false`() {
    //given
    prepareEvents()
    //when
    val result = runBlocking {
      service.getPromotionEventList(
        dateFrom = Instant.parse("2024-08-01T00:00:00Z"),
        dateTo = Instant.parse("2024-09-01T00:00:00Z"),
        enabled = false,
        eventType = PromotionEventType.GLOBAL,
      )
    }
    //then
    val filteredResult = result.filter { it.id.startsWith("TEST") }
    assertThat(filteredResult).hasSize(1)
    val ids = filteredResult.map { it.id }
    assertThat(ids).containsExactly("TEST_6")
    val test6 = filteredResult.find { it.id == "TEST_6" }!!
    assertThat(test6.dateTo).isEqualTo(Instant.parse("2024-09-06T00:00:00Z"))
    assertThat(test6.dateFrom).isEqualTo(Instant.parse("2024-01-01T00:00:00Z"))
    assertThat(test6.useUserTimeZone).isTrue()
  }

  @Test
  fun `SHOLD throw an exception WHEN we are trying to store invalid json`() {
    val logger = LoggerFactory.getLogger("root") as Logger
    val currentLoggerLevelBackup = logger.level
    logger.level = Level.ERROR
    try {
      assertThrows<ExposedSQLException> { prepareEvent(id = "TEST_1", cfg = "{abcd") }
    } finally {
      logger.level = currentLoggerLevelBackup
    }
  }

  private fun assertEvent(
    id: String,
    dateFrom: Instant,
    dateTo: Instant,
    cfg: String,
    experimentKey: String,
    variationKey: String,
    priorityKey: Int
  ) {
    val configList = transaction(database) {
      PromotionEventCfgTable
        .select { PromotionEventCfgTable.id eq id }
        .map {
          PromotionEvent(
            id = it[PromotionEventCfgTable.id].value,
            dateFrom = it[PromotionEventCfgTable.dateFrom],
            dateTo = it[PromotionEventCfgTable.dateTo],
            useUserTimeZone = it[useUserTimeZone],
            uiConfiguration = it[PromotionEventCfgTable.cfg],
            experimentKey = it[PromotionEventCfgTable.experimentKey],
            variationKey = it[PromotionEventCfgTable.variationKey],
            priorityKey = it[PromotionEventCfgTable.priorityKey],
            eventType = PromotionEventType.valueOf(it[PromotionEventCfgTable.eventType])
          )
        }
    }
    assertThat(configList).hasSize(1)
    assertThat(configList[0]).isEqualTo(
      PromotionEvent(
        id = id,
        dateFrom = dateFrom,
        dateTo = dateTo,
        useUserTimeZone = false,
        uiConfiguration = cfg,
        experimentKey = experimentKey,
        variationKey = variationKey,
        priorityKey = priorityKey,
        eventType = PromotionEventType.GLOBAL
      )
    )
  }

  private fun prepareEvent(
    id: String,
    dateFrom: Instant = Instant.parse("2024-01-01T00:00:00Z"),
    dateTo: Instant = Instant.parse("2024-01-03T00:00:00Z"),
    cfg: String = "{}",
    useUserTimeZone: Boolean = false,
    enabled: Boolean = true,
    experimentKey: String = "aaTest",
    variationKey: String = "groupA",
    priorityKey: Int = 0,
    promotionEventType: PromotionEventType = PromotionEventType.GLOBAL,
  ) {
    transaction(database) {
      PromotionEventCfgTable.insert {
        it[PromotionEventCfgTable.id] = id
        it[PromotionEventCfgTable.dateFrom] = dateFrom
        it[PromotionEventCfgTable.dateTo] = dateTo
        it[PromotionEventCfgTable.cfg] = cfg
        it[PromotionEventCfgTable.useUserTimeZone] = useUserTimeZone
        it[PromotionEventCfgTable.enabled] = enabled
        it[PromotionEventCfgTable.experimentKey] = experimentKey
        it[PromotionEventCfgTable.variationKey] = variationKey
        it[PromotionEventCfgTable.priorityKey] = priorityKey
        it[PromotionEventCfgTable.eventType] = promotionEventType.name
      }
    }
  }


}