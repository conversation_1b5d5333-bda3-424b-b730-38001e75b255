package com.moregames.playtime.user.challenge

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNotNull
import assertk.assertions.isNull
import com.google.inject.Provider
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.variations.SpecialOfferAfterChallengeClaimVariation
import com.moregames.base.app.BuildVariant
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.exceptions.BaseException.Companion.DEFAULT_EXTERNAL_MESSAGE
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.earnings.currency.CurrencyExchangeService
import com.moregames.playtime.earnings.currency.dto.CurrencyExchangeResultDto
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import com.moregames.playtime.user.cashout.dto.CashoutPeriodDto
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.common.ChallengeSpecialOfferClaimsService
import com.moregames.playtime.user.challenge.dto.*
import com.moregames.playtime.user.challenge.dto.bq.UserChallengeEventUpdatedBqDto
import com.moregames.playtime.user.challenge.dto.claim.event.ClaimBoxType
import com.moregames.playtime.user.challenge.dto.claim.event.ClaimEventRequestApiDto
import com.moregames.playtime.user.challenge.dto.claim.event.ClaimEventResponseApiDto
import com.moregames.playtime.user.challenge.dto.claim.event.ClaimEventResponseApiDto.SpecialOffer
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.never
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import org.mockito.kotlin.verifyNoMoreInteractions
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*

@ExtendWith(MockExtension::class)
class ClaimChallengeEventServiceTest(
  private val challengeRewardingService: ChallengeRewardingService,
  private val marketService: MarketService,
  private val currencyExchangeService: CurrencyExchangeService,
  private val challengeService: ChallengeService,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val timeService: TimeService,
  private val abTestingService: AbTestingService,
  private val cashoutPeriodsService: CashoutPeriodsService,
  private val buildVariantProvider: Provider<BuildVariant>,
  private val challengeSpecialOfferClaimsService: ChallengeSpecialOfferClaimsService,
) {
  private val underTest = ClaimChallengeEventService(
    challengeRewardingService = challengeRewardingService,
    marketService = marketService,
    currencyExchangeService = currencyExchangeService,
    challengeService = challengeService,
    bigQueryEventPublisher = bigQueryEventPublisher,
    timeService = timeService,
    abTestingService = abTestingService,
    cashoutPeriodsService = cashoutPeriodsService,
    buildVariantProvider = buildVariantProvider,
    challengeSpecialOfferClaimsService = challengeSpecialOfferClaimsService,
  )

  @BeforeEach
  fun before() {
    timeService.mock({ now() }, now)
    marketService.mock({ getUserCurrency(USER_ID) }, currencyAud)
    currencyExchangeService.mock({ convert(BigDecimal.ZERO, currencyAud) }, zeroExchange)
  }

  @Test
  fun `SHOULD return not claimed response WHEN event is in invalid state`() {
    val eventId = ChallengeEventId("13")
    val userEvent = UserChallengeEvent(
      eventId = eventId,
      userId = USER_ID,
      earnings = BigDecimal.ZERO,
      state = ChallengeEventState.IN_PROGRESS,
      startedAt = startedAt,
      applovinNonBannerRevenue = null,
    )
    challengeService.mock({ getUserChallengeEvent(USER_ID, eventId) }, userEvent)

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }

    assertThat(result).isEqualTo(
      ClaimEventResponseApiDto("A$ 0.00", null, errorMessage = DEFAULT_EXTERNAL_MESSAGE)
    )

    verifyNoInteractions(challengeRewardingService)
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD return not claimed response WHEN event is in invalid state after claiming`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.25")
    val noEarningsEvent = UserChallengeEvent(
      eventId = eventId,
      userId = USER_ID,
      earnings = BigDecimal.ZERO,
      state = ChallengeEventState.COMPLETED,
      startedAt = startedAt,
      applovinNonBannerRevenue = null,
    )
    val withEarningsEvent = UserChallengeEvent(
      eventId = eventId,
      userId = USER_ID,
      earnings = earnings,
      state = ChallengeEventState.COMPLETED,
      startedAt = startedAt,
      applovinNonBannerRevenue = null,
    )
    val reward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("0.05"),
      userCurrency = currencyAud,
      amount = BigDecimal("0.12"),
      amountNoRounding = BigDecimal("0.1234"),
    )

    challengeService.mock({ getUserChallengeEvent(USER_ID, eventId) }, noEarningsEvent, arrayOf(withEarningsEvent))
    challengeRewardingService.mock({ calculateChallengeEventRewardUsd(noEarningsEvent) }, BigDecimal("0.05"))
    challengeService.mock({ claimEvent(eventId, USER_ID, BigDecimal("0.05")) }, false)
    currencyExchangeService.mock({ convert(BigDecimal("0.05"), currencyAud) }, reward)
    challengeRewardingService.mock({ giveChallengeEventReward(noEarningsEvent, reward) }, true)

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }

    assertThat(result).isEqualTo(
      ClaimEventResponseApiDto("A$ 0.00", null, errorMessage = DEFAULT_EXTERNAL_MESSAGE)
    )

    verifyBlocking(challengeRewardingService) { giveChallengeEventReward(noEarningsEvent, reward) }
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD return claimed response WHEN event has been claimed by other request at the same time`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.25")
    val completedEvent = UserChallengeEvent(
      eventId = eventId,
      userId = USER_ID,
      earnings = BigDecimal.ZERO,
      state = ChallengeEventState.COMPLETED,
      startedAt = startedAt,
      applovinNonBannerRevenue = null,
    )
    val claimedEvent = UserChallengeEvent(
      eventId = eventId,
      userId = USER_ID,
      earnings = earnings,
      state = ChallengeEventState.CLAIMED,
      startedAt = startedAt,
      applovinNonBannerRevenue = null,
    )
    val reward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("0.05"),
      userCurrency = currencyAud,
      amount = BigDecimal("0.12"),
      amountNoRounding = BigDecimal("0.1234"),
    )
    val claimedReward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.25"),
      userCurrency = currencyAud,
      amount = BigDecimal("2.34"),
      amountNoRounding = BigDecimal("2.3456"),
    )

    challengeService.mock({ getUserChallengeEvent(USER_ID, eventId) }, completedEvent, arrayOf(claimedEvent))
    challengeRewardingService.mock({ calculateChallengeEventRewardUsd(completedEvent) }, BigDecimal("0.05"))
    challengeService.mock({ claimEvent(eventId, USER_ID, BigDecimal("0.05")) }, false)
    currencyExchangeService.mock({ convert(BigDecimal("0.05"), currencyAud) }, reward)
    currencyExchangeService.mock({ convert(BigDecimal("1.25"), currencyAud) }, claimedReward)

    challengeRewardingService.mock({ giveChallengeEventReward(completedEvent, reward) }, true)

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }

    assertThat(result).isEqualTo(
      ClaimEventResponseApiDto("A$ 2.34", ClaimBoxType.SILVER)
    )

    verifyBlocking(challengeRewardingService) { giveChallengeEventReward(completedEvent, reward) }
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD return claimed response WHEN earnings reward already added`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.25")
    val completedEvent = UserChallengeEvent(
      eventId = eventId,
      userId = USER_ID,
      earnings = BigDecimal.ZERO,
      state = ChallengeEventState.COMPLETED,
      startedAt = startedAt,
      applovinNonBannerRevenue = null,
    )
    val claimedEvent = UserChallengeEvent(
      eventId = eventId,
      userId = USER_ID,
      earnings = earnings,
      state = ChallengeEventState.CLAIMED,
      startedAt = startedAt,
      applovinNonBannerRevenue = null,
    )
    val reward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("0.05"),
      userCurrency = currencyAud,
      amount = BigDecimal("0.12"),
      amountNoRounding = BigDecimal("0.1234"),
    )
    val claimedReward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.25"),
      userCurrency = currencyAud,
      amount = BigDecimal("2.34"),
      amountNoRounding = BigDecimal("2.3456"),
    )

    challengeService.mock({ getUserChallengeEvent(USER_ID, eventId) }, completedEvent, arrayOf(claimedEvent))
    challengeRewardingService.mock({ calculateChallengeEventRewardUsd(completedEvent) }, BigDecimal("0.05"))
    currencyExchangeService.mock({ convert(BigDecimal("0.05"), currencyAud) }, reward)
    currencyExchangeService.mock({ convert(BigDecimal("1.25"), currencyAud) }, claimedReward)
    challengeRewardingService.mock({ giveChallengeEventReward(completedEvent, reward) }, false)

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }

    assertThat(result).isEqualTo(
      ClaimEventResponseApiDto("A$ 2.34", ClaimBoxType.SILVER)
    )

    verifyBlocking(challengeRewardingService) { giveChallengeEventReward(completedEvent, reward) }
    verifyBlocking(challengeRewardingService) { calculateChallengeEventRewardUsd(completedEvent) }
    verifyNoMoreInteractions(challengeRewardingService)
    verifyBlocking(challengeService, never()) { claimEvent(eventId, USER_ID, earnings) }
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD return claimed response WHEN event is completed`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.25")
    val userEvent = UserChallengeEvent(
      eventId = eventId,
      userId = USER_ID,
      earnings = BigDecimal.ZERO,
      state = ChallengeEventState.COMPLETED,
      startedAt = startedAt,
      applovinNonBannerRevenue = BigDecimal.TEN,
    )
    val reward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.25"),
      userCurrency = currencyAud,
      amount = BigDecimal("1.67"),
      amountNoRounding = BigDecimal("1.6789"),
    )

    challengeService.mock({ getUserChallengeEvent(USER_ID, eventId) }, userEvent)
    challengeService.mock({ claimEvent(eventId, USER_ID, earnings) }, true)
    challengeService.mock( { getChallengeEventById(eventId)}, challengeEvent)
    challengeRewardingService.mock({ calculateChallengeEventRewardUsd(userEvent) }, earnings)
    currencyExchangeService.mock({ convert(BigDecimal("1.25"), currencyAud) }, reward)
    challengeRewardingService.mock({ giveChallengeEventReward(userEvent, reward) }, true)

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }

    assertThat(result).isEqualTo(
      ClaimEventResponseApiDto("A$ 1.67", ClaimBoxType.SILVER)
    )

    verifyBlocking(challengeRewardingService) { giveChallengeEventReward(userEvent, reward) }
    verifyBlocking(bigQueryEventPublisher) { publish(UserChallengeEventUpdatedBqDto(
      userId = USER_ID,
      earnings = earnings,
      challengeEventId = eventId,
      state = ChallengeEventState.CLAIMED,
      applovinNonBannerRevenue = BigDecimal.TEN,
      eventStart = challengeEvent.dateFrom,
      eventEnd = challengeEvent.dateTo,
      createdAt = now
    ))  }
  }

  @Test
  fun `SHOULD return claimed response WHEN event has been already claimed`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.25")
    val exchange = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.25"),
      userCurrency = currencyAud,
      amount = BigDecimal("2.34"),
      amountNoRounding = BigDecimal("2.3456"),
    )

    challengeService.mock(
      { getUserChallengeEvent(USER_ID, eventId) },
      UserChallengeEvent(
        eventId = eventId,
        userId = USER_ID,
        earnings = earnings,
        state = ChallengeEventState.CLAIMED,
        startedAt = startedAt,
        applovinNonBannerRevenue = null,
      )
    )
    currencyExchangeService.mock({ convert(BigDecimal("1.25"), currencyAud) }, exchange)

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }
    assertThat(result).isEqualTo(
      ClaimEventResponseApiDto("A$ 2.34", ClaimBoxType.SILVER)
    )
    verifyNoInteractions(challengeRewardingService)
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD correctly handle many decimal digits ON claimEvent WHEN event has been already claimed`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.23456789")
    val exchange = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.23456789"),
      userCurrency = currencyAud,
      amount = BigDecimal("2.34"),
      amountNoRounding = BigDecimal("2.3456"),
    )

    challengeService.mock(
      { getUserChallengeEvent(USER_ID, eventId) },
      UserChallengeEvent(
        eventId = eventId,
        userId = USER_ID,
        earnings = earnings,
        state = ChallengeEventState.CLAIMED,
        startedAt = startedAt,
        applovinNonBannerRevenue = BigDecimal.ZERO
      )
    )
    currencyExchangeService.mock({ convert(BigDecimal("1.23456789"), currencyAud) }, exchange)

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }
    assertThat(result).isEqualTo(
      ClaimEventResponseApiDto("A$ 2.34", ClaimBoxType.SILVER)
    )
    verifyNoInteractions(challengeRewardingService)
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD return boosted mode offer WHEN all conditions are met`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.25")
    val userEvent = createCompletedEvent(eventId)
    val reward = createReward(earnings)
    val variation = SpecialOfferAfterChallengeClaimVariation.BmWithoutEarningBoostEverySingleDay
    val cashoutPeriod = createCashoutPeriod(now.minus(1, ChronoUnit.HOURS), now.plus(4, ChronoUnit.HOURS))

    setupSuccessfulClaim(eventId, userEvent, reward, earnings)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.SPECIAL_OFFER_AFTER_CHALLENGE_CLAIM) }, variation)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    buildVariantProvider.mock({ get() }, BuildVariant.TEST)
    challengeSpecialOfferClaimsService.mock({ findLastClaimedSpecialOfferAt(USER_ID) }, null)

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }

    assertThat(result.specialOffer).isEqualTo(
      SpecialOffer(
        title = "Want to Double your next Cash-Out?",
        description = "Claim the offer to earn up to 200% for all games!",
        timeToClaim = 120
      )
    )
  }

  @Test
  fun `SHOULD return null boosted mode WHEN user not in experiment`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.25")
    val userEvent = createCompletedEvent(eventId)
    val reward = createReward(earnings)

    setupSuccessfulClaim(eventId, userEvent, reward, earnings)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.SPECIAL_OFFER_AFTER_CHALLENGE_CLAIM) }, null)

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }

    assertThat(result.specialOffer).isNull()
  }

  @Test
  fun `SHOULD return null boosted mode WHEN production and cashout period less than 3 hours`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.25")
    val userEvent = createCompletedEvent(eventId)
    val reward = createReward(earnings)
    val variation = SpecialOfferAfterChallengeClaimVariation.BmWithoutEarningBoostEverySingleDay
    val shortCashoutPeriod = createCashoutPeriod(now.minus(1, ChronoUnit.MINUTES), now.plus(2, ChronoUnit.MINUTES))

    setupSuccessfulClaim(eventId, userEvent, reward, earnings)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.SPECIAL_OFFER_AFTER_CHALLENGE_CLAIM) }, variation)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, shortCashoutPeriod)
    buildVariantProvider.mock({ get() }, BuildVariant.PRODUCTION)

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }

    assertThat(result.specialOffer).isNull()
  }

  @Test
  fun `SHOULD return null boosted mode WHEN less than 15 minutes until cashout period end`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.25")
    val userEvent = createCompletedEvent(eventId)
    val reward = createReward(earnings)
    val variation = SpecialOfferAfterChallengeClaimVariation.BmWithoutEarningBoostEverySingleDay
    val cashoutPeriod = createCashoutPeriod(now.minus(1, ChronoUnit.HOURS), now.plus(10, ChronoUnit.MINUTES))

    setupSuccessfulClaim(eventId, userEvent, reward, earnings)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.SPECIAL_OFFER_AFTER_CHALLENGE_CLAIM) }, variation)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    buildVariantProvider.mock({ get() }, BuildVariant.TEST)

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }

    assertThat(result.specialOffer).isNull()
  }

  @Test
  fun `SHOULD return null boosted mode WHEN last accepted offer within days gap`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.25")
    val userEvent = createCompletedEvent(eventId)
    val reward = createReward(earnings)
    val variation = SpecialOfferAfterChallengeClaimVariation.BmWithoutEarningBoostEverySingleDay
    val cashoutPeriod = createCashoutPeriod(now.minus(1, ChronoUnit.HOURS), now.plus(4, ChronoUnit.HOURS))

    setupSuccessfulClaim(eventId, userEvent, reward, earnings)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.SPECIAL_OFFER_AFTER_CHALLENGE_CLAIM) }, variation)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    buildVariantProvider.mock({ get() }, BuildVariant.TEST)
    challengeSpecialOfferClaimsService.mock({ findLastClaimedSpecialOfferAt(USER_ID) }, now.minus(12, ChronoUnit.HOURS))

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }

    assertThat(result.specialOffer).isNull()
  }

  @Test
  fun `SHOULD return boosted mode offer WHEN last accepted offer outside days gap`() {
    val eventId = ChallengeEventId("13")
    val earnings = BigDecimal("1.25")
    val userEvent = createCompletedEvent(eventId)
    val reward = createReward(earnings)
    val variation = SpecialOfferAfterChallengeClaimVariation.BmWithoutEarningBoostEverySingleDay
    val cashoutPeriod = createCashoutPeriod(now.minus(1, ChronoUnit.HOURS), now.plus(4, ChronoUnit.HOURS))

    setupSuccessfulClaim(eventId, userEvent, reward, earnings)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.SPECIAL_OFFER_AFTER_CHALLENGE_CLAIM) }, variation)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    buildVariantProvider.mock({ get() }, BuildVariant.TEST)
    challengeSpecialOfferClaimsService.mock({ findLastClaimedSpecialOfferAt(USER_ID) }, now.minus(2, ChronoUnit.DAYS))

    val result = runBlocking { underTest.claimEvent(USER_ID, ClaimEventRequestApiDto(eventId)) }

    assertThat(result.specialOffer).isNotNull()
  }

  private fun createCompletedEvent(eventId: ChallengeEventId) = UserChallengeEvent(
    eventId = eventId,
    userId = USER_ID,
    earnings = BigDecimal.ZERO,
    state = ChallengeEventState.COMPLETED,
    startedAt = startedAt,
    applovinNonBannerRevenue = BigDecimal.TEN,
  )

  private fun createReward(earnings: BigDecimal) = CurrencyExchangeResultDto(
    usdAmount = earnings,
    userCurrency = currencyAud,
    amount = BigDecimal("1.67"),
    amountNoRounding = BigDecimal("1.6789"),
  )

  private fun createCashoutPeriod(periodStart: Instant, periodEnd: Instant) = CashoutPeriodDto(
    userId = USER_ID,
    periodStart = periodStart,
    periodEnd = periodEnd,
    coinGoal = 2000,
    counter = 3,
    noEarningsCounter = 0,
    coinGoalMilestones = emptyList(),
  )

  private fun setupSuccessfulClaim(eventId: ChallengeEventId, userEvent: UserChallengeEvent, reward: CurrencyExchangeResultDto, earnings: BigDecimal) {
    challengeService.mock({ getUserChallengeEvent(USER_ID, eventId) }, userEvent)
    challengeService.mock({ claimEvent(eventId, USER_ID, earnings) }, true)
    challengeService.mock({ getChallengeEventById(eventId) }, challengeEvent)
    challengeRewardingService.mock({ calculateChallengeEventRewardUsd(userEvent) }, earnings)
    currencyExchangeService.mock({ convert(earnings, currencyAud) }, reward)
    challengeRewardingService.mock({ giveChallengeEventReward(userEvent, reward) }, true)
  }

  companion object {
    const val USER_ID = "userId"
    val startedAt: Instant = Instant.parse("2025-01-31T12:13:14Z")
    val currencyAud: Currency = Currency.getInstance("AUD")
    val zeroExchange = CurrencyExchangeResultDto(
      usdAmount = BigDecimal.ZERO,
      userCurrency = currencyAud,
      amount = BigDecimal.ZERO,
      amountNoRounding = BigDecimal.ZERO,
    )
    val challengeEvent = ChallengeEvent(
      id = ChallengeEventId("13"),
      dateFrom = Instant.parse("2025-01-31T12:13:14Z"),
      dateTo = Instant.parse("2025-02-20T12:13:14Z"),
      cfg = "{}",
      enabled = true,
      challenges = emptyList(),
      eventType = ChallengeEventType.GLOBAL,
      bonusId = "bonusId",
    )
    val now: Instant = Instant.parse("2025-02-15T12:13:14Z")
  }
}
