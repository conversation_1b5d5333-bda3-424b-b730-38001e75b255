package com.moregames.playtime.user.coingoal

import assertk.all
import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import assertk.assertions.isEqualTo
import assertk.assertions.isNotNull
import assertk.assertions.isNull
import com.moregames.base.abtesting.Variations
import com.moregames.base.dto.AppPlatform
import com.moregames.base.gamecoingoals.GameCoinGoalEntity
import com.moregames.base.gamecoingoals.GameCoinGoalPersistenceService
import com.moregames.base.gamecoingoals.GameCoinGoalSetEntity
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestFactory
import org.junit.jupiter.api.extension.ExtensionContext
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.ArgumentsProvider
import org.junit.jupiter.params.provider.ArgumentsSource
import org.mockito.Mockito
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant
import java.util.stream.Stream
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class GameCoinGoalSetGeneratorTest {
  private val now = Instant.now()
  private val timeService: TimeService = mock {
    on { now() } doReturn now
  }
  private val userService: UserService = mock()
  private val gameCoinGoalPersistenceService: GameCoinGoalPersistenceService = mock()
  private val coinGoalsGamesProvider: GameCoinGoalsGamesProvider = mock()
  private val coinGoalService: CoinGoalService = mock()

  private val underTest = GameCoinGoalSetGenerator(
    coinGoalService = coinGoalService,
    coinGoalsGamesProvider = coinGoalsGamesProvider,
    gameCoinGoalPersistenceService = gameCoinGoalPersistenceService,
    userService = userService,
    timeService = timeService,
  )

  @Nested
  inner class GenerateNewSet {
    //region Generic goal
    @ParameterizedTest
    @ArgumentsSource(GenericGoalVariationsProviderForFirstSet::class)
    fun `SHOULD generate new set with first cp goal and generic goal WHEN variation has generic goal AND first set`(variations: Variations) = runTest {
      val user = userDtoStub.copy(id = userId)
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 0L)
      coinGoalService.mock({ getFirstCpCoinGoal(user) }, 50)

      underTest.generateNewSet(user, variations)

      val captor = argumentCaptor<GameCoinGoalEntity>()
      verifyBlocking(gameCoinGoalPersistenceService) { createSet(eq(userId), captor.capture()) }

      val entity = captor.firstValue
      assertThat(entity).all {
        transform { it.gameId }.isNull()
        transform { it.goalBalance }.isNull()
        transform { it.goal }.isEqualTo(50)
        transform { it.order }.isEqualTo(1)
      }
    }

    @ParameterizedTest
    @ArgumentsSource(GenericGoalVariationsProvider::class)
    fun `SHOULD generate new set with non-first cp goal and generic goal WHEN variation has generic goal AND second set`(variations: Variations) = runTest {
      val user = userDtoStub.copy(id = userId)
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 1L)
      coinGoalService.mock({ getNextCoinGoal(user) }, 150)

      underTest.generateNewSet(user, variations)

      val captor = argumentCaptor<GameCoinGoalEntity>()
      verifyBlocking(gameCoinGoalPersistenceService) { createSet(eq(userId), captor.capture()) }

      val entity = captor.firstValue
      assertThat(entity).all {
        transform { it.gameId }.isNull()
        transform { it.goalBalance }.isNull()
        transform { it.goal }.isEqualTo(150)
        transform { it.order }.isEqualTo(1)
      }
    }

    @ParameterizedTest
    @ArgumentsSource(EasyGoalVariationsProvider::class)
    fun `SHOULD generate new set with first cp goal and generic goal WHEN variation has easy goal AND second set`(variations: Variations) = runTest {
      val user = userDtoStub.copy(id = userId)
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 1L)
      coinGoalService.mock({ getFirstCpCoinGoal(user) }, 50)

      underTest.generateNewSet(user, variations)

      val captor = argumentCaptor<GameCoinGoalEntity>()
      verifyBlocking(gameCoinGoalPersistenceService) { createSet(eq(userId), captor.capture()) }

      val entity = captor.firstValue
      assertThat(entity).all {
        transform { it.gameId }.isNull()
        transform { it.goalBalance }.isNull()
        transform { it.goal }.isEqualTo(50)
        transform { it.order }.isEqualTo(1)
      }
    }
    //endregion

    //region Game goal
    @ParameterizedTest
    @ArgumentsSource(GameGoalVariationsProvider::class)
    fun `SHOULD generate new set with first cp goal and game goal WHEN variation has game goal AND first set`(variations: Variations, platform: AppPlatform) =
      runTest {
        val user = userDtoStub.copy(id = userId, appPlatform = platform)
        gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 0L)
        coinGoalService.mock({ getFirstCpCoinGoal(user) }, 50)
        coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(listOf(123), emptyList()))

        underTest.generateNewSet(user, variations)

        val captor = argumentCaptor<GameCoinGoalEntity>()
        verifyBlocking(gameCoinGoalPersistenceService) { createSet(eq(userId), captor.capture()) }

        val entity = captor.firstValue
        assertThat(entity).all {
          transform { it.gameId }.isEqualTo(123)
          transform { it.goalBalance }.isNotNull().isEqualByComparingTo(BigDecimal.ZERO)
          transform { it.goal }.isEqualTo(50)
          transform { it.order }.isEqualTo(1)
        }
      }

    @ParameterizedTest
    @ArgumentsSource(GameGoalVariationsProviderForNonFirstSet::class)
    fun `SHOULD generate new set with non-first cp goal and game goal WHEN variation has game goal AND second set`(
      variations: Variations,
      platform: AppPlatform
    ) = runTest {
      val user = userDtoStub.copy(id = userId, appPlatform = platform)
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 1L)
      coinGoalService.mock({ getNextCoinGoal(user) }, 150)
      coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(listOf(123), emptyList()))

      underTest.generateNewSet(user, variations)

      val captor = argumentCaptor<GameCoinGoalEntity>()
      verifyBlocking(gameCoinGoalPersistenceService) { createSet(eq(userId), captor.capture()) }

      val entity = captor.firstValue
      assertThat(entity).all {
        transform { it.gameId }.isEqualTo(123)
        transform { it.goalBalance }.isNotNull().isEqualByComparingTo(BigDecimal.ZERO)
        transform { it.goal }.isEqualTo(150)
        transform { it.order }.isEqualTo(1)
      }
    }
    //endregion

    @ParameterizedTest
    @ArgumentsSource(GameGoalVariationsProvider::class)
    fun `SHOULD do nothing WHEN games sequence is empty`(variations: Variations, platform: AppPlatform) = runTest {
      val user = userDtoStub.copy(id = userId, appPlatform = platform)
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 0L)
      coinGoalService.mock({ getFirstCpCoinGoal(user) }, 50)

      coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(emptyList(), emptyList()))

      underTest.generateNewSet(user, variations)

      verifyBlocking(gameCoinGoalPersistenceService, never()) { createSet(any(), any()) }
    }
  }

  @Nested
  inner class PopulateNewGoal {
    @ParameterizedTest
    @ArgumentsSource(AllVariationsProvider::class)
    fun `SHOULD return false WHEN all goals are completed`(variations: Variations) = runTest {
      val user = userDtoStub.copy(id = userId)
      val gcgVariation = GameCoinGoalSetGenerator.variationsMap[variations]!!
      val entity = mock<GameCoinGoalSetEntity> {
        on { goals } doReturn List(gcgVariation.overallCount) { mock() }
      }
      gameCoinGoalPersistenceService.mock({ findActiveCoinGoalSet(userId) }, entity)
      assertFalse { underTest.populateNewGoalIfNeeded(user, gcgVariation) }
    }

    @ParameterizedTest
    @ArgumentsSource(AllVariationsProvider::class)
    fun `SHOULD return false WHEN games sequence is empty`(variations: Variations, platform: AppPlatform) = runTest {
      val user = userDtoStub.copy(id = userId, appPlatform = platform)
      val gcgVariation = GameCoinGoalSetGenerator.variationsMap[variations]!!
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 1L)
      coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(emptyList(), emptyList()))
      val entity = mock<GameCoinGoalSetEntity> {
        on { goals } doReturn emptyList()
      }
      gameCoinGoalPersistenceService.mock({ findActiveCoinGoalSet(userId) }, entity)
      assertFalse { underTest.populateNewGoalIfNeeded(user, gcgVariation) }
    }

    @ParameterizedTest
    @ArgumentsSource(AllVariationsProvider::class)
    fun `SHOULD return true WHEN it's needed to populate goal`(variations: Variations, platform: AppPlatform) = runTest {
      val user = userDtoStub.copy(id = userId, appPlatform = platform)
      val gcgVariation = GameCoinGoalSetGenerator.variationsMap[variations]!!
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 1L)
      coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(listOf(123), emptyList()))
      val entity = mock<GameCoinGoalSetEntity> {
        on { id } doReturn "setId"
        on { goals } doReturn listOf(GameCoinGoalEntity(321, 100, BigDecimal.ZERO, 1))
      }
      gameCoinGoalPersistenceService.mock({ findActiveCoinGoalSet(userId) }, entity)
      assertTrue { underTest.populateNewGoalIfNeeded(user, gcgVariation) }

      val captor = argumentCaptor<GameCoinGoalEntity>()
      verifyBlocking(gameCoinGoalPersistenceService) { addGoal(eq("setId"), captor.capture()) }
      assertThat(captor.firstValue).all {
        transform { it.gameId }.isEqualTo(123)
        transform { it.goalBalance }.isNotNull().isEqualByComparingTo(BigDecimal.ZERO)
        transform { it.goal }.isEqualTo(100)
        transform { it.order }.isEqualTo(2)
      }
    }
  }

  @Nested
  inner class GamesFlow {
    val entity = mock<GameCoinGoalSetEntity> {
      on { id } doReturn "setId"
      on { goals } doReturn listOf(GameCoinGoalEntity(321, 100, BigDecimal.ZERO, 1))
    }

    @BeforeEach
    fun setUp() {
      Mockito.reset(gameCoinGoalPersistenceService) // resetting for dynamic tests
      gameCoinGoalPersistenceService.mock({ findActiveCoinGoalSet(userId) }, entity)
    }

    @TestFactory
    fun `SHOULD return first new game WHEN it's first two sets`(): List<DynamicTest> {
      return AllVariationsProvider.provideArguments(null).toList().flatMap {
        val (variations, platform) = it as VariationArgument
        arrayOf(0L, 1L).map { setsCount ->
          DynamicTest.dynamicTest("$variations, $setsCount") {
            setUp() // test factory doesn't support lifecycle callbacks, so...
            runTest {
              val user = userDtoStub.copy(id = userId, appPlatform = platform)
              val gcgVariation = GameCoinGoalSetGenerator.variationsMap[variations]!!
              gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, setsCount)
              coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(listOf(123, 321), emptyList()))
              assertTrue { underTest.populateNewGoalIfNeeded(user, gcgVariation) }
              val captor = argumentCaptor<GameCoinGoalEntity>()
              verifyBlocking(gameCoinGoalPersistenceService) { addGoal(eq("setId"), captor.capture()) }
              assertThat(captor.firstValue).all {
                transform { it.gameId }.isEqualTo(123)
              }
            }
          }
        }
      }
    }

    @ParameterizedTest
    @ArgumentsSource(AllVariationsProvider::class)
    fun `SHOULD return first new game WHEN it's third set, but current set count is 0`(variations: Variations, platform: AppPlatform) = runTest {
      val entity = mock<GameCoinGoalSetEntity> {
        on { id } doReturn "setId"
        on { goals } doReturn listOf(GameCoinGoalEntity(null, 100, null, 1))
      }
      gameCoinGoalPersistenceService.mock({ findActiveCoinGoalSet(userId) }, entity)
      val user = userDtoStub.copy(id = userId, appPlatform = platform)
      val gcgVariation = GameCoinGoalSetGenerator.variationsMap[variations]!!
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 3L)
      coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(listOf(123, 321), emptyList()))

      assertTrue { underTest.populateNewGoalIfNeeded(user, gcgVariation) }
      val captor = argumentCaptor<GameCoinGoalEntity>()
      verifyBlocking(gameCoinGoalPersistenceService) { addGoal(eq("setId"), captor.capture()) }
      assertThat(captor.firstValue).all {
        transform { it.gameId }.isEqualTo(123)
      }
    }

    @ParameterizedTest
    @ArgumentsSource(AllVariationsProvider::class)
    fun `SHOULD return first new game WHEN it's third set, but current set count is 1`(variations: Variations, platform: AppPlatform) = runTest {
      val entity = mock<GameCoinGoalSetEntity> {
        on { id } doReturn "setId"
        on { goals } doReturn listOf(GameCoinGoalEntity(123, 100, null, 1))
      }
      gameCoinGoalPersistenceService.mock({ findActiveCoinGoalSet(userId) }, entity)
      val user = userDtoStub.copy(id = userId, appPlatform = platform)
      val gcgVariation = GameCoinGoalSetGenerator.variationsMap[variations]!!
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 3L)
      coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(listOf(123, 321), emptyList()))

      assertTrue { underTest.populateNewGoalIfNeeded(user, gcgVariation) }
      val captor = argumentCaptor<GameCoinGoalEntity>()
      verifyBlocking(gameCoinGoalPersistenceService) { addGoal(eq("setId"), captor.capture()) }
      assertThat(captor.firstValue).all {
        transform { it.gameId }.isEqualTo(123)
      }
    }

    @ParameterizedTest
    @ArgumentsSource(AllVariationsProvider::class)
    fun `SHOULD return played game WHEN it's third set, but current set count is 2`(variations: Variations, platform: AppPlatform) = runTest {
      val entity = mock<GameCoinGoalSetEntity> {
        on { id } doReturn "setId"
        on { goals } doReturn listOf(GameCoinGoalEntity(123, 100, null, 1), GameCoinGoalEntity(123, 100, null, 1))
      }
      gameCoinGoalPersistenceService.mock({ findActiveCoinGoalSet(userId) }, entity)
      val user = userDtoStub.copy(id = userId, appPlatform = platform)
      val gcgVariation = GameCoinGoalSetGenerator.variationsMap[variations]!!
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 3L)

      coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(listOf(123, 321), listOf(456)))

      userService.mock({ loadUserGameCoins(userId) }, emptyMap())

      assertTrue { underTest.populateNewGoalIfNeeded(user, gcgVariation) }
      val captor = argumentCaptor<GameCoinGoalEntity>()
      verifyBlocking(gameCoinGoalPersistenceService) { addGoal(eq("setId"), captor.capture()) }
      assertThat(captor.firstValue).all {
        transform { it.gameId }.isEqualTo(456)
      }
    }

    @ParameterizedTest
    @ArgumentsSource(AllVariationsProvider::class)
    fun `SHOULD fallback to played games with excluded recent WHEN there are no new games`(variations: Variations, platform: AppPlatform) = runTest {
      val user = userDtoStub.copy(id = userId, appPlatform = platform)
      val gcgVariation = GameCoinGoalSetGenerator.variationsMap[variations]!!
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 1L)
      coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(emptyList(), listOf(123, 321)))

      userService.stub {
        onBlocking { loadUserGameCoins(userId) } doReturn mapOf(
          123 to UserPersistenceService.GamePlayStatusDto(
            123,
            0,
            true,
            null,
            now.minus(Duration.ofHours(15))
          )
        )
      }

      assertTrue { underTest.populateNewGoalIfNeeded(user, gcgVariation) }
      val captor = argumentCaptor<GameCoinGoalEntity>()
      verifyBlocking(gameCoinGoalPersistenceService) { addGoal(eq("setId"), captor.capture()) }
      assertThat(captor.firstValue).all {
        transform { it.gameId }.isEqualTo(321) // skipped 123 as it was played recently
      }
    }

    @ParameterizedTest
    @ArgumentsSource(AllVariationsProvider::class)
    fun `SHOULD choose first played game game WHEN there are no new games and no recently played`(variations: Variations, platform: AppPlatform) = runTest {
      val user = userDtoStub.copy(id = userId, appPlatform = platform)
      val gcgVariation = GameCoinGoalSetGenerator.variationsMap[variations]!!
      gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 1L)
      coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(emptyList(), listOf(123, 321)))

      userService.stub {
        onBlocking { loadUserGameCoins(userId) } doReturn mapOf(
          123 to UserPersistenceService.GamePlayStatusDto(
            123,
            0,
            true,
            null,
            now.minus(Duration.ofHours(20))
          ),
          321 to UserPersistenceService.GamePlayStatusDto(
            321,
            0,
            true,
            null,
            now.minus(Duration.ofHours(19))
          ),
        )
      }

      assertTrue { underTest.populateNewGoalIfNeeded(user, gcgVariation) }
      val captor = argumentCaptor<GameCoinGoalEntity>()
      verifyBlocking(gameCoinGoalPersistenceService) { addGoal(eq("setId"), captor.capture()) }
      assertThat(captor.firstValue).all {
        transform { it.gameId }.isEqualTo(123)
      }
    }

    @ParameterizedTest
    @ArgumentsSource(AllVariationsProvider::class)
    fun `SHOULD choose oldest played game game WHEN there are no new games but all games were played recently`(variations: Variations, platform: AppPlatform) =
      runTest {
        val user = userDtoStub.copy(id = userId, appPlatform = platform)
        val gcgVariation = GameCoinGoalSetGenerator.variationsMap[variations]!!
        gameCoinGoalPersistenceService.mock({ getSetsCount(userId) }, 1L)
        coinGoalsGamesProvider.mock({ ios(userId) }, GameCoinGoalsGamesProvider.GameIds(emptyList(), listOf(123, 321)))

        userService.stub {
          onBlocking { loadUserGameCoins(userId) } doReturn mapOf(
            123 to UserPersistenceService.GamePlayStatusDto(
              123,
              0,
              true,
              null,
              now.minus(Duration.ofHours(1))
            ),
            321 to UserPersistenceService.GamePlayStatusDto(
              321,
              0,
              true,
              null,
              now.minus(Duration.ofHours(2))
            ),
          )
        }

        assertTrue { underTest.populateNewGoalIfNeeded(user, gcgVariation) }
        val captor = argumentCaptor<GameCoinGoalEntity>()
        verifyBlocking(gameCoinGoalPersistenceService) { addGoal(eq("setId"), captor.capture()) }
        assertThat(captor.firstValue).all {
          transform { it.gameId }.isEqualTo(321)
        }
      }
  }

  object GenericGoalVariationsProvider : ArgumentsProvider {
    override fun provideArguments(context: ExtensionContext?): Stream<out Arguments> {
      return Stream.of(
        VariationArgument(Variations.IOS_GENERAL_PLUS_TWO_8H, AppPlatform.IOS),
        VariationArgument(Variations.IOS_GENERAL_PLUS_FIVE_8H, AppPlatform.IOS),
        VariationArgument(Variations.IOS_GENERAL_PLUS_TWO_8H, AppPlatform.IOS_WEB),
        VariationArgument(Variations.IOS_GENERAL_PLUS_FIVE_8H, AppPlatform.IOS_WEB),
      )
    }
  }

  object GenericGoalVariationsProviderForFirstSet : ArgumentsProvider {
    override fun provideArguments(context: ExtensionContext?): Stream<out Arguments> {
      return Stream.of(
        VariationArgument(Variations.IOS_GENERAL_PLUS_TWO_8H, AppPlatform.IOS),
        VariationArgument(Variations.IOS_GENERAL_PLUS_FIVE_8H, AppPlatform.IOS),
        VariationArgument(Variations.IOS_GENERAL_ONCE_THEN_3_GAMES_8H, AppPlatform.IOS),
        VariationArgument(Variations.IOS_GENERAL_PLUS_TWO_8H, AppPlatform.IOS_WEB),
        VariationArgument(Variations.IOS_GENERAL_PLUS_FIVE_8H, AppPlatform.IOS_WEB),
        VariationArgument(Variations.IOS_GENERAL_ONCE_THEN_3_GAMES_8H, AppPlatform.IOS_WEB),
      )
    }
  }

  object GameGoalVariationsProvider : ArgumentsProvider {
    override fun provideArguments(context: ExtensionContext?): Stream<out Arguments> {
      return Stream.of(
        VariationArgument(Variations.IOS_THREE_GAMES_8H, AppPlatform.IOS),
        VariationArgument(Variations.IOS_SIX_GAMES_8H, AppPlatform.IOS),
        VariationArgument(Variations.IOS_THREE_GAMES_8H, AppPlatform.IOS_WEB),
        VariationArgument(Variations.IOS_SIX_GAMES_8H, AppPlatform.IOS_WEB),
      )
    }
  }

  object GameGoalVariationsProviderForNonFirstSet : ArgumentsProvider {
    override fun provideArguments(context: ExtensionContext?): Stream<out Arguments> {
      return Stream.of(
        VariationArgument(Variations.IOS_THREE_GAMES_8H, AppPlatform.IOS),
        VariationArgument(Variations.IOS_SIX_GAMES_8H, AppPlatform.IOS),
        VariationArgument(Variations.IOS_GENERAL_ONCE_THEN_3_GAMES_8H, AppPlatform.IOS),
        VariationArgument(Variations.IOS_THREE_GAMES_8H, AppPlatform.IOS_WEB),
        VariationArgument(Variations.IOS_SIX_GAMES_8H, AppPlatform.IOS_WEB),
        VariationArgument(Variations.IOS_GENERAL_ONCE_THEN_3_GAMES_8H, AppPlatform.IOS_WEB),
      )
    }
  }

  object EasyGoalVariationsProvider : ArgumentsProvider {
    override fun provideArguments(context: ExtensionContext?): Stream<out Arguments> {
      return Stream.of(
        VariationArgument(Variations.IOS_GENERAL_PLUS_TWO_EASY_GOALS_8H, AppPlatform.IOS),
        VariationArgument(Variations.IOS_GENERAL_PLUS_TWO_EASY_GOALS_8H, AppPlatform.IOS_WEB),
      )
    }
  }

  object AllVariationsProvider : ArgumentsProvider {
    override fun provideArguments(context: ExtensionContext?): Stream<out Arguments> {
      return Stream.concat(GenericGoalVariationsProvider.provideArguments(context), GameGoalVariationsProvider.provideArguments(context))
        .let { Stream.concat(it, EasyGoalVariationsProvider.provideArguments(context)) }
    }
  }

  data class VariationArgument(val variations: Variations, val platform: AppPlatform) : Arguments {
    override fun get(): Array<Any> = arrayOf(variations, platform)
  }

  companion object {
    val userId = "userId"
  }
}