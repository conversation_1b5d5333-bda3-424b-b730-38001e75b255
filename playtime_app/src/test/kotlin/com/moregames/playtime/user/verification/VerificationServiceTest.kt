package com.moregames.playtime.user.verification

import assertk.assertThat
import assertk.assertions.*
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.read.ListAppender
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCurrencyEarnings
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserEarningsWithOfferwallAmount
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.app.PaymentProviderType.TREMENDOUS_PAYPAL
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.*
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.encryption.HashService
import com.moregames.base.exceptions.GpsVerificationFailedException
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.messaging.dto.UserFaceVerifiedEventDto
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.base.util.throwException
import com.moregames.playtime.administration.user.FacetecFacesService
import com.moregames.playtime.app.forbidIosBipaCashoutWithFaceVerification
import com.moregames.playtime.buseffects.SendLocationCheckedEventEffect
import com.moregames.playtime.checks.IpService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.ios.dto.IosExaminationEnv
import com.moregames.playtime.ios.examination.IosExaminationService
import com.moregames.playtime.ios.examination.dto.IosExaminationRequestApiDto
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.translations.TranslationResource.EXCEPTION_LIVENESS_SELFIE_TIPS_EXPERIMENT
import com.moregames.playtime.translations.TranslationResource.EXCEPTION_UNIQUENESS
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.EmailValidationService
import com.moregames.playtime.user.UserCheckManager
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.cashout.exception.VerificationIncompleteException
import com.moregames.playtime.user.exception.AttestationStatementExaminationFailedException
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.verification.VerificationPersistenceService.VerificationData
import com.moregames.playtime.user.verification.VerificationService.Companion.UNTRUSTED_DEVICES_UA_NETWORK
import com.moregames.playtime.user.verification.VerificationService.VerificationStatusExt
import com.moregames.playtime.user.verification.dto.*
import com.moregames.playtime.user.verification.dto.VerificationResultType.*
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationStatus
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationStatus.REQUIRED
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationStatus.VERIFIED
import com.moregames.playtime.user.verification.dto.VerificationSessionDto.VerificationType.*
import com.moregames.playtime.user.verification.email.SeonService
import com.moregames.playtime.user.verification.exception.FaceLivenessCheckFailedException
import com.moregames.playtime.user.verification.exception.FaceUniquenessCheckFailedException
import com.moregames.playtime.user.verification.exception.JailBreakUsageException
import com.moregames.playtime.user.verification.exception.SessionExpiredException
import com.moregames.playtime.utils.ES_LOCALE
import com.moregames.playtime.utils.FR_LOCALE
import com.moregames.playtime.utils.adjustInstallationStub
import com.moregames.playtime.utils.userDtoStub
import com.moregames.playtime.web.WebUserService
import com.moregames.playtime.webhook.adjust.AdjustEventPersistenceService
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

@OptIn(ExperimentalCoroutinesApi::class)
class VerificationServiceTest {
  private val verificationPersistenceService: VerificationPersistenceService = mock {
    onBlocking { userVerifiedBy(any(), any()) } doReturn false
  }
  private val facetecClient: FacetecClient = mock {
    onBlocking { initiateSession() } doReturn SESSION_ID
  }
  private val userService: UserService = mock {
    onBlocking { userExists(USER_ID) } doReturn true
    onBlocking { getUser(USER_ID) } doReturn userDtoStub.copy(id = USER_ID)
  }
  private val timeService: TimeService = mock()
  private val userCheckManager: UserCheckManager = mock()
  private val fraudScoreService: FraudScoreService = mock {
    onBlocking { isUserSharedEmail(any()) } doReturn false
  }
  private val translationService: UserTranslationService = mock()
  private val facetecFacesService: FacetecFacesService = mock()
  private val cashoutService: CashoutService = mock()
  private val iosExaminationService: IosExaminationService = mock()
  private val marketService: MarketService = mock()
  private val rewardingFacade: RewardingFacade = mock()
  private val seonService: SeonService = mock()
  private val abTestingService: AbTestingService = mock()
  private val adjustEventPersistenceService: AdjustEventPersistenceService = mock()
  private val testScope = TestScope()
  private val faceFraudstersCounter = mock<FaceFraudstersCounter> {
    on { counterLimit } doReturn 10L
  }
  private val faceFraudstersPersistenceService = mock<FaceFraudstersPersistenceService> {
    onBlocking { isFraudster(any()) } doReturn false
  }
  private val logEntriesListAppender = ListAppender<ILoggingEvent>()
  private val localLogger = LoggerFactory.getLogger(VerificationService::class.java) as Logger
  private val associatedUsersService: AssociatedUsersService = mock()
  private val hashService: HashService = mock()
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()
  private val messageBus: MessageBus = mock()
  private val featureFlagsFacade: FeatureFlagsFacade = mock()
  private val ipService: IpService = mock()
  private val encryptionService: EncryptionService = mock()
  private val emailValidationService: EmailValidationService = mock()
  private val webUserService: WebUserService = mock()

  private val service = VerificationService(
    verificationPersistenceService = verificationPersistenceService,
    userService = userService,
    facetecClient = facetecClient,
    timeService = timeService,
    userCheckManager = userCheckManager,
    fraudScoreService = fraudScoreService,
    translationService = translationService,
    facetecFacesService = facetecFacesService,
    bigQueryEventPublisher = bigQueryEventPublisher,
    cashoutService = cashoutService,
    iosExaminationService = iosExaminationService,
    marketService = marketService,
    abTestingService = abTestingService,
    rewardingFacade = rewardingFacade,
    seonService = seonService,
    buildVariant = BuildVariant.TEST,
    faceFraudstersCounter = faceFraudstersCounter,
    faceFraudstersPersistenceService = faceFraudstersPersistenceService,
    associatedUsersService = associatedUsersService,
    hashService = hashService,
    messageBus = messageBus,
    featureFlagsFacade = featureFlagsFacade,
    ipService = ipService,
    encryptionService = encryptionService,
    emailValidationService = emailValidationService,
    webUserService = webUserService,
    adjustEventPersistenceService = adjustEventPersistenceService
  )

  private companion object {
    const val USER_ID = "userId"
    const val ADJUST_ID = "adjustId"
    const val SESSION_ID = "sessionId"
    const val USER_AGENT = "userAgent"
    const val EXCEPTION_FR_TRANSLATION = "Essayez de nettoyer délicatement votre appareil photo avec un chiffon sec\n" +
      "Assurez-vous qu'il y a suffisamment de lumière autour de vous, de préférence la lumière du jour, mais évitez toute lumière directe sur la caméra et les reflets\n" +
      "Placez votre téléphone sur une surface stable à hauteur de vos yeux\n" +
      "Essayez d'enlever vos lunettes si vous en portez"
    const val EMAIL_HASH = "EMAIL_HASH"
    const val NORMALIZED_EMAIL_HASH = "normalizedEMAIL_HASH"
    val now: Instant = Instant.now()
    val androidAppVersion = AppVersionDto(platform = ANDROID, version = 42)
    val iosAppVersion = AppVersionDto(platform = IOS, version = 134)
  }

  @BeforeEach
  fun before() {
    whenever(timeService.now()).thenReturn(now)
    verificationPersistenceService.mock({ getUsersWithSameTrackingIdVerifiedBy(any(), any()) }, emptyList())
    translationService.mock(
      { translateOrDefault(EXCEPTION_LIVENESS_SELFIE_TIPS_EXPERIMENT, ES_LOCALE, USER_ID) },
      EXCEPTION_LIVENESS_SELFIE_TIPS_EXPERIMENT.defaultValue
    )
    translationService.mock(
      { translateOrDefault(EXCEPTION_LIVENESS_SELFIE_TIPS_EXPERIMENT, FR_LOCALE, USER_ID) },
      EXCEPTION_FR_TRANSLATION
    )
    translationService.mock(
      { translateOrDefault(EXCEPTION_UNIQUENESS, ES_LOCALE, USER_ID) },
      EXCEPTION_UNIQUENESS.defaultValue
    )
    cashoutService.mock({ userHasSuccessfulCashout(USER_ID) }, true)
    webUserService.mock({ jailBreakCheckPassed(USER_ID) }, true)
    userService.mock({ getAppVersion(USER_ID) }, AppVersionDto(platform = ANDROID, version = 42))
    abTestingService.mock({ isSeonIntegrationParticipant(USER_ID) }, false)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GPS_VERIFICATION) }, DEFAULT)
    logEntriesListAppender.start()
    localLogger.addAppender(logEntriesListAppender)
    associatedUsersService.mock({ check(any(), any()) }, AssociatedUsersCheckResult.PASSED)
    whenever(hashService.emailSha256(any())).thenReturn(EMAIL_HASH)
    featureFlagsFacade.mock({ forbidIosBipaCashoutWithFaceVerification() }, false)
    ipService.mock({ isFromBlockedBipaState(any()) }, false)
    adjustEventPersistenceService.mock({ loadLastAdjustInstallationByAdjustId(any()) }, adjustInstallationStub)
    userService.mock({ getAdjustId(any()) }, ADJUST_ID)
  }

  @AfterEach
  fun after() {
    localLogger.detachAppender(logEntriesListAppender)
    logEntriesListAppender.stop()
  }

  @Test
  fun `SHOULD skip session creation ON initiateVerification WHEN verification is stubbed`() = testScope.runTest {
    verificationPersistenceService.mock({ findActiveAndVerifiedSession(USER_ID) }, "stubSessionId" to now.plus(5, ChronoUnit.MINUTES))

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    assertThat(actual).isEqualTo(
      VerificationSessionDto(
        sessionId = "stubSessionId",
        userId = USER_ID,
        expiredAt = now.plus(5, ChronoUnit.MINUTES),
        verification = emptyList()
      )
    )
    verifyBlocking(verificationPersistenceService, times(1)) { findActiveAndVerifiedSession(USER_ID) }
    verifyNoMoreInteractions(verificationPersistenceService)
    verifyNoInteractions(facetecClient)
    verifyNoInteractions(fraudScoreService)
  }

  @Test
  fun `SHOULD create session ON initiateVerification WHEN verification is not needed (with facetec as default)`() = testScope.runTest {
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, true)

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    assertThat(actual).isEqualToIgnoringGivenProperties(
      VerificationSessionDto(
        sessionId = "any",
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = emptyList()
      ),
      VerificationSessionDto::sessionId
    )

    verifyBlocking(verificationPersistenceService) { createSession(actual) }
    verifyNoInteractions(facetecClient)
  }

  @Test
  fun `SHOULD create session based on facetec session ON initiateVerification WHEN face verification required coz of fraud`() = testScope.runTest {
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, true)
    faceFraudstersPersistenceService.mock({ isFraudster(USER_ID) }, true)

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    assertThat(actual).isEqualTo(
      VerificationSessionDto(
        sessionId = SESSION_ID,
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = listOf(
          VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1)
        )
      )
    )
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
    verifyBlocking(facetecClient) { initiateSession() }
  }

  @Test
  fun `SHOULD create session based on facetec session ON initiateVerification WHEN face verification required coz of absence of adjustId`() =
    testScope.runTest {
      userService.mock({ getAdjustId(USER_ID) }, null)
      verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)

      val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

      assertThat(actual).isEqualTo(
        VerificationSessionDto(
          sessionId = SESSION_ID,
          userId = USER_ID,
          expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
          verification = listOf(
            VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1)
          )
        )
      )

      verifyBlocking(userService) { getAdjustId(USER_ID) }
      verifyBlocking(verificationPersistenceService) { createSession(actual) }
      verifyBlocking(facetecClient) { initiateSession() }
    }

  @Test
  fun `SHOULD create session based on facetec session ON initiateVerification WHEN face verification required coz of unknown UA network`() = testScope.runTest {
    userService.mock({ getAdjustId(USER_ID) }, ADJUST_ID)
    adjustEventPersistenceService.mock({ loadLastAdjustInstallationByAdjustId(any()) }, null)
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    assertThat(actual).isEqualTo(
      VerificationSessionDto(
        sessionId = SESSION_ID,
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = listOf(
          VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1)
        )
      )
    )

    verifyBlocking(userService) { getAdjustId(USER_ID) }
    verifyBlocking(adjustEventPersistenceService) { loadLastAdjustInstallationByAdjustId(ADJUST_ID) }
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
    verifyBlocking(facetecClient) { initiateSession() }
  }

  @Test
  fun `SHOULD not request validation ON initiateVerification WHEN no adjust data AND face verification already done`() =
    testScope.runTest {
      userService.mock({ getAdjustId(USER_ID) }, ADJUST_ID)
      adjustEventPersistenceService.mock({ loadLastAdjustInstallationByAdjustId(any()) }, null)
      verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, true)

      val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

      assertThat(actual).isEqualTo(
        VerificationSessionDto(
          sessionId = actual.sessionId,
          userId = USER_ID,
          expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
          verification = emptyList()
        )
      )

      verifyBlocking(userService) { getAdjustId(USER_ID) }
      verifyBlocking(adjustEventPersistenceService) { loadLastAdjustInstallationByAdjustId(ADJUST_ID) }
      verifyBlocking(verificationPersistenceService) { createSession(actual) }
      verifyBlocking(verificationPersistenceService, times(2)) { userVerifiedBy(USER_ID, FACE) }
      verifyNoInteractions(facetecClient)
    }


  @Test
  fun `SHOULD create session based on facetec session ON initiateVerification WHEN face verification required coz of untrusted devices UA network`() =
    testScope.runTest {
      userService.mock({ getAdjustId(USER_ID) }, ADJUST_ID)
      adjustEventPersistenceService.mock({ loadLastAdjustInstallationByAdjustId(any()) }, adjustInstallationStub.copy(adNetwork = UNTRUSTED_DEVICES_UA_NETWORK))
      verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)

      val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

      assertThat(actual).isEqualTo(
        VerificationSessionDto(
          sessionId = SESSION_ID,
          userId = USER_ID,
          expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
          verification = listOf(
            VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1)
          )
        )
      )

      verifyBlocking(userService) { getAdjustId(USER_ID) }
      verifyBlocking(adjustEventPersistenceService) { loadLastAdjustInstallationByAdjustId(ADJUST_ID) }
      verifyBlocking(verificationPersistenceService) { createSession(actual) }
      verifyBlocking(facetecClient) { initiateSession() }
    }

  @Test
  fun `SHOULD create session based on facetec session ON initiateVerification WHEN face verification required`() = testScope.runTest {
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    assertThat(actual).isEqualTo(
      VerificationSessionDto(
        sessionId = SESSION_ID,
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = listOf(
          VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1)
        )
      )
    )
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
    verifyBlocking(facetecClient) { initiateSession() }
  }

  @Test
  fun `SHOULD fail ON initiateVerification WHEN face verification required AND platform is ios AND ff is enabled AND user is from bipa state AND NOT version is 135`() =
    testScope.runTest {
      val lowVersion = iosAppVersion.copy(version = 134)

      verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
      featureFlagsFacade.mock({ forbidIosBipaCashoutWithFaceVerification() }, true)
      ipService.mock({ isFromBlockedBipaState("********") }, true)

      assertThrows<IllegalStateException> { service.initiateVerification(USER_ID, userIp = "********", appVersion = lowVersion) }
    }

  @Test
  fun `SHOULD create session based on facetec ON initiateVerification WHEN face verification required AND platform is ios AND ff is enabled AND user is from bipa state AND version is 135`() =
    testScope.runTest {
      val highVersion = iosAppVersion.copy(version = 135)

      verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
      featureFlagsFacade.mock({ forbidIosBipaCashoutWithFaceVerification() }, true)
      ipService.mock({ isFromBlockedBipaState("********") }, true)

      service.initiateVerification(USER_ID, userIp = "********", appVersion = highVersion)
    }

  @Test
  fun `SHOULD create session based on facetec ON initiateVerification WHEN face verification required AND NOT platform is ios AND ff is enabled AND user is from bipa state`() =
    testScope.runTest {
      verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
      featureFlagsFacade.mock({ forbidIosBipaCashoutWithFaceVerification() }, true)
      ipService.mock({ isFromBlockedBipaState("********") }, true)

      service.initiateVerification(USER_ID, userIp = "********", appVersion = androidAppVersion)
    }

  @Test
  fun `SHOULD create session based on facetec ON initiateVerification WHEN face verification required AND platform is ios AND NOT ff is enabled AND user is from bipa state AND NOT version is 135`() =
    testScope.runTest {
      verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
      featureFlagsFacade.mock({ forbidIosBipaCashoutWithFaceVerification() }, false)
      ipService.mock({ isFromBlockedBipaState("********") }, true)

      service.initiateVerification(USER_ID, userIp = "********", appVersion = iosAppVersion)
    }

  @Test
  fun `SHOULD create session based on facetec ON initiateVerification WHEN face verification required AND platform is ios AND ff is enabled AND NOT user is from bipa state AND NOT version is 135`() =
    testScope.runTest {
      verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
      featureFlagsFacade.mock({ forbidIosBipaCashoutWithFaceVerification() }, true)
      ipService.mock({ isFromBlockedBipaState("********") }, false)

      service.initiateVerification(USER_ID, userIp = "********", appVersion = iosAppVersion)
    }

  @Test
  fun `SHOULD create session based on facetec session ON initiateVerification WHEN platform is iOS and first cashout`() = testScope.runTest {
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    cashoutService.mock({ userHasSuccessfulCashout(USER_ID) }, false)

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = iosAppVersion)

    assertThat(actual).isEqualTo(
      VerificationSessionDto(
        sessionId = SESSION_ID,
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = listOf(
          VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1),
          VerificationSessionDto.VerificationStep(EXAMINATION, REQUIRED, 2),
          VerificationSessionDto.VerificationStep(JAIL_BREAK, REQUIRED, 3),
          VerificationSessionDto.VerificationStep(LOCATION, REQUIRED, 4),
        )
      )
    )
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
    verifyBlocking(facetecClient) { initiateSession() }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD create session with jailBreak step ON initiateVerification WHEN platform is iOS WEB and jail break check not passed`(jailBreakPassed: Boolean) =
    testScope.runTest {
      verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
      webUserService.mock({ jailBreakCheckPassed(USER_ID) }, jailBreakPassed)

      val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = iosAppVersion.copy(platform = IOS_WEB))

      assertThat(actual).isEqualTo(
        VerificationSessionDto(
          sessionId = SESSION_ID,
          userId = USER_ID,
          expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
          verification = listOfNotNull(
            VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1),
            VerificationSessionDto.VerificationStep(JAIL_BREAK, REQUIRED, 3).takeIf { !jailBreakPassed },
          )
        )
      )
      verifyBlocking(verificationPersistenceService) { createSession(actual) }
      verifyBlocking(facetecClient) { initiateSession() }
    }

  @ParameterizedTest
  @CsvSource("active", "earnings10", "earnings20", "ofwRevOnly")
  fun `SHOULD create session WITH gps verification ON initiateVerification WHEN platform is ANDROID AND first cashout AND location experiment active AND client version is high enough`(
    assignedVariation: String
  ) = testScope.runTest {
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    cashoutService.mock({ userHasSuccessfulCashout(USER_ID) }, false)
    abTestingService.mock(
      { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GPS_VERIFICATION) },
      Variations.byKeys(ClientExperiment.ANDROID_GPS_VERIFICATION, assignedVariation)
    )
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(amountUsd = BigDecimal.valueOf(25), Currency.getInstance("USD"), BigDecimal.valueOf(25))
    )
    rewardingFacade.mock({ getNonCashedUserEarningsWithOfferwallAmount(USER_ID) }, UserEarningsWithOfferwallAmount(BigDecimal("5.0"), BigDecimal("5.0")))
    userService.mock({ getAppVersion(USER_ID) }, AppVersionDto(platform = ANDROID, version = 50))

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    assertThat(actual).isEqualTo(
      VerificationSessionDto(
        sessionId = SESSION_ID,
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = listOf(
          VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1),
          VerificationSessionDto.VerificationStep(LOCATION, REQUIRED, 4),
        )
      )
    )
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
    verifyBlocking(facetecClient) { initiateSession() }
  }

  @ParameterizedTest
  @CsvSource("earnings10", "earnings20", "ofwRevOnly")
  fun `SHOULD create session WITHOUT gps verification ON initiateVerification WHEN platform is ANDROID AND first cashout AND location experiment active but user don't match exp conditions AND client version is high enough`(
    assignedVariation: String
  ) = testScope.runTest {
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    cashoutService.mock({ userHasSuccessfulCashout(USER_ID) }, false)
    abTestingService.mock(
      { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GPS_VERIFICATION) },
      Variations.byKeys(ClientExperiment.ANDROID_GPS_VERIFICATION, assignedVariation)
    )
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(amountUsd = BigDecimal.valueOf(5), Currency.getInstance("USD"), BigDecimal.valueOf(5))
    )
    rewardingFacade.mock({ getNonCashedUserEarningsWithOfferwallAmount(USER_ID) }, UserEarningsWithOfferwallAmount(BigDecimal("5.0"), BigDecimal("0.0")))
    userService.mock({ getAppVersion(USER_ID) }, AppVersionDto(platform = ANDROID, version = 50))

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    assertThat(actual).isEqualTo(
      VerificationSessionDto(
        sessionId = SESSION_ID,
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = listOf(
          VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1),
        )
      )
    )
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
    verifyBlocking(facetecClient) { initiateSession() }
  }

  @ParameterizedTest
  @CsvSource("11.0,11.0,true", "5.0,11.0,false", "0.0,11.0,false", "0.0,0.0,false")
  fun `SHOULD create session WITH gps verification ON initiateVerification WHEN platform is ANDROID and location experiment is on offerwall check DEPENDING on user offerwall amount`(
    offerwallRevenue: String, totalRevenue: String, isGpsCheckRequired: Boolean,
  ) = testScope.runTest {
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    cashoutService.mock({ userHasSuccessfulCashout(USER_ID) }, false)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GPS_VERIFICATION) }, Variations.OFFERWALL_REVENUE_ONLY)
    rewardingFacade.mock(
      { getNonCashedUserEarningsWithOfferwallAmount(USER_ID) },
      UserEarningsWithOfferwallAmount(totalRevenue = BigDecimal(totalRevenue), offerwallRevenue = BigDecimal(offerwallRevenue))
    )
    userService.mock({ getAppVersion(USER_ID) }, AppVersionDto(platform = ANDROID, version = 50))

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    val faceStep = VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1)
    val gpsStep = VerificationSessionDto.VerificationStep(LOCATION, REQUIRED, 4)
    assertThat(actual).isEqualTo(
      VerificationSessionDto(
        sessionId = SESSION_ID,
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = if (isGpsCheckRequired) listOf(faceStep, gpsStep) else listOf(faceStep)
      )
    )
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
    verifyBlocking(facetecClient) { initiateSession() }
  }

  @ParameterizedTest
  @CsvSource("35.0,true", "30.0,true", "29.99,false", "0.0,false")
  fun `SHOULD create session WITH gps verification ON initiateVerification WHEN platform is ANDROID and location experiment is on REVENUE_ABOVE_30 variation`(
    last2DaysRevenue: String,
    isGpsCheckRequired: Boolean
  ) = testScope.runTest {
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    cashoutService.mock({ userHasSuccessfulCashout(USER_ID) }, true)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GPS_VERIFICATION) }, Variations.REVENUE_ABOVE_30)
    rewardingFacade.mock({ getUserRevenueLast2Days(USER_ID) }, BigDecimal(last2DaysRevenue))
    userService.mock({ getAppVersion(USER_ID) }, AppVersionDto(platform = ANDROID, version = 50))

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    val faceStep = VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1)
    val gpsStep = VerificationSessionDto.VerificationStep(LOCATION, REQUIRED, 4)

    assertThat(actual).isEqualTo(
      VerificationSessionDto(
        sessionId = SESSION_ID,
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = if (isGpsCheckRequired) listOf(faceStep, gpsStep) else listOf(faceStep)
      )
    )

    verifyBlocking(verificationPersistenceService) { createSession(actual) }
    verifyBlocking(facetecClient) { initiateSession() }
  }

  @Test
  fun `SHOULD create session WITH gps verification ON initiateVerification WHEN platform is ANDROID and variation REVENUE_ABOVE_30 ignore successful cashouts`() =
    testScope.runTest {
      verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
      cashoutService.mock({ userHasSuccessfulCashout(USER_ID) }, true)
      abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GPS_VERIFICATION) }, Variations.REVENUE_ABOVE_30)
      rewardingFacade.mock({ getUserRevenueLast2Days(USER_ID) }, BigDecimal("50.0"))
      userService.mock({ getAppVersion(USER_ID) }, AppVersionDto(platform = ANDROID, version = 50))

      val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

      val faceStep = VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1)
      val gpsStep = VerificationSessionDto.VerificationStep(LOCATION, REQUIRED, 4)

      assertThat(actual).isEqualTo(
        VerificationSessionDto(
          sessionId = SESSION_ID,
          userId = USER_ID,
          expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
          verification = listOf(faceStep, gpsStep) // GPS required despite successful cashout
        )
      )

      verifyBlocking(verificationPersistenceService) { createSession(actual) }
      verifyBlocking(facetecClient) { initiateSession() }
    }

  @Test
  fun `SHOULD create session WITHOUT gps verification ON initiateVerification WHEN platform is IOS and user has successful cashout`() = testScope.runTest {
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    cashoutService.mock({ userHasSuccessfulCashout(USER_ID) }, true)
    userService.mock({ getAppVersion(USER_ID) }, AppVersionDto(platform = IOS, version = 50))

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = iosAppVersion)

    val faceStep = VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1)

    assertThat(actual).isEqualTo(
      VerificationSessionDto(
        sessionId = SESSION_ID,
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = listOf(faceStep)
      )
    )

    verifyBlocking(verificationPersistenceService) { createSession(actual) }
    verifyBlocking(facetecClient) { initiateSession() }
  }

  @Test
  fun `SHOULD create session based on facetec session ON initiateVerification WHEN platform is iOS and not first cashout`() = testScope.runTest {
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    assertThat(actual).isEqualTo(
      VerificationSessionDto(
        sessionId = SESSION_ID,
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = listOf(
          VerificationSessionDto.VerificationStep(FACE, REQUIRED, 1),
        )
      )
    )
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
    verifyBlocking(facetecClient) { initiateSession() }
  }

  @Test
  fun `SHOULD check liveness and uniqueness and then verify user ON verifyFace`() = testScope.runTest {
    val expected = VerifyFaceApiResponseDto(scanResultBlob = "scanResultBlob")
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    facetecClient.mock({ checkUniqueness(SESSION_ID, USER_AGENT, USER_ID) }, UniquenessCheckResultDto(true, emptyList(), emptyList()))
    facetecClient.mock(
      { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }, VerifyFaceLivenessResponseDto(
        success = true, error = false, errorMessage = null,
        scanResultBlob = "scanResultBlob",
        faceScanSecurityChecks = VerifyFaceLivenessResponseDto.FaceScanSecurityChecks(
          faceScanLivenessCheckSucceeded = true,
          auditTrailVerificationCheckSucceeded = true,
          replayCheckSucceeded = true,
          sessionTokenCheckSucceeded = true
        )
      )
    )

    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = ES_LOCALE))
    val result = service.verifyFace(SESSION_ID, USER_AGENT, request)

    assertThat(result).isEqualTo(expected)
    verifyBlocking(facetecClient) { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }
    verifyBlocking(facetecClient) { checkUniqueness(SESSION_ID, USER_AGENT, USER_ID) }
    verifyBlocking(facetecClient) { saveToDatabase(SESSION_ID, USER_AGENT, USER_ID) }
    verifyBlocking(verificationPersistenceService) { verifyUserSessionBy(SESSION_ID, FACE) }
    verifyBlocking(bigQueryEventPublisher) { publish(UserFaceVerifiedEventDto(userId = USER_ID, createdAt = now)) }
    verifyBlocking(userCheckManager) { onUserFaceVerified(USER_ID) }
  }

  @Test
  fun `SHOULD check liveness and uniqueness and then successfully verify user ON verifyFace WHEN associated users check passed`() = testScope.runTest {
    val associatedUsers = listOf("anotherUserId")
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = ES_LOCALE))
    val expected = VerifyFaceApiResponseDto(scanResultBlob = "scanResultBlob")
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    facetecClient.mock(
      { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }, VerifyFaceLivenessResponseDto(
        success = true, error = false, errorMessage = null,
        scanResultBlob = "scanResultBlob",
        faceScanSecurityChecks = VerifyFaceLivenessResponseDto.FaceScanSecurityChecks(
          faceScanLivenessCheckSucceeded = true,
          auditTrailVerificationCheckSucceeded = true,
          replayCheckSucceeded = true,
          sessionTokenCheckSucceeded = true
        )
      )
    )
    facetecClient.mock({ checkUniqueness(SESSION_ID, USER_AGENT, USER_ID) }, UniquenessCheckResultDto(false, associatedUsers, emptyList()))

    val result = service.verifyFace(SESSION_ID, USER_AGENT, request)

    assertThat(result).isEqualTo(expected)
    verifyBlocking(facetecClient) { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }
    verifyBlocking(facetecClient) { checkUniqueness(SESSION_ID, USER_AGENT, USER_ID) }
    verifyBlocking(facetecClient) { saveToDatabase(SESSION_ID, USER_AGENT, USER_ID) }
    verifyBlocking(associatedUsersService) { check(USER_ID, associatedUsers) }
    verifyBlocking(verificationPersistenceService) { verifyUserSessionBy(SESSION_ID, FACE) }
    verifyBlocking(bigQueryEventPublisher) { publish(UserFaceVerifiedEventDto(userId = USER_ID, createdAt = now)) }
    verifyBlocking(userCheckManager) { onUserFaceVerified(USER_ID) }
    verifyBlocking(verificationPersistenceService) { saveAssociatedUsers(SESSION_ID, associatedUsers) }
    verifyBlocking(verificationPersistenceService, never()) { saveAssociatedUsersCheckResults(any(), any()) }
  }

  @Test
  fun `SHOULD throw FaceLivenessCheckFailedException with custom message and track error ON verifyFace WHEN face is not live`() = testScope.runTest {
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = FR_LOCALE))
    facetecClient.mock(
      { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }, VerifyFaceLivenessResponseDto(
        success = true, error = false, errorMessage = null,
        faceScanSecurityChecks = VerifyFaceLivenessResponseDto.FaceScanSecurityChecks(
          faceScanLivenessCheckSucceeded = false,
          auditTrailVerificationCheckSucceeded = true,
          replayCheckSucceeded = true,
          sessionTokenCheckSucceeded = true
        )
      )
    )

    assertFailsWith(FaceLivenessCheckFailedException::class) {
      service.verifyFace(SESSION_ID, USER_AGENT, request)
    }.let {
      assertThat(it.externalMessage).isEqualTo(EXCEPTION_FR_TRANSLATION)
    }

    verifyBlocking(verificationPersistenceService) { sessionExpired(SESSION_ID) }
    verifyBlocking(verificationPersistenceService) { getUserIdBySessionId(SESSION_ID) }
    verifyBlocking(verificationPersistenceService) {
      addErrorMessageToSession(
        SESSION_ID, FACE, LIVENESS_CHECK_FAILED, "Liveness check is failed for user $USER_ID"
      )
    }
    verifyNoMoreInteractions(verificationPersistenceService)
  }

  @Test
  fun `SHOULD throw FaceLivenessCheckFailedException with custom message, default to en and track error ON verifyFace WHEN face is not live`() =
    testScope.runTest {
      val request = VerifyFaceLivenessRequestDto(
        faceScan = "faceScanBase64",
        auditTrailImage = "auditTrailImageBase64",
        lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
      )
      verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
      verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
      userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = ES_LOCALE))
      facetecClient.mock(
        { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }, VerifyFaceLivenessResponseDto(
          success = true, error = false, errorMessage = null,
          faceScanSecurityChecks = VerifyFaceLivenessResponseDto.FaceScanSecurityChecks(
            faceScanLivenessCheckSucceeded = false,
            auditTrailVerificationCheckSucceeded = true,
            replayCheckSucceeded = true,
            sessionTokenCheckSucceeded = true
          )
        )
      )

      assertFailsWith(FaceLivenessCheckFailedException::class) {
        service.verifyFace(SESSION_ID, USER_AGENT, request)
      }.let {
        assertThat(it.externalMessage).isEqualTo(EXCEPTION_LIVENESS_SELFIE_TIPS_EXPERIMENT.defaultValue)
      }

      verifyBlocking(verificationPersistenceService) { sessionExpired(SESSION_ID) }
      verifyBlocking(verificationPersistenceService) { getUserIdBySessionId(SESSION_ID) }
      verifyBlocking(verificationPersistenceService) {
        addErrorMessageToSession(
          SESSION_ID, FACE, LIVENESS_CHECK_FAILED, "Liveness check is failed for user $USER_ID"
        )
      }
      verifyNoMoreInteractions(verificationPersistenceService)
    }

  @Test
  fun `SHOULD throw FaceLivenessCheckFailedException with custom message, translate it and track error ON verifyFace WHEN face is not live AND user is participant to experiment`() =
    testScope.runTest {
      val request = VerifyFaceLivenessRequestDto(
        faceScan = "faceScanBase64",
        auditTrailImage = "auditTrailImageBase64",
        lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
      )
      verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
      verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
      userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = FR_LOCALE))
      facetecClient.mock(
        { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }, VerifyFaceLivenessResponseDto(
          success = true, error = false, errorMessage = null,
          faceScanSecurityChecks = VerifyFaceLivenessResponseDto.FaceScanSecurityChecks(
            faceScanLivenessCheckSucceeded = false,
            auditTrailVerificationCheckSucceeded = true,
            replayCheckSucceeded = true,
            sessionTokenCheckSucceeded = true
          )
        )
      )

      assertFailsWith(FaceLivenessCheckFailedException::class) {
        service.verifyFace(SESSION_ID, USER_AGENT, request)
      }.let {
        assertThat(it.externalMessage).isEqualTo(EXCEPTION_FR_TRANSLATION)
      }

      verifyBlocking(verificationPersistenceService) { sessionExpired(SESSION_ID) }
      verifyBlocking(verificationPersistenceService) { getUserIdBySessionId(SESSION_ID) }
      verifyBlocking(verificationPersistenceService) {
        addErrorMessageToSession(
          SESSION_ID, FACE, LIVENESS_CHECK_FAILED, "Liveness check is failed for user $USER_ID"
        )
      }
      verifyNoMoreInteractions(verificationPersistenceService)
    }

  @Test
  fun `SHOULD check liveness and uniqueness and then verify user ON verifyFace WHEN user is fraud AND user in whitelist`() = testScope.runTest {
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    val expected = VerifyFaceApiResponseDto(scanResultBlob = "scanResultBlob")
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    facetecClient.mock({ checkUniqueness(SESSION_ID, USER_AGENT, USER_ID) }, UniquenessCheckResultDto(true, emptyList(), emptyList()))
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, isWhitelisted = true))
    facetecClient.mock(
      { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }, VerifyFaceLivenessResponseDto(
        success = true, error = false, errorMessage = null,
        scanResultBlob = "scanResultBlob",
        faceScanSecurityChecks = VerifyFaceLivenessResponseDto.FaceScanSecurityChecks(
          faceScanLivenessCheckSucceeded = true,
          auditTrailVerificationCheckSucceeded = true,
          replayCheckSucceeded = true,
          sessionTokenCheckSucceeded = true
        )
      )
    )
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = ES_LOCALE))

    val result = service.verifyFace(SESSION_ID, USER_AGENT, request)

    assertThat(result).isEqualTo(expected)
    verifyBlocking(facetecClient) { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }
    verifyBlocking(facetecClient) { checkUniqueness(SESSION_ID, USER_AGENT, USER_ID) }
    verifyBlocking(facetecClient) { saveToDatabase(SESSION_ID, USER_AGENT, USER_ID) }
    verifyBlocking(verificationPersistenceService) { verifyUserSessionBy(SESSION_ID, FACE) }
    verifyBlocking(bigQueryEventPublisher) { publish(UserFaceVerifiedEventDto(userId = USER_ID, createdAt = now)) }
    verifyBlocking(userCheckManager) { onUserFaceVerified(USER_ID) }
    verifyNoInteractions(faceFraudstersCounter, faceFraudstersPersistenceService)
  }

  @Test
  fun `SHOULD throw FaceUniquenessCheckFailedException and track error ON verifyFace WHEN face is not unique and associated users check failed`() =
    testScope.runTest {
      val request = VerifyFaceLivenessRequestDto(
        faceScan = "faceScanBase64",
        auditTrailImage = "auditTrailImageBase64",
        lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
      )
      whenever(timeService.now()).thenReturn(now)
      verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
      verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
      facetecClient.mock({ checkUniqueness(SESSION_ID, USER_AGENT, USER_ID) }, UniquenessCheckResultDto(false, listOf("anotherUserId"), emptyList()))
      userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = ES_LOCALE))
      facetecClient.mock(
        { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }, VerifyFaceLivenessResponseDto(
          success = true, error = false, errorMessage = null,
          faceScanSecurityChecks = VerifyFaceLivenessResponseDto.FaceScanSecurityChecks(
            faceScanLivenessCheckSucceeded = true,
            auditTrailVerificationCheckSucceeded = true,
            replayCheckSucceeded = true,
            sessionTokenCheckSucceeded = true
          )
        )
      )
      associatedUsersService.mock({ check(USER_ID, listOf("anotherUserId")) }, AssociatedUsersCheckResult.TOO_EARLY_WITH_NEW_TRACKING_ID)

      assertFailsWith(FaceUniquenessCheckFailedException::class) {
        service.verifyFace(SESSION_ID, USER_AGENT, request)
      }.let {
        assertThat(it.externalMessage).isEqualTo("You have already been associated with another JustPlay account in the past. Please verify another person for this cash-out")
      }

      verifyBlocking(verificationPersistenceService) { sessionExpired(SESSION_ID) }
      verifyBlocking(verificationPersistenceService) { getUserIdBySessionId(SESSION_ID) }
      verifyBlocking(verificationPersistenceService) {
        addErrorMessageToSession(
          SESSION_ID, FACE, TOO_EARLY_WITH_NEW_TRACKING_ID, "Uniqueness check is failed for user $USER_ID"
        )
      }
      verifyBlocking(userCheckManager) { onUserFaceUniquenessCheckFailed(USER_ID) }
      verifyBlocking(verificationPersistenceService) { saveAssociatedUsers(SESSION_ID, listOf("anotherUserId")) }
      verifyBlocking(verificationPersistenceService) { saveAssociatedUsersCheckResults(SESSION_ID, AssociatedUsersCheckResult.TOO_EARLY_WITH_NEW_TRACKING_ID) }
      verifyNoMoreInteractions(verificationPersistenceService)
    }

  @Test
  fun `SHOULD allow user to skip face verification ON verifyFace WHEN unexpected exception occurs`() {
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    val exception = IllegalThreadStateException("Some unexpected exception")
    facetecClient.throwException({ checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }, exception)

    testScope.runTest {
      service.verifyFace(SESSION_ID, USER_AGENT, request)
    }
    verifyBlocking(verificationPersistenceService) { verifyUserSessionBy(SESSION_ID, FACE, VerificationStatus.ALLOWED_ONCE) }
  }

  @Test
  fun `SHOULD throw SessionExpiredException ON verifyFace WHEN session time is expired`() = testScope.runTest {
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, true)

    assertFailsWith(SessionExpiredException::class) {
      service.verifyFace(SESSION_ID, USER_AGENT, request)
    }

    verifyBlocking(verificationPersistenceService) { sessionExpired(SESSION_ID) }
    verifyBlocking(verificationPersistenceService) {
      addErrorMessageToSession(
        SESSION_ID, FACE, SESSION_EXPIRED, "Session time is expired, sessionId: $SESSION_ID"
      )
    }
    verifyNoMoreInteractions(verificationPersistenceService)
  }

  @Test
  fun `SHOULD throw VerificationUncompletedException ON validate WHEN session is not completed`() = testScope.runTest {
    verificationPersistenceService.mock({ isSessionValidAndCompleted(USER_ID, SESSION_ID) }, false)

    assertFailsWith(VerificationIncompleteException::class) {
      service.validateSession(USER_ID, SESSION_ID)
    }
  }

  @Test
  fun `SHOULD do nothing ON validate WHEN session is completed`() {
    verificationPersistenceService.mock({ isSessionValidAndCompleted(USER_ID, SESSION_ID) }, true)

    testScope.runTest {
      service.validateSession(USER_ID, SESSION_ID)
    }

    verifyBlocking(verificationPersistenceService) { isSessionValidAndCompleted(USER_ID, SESSION_ID) }
    verifyNoMoreInteractions(verificationPersistenceService)
    verifyNoInteractions(facetecClient)
  }

  @Test
  fun `SHOULD throw exception ON initiateVerification WHEN there is no user with such userId`() = testScope.runTest {
    userService.mock({ userExists(USER_ID) }, false)

    assertFailsWith(UserRecordNotFoundException::class) {
      service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)
    }
  }

  @Test
  fun `SHOULD call onNewUserEmail ON initiateVerification WHEN email was provided`() = testScope.runTest {
    val email = "<EMAIL>"
    whenever(emailValidationService.normalize(email)).thenReturn("<EMAIL>")
    whenever(hashService.emailSha256("<EMAIL>")).thenReturn(NORMALIZED_EMAIL_HASH)
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, EMAIL) }, false)

    val actual = service.initiateVerification(USER_ID, PaymentProviderType.DOCTORS_WITHOUT_BORDERS, email, userIp = null, appVersion = androidAppVersion)

    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, EMAIL_HASH, NORMALIZED_EMAIL_HASH) }
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
  }

  @Test
  fun `SHOULD create session without verification ON initiateVerification WHEN user with same gaid verified and associated users are fine`() =
    testScope.runTest {
      val previousUsers = listOf("oldUser1")
      val verificationVerifiedStep = VerificationSessionDto.VerificationStep(
        type = FACE,
        status = VERIFIED,
        order = 1
      )
      val verificationSession = VerificationSessionDto(
        sessionId = "sessionId",
        userId = USER_ID,
        expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
        verification = listOf()
      )
      prepareInitiateVerification(previousUsers)

      val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

      assertThat(actual).isEqualTo(verificationSession)
      verifyBlocking(verificationPersistenceService) { createSession(actual.copy(verification = listOf(verificationVerifiedStep))) }
    }

  @Test
  fun `SHOULD create session with verification ON initiateVerification WHEN user with same gaid verified but created too many users`() = testScope.runTest {
    val previousUsers = (1..5).map { "oldUser$it" }
    val verificationRequiredStep = VerificationSessionDto.VerificationStep(
      type = FACE,
      status = REQUIRED,
      order = 1
    )
    val verificationSession = VerificationSessionDto(
      sessionId = "sessionId",
      userId = USER_ID,
      expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
      verification = listOf(verificationRequiredStep)
    )

    prepareInitiateVerification(previousUsers)
    associatedUsersService.mock({ check(USER_ID, previousUsers) }, TOO_MANY_WITH_SAME_TRACKING_ID)

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    assertThat(actual).isEqualTo(verificationSession)
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
  }

  @Test
  fun `SHOULD create session with verification ON initiateVerification WHEN associated users do not match conditions`() = testScope.runTest {
    val previousUsers = (1..2).map { "oldUser$it" }
    val verificationRequiredStep = VerificationSessionDto.VerificationStep(
      type = FACE,
      status = REQUIRED,
      order = 1
    )
    val verificationSession = VerificationSessionDto(
      sessionId = "sessionId", userId = USER_ID,
      expiredAt = now.plus(VerificationService.SESSION_EXPIRATION_MINUTES, ChronoUnit.MINUTES),
      verification = listOf(verificationRequiredStep)
    )
    prepareInitiateVerification(previousUsers)
    associatedUsersService.mock({ check(USER_ID, previousUsers) }, TOO_EARLY_WITH_NEW_TRACKING_ID)

    val actual = service.initiateVerification(USER_ID, userIp = null, appVersion = androidAppVersion)

    assertThat(actual).isEqualTo(verificationSession)
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
  }

  @Test
  fun `SHOULD disassociate sessions ON disassociateSession`() = testScope.runTest {
    verificationPersistenceService.mock({ getAllSessionsForUser("userId") }, listOf("sessionId1", "sessionId2"))

    verificationPersistenceService.mock({ disassociateSession("sessionId1") }, 13)
    verificationPersistenceService.mock({ disassociateSession("sessionId2") }, 17)

    val actual = service.disassociateSession("userId")

    assertThat(actual).isEqualTo(30)
  }

  @Test
  fun `SHOULD remove faces ON removeFaces`() = testScope.runTest {
    verificationPersistenceService.mock({ getAllSessionsForUser(USER_ID) }, listOf("session1", "session2"))
    facetecClient.mock({ checkUniqueness(any(), eq(""), eq(USER_ID)) }, UniquenessCheckResultDto(true, emptyList(), listOf("duplicateFacetecId")))

    service.removeFaces(USER_ID)

    verifyBlocking(facetecClient) { removeFromDatabase(null, USER_ID) }
    verifyBlocking(facetecClient) { removeFromDatabase("session1", USER_ID) }
    verifyBlocking(facetecClient) { removeFromDatabase("session2", USER_ID) }
    verifyBlocking(facetecClient, times(2)) { removeFromDatabase("duplicateFacetecId") }
  }

  @Test
  fun `SHOULD return NOT TRIED on getFaceVerificationStatus WHEN there is no face verification data for user`() = testScope.runTest {
    verificationPersistenceService.mock({ getFaceVerificationDataForUser(USER_ID) }, null)

    service.getFaceVerificationStatus(USER_ID)
      .let { assertThat(it).isEqualTo(VerificationStatusExt.NOT_TRIED) }
  }

  @ParameterizedTest
  @CsvSource(
    "REQUIRED, , REQUIRED",
    "REQUIRED, some text, REQUIRED",
    "REQUIRED, Uniqueness check is failed for user 000218ea-36dd-4ba4-8845-075f1c444f59, UNIQUENESS_CHECK_FAILED",
    "REQUIRED, Liveness check is failed for user 0003e4f9-5d12-485f-81a3-c04de438c914, LIVENESS_CHECK_FAILED",
    "VERIFIED, , VERIFIED",
    "VERIFIED, some text, VERIFIED",
    "ALLOWED_ONCE, , VERIFIED"
  )
  fun `SHOULD return some status on getFaceVerificationStatus WHEN there is some face verification data for user`(
    verificationStatus: VerificationStatus,
    errorMessage: String?,
    verificationStatusExt: VerificationStatusExt
  ) = testScope.runTest {
    println(verificationStatus)
    verificationPersistenceService.mock({ getFaceVerificationDataForUser(USER_ID) }, VerificationData(verificationStatus, errorMessage, Instant.now()))

    service.getFaceVerificationStatus(USER_ID)
      .let { assertThat(it).isEqualTo(verificationStatusExt) }
  }

  @Test
  fun `SHOULD throw IllegalStateException when removeOldFaceMaps is called with less than 120 days back`() = testScope.runTest {
    assertFailsWith<IllegalStateException> {
      service.removeOldFaceMaps(119, 10000, false)
    }.let {
      assertThat(it.message).isEqualTo("This function is not allowed for value 119!")
    }
  }

  @Test
  fun `SHOULD return only data info when removeOldFaceMaps is called with dryRun true`() = testScope.runTest {
    verificationPersistenceService.mock(
      { getVerificationSessionsOlderThanDays(120, 10000) },
      listOf(VerificationPersistenceService.VerificationSessionData(SESSION_ID, USER_ID))
    )
    val result = service.removeOldFaceMaps(120, 10000, true)
    assertThat(result).isEqualTo(0)
    verifyBlocking(verificationPersistenceService, never()) { markFaceMapDeleted(any()) }
    verifyBlocking(facetecFacesService, never()) { deleteFace(any()) }
    verifyBlocking(facetecClient, never()) { removeFromDatabase(any(), any()) }
  }

  @Test
  fun `SHOULD compose externalDatabaseRefID from userId and sessionId WHEN sessionId is facetec assigned`() = testScope.runTest {
    val sessionIdLocal = "5jdgeeVCicB4mN9Ed1KbbefLfLNXTb5L3WvWnXscWdv4zA297EVoamm"
    val userIdLocal = "614eb68e-7f6e-4a16-b8aa-e51f11258ac1"
    val externalDatabaseRefID = "${userIdLocal}_${sessionIdLocal}".take(64)
    verificationPersistenceService.mock(
      { getVerificationSessionsOlderThanDays(120, 10000) },
      listOf(VerificationPersistenceService.VerificationSessionData(sessionIdLocal, userIdLocal))
    )
    verificationPersistenceService.mock({ markFaceMapDeleted(sessionIdLocal) }, 0)
    facetecClient.mock({ removeFromDatabase(sessionIdLocal, userIdLocal) }, DeleteFaceFromSearchDatabaseResponseDto(success = true, error = false))
    facetecFacesService.mock({ deleteFace(externalDatabaseRefID) }, 1)
    val result = service.removeOldFaceMaps(120, 10000, false)
    assertThat(result).isEqualTo(1)
    verifyBlocking(facetecFacesService, never()) { deleteFace(userIdLocal) }
  }

  @Test
  fun `SHOULD use as externalDatabaseRefID userId WHEN sessionId is NOT facetec assigned`() = testScope.runTest {
    verificationPersistenceService.mock(
      { getVerificationSessionsOlderThanDays(120, 10000) },
      listOf(VerificationPersistenceService.VerificationSessionData(SESSION_ID, USER_ID))
    )
    verificationPersistenceService.mock({ markFaceMapDeleted(SESSION_ID) }, 0)
    facetecClient.mock({ removeFromDatabase(null, USER_ID) }, DeleteFaceFromSearchDatabaseResponseDto(success = true, error = false))
    facetecClient.mock({ removeFromDatabase(SESSION_ID, USER_ID) }, DeleteFaceFromSearchDatabaseResponseDto(success = true, error = false))
    facetecFacesService.mock({ deleteFace(USER_ID) }, 1)
    val result = service.removeOldFaceMaps(120, 10000, false)
    assertThat(result).isEqualTo(1)
    verifyBlocking(facetecFacesService, never()) { deleteFace("${USER_ID}_${SESSION_ID}".take(64)) }
  }

  @Test
  fun `SHOULD return iOS challenge ON generateIosChallenge`() = testScope.runTest {
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    iosExaminationService.mock({ generateChallenge(USER_ID) }, "challenge")


    assertEquals("challenge", service.generateIosChallenge(SESSION_ID))
  }

  @Test
  fun `SHOULD verify EXAMINATION step on examineIosDevice`() = testScope.runTest {
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    val examinationRequest = IosExaminationRequestApiDto("", "", "")
    iosExaminationService.mock({ examine(USER_ID, examinationRequest, IosExaminationEnv.SANDBOX_JP) }, Unit)


    service.examineIosDevice(SESSION_ID, examinationRequest, IosExaminationEnv.SANDBOX_JP)


    verifyBlocking(verificationPersistenceService, times(1)) { verifyUserSessionBy(SESSION_ID, EXAMINATION) }
    verifyBlocking(verificationPersistenceService, never()) { addErrorMessageToSession(any(), any(), any(), any()) }
    verifyBlocking(fraudScoreService, never()) { blockUserOnDeviceExaminationFail(any()) }
  }

  @Test
  fun `SHOULD ad error on EXAMINATION step on examineIosDevice WHEN examination is failed`() = testScope.runTest {
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    val examinationRequest = IosExaminationRequestApiDto("", "", "")
    iosExaminationService.throwException(
      { examine(USER_ID, examinationRequest, IosExaminationEnv.SANDBOX_JP) },
      AttestationStatementExaminationFailedException(USER_ID)
    )

    assertThrows<AttestationStatementExaminationFailedException> {
      runBlocking {
        service.examineIosDevice(SESSION_ID, examinationRequest, IosExaminationEnv.SANDBOX_JP)
      }
    }.also {
      assertEquals("Attestation statement examination failed for 'userId'", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }

    verifyBlocking(verificationPersistenceService, times(1)) {
      addErrorMessageToSession(
        sessionId = SESSION_ID,
        verificationType = EXAMINATION,
        verificationResultType = IOS_EXAMINATION_ERROR,
        message = "Attestation statement examination failed for 'userId'"
      )
    }
    verifyBlocking(verificationPersistenceService, never()) { verifyUserSessionBy(any(), any(), any()) }
    verifyNoInteractions(fraudScoreService)
  }

  @ParameterizedTest
  @EnumSource(IosExaminationEnv::class)
  fun `SHOULD respect app env ON examineIosDevice`(iosExaminationEnv: IosExaminationEnv) {
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    val examinationRequest = IosExaminationRequestApiDto("", "", "")
    iosExaminationService.mock({ examine(USER_ID, examinationRequest, iosExaminationEnv) }, Unit)

    testScope.runTest {
      service.examineIosDevice(SESSION_ID, examinationRequest, iosExaminationEnv)
    }

    verifyBlocking(iosExaminationService) { examine(USER_ID, examinationRequest, iosExaminationEnv) }

  }

  @Test
  fun `SHOULD verify JAIL_BREAK step on verifyIosJailBreak`() {
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    val request = JailBreakRequestApiDto(false)

    testScope.runTest {
      service.verifyIosJailBreak(SESSION_ID, request)
    }

    verifyBlocking(verificationPersistenceService, times(1)) { verifyUserSessionBy(SESSION_ID, JAIL_BREAK) }
    verifyBlocking(verificationPersistenceService, never()) { addErrorMessageToSession(any(), any(), any(), any()) }
    verifyBlocking(fraudScoreService, never()) { blockUserOnJailBreakUsageDetected(any()) }
  }

  @Test
  fun `SHOULD ad error on JAIL_BREAK step on verifyIosJailBreak WHEN check failed`() = testScope.runTest {
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    val request = JailBreakRequestApiDto(true)


    assertThrows<JailBreakUsageException> {
      runBlocking {
        service.verifyIosJailBreak(SESSION_ID, request)
      }
    }.also {
      assertEquals("UserId: 'userId'. JailBreak usage detected", it.internalMessage)
      assertEquals("JailBreak usage is not allowed", it.externalMessage)
    }

    verifyBlocking(verificationPersistenceService, times(1)) {
      addErrorMessageToSession(
        sessionId = SESSION_ID,
        verificationType = JAIL_BREAK,
        verificationResultType = IOS_JAIL_BREAK_USAGE_DETECTED,
        message = "UserId: 'userId'. JailBreak usage detected"
      )
    }
    verifyBlocking(fraudScoreService, times(1)) { blockUserOnJailBreakUsageDetected(USER_ID) }
    verifyBlocking(verificationPersistenceService, never()) { verifyUserSessionBy(any(), any(), any()) }
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD verify LOCATION step ON verifyGpsLocation`(appPlatform: AppPlatform) = testScope.runTest {
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    marketService.mock({ getAllowedCountries() }, setOf("DE", "GB"))
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GPS_VERIFICATION) }, Variations.GPS_CHECK_ACTIVE)

    val request = GpsLocationRequestApiDto("DE")

    service.verifyGpsLocation(SESSION_ID, request.location, request.isMocked, appPlatform)

    advanceUntilIdle()

    verifyBlocking(verificationPersistenceService, times(1)) { verifyUserSessionBy(SESSION_ID, LOCATION) }
    verifyBlocking(userService, times(1)) { updateGpsLocationCountry(USER_ID, "DE") }
    verifyBlocking(verificationPersistenceService, never()) { addErrorMessageToSession(any(), any(), any(), any()) }
    verifyBlocking(fraudScoreService, times(1)) { onLocationCheck(USER_ID, true) }
    verifyBlocking(messageBus) {
      publishAsync(
        SendLocationCheckedEventEffect(
          USER_ID,
          appPlatform,
          SESSION_ID,
          gpsCheckResult = true,
          gpsServiceCheckOption = Variations.GPS_CHECK_ACTIVE.getKey()
        )
      )
    }
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD verify LOCATION step ON verifyGpsLocation WHEN country in not allowed but user whitelisted`(appPlatform: AppPlatform) = testScope.runTest {
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    marketService.mock({ getAllowedCountries() }, setOf("DE", "GB"))
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, isWhitelisted = true))
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GPS_VERIFICATION) }, Variations.GPS_CHECK_ACTIVE)

    service.verifyGpsLocation(SESSION_ID, "KZ", null, appPlatform)
    advanceUntilIdle()

    verifyBlocking(verificationPersistenceService, times(1)) { verifyUserSessionBy(SESSION_ID, LOCATION) }
    verifyBlocking(userService, times(1)) { updateGpsLocationCountry(USER_ID, "KZ") }
    verifyBlocking(verificationPersistenceService, never()) { addErrorMessageToSession(any(), any(), any(), any()) }
    verifyBlocking(fraudScoreService, times(1)) { onLocationCheck(USER_ID, true) }
    verifyBlocking(messageBus) {
      publishAsync(
        SendLocationCheckedEventEffect(
          USER_ID,
          appPlatform,
          SESSION_ID,
          gpsCheckResult = true,
          gpsServiceCheckOption = Variations.GPS_CHECK_ACTIVE.getKey()
        )
      )
    }
  }

  @Test
  fun `SHOULD ad error on LOCATION step ON verifyGpsLocation WHEN check failed AND platform is IOS`() = testScope.runTest {
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    marketService.mock({ getAllowedCountries() }, setOf("DE", "GB"))
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GPS_VERIFICATION) }, Variations.GPS_CHECK_ACTIVE)

    assertThrows<GpsVerificationFailedException> {
      runBlocking {
        service.verifyGpsLocation(SESSION_ID, "KZ", null, IOS)
      }
    }.also {
      assertEquals("Location verification failed", it.internalMessage)
      assertEquals("We're unable to verify your location. Please try again later.", it.externalMessage)
    }

    advanceUntilIdle()

    verifyBlocking(verificationPersistenceService, times(1)) {
      addErrorMessageToSession(
        sessionId = SESSION_ID,
        verificationType = LOCATION,
        verificationResultType = GPS_LOCATION_NOT_ALLOWED,
        message = "UserId: 'userId'. GPS location is not allowed"
      )
    }
    verifyBlocking(userService, times(1)) { updateGpsLocationCountry(USER_ID, "KZ") }
    verifyBlocking(fraudScoreService, times(1)) { onLocationCheck(USER_ID, false) }
    verifyBlocking(verificationPersistenceService, never()) { verifyUserSessionBy(any(), any(), any()) }
    verifyBlocking(messageBus) {
      publishAsync(
        SendLocationCheckedEventEffect(
          userId = USER_ID,
          appPlatform = IOS,
          sessionId = SESSION_ID,
          gpsCheckResult = false,
          gpsServiceCheckOption = Variations.GPS_CHECK_ACTIVE.getKey()
        )
      )
    }
  }

  @Test
  fun `SHOULD ad error on LOCATION step ON verifyGpsLocation WHEN check failed AND platform is ANDROID`() = testScope.runTest {
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    marketService.mock({ getAllowedCountries() }, setOf("DE", "GB"))
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GPS_VERIFICATION) }, Variations.GPS_CHECK_ACTIVE)

    assertThrows<GpsVerificationFailedException> {
      runBlocking {
        service.verifyGpsLocation(SESSION_ID, "KZ", null, ANDROID)
      }
    }.also {
      assertEquals("Location verification failed", it.internalMessage)
      assertEquals("We're unable to verify your location. Please try again later.", it.externalMessage)
    }

    advanceUntilIdle()

    verifyBlocking(verificationPersistenceService, times(1)) {
      addErrorMessageToSession(
        sessionId = SESSION_ID,
        verificationType = LOCATION,
        verificationResultType = GPS_LOCATION_NOT_ALLOWED,
        message = "UserId: 'userId'. GPS location is not allowed"
      )
    }
    verifyBlocking(userService, times(1)) { updateGpsLocationCountry(USER_ID, "KZ") }
    verifyBlocking(fraudScoreService, times(1)) { onLocationCheck(USER_ID, false) }
    verifyBlocking(verificationPersistenceService, never()) { verifyUserSessionBy(any(), any(), any()) }
    verifyBlocking(messageBus) {
      publishAsync(
        SendLocationCheckedEventEffect(
          userId = USER_ID,
          appPlatform = ANDROID,
          sessionId = SESSION_ID,
          gpsCheckResult = false,
          gpsServiceCheckOption = Variations.GPS_CHECK_ACTIVE.getKey()
        )
      )
    }
  }

  @Test
  fun `SHOULD ad error on LOCATION step ON verifyGpsLocation WHEN gps is mocked AND platform is ANDROID`() = testScope.runTest {
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    marketService.mock({ getAllowedCountries() }, setOf("DE", "GB"))
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GPS_VERIFICATION) }, Variations.GPS_CHECK_ACTIVE)

    assertThrows<GpsVerificationFailedException> {
      runBlocking {
        service.verifyGpsLocation(SESSION_ID, "KZ", isMocked = true, ANDROID)
      }
    }.also {
      assertEquals("Location verification failed", it.internalMessage)
      assertEquals("We're unable to verify your location. Please try again later.", it.externalMessage)
    }

    advanceUntilIdle()

    verifyBlocking(verificationPersistenceService, times(1)) {
      addErrorMessageToSession(
        sessionId = SESSION_ID,
        verificationType = LOCATION,
        verificationResultType = GPS_LOCATION_IS_MOCKED,
        message = "UserId: 'userId'. GPS location is mocked"
      )
    }
    verifyBlocking(userService, never()) { updateGpsLocationCountry(USER_ID, "KZ") }
    verifyBlocking(fraudScoreService, times(1)) { blockUserOnMockedLocation(USER_ID) }
    verifyBlocking(verificationPersistenceService, never()) { verifyUserSessionBy(any(), any(), any()) }
    verifyBlocking(messageBus) {
      publishAsync(
        SendLocationCheckedEventEffect(
          userId = USER_ID,
          appPlatform = ANDROID,
          sessionId = SESSION_ID,
          gpsCheckResult = false,
          gpsServiceCheckOption = Variations.GPS_CHECK_ACTIVE.getKey()
        )
      )
    }
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD short circuit face verification WHEN email is validated through SEON and trusted email`(appPlatform: AppPlatform) = testScope.runTest {
    val appVersion = AppVersionDto(platform = appPlatform, version = 142)
    val email = "<EMAIL>"
    val encryptedEmail = "encryptedEmail"
    val normalizedEmail = "<EMAIL>"
    val normalizedEncryptedEmail = "normalizedEncryptedEmail"
    encryptionService.mock({ encrypt(email) }, encryptedEmail)
    encryptionService.mock({ encrypt(normalizedEmail) }, normalizedEncryptedEmail)
    whenever(hashService.emailSha256(email)).thenReturn(EMAIL_HASH)
    whenever(emailValidationService.normalize(email)).thenReturn(normalizedEmail)
    whenever(hashService.emailSha256(normalizedEmail)).thenReturn(NORMALIZED_EMAIL_HASH)
    rewardingFacade.mock({ getTotalUsdEarningsForUser(USER_ID) }, BigDecimal.ONE)
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(createdAt = Instant.now()))
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, EMAIL) }, false)
    abTestingService.mock({ isSeonIntegrationParticipant(USER_ID) }, true)
    seonService.mock({ isTrustedEmail(USER_ID, encryptedEmail, EMAIL_HASH, normalizedEncryptedEmail, NORMALIZED_EMAIL_HASH) }, true)

    val actual = service.initiateVerification(USER_ID, TREMENDOUS_PAYPAL, email, userIp = null, appVersion = appVersion)

    assertThat(actual.verification).isEqualTo(emptyList())
    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, EMAIL_HASH, NORMALIZED_EMAIL_HASH) }
    verifyBlocking(verificationPersistenceService) {
      createSession(
        actual.copy(verification = listOf(VerificationSessionDto.VerificationStep(EMAIL, VERIFIED, 1)))
      )
    }
  }

  @Test
  fun `SHOULD not short circuit face verification WHEN email is validated through SEON and fraudulent email`() = testScope.runTest {
    val email = "<EMAIL>"
    val encryptedEmail = "encryptedEmail"
    val normalizedEmail = "<EMAIL>"
    val normalizedEncryptedEmail = "normalizedEncryptedEmail"
    encryptionService.mock({ encrypt(email) }, encryptedEmail)
    encryptionService.mock({ encrypt(normalizedEmail) }, normalizedEncryptedEmail)
    whenever(hashService.emailSha256(email)).thenReturn(EMAIL_HASH)
    whenever(emailValidationService.normalize(email)).thenReturn(normalizedEmail)
    whenever(hashService.emailSha256(normalizedEmail)).thenReturn(NORMALIZED_EMAIL_HASH)
    rewardingFacade.mock({ getTotalUsdEarningsForUser(USER_ID) }, BigDecimal.ONE)
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, EMAIL) }, false)
    abTestingService.mock({ isSeonIntegrationParticipant(USER_ID) }, true)
    seonService.mock({ isTrustedEmail(USER_ID, encryptedEmail, EMAIL_HASH, normalizedEncryptedEmail, NORMALIZED_EMAIL_HASH) }, false)

    val actual = service.initiateVerification(USER_ID, TREMENDOUS_PAYPAL, email, userIp = null, appVersion = androidAppVersion)

    assertThat(actual.verification).isEqualTo(listOf(VerificationSessionDto.VerificationStep(FACE, REQUIRED, order = 1)))
    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, EMAIL_HASH, NORMALIZED_EMAIL_HASH) }
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
  }

  @Test
  fun `SHOULD not short circuit face verification WHEN email is shared with other users`() = testScope.runTest {
    val email = "<EMAIL>"
    val encryptedEmail = "encryptedEmail"
    val normalizedEncryptedEmail = "normalizedEncryptedEmail"
    encryptionService.mock({ encrypt(email) }, encryptedEmail)
    whenever(hashService.emailSha256(email)).thenReturn(EMAIL_HASH)
    whenever(emailValidationService.normalize(email)).thenReturn("<EMAIL>")
    whenever(hashService.emailSha256("<EMAIL>")).thenReturn(NORMALIZED_EMAIL_HASH)
    rewardingFacade.mock({ getTotalUsdEarningsForUser(USER_ID) }, BigDecimal.ZERO)
    fraudScoreService.mock({ isUserSharedEmail(USER_ID) }, true)
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, EMAIL) }, false)
    abTestingService.mock({ isSeonIntegrationParticipant(USER_ID) }, true)
    seonService.mock({ isTrustedEmail(USER_ID, encryptedEmail, EMAIL_HASH, normalizedEncryptedEmail, NORMALIZED_EMAIL_HASH) }, true)

    val actual = service.initiateVerification(USER_ID, TREMENDOUS_PAYPAL, email, userIp = null, appVersion = androidAppVersion)

    assertThat(actual.verification).isEqualTo(listOf(VerificationSessionDto.VerificationStep(FACE, REQUIRED, order = 1)))
    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, EMAIL_HASH, NORMALIZED_EMAIL_HASH) }
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
  }

  @Test
  fun `SHOULD short circuit face verification WHEN email verification has already been used for user`() = testScope.runTest {
    val email = "<EMAIL>"
    val encryptedEmail = "encryptedEmail"
    val normalizedEmail = "<EMAIL>"
    val normalizedEncryptedEmail = "normalizedEncryptedEmail"
    encryptionService.mock({ encrypt(email) }, encryptedEmail)
    encryptionService.mock({ encrypt(normalizedEmail) }, normalizedEncryptedEmail)
    whenever(hashService.emailSha256(email)).thenReturn(EMAIL_HASH)
    whenever(emailValidationService.normalize(email)).thenReturn(normalizedEmail)
    whenever(hashService.emailSha256(normalizedEmail)).thenReturn(NORMALIZED_EMAIL_HASH)
    rewardingFacade.mock({ getTotalUsdEarningsForUser(USER_ID) }, BigDecimal.ZERO)
    abTestingService.mock({ isSeonIntegrationParticipant(USER_ID) }, true)
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    seonService.mock({ isTrustedEmail(USER_ID, encryptedEmail, EMAIL_HASH, normalizedEncryptedEmail, NORMALIZED_EMAIL_HASH) }, true)

    val actual = service.initiateVerification(USER_ID, TREMENDOUS_PAYPAL, email, userIp = null, appVersion = androidAppVersion)

    assertThat(actual.verification).isEmpty()
    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, EMAIL_HASH, NORMALIZED_EMAIL_HASH) }
    verifyBlocking(verificationPersistenceService) {
      createSession(
        actual.copy(verification = listOf(VerificationSessionDto.VerificationStep(EMAIL, VERIFIED, 1)))
      )
    }
  }

  @Test
  fun `SHOULD not short circuit face verification WHEN user has earnings of more than 8 USD in less than 24 hours after creation`() = testScope.runTest {
    val email = "<EMAIL>"
    val encryptedEmail = "encryptedEmail"
    val normalizedEncryptedEmail = "normalizedEncryptedEmail"

    encryptionService.mock({ encrypt(email) }, encryptedEmail)
    whenever(hashService.emailSha256(email)).thenReturn(EMAIL_HASH)
    whenever(emailValidationService.normalize(email)).thenReturn("<EMAIL>")
    whenever(hashService.emailSha256("<EMAIL>")).thenReturn(NORMALIZED_EMAIL_HASH)
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(createdAt = Instant.now()))
    rewardingFacade.mock({ getTotalUsdEarningsForUser(USER_ID) }, BigDecimal(16))
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, EMAIL) }, false)
    abTestingService.mock({ isSeonIntegrationParticipant(USER_ID) }, true)
    seonService.mock({ isTrustedEmail(USER_ID, encryptedEmail, EMAIL_HASH, normalizedEncryptedEmail, NORMALIZED_EMAIL_HASH) }, true)

    val actual = service.initiateVerification(USER_ID, TREMENDOUS_PAYPAL, email, userIp = null, appVersion = androidAppVersion)

    assertThat(actual.verification).isEqualTo(listOf(VerificationSessionDto.VerificationStep(FACE, REQUIRED, order = 1)))
    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, EMAIL_HASH, NORMALIZED_EMAIL_HASH) }
    verifyBlocking(verificationPersistenceService) { createSession(actual) }
  }

  @Test
  fun `SHOULD short circuit face verification WHEN user has earnings of less than 8 USD in less than 24 hours after creation`() = testScope.runTest {
    val email = "<EMAIL>"
    val encryptedEmail = "encryptedEmail"
    val normalizedEmail = "<EMAIL>"
    val normalizedEncryptedEmail = "normalizedEncryptedEmail"
    encryptionService.mock({ encrypt(email) }, encryptedEmail)
    encryptionService.mock({ encrypt(normalizedEmail) }, normalizedEncryptedEmail)
    whenever(hashService.emailSha256(email)).thenReturn(EMAIL_HASH)
    whenever(emailValidationService.normalize(email)).thenReturn(normalizedEmail)
    whenever(hashService.emailSha256(normalizedEmail)).thenReturn(NORMALIZED_EMAIL_HASH)
    rewardingFacade.mock({ getTotalUsdEarningsForUser(USER_ID) }, BigDecimal(7))
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(createdAt = Instant.now()))
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, EMAIL) }, false)
    abTestingService.mock({ isSeonIntegrationParticipant(USER_ID) }, true)
    seonService.mock({ isTrustedEmail(USER_ID, encryptedEmail, EMAIL_HASH, normalizedEncryptedEmail, NORMALIZED_EMAIL_HASH) }, true)

    val actual = service.initiateVerification(USER_ID, TREMENDOUS_PAYPAL, email, userIp = null, appVersion = androidAppVersion)

    assertThat(actual.verification).isEqualTo(emptyList())
    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, EMAIL_HASH, NORMALIZED_EMAIL_HASH) }
    verifyBlocking(verificationPersistenceService) {
      createSession(
        actual.copy(verification = listOf(VerificationSessionDto.VerificationStep(EMAIL, VERIFIED, 1)))
      )
    }
  }

  @Test
  fun `SHOULD short circuit face verification WHEN user has earnings of more than 8 USD in more than 24 hours after creation`() = testScope.runTest {
    val email = "<EMAIL>"
    val encryptedEmail = "encryptedEmail"
    val normalizedEmail = "<EMAIL>"
    val normalizedEncryptedEmail = "normalizedEncryptedEmail"
    encryptionService.mock({ encrypt(email) }, encryptedEmail)
    encryptionService.mock({ encrypt(normalizedEmail) }, normalizedEncryptedEmail)
    whenever(hashService.emailSha256(email)).thenReturn(EMAIL_HASH)
    whenever(emailValidationService.normalize(email)).thenReturn(normalizedEmail)
    whenever(hashService.emailSha256(normalizedEmail)).thenReturn(NORMALIZED_EMAIL_HASH)
    rewardingFacade.mock({ getTotalUsdEarningsForUser(USER_ID) }, BigDecimal(16))
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(createdAt = Instant.now().minus(26, ChronoUnit.HOURS)))
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, EMAIL) }, false)
    abTestingService.mock({ isSeonIntegrationParticipant(USER_ID) }, true)
    seonService.mock({ isTrustedEmail(USER_ID, encryptedEmail, EMAIL_HASH, normalizedEncryptedEmail, NORMALIZED_EMAIL_HASH) }, true)

    val actual = service.initiateVerification(USER_ID, TREMENDOUS_PAYPAL, email, userIp = null, appVersion = androidAppVersion)

    assertThat(actual.verification).isEqualTo(emptyList())
    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, EMAIL_HASH, NORMALIZED_EMAIL_HASH) }
    verifyBlocking(verificationPersistenceService) {
      createSession(
        actual.copy(
          verification = listOf(VerificationSessionDto.VerificationStep(EMAIL, VERIFIED, 1))
        )
      )
    }
  }

  @Test
  fun `SHOULD increment counter and mark user as fraudster WHEN some response params are not success`() = runTest {
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = ES_LOCALE))
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    faceFraudstersCounter.mock({ incrementAndGet() }, 1)
    facetecClient.mock({ checkUniqueness(SESSION_ID, USER_AGENT, USER_ID) }, UniquenessCheckResultDto(true, emptyList(), emptyList()))
    facetecClient.mock(
      { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }, VerifyFaceLivenessResponseDto(
        success = true, error = false, errorMessage = null,
        scanResultBlob = "scanResultBlob",
        faceScanSecurityChecks = VerifyFaceLivenessResponseDto.FaceScanSecurityChecks(
          faceScanLivenessCheckSucceeded = true,
          auditTrailVerificationCheckSucceeded = true,
          replayCheckSucceeded = false,
          sessionTokenCheckSucceeded = true
        )
      )
    )

    service.verifyFace(SESSION_ID, USER_AGENT, request)

    verifyBlocking(faceFraudstersCounter) { incrementAndGet() }
    verifyBlocking(faceFraudstersPersistenceService) { save(USER_ID, FaceFraudResult.SNEAKED) }
  }

  @Test
  fun `SHOULD increment counter and fail and alert WHEN too much fraudsters`() = runTest {
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = FR_LOCALE))
    faceFraudstersCounter.mock({ incrementAndGet() }, 11)
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    facetecClient.mock({ checkUniqueness(SESSION_ID, USER_AGENT, USER_ID) }, UniquenessCheckResultDto(true, emptyList(), emptyList()))
    facetecClient.mock(
      { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }, VerifyFaceLivenessResponseDto(
        success = true, error = false, errorMessage = null,
        scanResultBlob = "scanResultBlob",
        faceScanSecurityChecks = VerifyFaceLivenessResponseDto.FaceScanSecurityChecks(
          faceScanLivenessCheckSucceeded = true,
          auditTrailVerificationCheckSucceeded = true,
          replayCheckSucceeded = false,
          sessionTokenCheckSucceeded = true
        )
      )
    )
    marketService.mock({ isUsMarket() }, true)

    assertFailsWith<FaceLivenessCheckFailedException> {
      service.verifyFace(SESSION_ID, USER_AGENT, request)
    }

    verifyBlocking(faceFraudstersCounter) { incrementAndGet() }
    verifyBlocking(faceFraudstersPersistenceService) { save(USER_ID, FaceFraudResult.BLOCKED) }
    assertEquals("ALERT: Face fraudsters counter limit reached: 11", logEntriesListAppender.list.first().message)
  }

  @Test
  fun `SHOULD increment counter and fail and no alert WHEN too much fraudsters but long running`() = runTest {
    val request = VerifyFaceLivenessRequestDto(
      faceScan = "faceScanBase64",
      auditTrailImage = "auditTrailImageBase64",
      lowQualityAuditTrailImage = "lowQualityAuditTrailImageBase64"
    )
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = FR_LOCALE))
    faceFraudstersCounter.mock({ incrementAndGet() }, 20)
    verificationPersistenceService.mock({ sessionExpired(SESSION_ID) }, false)
    verificationPersistenceService.mock({ getUserIdBySessionId(SESSION_ID) }, USER_ID)
    facetecClient.mock({ checkUniqueness(SESSION_ID, USER_AGENT, USER_ID) }, UniquenessCheckResultDto(true, emptyList(), emptyList()))
    facetecClient.mock(
      { checkLiveness(SESSION_ID, USER_AGENT, USER_ID, request) }, VerifyFaceLivenessResponseDto(
        success = true, error = false, errorMessage = null,
        scanResultBlob = "scanResultBlob",
        faceScanSecurityChecks = VerifyFaceLivenessResponseDto.FaceScanSecurityChecks(
          faceScanLivenessCheckSucceeded = true,
          auditTrailVerificationCheckSucceeded = true,
          replayCheckSucceeded = false,
          sessionTokenCheckSucceeded = true
        )
      )
    )

    assertFailsWith<FaceLivenessCheckFailedException> {
      service.verifyFace(SESSION_ID, USER_AGENT, request)
    }

    verifyBlocking(faceFraudstersCounter) { incrementAndGet() }
    verifyBlocking(faceFraudstersPersistenceService) { save(USER_ID, FaceFraudResult.BLOCKED) }
    assertThat(logEntriesListAppender.list).extracting { it.message }.none { it.startsWith("ALERT: ") }
  }

  private fun prepareInitiateVerification(previousUsers: List<String>) {
    verificationPersistenceService.mock({ userVerifiedBy(USER_ID, FACE) }, false)
    verificationPersistenceService.mock({ getUsersWithSameTrackingIdVerifiedBy(USER_ID, FACE) }, previousUsers)
  }
}
