package com.moregames.playtime.user.cashout

import assertk.all
import assertk.assertThat
import assertk.assertions.*
import com.google.inject.Provider
import com.justplayapps.playtime.proto.cashoutCreatedEvent
import com.justplayapps.proxybase.PaymentAccounts.PAYPAL_PS
import com.justplayapps.proxybase.PaymentAccounts.TREMENDOUS_JP
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCurrencyEarnings
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.ClientExperiment.*
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.abtesting.Variations.ANDROID_HIDE_EARNINGS_GRAY_GREEN
import com.moregames.base.abtesting.Variations.CHANGE_EMAIL
import com.moregames.base.abtesting.variations.AndroidCashout2xOfferVariation
import com.moregames.base.abtesting.variations.AndroidFullscreenCashoutVariation
import com.moregames.base.abtesting.variations.CoinsDoNotResetVariation
import com.moregames.base.abtesting.variations.PaymentProviderSurveyVariation
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.BuildVariant.TEST
import com.moregames.base.app.PaymentProviderType.*
import com.moregames.base.app.UserIdentifierType
import com.moregames.base.bus.MessageBus
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.config.CashoutConfig
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.encryption.HashService
import com.moregames.base.gameStub
import com.moregames.base.ipregistry.IpData
import com.moregames.base.messaging.dto.AdMarketEvent
import com.moregames.base.messaging.dto.CashoutRequestCreatedEventDto
import com.moregames.base.messaging.dto.XHoursPassedSinceNoEarningsCheckDto
import com.moregames.base.table.UserCashoutTransactionsTable.Status.*
import com.moregames.base.user.UserBonusBalanceType
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.TimeService
import com.moregames.base.util.io
import com.moregames.base.util.mock
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.app.ga.GoogleAnalyticsService
import com.moregames.playtime.app.messaging.dto.AdjustCustomEvent
import com.moregames.playtime.app.messaging.dto.MolocoInAppEvent
import com.moregames.playtime.boost.BoostedModeService
import com.moregames.playtime.buseffects.DeletePopupMessageEffect
import com.moregames.playtime.buseffects.SendMolocoInAppEventEffect
import com.moregames.playtime.checks.IpService
import com.moregames.playtime.earnings.CashoutSettingsService
import com.moregames.playtime.earnings.CashoutSettingsServiceTest.Companion.maxEarnings
import com.moregames.playtime.earnings.currency.CurrencyExchangeService
import com.moregames.playtime.earnings.currency.dto.CurrencyExchangeResultDto
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.ios.cashoutcoins.CashoutCoinsService
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.tracking.AdMarketService
import com.moregames.playtime.tracking.AdjustApiClient
import com.moregames.playtime.tracking.AdjustService
import com.moregames.playtime.tracking.RevenueEventsServiceTest
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.*
import com.moregames.playtime.user.PopupMessageReason.EARNINGS_THRESHOLD_REACHED
import com.moregames.playtime.user.cashout.dto.*
import com.moregames.playtime.user.cashout.exception.NoEarningsToCashoutException
import com.moregames.playtime.user.cashout.offers.CashoutOffersService
import com.moregames.playtime.user.cashout.offers.CashoutOffersService.CashoutOffer
import com.moregames.playtime.user.cashout.offers.CashoutOffersService.CashoutOfferSet
import com.moregames.playtime.user.dto.CashoutButtonStyle
import com.moregames.playtime.user.dto.PaymentProviderSurvey
import com.moregames.playtime.user.dto.UserExternalIds
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.highlightedgames.AndroidHighlightedGamesService
import com.moregames.playtime.user.interview.UserInterviewService
import com.moregames.playtime.user.offer.AndroidInstallationLinkProvider
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarService
import com.moregames.playtime.user.survey.SurveyService
import com.moregames.playtime.user.survey.paymentprovider.PaymentProviderSurveyService
import com.moregames.playtime.utils.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.*
import kotlinx.serialization.ExperimentalSerializationApi
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.*
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import java.lang.Math.round
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.time.temporal.ChronoUnit.MINUTES
import java.util.*
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

@OptIn(ExperimentalSerializationApi::class)
@ExperimentalCoroutinesApi
class CashoutServiceTest {
  private val cashoutPersistenceService = mock<CashoutPersistenceService>()
  private val cashoutTimeCalculationsService = mock<CashoutTimeCalculationsService>()
  private val cashoutPeriodsService: CashoutPeriodsService = mock()
  private val cashoutStatusService: CashoutStatusService = mock()
  private val cashoutValidationService: CashoutValidationService = mock()
  private val paymentProvidersService = mock<PaymentProvidersService>()
  private val userPersistenceService = mock<UserPersistenceService>()
  private val timeService = mock<TimeService>()
  private val marketService: MarketService = mock()
  private val abTestingService: AbTestingService = mock {
    onBlocking { isUserExperimentParticipant(USER_ID, ANDROID_CASHOUT_2X_OFFER) } doReturn false
    onBlocking { assignedVariationValue(USER_ID, ANDROID_HIDE_EARNINGS) } doReturn DEFAULT
    onBlocking { assignedVariationValue(USER_ID, ANDROID_CASHOUT_PROGRESS_BAR) } doReturn DEFAULT
    onBlocking { assignedVariationValue(USER_ID, ANDROID_PAYPAL_HINTS) } doReturn DEFAULT
    onBlocking { assignedVariationValue(USER_ID, BONUS_BANK) } doReturn DEFAULT
    onBlocking { assignedVariationValue(USER_ID, ANDROID_FULLSCREEN_CASHOUT_FORM_STYLE) } doReturn DEFAULT
    onBlocking { isUserExperimentParticipant(USER_ID, ANDROID_ONBOARDING_PROGRESS_BAR) } doReturn false
    onBlocking { shouldEnableVenmo(USER_ID) } doReturn false
  }
  private val userCheckManager = mock<UserCheckManager>()
  private val fraudScoreService = mock<FraudScoreService>()
  private val userEarningsPersistenceService = mock<UserEarningsPersistenceService>()
  private val translationService: UserTranslationService = mock()
  private val cashoutSettingsService: CashoutSettingsService = mock()
  private val accountsService: AccountsService = mock()
  private val cashoutWithholdsService: CashoutWithholdsAndBonusesService = mock()
  private val imageService: ImageService = mock()
  private val randomGenerator: RandomGenerator = mock()
  private val ipService: IpService = mock()
  private val googleAnalyticsService: GoogleAnalyticsService = mock()
  private val applicationConfig: ApplicationConfig = mock {
    on { justplayMarket } doReturn "test-market"
  }
  private val encryptionService: EncryptionService = mock()
  private val userInterviewService: UserInterviewService = mock()
  private val adMarketService: AdMarketService = mock()
  private val testScope = TestScope()

  @OptIn(ExperimentalSerializationApi::class)
  private val cashoutCoinsService: CashoutCoinsService = mock {
    onBlocking { getBonusCoinsAfterFirstCashout(USER_ID, IOS) } doReturn null
  }
  private val adjustService: AdjustService = mock()
  private val adjustApiClient: AdjustApiClient = mock()
  private val paymentProviderSurveyService: PaymentProviderSurveyService = mock()
  private val hashService: HashService = mock()
  private val userService: UserService = mock()
  private val incompleteCashoutService: IncompleteCashoutService = mock()
  private val cashoutPeriodsConfigService: CashoutPeriodsConfigService = mock()
  private val hideCashoutAmountExperimentService: HideCashoutAmountExperimentService = mock {
    onBlocking { shouldShowGiftBox(eq(USER_ID), any(), any()) } doReturn false
  }
  private val buildVariantProvider: Provider<BuildVariant> = mock {
    whenever(it.get()).thenReturn(TEST)
  }
  private val surveyService: SurveyService = mock()
  private val rewardingFacade: RewardingFacade = mock()
  private val userPopupMessagesService: UserPopupMessagesService = mock()
  private val gamesService: GamesService = mock()
  private val cashoutOffersService: CashoutOffersService = mock()
  private val androidInstallationLinkProvider: AndroidInstallationLinkProvider = mock()
  private val onboardingProgressBarService: OnboardingProgressBarService = mock()
  private val androidHighlightedGamesService: AndroidHighlightedGamesService = mock {
    onBlocking { shouldShowHighlightedGamesOnCashoutCancel(USER_ID) } doReturn false
  }
  private val messageBus: MessageBus = mock()
  private val currencyExchangeService: CurrencyExchangeService = mock()
  private val boostedModeService: BoostedModeService = mock()

  private val cashoutService = CashoutService(
    cashoutPersistenceService = cashoutPersistenceService,
    cashoutPeriodsService = cashoutPeriodsService,
    cashoutStatusService = cashoutStatusService,
    cashoutValidationService = cashoutValidationService,
    paymentProvidersService = paymentProvidersService,
    userService = userService,
    userPersistenceService = userPersistenceService,
    fraudScoreService = fraudScoreService,
    marketService = marketService,
    abTestingService = abTestingService,
    userCheckManager = userCheckManager,
    userEarningsPersistenceService = userEarningsPersistenceService,
    messageBus = messageBus,
    timeService = timeService,
    buildVariantProvider = buildVariantProvider,
    translationService = translationService,
    cashoutSettingsService = cashoutSettingsService,
    accountsService = accountsService,
    cashoutWithholdsService = cashoutWithholdsService,
    imageService = imageService,
    ipService = ipService,
    applicationConfig = applicationConfig,
    coroutineScope = { testScope.io() },
    googleAnalyticsService = googleAnalyticsService,
    encryptionService = encryptionService,
    userInterviewService = userInterviewService,
    adMarketService = adMarketService,
    cashoutCoinsService = cashoutCoinsService,
    adjustService = adjustService,
    adjustApiClient = adjustApiClient,
    paymentProviderSurveyService = paymentProviderSurveyService,
    hashService = hashService,
    incompleteCashoutService = incompleteCashoutService,
    cashoutPeriodsConfigService = cashoutPeriodsConfigService,
    hideCashoutAmountExperimentService = hideCashoutAmountExperimentService,
    surveyService = surveyService,
    userPopupMessagesService = userPopupMessagesService,
    cashoutOffersService = cashoutOffersService,
    gamesService = gamesService,
    androidInstallationLinkProvider = androidInstallationLinkProvider,
    onboardingProgressBarService = onboardingProgressBarService,
    androidHighlightedGamesService = androidHighlightedGamesService,
    rewardingFacade = rewardingFacade,
    currencyExchangeService = currencyExchangeService,
    boostedModeService = boostedModeService,
  )

  init {
    Dispatchers.setMain(StandardTestDispatcher())
  }

  private companion object {

    @JvmStatic
    fun paramsPaymentProviderSurvey(): Stream<Arguments> {
      return PaymentProviderSurveyVariation.variations.flatMap {
        listOf(
          Arguments.of(ANDROID, ANDROID_PAYMENT_PROVIDER_SURVEY, it),
        )
      }.stream()
    }

    val cashoutConfig = CashoutConfig(
      headerTextCashoutAvailable = "cashout available",
      headerTextCashoutUnavailable = "cashout {{coins}}unavailable",
      headerTextCashoutWithPeriod = "During the last {{period_duration_description}} earned",
      iconFilenameCashoutAvailable = "icon1",
      iconFilenameCashoutUnavailable = "icon2",
      cashoutMinutesAfterMidnightUtc = 60,
      lastPossibleUserCreationMinutesBeforeMidnightUtc = 60,
      fyberCoinsToUsdConversionRatio = BigDecimal(0.5),
      userEarningsCalculationFactor = BigDecimal(0.8),
      disclaimer = "disclaimer",
      ironSourceCoinsToUsdConversionRatio = BigDecimal("220"),
      maxCashoutAmount = BigDecimal("25"),
      tapjoyCoinsToUsdConversionRatio = BigDecimal("220"),
      offerwallCoinsToUsdConversionRatio = BigDecimal("220")
    )
    val cashoutProviders = listOf(
      CashoutProvider(
        displayName = "Amazon",
        url = "https://www.amazon.com",
        videoUrl = "https://www.youtube.com",
        iconFilename = "icon",
        largeIconFilename = "largeIcon",
        smallIconFilename = "smallIcon",
        text = "text",
        shortText = "shortText",
        providerType = AMAZON,
        disclaimer = "disclaimer",
        emailHint = "Amazon account",
        minimumAmount = BigDecimal("0.01"),
        maximumAmount = BigDecimal("2000"),
        orderKey = 0,
        identifierType = UserIdentifierType.EMAIL,
        identifierHint = "Amazon account"
      ),
      CashoutProvider(
        displayName = "PayPal",
        url = "https://www.paypal.com",
        videoUrl = "https://www.youtube.com",
        iconFilename = "icon2",
        largeIconFilename = "largeIcon2",
        smallIconFilename = "smallIcon2",
        text = "text2",
        shortText = "shortText2",
        providerType = PAYPAL,
        disclaimer = null,
        emailHint = "PayPal account",
        minimumAmount = null,
        maximumAmount = null,
        orderKey = 0,
        identifierType = UserIdentifierType.EMAIL,
        identifierHint = "PayPal account"
      ),
    )
    val cashoutProvidersApiDto = listOf(
      CashoutProviderApiDto(
        displayName = "Amazon",
        url = "https://www.amazon.com",
        videoUrl = "https://www.youtube.com",
        iconUrl = "http://someUrlHere/icon.ico",
        text = "text",
        shortText = "shortText",
        provider = AMAZON,
        donation = false,
        disclaimer = "disclaimer",
        emailHint = "Amazon account",
        minimumAmount = "$0.01",
        maximumAmount = "$2,000.00",
        enabled = true,
        bonusEnabled = false,
        identifierType = UserIdentifierType.EMAIL,
        identifierHint = "Amazon account"
      ),
      CashoutProviderApiDto(
        displayName = "PayPal",
        url = "https://www.paypal.com",
        videoUrl = "https://www.youtube.com",
        iconUrl = "http://someUrlHere/icon2.ico",
        text = "text2",
        shortText = "shortText2",
        provider = PAYPAL,
        donation = false,
        disclaimer = null,
        emailHint = "PayPal account",
        minimumAmount = null,
        maximumAmount = null,
        enabled = true,
        bonusEnabled = false,
        identifierType = UserIdentifierType.EMAIL,
        identifierHint = "PayPal account"
      )
    )

    val now: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)

    const val USER_ID = "userId"
    const val CASHOUT_TRANSACTION_ID = "transactionId"
    const val DONATION_TRANSACTION_ID = "donationTransactionId"

    val earningsCashoutDemand = CashoutDemand(
      provider = PAYPAL,
      countryCode = "DE",
      encryptedName = "Encrypted Max Mustermann",
      encryptedAddress = "Encrypted Musterweg 5",
      encryptedEmail = "<EMAIL>",
      emailHash = "emailHash",
      isBonusRequest = false,
      userIp = "123",
      normalizedEmailHash = "normalizedEmailHash",
    )
    val cashoutDemandApiSample = CashoutDemandApiDto(
      provider = PAYPAL,
      name = "Bart Simpson",
      address = "742 Evergreen Terrace",
      email = "<EMAIL>",
      isBonusRequest = true
    )
    val ipData = IpData.Extended(
      ip = "123",
      countryCode = "US",
      doNotTrackIp = false,
      regionCode = "IL",
      countryName = "United States",
      cityName = "Chicago",
      regionName = "Illinois",
      timeZone = "America/Chicago"
    )
  }

  @BeforeEach
  fun setUp() {
    val userEarnings = UserCurrencyEarnings(BigDecimal.ZERO, Currency.getInstance("USD"), BigDecimal.ZERO)
    userPersistenceService.mock({ getUserConsentInfo(USER_ID) }, LimitedTrackingInfo(false, "US", false))
    userPersistenceService.mock({ loadCoinGoalUser(USER_ID) }, user)
    rewardingFacade.mock({ getUserCurrentCoinsBalance(USER_ID, user.appPlatform) }, UserCurrentCoinsGoalBalance(100, 0, 0))
    userService.mock({ getUser(eq(USER_ID), any()) }, userDtoStub.copy(createdAt = now))
    marketService.mock({ getUserAllowedCountryCodeOrUS(USER_ID) }, "US")
    marketService.mock({ isUserFromAllowedCountry(USER_ID) }, true)
    marketService.mock({ isGdprAppliesToCountry(any()) }, false)
    fraudScoreService.mock({ banUserWithHighFraudScore(USER_ID) }, false)
    fraudScoreService.mock({ getFraudScore(USER_ID) }, 100)
    fraudScoreService.mock({ isUserBlocked(USER_ID) }, false)
    timeService.mock({ now() }, now)
    cashoutSettingsService.mock({ getCashoutConfig() }, cashoutConfig)
    paymentProvidersService.mock({ loadPaymentProviders(eq(USER_ID), any(), any(), any(), any()) }, cashoutProviders)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(BigDecimal.ZERO, Currency.getInstance("USD"), BigDecimal.ZERO)
    )
    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, userEarnings)
    cashoutSettingsService.mock({ getUserMaxEarningsAmount(USER_ID) }, BigDecimal(1000))
    cashoutPersistenceService.mock({ loadPaymentProviders(any()) }, cashoutProviders)
    cashoutPersistenceService.mock({ loadRecentCashoutsByUserId(USER_ID) }, emptyList())
    cashoutPersistenceService.mock({ loadRecentCashoutsByEmail(any()) }, emptyList())
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(USER_ID) }, BigDecimal.ZERO)
    cashoutPersistenceService.mock({ getProviderImagesConfig(any(), any()) }, ProviderImagesCfg.empty())
    cashoutPersistenceService.mock({ hasCashouts(USER_ID) }, true)
    cashoutTimeCalculationsService.mock({ alreadyCashedOutToday(USER_ID) }, false)
    whenever(cashoutTimeCalculationsService.calculateNextCashout(cashoutConfig, now)).thenReturn(now)
    cashoutValidationService.mock({ isCashoutThrottled(USER_ID) }, false)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodStub)
    whenever(runBlocking { translationService.tryTranslate(any(), any(), any()) }).then { it.getArgument(0) }
    whenever(runBlocking { translationService.translateOrDefault(any(), any(), any()) }).then { (it.getArgument(0) as TranslationResource).defaultValue }
    accountsService.mock({ getAccount(PAYPAL) }, PAYPAL_PS)
    cashoutWithholdsService.mock({ getWithholdsAndBonusesAmounts() }, WithholdsAndBonusesAmounts.ZERO)
    imageService.mock({ toUrl("icon") }, "http://someUrlHere/icon.ico")
    imageService.mock({ toUrl("icon1") }, "http://someUrlHere/icon1.ico")
    imageService.mock({ toUrl("icon2") }, "http://someUrlHere/icon2.ico")
    imageService.mock({ toUrl("a.ico") }, "http://someUrlHere/a.ico")
    imageService.mock({ toUrl("walmart.png") }, "http://somePathHerewalmart.png")
    imageService.mock({ toUrl("google.png") }, "http://somePathHeregoogle.png")
    imageService.mock({ toUrl("reward.png") }, "http://somePathHerereward.png")
    randomGenerator.mock({ nextUUID() }, CASHOUT_TRANSACTION_ID, arrayOf(DONATION_TRANSACTION_ID))
    cashoutValidationService.mock({ isCashoutAllowed(USER_ID) }, true)
    cashoutCoinsService.mock({ getBonusCoinsAfterFirstCashout(USER_ID, IOS) }, null)
    rewardingFacade.mock({ addBonusCoinsIfNotExists(any(), any(), any(), any(), any()) }, true)
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, ANDROID_INCOMPLETE_CASHOUT_RESTORING) }, false)
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, SPECIAL_CASHOUT_OFFERS) }, false)
    cashoutPeriodsConfigService.mock({ firstPeriodDuration(USER_ID) }, Duration.ofMinutes(180))
    userEarningsPersistenceService.mock({ getCashoutRelatedRevenueSum(CASHOUT_TRANSACTION_ID) }, BigDecimal("11.00"))
    abTestingService.mock({ isEm2Participant(USER_ID) }, false)
    rewardingFacade.mock({ getTotalUsdEarningsForUser(USER_ID) }, BigDecimal(0.1))
  }

  @Test
  fun `SHOULD return enabled cashout ON get cashout status WHEN user has earnings`() = testScope.runTest {
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(BigDecimal("5.00999"), Currency.getInstance("USD"), BigDecimal("5.00999"))
    )
    whenever(cashoutTimeCalculationsService.calculateNextCashout(cashoutConfig, now)).thenReturn(now.minus(1, ChronoUnit.DAYS))
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(USER_ID) }, BigDecimal.ZERO)


    val result = cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)

    testScope.advanceUntilIdle()

    assertThat(result.isEnabled).isTrue()
    assertThat(result.nextCashout).isEqualTo(now.plus(12, ChronoUnit.HOURS))
    assertThat(result.headerText).isEqualTo(cashoutConfig.headerTextCashoutAvailable)
    assertThat(result.iconFilename).isEqualTo(cashoutConfig.iconFilenameCashoutAvailable)
    assertThat(result.amount).isEqualByComparingTo(BigDecimal("5.00"))
    assertThat(result.providers).isEqualTo(cashoutProviders)

    verifyBlocking(userInterviewService) { onCashoutStatusGetCall(USER_ID, ANDROID, result) }
  }

  @Test
  fun `SHOULD return enabled cashout and complete onboarding step ON get cashout status WHEN user has earnings and is exp participant`() = testScope.runTest {
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, ANDROID_ONBOARDING_PROGRESS_BAR) }, true)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(BigDecimal("5.00999"), Currency.getInstance("USD"), BigDecimal("5.00999"))
    )
    whenever(cashoutTimeCalculationsService.calculateNextCashout(cashoutConfig, now)).thenReturn(now.minus(1, ChronoUnit.DAYS))
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(USER_ID) }, BigDecimal.ZERO)


    val result = cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)

    testScope.advanceUntilIdle()

    assertThat(result.isEnabled).isTrue()
    assertThat(result.nextCashout).isEqualTo(now.plus(12, ChronoUnit.HOURS))
    assertThat(result.headerText).isEqualTo(cashoutConfig.headerTextCashoutAvailable)
    assertThat(result.iconFilename).isEqualTo(cashoutConfig.iconFilenameCashoutAvailable)
    assertThat(result.amount).isEqualByComparingTo(BigDecimal("5.00"))
    assertThat(result.providers).isEqualTo(cashoutProviders)

    verifyBlocking(userInterviewService) { onCashoutStatusGetCall(USER_ID, ANDROID, result) }
    verifyBlocking(onboardingProgressBarService) { completeRouteToCashout(USER_ID) }
  }

  @Test
  fun `SHOULD return enabled cashout ON get cashout status WHEN user has revenue and high fraud score`() {
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(BigDecimal(5.0), Currency.getInstance("USD"), BigDecimal(5.0))
    )
    whenever(cashoutTimeCalculationsService.calculateNextCashout(cashoutConfig, now)).thenReturn(now.minus(1, ChronoUnit.DAYS))
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(USER_ID) }, BigDecimal.ZERO)
    fraudScoreService.mock({ banUserWithHighFraudScore(USER_ID) }, true)

    val result = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(result.isEnabled).isTrue()
    assertThat(result.nextCashout).isEqualTo(now.plus(12, ChronoUnit.HOURS))
    assertThat(result.headerText).isEqualTo(cashoutConfig.headerTextCashoutAvailable)
    assertThat(result.iconFilename).isEqualTo(cashoutConfig.iconFilenameCashoutAvailable)
    assertThat(result.amount).isEqualByComparingTo(BigDecimal(5.0))
    assertThat(result.providers).isEqualTo(cashoutProviders)
  }

  @Test
  fun `SHOULD extract next cashout time and status from dedicated entities ON get cashout status`() {
    val nextCashoutInstant = Instant.now().plus(12, ChronoUnit.HOURS)
    val cashoutPeriodMock: CashoutPeriodDto = mock()
    whenever(cashoutPeriodMock.periodEnd).thenReturn(nextCashoutInstant)

    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(BigDecimal(5.0), Currency.getInstance("USD"), BigDecimal(5.0))
    )
    fraudScoreService.mock({ banUserWithHighFraudScore(USER_ID) }, true)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodMock)
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(USER_ID) }, BigDecimal.ZERO)

    val result = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(result.isEnabled).isTrue()
    assertThat(result.nextCashout).isEqualTo(nextCashoutInstant)
    assertThat(result.headerText).isEqualTo(cashoutConfig.headerTextCashoutAvailable)
    assertThat(result.iconFilename).isEqualTo(cashoutConfig.iconFilenameCashoutAvailable)
    assertThat(result.amount).isEqualByComparingTo(BigDecimal(5.0))
    assertThat(result.providers).isEqualTo(cashoutProviders)

    verifyBlocking(cashoutTimeCalculationsService, never()) { calculateNextCashout(any(), any()) }
  }

  @Test
  fun `SHOULD NOT return enabled cashout AND format coins number ON get cashout status WHEN NOT user has revenue`() {
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(USER_ID) }, BigDecimal.ZERO)
    userPersistenceService.mock({ loadCoinGoalUser(USER_ID) }, user)
    rewardingFacade.mock({ getUserCurrentCoinsBalance(USER_ID, user.appPlatform) }, UserCurrentCoinsGoalBalance(3141592, 0, 0))

    val result = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(result.isEnabled).isFalse()
    assertThat(result.headerText).isEqualTo("cashout 3,141,592 unavailable")
    assertThat(result.iconFilename).isEqualTo(cashoutConfig.iconFilenameCashoutUnavailable)
    assertThat(result.amount).isEqualByComparingTo(BigDecimal.ZERO)
  }

  @Test
  fun `SHOULD return correct header text ON get cashout status WHEN coins are zero`() {
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(USER_ID) }, BigDecimal.ZERO)
    userPersistenceService.mock({ loadCoinGoalUser(USER_ID) }, user)
    rewardingFacade.mock({ getUserCurrentCoinsBalance(USER_ID, user.appPlatform) }, UserCurrentCoinsGoalBalance(0, 0, 0))

    val result = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(result.headerText).isEqualTo("cashout unavailable")
  }

  @ParameterizedTest
  @ValueSource(ints = [1, 2, 3])
  fun `SHOULD return cashout status texts with translation ON getCashoutStatus WHEN text parameters were used`(currentPeriodCount: Int) {
    val configWithParams = cashoutConfig.copy(
      headerTextCashoutUnavailable = "\$_unavailable",
      headerTextCashoutAvailable = "\$_available",
      headerTextCashoutWithPeriod = "\$_with_period",
      disclaimer = "\$_disclaimer"
    )
    translationService.mock({ tryTranslate("\$_unavailable", EN_LOCALE, USER_ID) }, "cashout unavailable en")
    translationService.mock({ tryTranslate("\$_available", EN_LOCALE, USER_ID) }, "cashout available en")
    translationService.mock({ tryTranslate("\$_with_period", EN_LOCALE, USER_ID) }, "cashout with period en {{period_duration_description}}")
    translationService.mock({ tryTranslate("\$_disclaimer", EN_LOCALE, USER_ID) }, "disclaimer en")
    val cashoutPeriodMock: CashoutPeriodDto = mock()
    whenever(cashoutPeriodMock.counter).thenReturn(currentPeriodCount)
    whenever(cashoutPeriodMock.periodEnd).thenReturn(now)

    if (currentPeriodCount != 1) { //let's first period be without earnings
      rewardingFacade.mock(
        { loadUnpaidUserCurrencyEarnings(USER_ID) },
        UserCurrencyEarnings(ONE, Currency.getInstance("USD"), ONE)
      )
    }
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodMock)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    cashoutSettingsService.mock({ getCashoutConfig() }, configWithParams)

    val result = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(result.headerText)
      .isEqualTo(
        when (currentPeriodCount) {
          1 -> "cashout unavailable en"
          2 -> "cashout with period en 3h"
          3 -> "cashout available en"
          else -> throw AssertionError("unexpected cashout period counter")
        }
      )
    assertThat(result.disclaimer).isEqualTo("disclaimer en")
  }

  @Test
  fun `SHOULD return disabled cashout ON get cashout status WHEN cashout is throttled`() {
    fraudScoreService.mock({ banUserWithHighFraudScore(USER_ID) }, false)
    cashoutValidationService.mock({ isCashoutThrottled(USER_ID) }, true)

    val result = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(result).isEqualTo(
      CashoutStatus.disabled(cashoutConfig, Currency.getInstance("USD"), now.plus(12, ChronoUnit.HOURS), userHasCashouts = true)
        .copy(headerText = "cashout 100 unavailable")
    )
  }

  @Test
  fun `SHOULD create earnings demand ON demandCashout WHEN NOT is bonus demand`() {
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(eq(USER_ID), any()) }, 1)
    userEarningsPersistenceService.mock(
      { getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) },
      UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    )

    runBlocking {
      withMockedUUID(CASHOUT_TRANSACTION_ID) {
        cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID)
      }
    }

    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, earningsCashoutDemand.emailHash, earningsCashoutDemand.normalizedEmailHash) }
    verifyBlocking(cashoutPersistenceService) { createTransaction(eq(USER_ID), any(), eq(earningsCashoutDemand)) }
    verifyBlocking(cashoutPersistenceService) { updateTransactionStatus(any(), eq(REQUESTED)) }
    verifyBlocking(cashoutStatusService, times(1)) { disableCashout(USER_ID) }
    verifyBlocking(messageBus) { publishAsync(DeletePopupMessageEffect(USER_ID, EARNINGS_THRESHOLD_REACHED)) }
    verifyBlocking(surveyService) { sendCashoutSurveyIfApplicable(USER_ID, ANDROID) }
  }

  @Test
  fun `SHOULD change cashout status to disabled ON demandCashout WHEN user is on individualCashoutPeriod experiment`() {
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(eq(USER_ID), any()) }, 1)
    userEarningsPersistenceService.mock(
      { getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) },
      UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    )

    runBlocking {
      withMockedUUID(CASHOUT_TRANSACTION_ID) {
        cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID)
      }
    }

    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, earningsCashoutDemand.emailHash, earningsCashoutDemand.normalizedEmailHash) }
    verifyBlocking(cashoutPersistenceService) { createTransaction(eq(USER_ID), any(), eq(earningsCashoutDemand)) }
    verifyBlocking(cashoutPersistenceService) { updateTransactionStatus(any(), eq(REQUESTED)) }
    verifyBlocking(cashoutStatusService) { disableCashout(USER_ID) }
    verifyBlocking(messageBus) { publishAsync(DeletePopupMessageEffect(USER_ID, EARNINGS_THRESHOLD_REACHED)) }
    verifyBlocking(surveyService) { sendCashoutSurveyIfApplicable(USER_ID, ANDROID) }
  }

  @Test
  fun `SHOULD throw NoEarningsToCashoutException ON demand cashout WHEN no cashout available AND NOT is bonus`() {
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(eq(USER_ID), any()) }, 0)

    assertThrows<NoEarningsToCashoutException> {
      runBlocking { cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID) }
    }
    verifyNoInteractions(cashoutStatusService)
    verifyNoInteractions(messageBus)
    verifyNoInteractions(userPopupMessagesService)
  }

  // =================================================================================================
  // =================================== A/B Experiment Tests ========================================
  // =================================================================================================

  @Test
  fun `SHOULD emit CashoutRequestCreatedEventDto ON demandCashout WHEN user is on rewards experiment`() {
    val userEarnings = UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(USER_ID, CASHOUT_TRANSACTION_ID) }, 1)
    userEarningsPersistenceService.mock({ getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) }, userEarnings)
    marketService.mock({ getUserAllowedCountryCodeOrUS(USER_ID) }, "CA")

    runBlocking {
      withMockedUUID(CASHOUT_TRANSACTION_ID) {
        cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID)
      }
    }

    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, earningsCashoutDemand.emailHash, earningsCashoutDemand.normalizedEmailHash) }
    verifyBlocking(cashoutPersistenceService) { createTransaction(USER_ID, CASHOUT_TRANSACTION_ID, earningsCashoutDemand) }
    verifyBlocking(cashoutPersistenceService) { updateTransactionStatus(CASHOUT_TRANSACTION_ID, REQUESTED) }
    verifyBlocking(cashoutPersistenceService) {
      updateCashoutTransactionCashoutAmount(
        CASHOUT_TRANSACTION_ID,
        amountUsd = BigDecimal("4.00"),
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyAmount = BigDecimal("4.40"),
        operationalWithholdAmountUsd = BigDecimal.ZERO.setScale(2),
        operationalWithholdUserCurrencyAmount = BigDecimal.ZERO.setScale(2)
      )
    }
    verifyBlocking(messageBus) {
      publish(
        CashoutRequestCreatedEventDto(
          cashoutTransactionId = CASHOUT_TRANSACTION_ID,
          userId = USER_ID,
          amount = BigDecimal("4.00"),
          userCurrencyCode = "CAD",
          userCurrencyAmount = BigDecimal("4.40"),
          provider = earningsCashoutDemand.provider,
          encryptedName = earningsCashoutDemand.encryptedName,
          encryptedEmail = earningsCashoutDemand.encryptedEmail,
          emailHash = earningsCashoutDemand.emailHash,
          countryCode = "CA",
          encryptedAddress = earningsCashoutDemand.encryptedAddress,
          market = "test-market",
          createdAt = now,
          account = PAYPAL_PS,
          platform = ANDROID
        )
      )
    }
    verifyBlocking(messageBus) {
      publishAsync(DeletePopupMessageEffect(USER_ID, EARNINGS_THRESHOLD_REACHED))
    }
    verifyBlocking(messageBus) {
      publish(
        cashoutCreatedEvent {
          this.userId = USER_ID
          this.cashoutTransactionId = CASHOUT_TRANSACTION_ID
        }
      )
    }
    verifyNoMoreInteractions(cashoutPersistenceService)
    verifyNoMoreInteractions(messageBus)
    verifyBlocking(cashoutStatusService, times(1)) { disableCashout(USER_ID) }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD emit add bonus coins ON demandCashout WHEN user is on cashout for coins experiment`(isParticipant: Boolean) {
    val userEarnings = UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(USER_ID, CASHOUT_TRANSACTION_ID) }, 1)
    userEarningsPersistenceService.mock({ getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) }, userEarnings)
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(appPlatform = IOS))
    marketService.mock({ getUserAllowedCountryCodeOrUS(USER_ID) }, "CA")
    cashoutCoinsService.mock({ getBonusCoinsAfterFirstCashout(USER_ID, IOS) }, if (isParticipant) 50 else null)

    runBlocking {
      withMockedUUID(CASHOUT_TRANSACTION_ID) {
        cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID)
      }
    }
    if (isParticipant) {
      verifyBlocking(rewardingFacade) { addBonusCoinsIfNotExists(USER_ID, IOS, 50, UserBonusBalanceType.COINS_AFTER_CASHOUT, USER_ID) }
      verifyNoMoreInteractions(rewardingFacade)
    } else {
      verifyNoInteractions(rewardingFacade)
    }
  }

  @Test
  fun `SHOULD emit CashoutRequestCreatedEventDto with bonuses ON demandCashout WHEN it is a variation with bonuses`() {
    val userEarnings = UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    val provider = TREMENDOUS_MONETARY
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(USER_ID, CASHOUT_TRANSACTION_ID) }, 1)
    userEarningsPersistenceService.mock({ getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) }, userEarnings)
    marketService.mock({ getUserAllowedCountryCodeOrUS(USER_ID) }, "CA")
    cashoutWithholdsService.mock(
      { getWithholdsAndBonusesAmounts() },
      noWithhold.copy(bonusAmountUsd = BigDecimal("0.25"), bonusUserCurrencyAmount = BigDecimal("0.30"))
    )
    accountsService.mock({ getAccount(TREMENDOUS_MONETARY) }, TREMENDOUS_JP)

    runBlocking {
      withMockedUUID(CASHOUT_TRANSACTION_ID) {
        cashoutService.demandCashout(USER_ID, earningsCashoutDemand.copy(provider = provider), ANDROID)
      }
    }

    verifyBlocking(messageBus) {
      publish(
        CashoutRequestCreatedEventDto(
          cashoutTransactionId = CASHOUT_TRANSACTION_ID,
          userId = USER_ID,
          amount = BigDecimal("4.25"),
          userCurrencyCode = "CAD",
          userCurrencyAmount = BigDecimal("4.70"),
          provider = provider,
          encryptedName = earningsCashoutDemand.encryptedName,
          encryptedEmail = earningsCashoutDemand.encryptedEmail,
          emailHash = earningsCashoutDemand.emailHash,
          countryCode = "CA",
          encryptedAddress = earningsCashoutDemand.encryptedAddress,
          market = "test-market",
          createdAt = now,
          account = TREMENDOUS_JP,
          platform = ANDROID
        )
      )
    }
    verifyBlocking(cashoutPersistenceService) {
      updateCashoutTransactionCashoutAmount(
        transactionId = CASHOUT_TRANSACTION_ID,
        amountUsd = BigDecimal("4.25"),
        operationalWithholdAmountUsd = -BigDecimal("0.25"),
        userCurrency = CAD,
        userCurrencyAmount = BigDecimal("4.70"),
        operationalWithholdUserCurrencyAmount = -BigDecimal("0.30")
      )
    }
    verifyBlocking(cashoutStatusService, times(1)) { disableCashout(USER_ID) }
  }

  @Test
  fun `SHOULD complete transaction with SUCCESSFUL status ON completeSuccessfulTransaction`() {
    cashoutPersistenceService.mock(
      { loadTransaction(CASHOUT_TRANSACTION_ID) },
      cashoutTransactionStub
    )

    runBlocking {
      cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
    }

    verifyBlocking(cashoutPersistenceService) {
      updateTransactionStatus(CASHOUT_TRANSACTION_ID, SUCCESSFUL)
    }
    verifyBlocking(userCheckManager) { onSuccessfulCashout(cashoutTransactionStub) }
    verifyBlocking(userPersistenceService) { trackUserLastCashoutData(cashoutTransactionStub) }
  }

  @Test
  fun `SHOULD publish a delayed task with notifying on no earnings ON completeSuccessfulTransaction`() {
    cashoutPersistenceService.mock({ loadTransaction(CASHOUT_TRANSACTION_ID) }, cashoutTransactionStub)

    runBlocking {
      cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
    }

    val message = XHoursPassedSinceNoEarningsCheckDto(userId = USER_ID, fromDate = timeService.now(), counter = 1)
    verifyBlocking(messageBus) { publish(message, timeService.now().plus(1, ChronoUnit.DAYS)) }
  }

  @Test
  fun `SHOULD not publish a delayed task with notifying on no earnings ON completeSuccessfulTransaction WHEN user is em2 participant`() {
    abTestingService.mock({ isEm2Participant(USER_ID) }, true)
    cashoutPersistenceService.mock({ loadTransaction(CASHOUT_TRANSACTION_ID) }, cashoutTransactionStub)

    runBlocking { cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID) }

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD complete transaction with SUCCESSFUL status AND disable cashout ON completeSuccessfulTransaction WHEN user is on individual cashout periods experiment`() {
    cashoutPersistenceService.mock(
      { loadTransaction(CASHOUT_TRANSACTION_ID) },
      cashoutTransactionStub
    )

    runBlocking {
      cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
    }

    verifyBlocking(cashoutPersistenceService) {
      updateTransactionStatus(CASHOUT_TRANSACTION_ID, SUCCESSFUL)
    }
    verifyBlocking(userCheckManager) { onSuccessfulCashout(cashoutTransactionStub) }
  }

  @Test
  fun `SHOULD complete transaction with FAILED status AND rollback earnings ON rollbackFailedTransaction`() {
    cashoutPersistenceService.mock(
      { loadTransaction(CASHOUT_TRANSACTION_ID) },
      cashoutTransactionStub
    )

    runBlocking {
      cashoutService.rollbackFailedTransaction(CASHOUT_TRANSACTION_ID)
    }

    verifyBlocking(cashoutPersistenceService) {
      updateTransactionStatus(CASHOUT_TRANSACTION_ID, FAILED)
    }
    verifyBlocking(cashoutStatusService) {
      enableCashout(cashoutTransactionStub.userId)
    }
    verifyBlocking(userEarningsPersistenceService) { clearEarningsTransactionInfo(listOf(CASHOUT_TRANSACTION_ID)) }
  }

  @Test
  fun `SHOULD complete transaction with FAILED status AND rollback earnings AND enable cashout ON rollbackFailedTransaction WHEN user is on individual cashout periods experiment`() {
    cashoutPersistenceService.mock(
      { loadTransaction(CASHOUT_TRANSACTION_ID) },
      cashoutTransactionStub
    )

    runBlocking {
      cashoutService.rollbackFailedTransaction(CASHOUT_TRANSACTION_ID)
    }

    verifyBlocking(cashoutPersistenceService) {
      updateTransactionStatus(CASHOUT_TRANSACTION_ID, FAILED)
    }
    verifyBlocking(cashoutStatusService) { enableCashout(USER_ID) }
    verifyBlocking(userEarningsPersistenceService) { clearEarningsTransactionInfo(listOf(CASHOUT_TRANSACTION_ID)) }
  }

  @Test
  fun `SHOULD return cashout statistics ON getCashoutStats`() {
    cashoutPersistenceService.mock(
      { calculateTotalCashoutsByProviderForUser(USER_ID) }, mapOf(
        PAYPAL to UserCurrencyEarnings(
          BigDecimal("1.124"),
          Currency.getInstance("USD"),
          BigDecimal("1.124")
        ),
        AMAZON to UserCurrencyEarnings(
          BigDecimal("2.124"),
          Currency.getInstance("USD"),
          BigDecimal("2.124")
        ),
        TARGET to UserCurrencyEarnings(
          BigDecimal("3.124"),
          Currency.getInstance("USD"),
          BigDecimal("3.124")
        ),
        DOCTORS_WITHOUT_BORDERS to UserCurrencyEarnings(
          BigDecimal("1.005"),
          Currency.getInstance("USD"),
          BigDecimal("1.005")
        ),
        THE_HUNGER_PROJECT to UserCurrencyEarnings(
          BigDecimal("2.006"),
          Currency.getInstance("USD"),
          BigDecimal("2.006")
        )
      )
    )
    val userEarnings = UserCurrencyEarnings(BigDecimal("0.641"), Currency.getInstance("USD"), BigDecimal("0.641"))
    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, userEarnings)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      userEarnings.roundDownToSecondDigit()
    )

    val actual = runBlocking {
      cashoutService.getCashoutStats(USER_ID)
    }

    assertThat(actual).isEqualTo(
      CashoutStatsApiDto(
        totalEarningsAmount = "$10.00",
        totalCashoutAmount = "$6.37",
        totalDonationsAmount = "$3.01",
        true
      )
    )
  }

  @Test
  fun `SHOULD return cashout statistics ON getCashoutStats WHEN no data available`() {
    marketService.mock({ getUserCurrency(USER_ID) }, Currency.getInstance("CAD"))
    cashoutPersistenceService.mock({ calculateTotalCashoutsByProviderForUser(USER_ID) }, emptyMap())
    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, null)

    val actual = runBlocking {
      cashoutService.getCashoutStats(USER_ID)
    }

    assertThat(actual).isEqualTo(
      CashoutStatsApiDto(
        totalEarningsAmount = "$0.00",
        totalCashoutAmount = "$0.00",
        totalDonationsAmount = "$0.00",
        false
      )
    )
  }

  @Test
  fun `SHOULD return transaction data ON loadTransaction`() {
    cashoutPersistenceService.mock(
      { loadTransaction(cashoutTransactionStub.cashoutTransactionId) },
      cashoutTransactionStub
    )

    val actual = runBlocking {
      cashoutService.loadTransaction(cashoutTransactionStub.cashoutTransactionId)
    }

    assertThat(actual).isEqualTo(cashoutTransactionStub)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return cashout exist flag ON userHasCashouts`(firstCashout: Boolean) {
    cashoutPersistenceService.mock({ hasCashouts(USER_ID) }, firstCashout)

    val actual = runBlocking {
      cashoutService.userHasCashouts(USER_ID)
    }

    assertThat(actual).isEqualTo(firstCashout)
  }

  @Test
  fun `SHOULD return recent cashouts ON loadRecentTransactions`() {
    val until = Instant.now()
    cashoutPersistenceService.mock({ loadRecentTransactions(USER_ID, until) }, listOf(cashoutTransactionStub))

    val actual = runBlocking {
      cashoutService.loadRecentTransactions(USER_ID, until, CashoutService.TransactionsFilterType.All)
    }

    assertThat(actual).isEqualTo(listOf(cashoutTransactionStub))
  }

  @Test
  fun `SHOULD return recent donation cashouts ON loadRecentTransactions WHEN filter is specified`() {
    val until = Instant.now()
    cashoutPersistenceService.mock(
      { loadRecentTransactions(USER_ID, until) },
      listOf(
        cashoutTransactionStub.copy(cashoutTransactionId = "1", provider = DOCTORS_WITHOUT_BORDERS),
        cashoutTransactionStub.copy(cashoutTransactionId = "2", provider = AMAZON),
        cashoutTransactionStub.copy(cashoutTransactionId = "3", provider = PAYPAL),
        cashoutTransactionStub.copy(cashoutTransactionId = "4", provider = THE_HUNGER_PROJECT)
      )
    )

    val donations = runBlocking {
      cashoutService.loadRecentTransactions(USER_ID, until, CashoutService.TransactionsFilterType.Donations)
    }

    assertThat(donations).isEqualTo(
      listOf(
        cashoutTransactionStub.copy(cashoutTransactionId = "1", provider = DOCTORS_WITHOUT_BORDERS),
        cashoutTransactionStub.copy(cashoutTransactionId = "4", provider = THE_HUNGER_PROJECT)
      )
    )

    val withdeawals = runBlocking {
      cashoutService.loadRecentTransactions(USER_ID, until, CashoutService.TransactionsFilterType.Withdrawals)
    }

    assertThat(withdeawals).isEqualTo(
      listOf(
        cashoutTransactionStub.copy(cashoutTransactionId = "2", provider = AMAZON),
        cashoutTransactionStub.copy(cashoutTransactionId = "3", provider = PAYPAL)
      )
    )
  }

  @Test
  fun `SHOULD return isEnabled false ON getCashoutStatus WHEN cashout is not allowed`() {
    cashoutValidationService.mock({ isCashoutAllowed(USER_ID) }, false)

    val result = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(result.isEnabled).isFalse()
  }

  @Test
  fun `SHOULD return isEnabled true ON getCashoutStatus WHEN cashout is allowed`() {
    cashoutValidationService.mock({ isCashoutAllowed(USER_ID) }, true)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(ONE, USD, ONE)
    )

    val result = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertTrue(result.isEnabled)
  }

  @ParameterizedTest
  @ValueSource(ints = [2, 3])
  fun `SHOULD return cashout header text with duration ON getCashoutStatus WHEN cashout is allowed AND previous cashout period first-notfirst`(
    currentPeriodCount: Int
  ) {
    val cashoutPeriodMock: CashoutPeriodDto = mock()
    whenever(cashoutPeriodMock.counter).thenReturn(currentPeriodCount)
    whenever(cashoutPeriodMock.periodEnd).thenReturn(now)

    fraudScoreService.mock({ isUserBlocked(USER_ID) }, true)
    marketService.mock({ isUserFromAllowedCountry(USER_ID) }, false)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(ONE, Currency.getInstance("USD"), ONE)
    )
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodMock)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)

    val result = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(result.headerText)
      .isEqualTo(
        when (currentPeriodCount) {
          2 -> cashoutConfig.headerTextCashoutWithPeriod.replace("{{period_duration_description}}", "3h")
          else -> cashoutConfig.headerTextCashoutAvailable
        }
      )
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD get provider images config on getProviderImagesConfig`(enabled: Boolean) {
    marketService.mock({ getUserAllowedCountryCodeOrUS(USER_ID) }, "BR")
    cashoutPersistenceService.mock({ getProviderImagesConfig("BR", enabled) }, providerImagesCfg)

    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(ONE, Currency.getInstance("USD"), ONE)
    )
    val cashoutStatus = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE).copy(isEnabled = enabled)
    }
    imageService.mock({ toUrl("walmart.png") }, "http://somePathHerewalmart1.png")
    imageService.mock({ toUrl("google.png") }, "http://somePathHeregoogle1.png")
    imageService.mock({ toUrl("reward.png") }, "http://somePathHerereward1.png")

    val actual = runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatus)
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatus)
    }

    verifyBlocking(cashoutPersistenceService, times(1)) { getProviderImagesConfig("BR", enabled) }
    assertThat(actual).isEqualTo(
      CashoutApiDto(
        isEnabled = enabled,
        headerText = "cashout available",
        iconUrl = "http://someUrlHere/icon1.ico",
        nextCashoutTimestamp = cashoutStatus.nextCashout,
        amountText = "$1.00",
        providers = cashoutProvidersApiDto,
        bonusStatus = CashoutBonusStatusApiDto.BACK_COMPATIBILITY_MOCK,
        disclaimer = "disclaimer",
        showRewards = true,
        providersImageList = listOf("http://somePathHerewalmart1.png", "http://somePathHeregoogle1.png", "http://somePathHerereward1.png"),
        timestamp = now,
        cashoutFormType = "HIDE_ADDRESS",
        consentedToAnalytics = true,
        giftBoxInsteadOfEarnings = false,
        cashoutButtonStyle = null,
        cashoutFormStyle = null,
        cashoutOffers = null,
      )
    )
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD replace email hint  ON getCashoutStatus WHEN platform is iOS or IOS web`(platform: AppPlatform) {
    userPersistenceService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(appPlatform = platform))
    rewardingFacade.mock({ getUserCurrentCoinsBalance(USER_ID, platform) }, UserCurrentCoinsGoalBalance(100, 0, 0))

    val result = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(result.providers).each { it.prop(CashoutProvider::emailHint).isEqualTo("Email address") }
  }

  @Test
  fun `SHOULD return enabled rewards ON getUserCashoutConfig WHEN user is on rewards variation but already cashed out`() {
    val cashoutStatus = cashoutStatusStub.copy(userHasCashouts = false)

    cashoutPersistenceService.mock({ getProviderImagesConfig(any(), any()) }, providerImagesCfg)

    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatus)
    }.let {
      assertThat(it).isEqualTo(
        cashoutApiDtoStub.copy(
          nextCashoutTimestamp = cashoutStatus.nextCashout,
          showRewards = false,
          timestamp = now,
        )
      )
    }
  }

  @Test
  fun `SHOULD return consentedToAnalytics false ON getUserCashoutConfig WHEN user is part of gdpr country and non consented`() {
    userPersistenceService.mock({ getUserConsentInfo(USER_ID) }, LimitedTrackingInfo(false, "GB", false))
    marketService.mock({ isGdprAppliesToCountry("GB") }, true)
    cashoutPersistenceService.mock({ getProviderImagesConfig(any(), any()) }, providerImagesCfg)

    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatusStub)
    }.let {
      assertThat(it).isEqualTo(
        cashoutApiDtoStub.copy(
          nextCashoutTimestamp = cashoutStatusStub.nextCashout,
          timestamp = now,
          consentedToAnalytics = false,
        )
      )
    }
  }

  @Test
  fun `SHOULD correctly fill videoAdType on getUserCashoutConfig`() {
    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatusStub)
    }.let {
      assertThat(it.videoAdType).isEqualTo("INTERSTITIAL")
    }
  }

  @ParameterizedTest
  @CsvSource("0, false", "0.99, false", "1, true", "2, true")
  fun `SHOULD return userReachedEarningsToShare flag according to total earnings ON getCashoutStats`(
    totalEarningsUsd: BigDecimal,
    userReachedEarningsToShare: Boolean
  ) {
    val currency = Currency.getInstance("CAD")
    marketService.mock({ getUserCurrency(USER_ID) }, currency)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(
        amountUsd = totalEarningsUsd,
        userCurrency = currency,
        userCurrencyAmount = BigDecimal(0.5)
      )
    )
    cashoutPersistenceService.mock({ calculateTotalCashoutsByProviderForUser(USER_ID) }, emptyMap())

    val actual = runBlocking {
      cashoutService.getCashoutStats(USER_ID)
    }

    assertThat(actual).isEqualTo(
      CashoutStatsApiDto(
        totalEarningsAmount = "$0.50",
        totalCashoutAmount = "$0.00",
        totalDonationsAmount = "$0.00",
        userReachedEarningsToShare = userReachedEarningsToShare
      )
    )
  }

  @Test
  fun `SHOULD obfuscate user cashout transactions ON obfuscateUserCashoutTransactionPersonals`() {
    cashoutPersistenceService.mock({ obfuscateUserCashoutTransactionPersonals("userId") }, 7)

    val actual = runBlocking { cashoutService.obfuscateUserCashoutTransactionPersonals("userId") }

    assertThat(actual).isEqualTo(7)
  }

  @Test
  fun `SHOULD load userIds by ON loadUserIdsForEmail`() {
    val expected = listOf("userId1", "userId2")
    whenever(hashService.emailSha256("email")).thenReturn("emailHash")
    cashoutPersistenceService.mock(
      { loadUserIdsForEmails(setOf("emailHash")) },
      expected.zip(listOf("encryptedEmail", "encryptedEmail"))
    )

    val actual = runBlocking { cashoutService.loadUserIdsForEmail("email") }

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD call cashoutPersistenceService_getLastUserPersonals ON getLastUserPersonals`() {
    runBlocking { cashoutService.getLastUserPersonals("userId") }

    verifyBlocking(cashoutPersistenceService) { getLastUserPersonals("userId") }
  }

  private val cashoutPeriodStub = CashoutPeriodDto(
    userId = USER_ID,
    periodStart = now.minus(12, ChronoUnit.HOURS),
    periodEnd = now.plus(12, ChronoUnit.HOURS),
    coinGoal = 2000,
    counter = 3,
    noEarningsCounter = 0,
    coinGoalMilestones = listOf(1, 2, 3),
  )

  @Test
  fun `SHOULD call userHasSuccessfulCashout ON userHasSuccessfulCashout`() {
    runBlocking {
      cashoutService.userHasSuccessfulCashout(USER_ID)
    }

    verifyBlocking(cashoutPersistenceService, times(1)) { userHasSuccessfulCashout(USER_ID) }
  }

  @Test
  fun `SHOULD return API model ON findLastSuccessfulTransaction`() {
    cashoutPersistenceService.mock({ loadLastSuccessfulTransaction(USER_ID) }, cashoutTransactionStub)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      someEarningsStub
    )
    paymentProvidersService.mock({ loadPaymentProvider(any(), any(), any(), any(), any()) }, cashoutProvidersStub[1])
    encryptionService.mock(
      { decryptOrEmpty(cashoutTransactionStub.encryptedEmail) }, "<EMAIL>"
    )
    encryptionService.mock(
      { decryptOrEmpty(cashoutTransactionStub.encryptedUserName) }, "Barbara"
    )
    encryptionService.mock(
      { decryptOrEmpty(cashoutTransactionStub.encryptedAddress) }, "Bart Apart"
    )

    runBlocking {
      cashoutService.findOneCLickCashoutData(USER_ID, Locale.ENGLISH)
    }.let {
      assertEquals(cashoutTransactionInfoApiDtoStub, it)
    }
  }

  @ParameterizedTest
  @EnumSource(value = Variations::class, names = ["CHANGE_EMAIL", "CHANGE_EMAIL_AND_FOLLOWING_TEXT"])
  fun `SHOULD return API model and apply androidPaypalHints exp ON findLastSuccessfulTransaction`(variation: Variations) {
    cashoutPersistenceService.mock({ loadLastSuccessfulTransaction(USER_ID) }, cashoutTransactionStub)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      someEarningsStub
    )
    paymentProvidersService.mock({ loadPaymentProvider(any(), any(), any(), any(), any()) }, cashoutProvidersStub[1])
    encryptionService.mock(
      { decryptOrEmpty(cashoutTransactionStub.encryptedEmail) }, "<EMAIL>"
    )
    encryptionService.mock(
      { decryptOrEmpty(cashoutTransactionStub.encryptedUserName) }, "Barbara"
    )
    encryptionService.mock(
      { decryptOrEmpty(cashoutTransactionStub.encryptedAddress) }, "Bart Apart"
    )
    abTestingService.mock({ assignedVariationValue(USER_ID, ANDROID_PAYPAL_HINTS) }, variation)

    runBlocking {
      cashoutService.findOneCLickCashoutData(USER_ID, Locale.ENGLISH)
    }.let {
      assertThat(it?.provider?.identifierHint).isEqualTo("Email associated with your Paypal")
      assertThat(it?.provider?.identifierExplanation).isEqualTo(
        if (variation == CHANGE_EMAIL) {
          null
        } else {
          "In case you do not have a PayPal account, you will receive an invitation"
        }
      )
    }
  }

  @Test
  fun `SHOULD return null ON findLastSuccessfulTransaction WHEN no successful cashout yet`() {
    cashoutPersistenceService.mock({ loadLastSuccessfulTransaction(USER_ID) }, null)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      someEarningsStub
    )
    paymentProvidersService.mock({ loadPaymentProvider(any(), any(), any(), any(), any()) }, cashoutProvidersStub[1])

    runBlocking {
      cashoutService.findOneCLickCashoutData(USER_ID, Locale.ENGLISH)
    }.let {
      assertNull(it)
    }
  }

  @Test
  fun `SHOULD return null ON findLastSuccessfulTransaction WHEN no earnings yet`() {
    cashoutPersistenceService.mock({ loadLastSuccessfulTransaction(USER_ID) }, cashoutTransactionStub)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      noEarningsStub
    )
    paymentProvidersService.mock({ loadPaymentProvider(any(), any(), any(), any(), any()) }, cashoutProvidersStub[1])

    runBlocking {
      cashoutService.findOneCLickCashoutData(USER_ID, Locale.ENGLISH)
    }.let {
      assertNull(it)
    }
  }

  @Test
  fun `SHOULD return null ON findLastSuccessfulTransaction WHEN provider is not available by some reasons`() {
    cashoutPersistenceService.mock({ loadLastSuccessfulTransaction(USER_ID) }, cashoutTransactionStub)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      someEarningsStub
    )
    paymentProvidersService.mock({ loadPaymentProvider(any(), any(), any(), any(), any()) }, null)

    runBlocking {
      cashoutService.findOneCLickCashoutData(USER_ID, Locale.ENGLISH)
    }.let {
      assertNull(it)
    }
  }

  @Test
  fun `SHOULD return null ON findLastSuccessfulTransaction WHEN provider is not enabled`() {
    cashoutPersistenceService.mock({ loadLastSuccessfulTransaction(USER_ID) }, cashoutTransactionStub)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      someEarningsStub
    )
    paymentProvidersService.mock({ loadPaymentProvider(any(), any(), any(), any(), any()) }, cashoutProvidersStub[1].copy(enabled = false))

    runBlocking {
      cashoutService.findOneCLickCashoutData(USER_ID, Locale.ENGLISH)
    }.let {
      assertNull(it)
    }
  }

  @Test
  fun `SHOULD return oneClickType`() {
    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatusStub)
    }.let {
      assertThat(it).isEqualTo(
        cashoutApiDtoStub.copy(
          nextCashoutTimestamp = cashoutStatusStub.nextCashout,
          oneClickType = "ONE_CLICK_ONLY",
          providersImageList = emptyList(),
          timestamp = now
        )
      )
    }
  }

  @Test
  fun `SHOULD return empty list IN cashoutOffers WHEN cashout offer set is on cooldown`() {
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, SPECIAL_CASHOUT_OFFERS) }, true)
    cashoutOffersService.mock({ getOfferSet(USER_ID) }, CashoutOfferSet.Cooldown(mock()))

    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatusStub)
    }.let {
      assertThat(it.cashoutOffers).isNotNull().isEmpty()
    }
  }

  @Test
  fun `SHOULD return empty list IN cashoutOffers WHEN cashout offer set is not created`() {
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, SPECIAL_CASHOUT_OFFERS) }, true)
    cashoutOffersService.mock({ getOfferSet(USER_ID) }, CashoutOfferSet.NotCreated)

    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatusStub)
    }.let {
      assertThat(it.cashoutOffers).isNotNull().isEmpty()
    }
  }

  @Test
  fun `SHOULD return correct list IN cashoutOffers WHEN cashout offer set is active`() {
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, SPECIAL_CASHOUT_OFFERS) }, true)
    imageService.stub {
      on { toUrl(any()) } doAnswer {
        "http://somePathHere/${it.arguments[0]}"
      }
    }
    cashoutOffersService.mock(
      { getOfferSet(USER_ID) }, CashoutOfferSet.Active(
        1, USER_ID, listOf(
          CashoutOffer.Active("offer1", 1, now),
          CashoutOffer.Claimed("offer2", 2),
          CashoutOffer.Unclaimed("offer3", 3)
        )
      )
    )
    gamesService.mock({ getGameById(ANDROID, 1) }, gameStub.copy(id = 1, iconFilename = "game1.jpg"))
    gamesService.mock({ getGameById(ANDROID, 2) }, gameStub.copy(id = 2, iconFilename = "game2.jpg"))
    gamesService.mock({ getGameById(ANDROID, 3) }, gameStub.copy(id = 3, iconFilename = "game3.jpg"))

    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatusStub)
    }.let {
      assertThat(it.cashoutOffers).isNotNull().all {
        index(0).all {
          transform { it.cashoutOfferId }.isEqualTo("offer1")
          transform { it.iconUrl }.isEqualTo("http://somePathHere/game1.jpg")
          transform { it.applicationId }.isEqualTo(gameStub.applicationId)
          transform { it.activityName }.isEqualTo(gameStub.activityName)
          transform { it.installationLink }.isNull()
          transform { it.status }.isEqualTo(CashoutOfferApiDto.Status.ACTIVE)
          transform { it.activeUntilDate }.isEqualTo(now)
        }
        index(1).all {
          transform { it.cashoutOfferId }.isEqualTo("offer2")
          transform { it.iconUrl }.isEqualTo("http://somePathHere/game2.jpg")
          transform { it.applicationId }.isEqualTo(gameStub.applicationId)
          transform { it.activityName }.isEqualTo(gameStub.activityName)
          transform { it.installationLink }.isNull()
          transform { it.status }.isEqualTo(CashoutOfferApiDto.Status.CLAIMED)
          transform { it.activeUntilDate }.isNull()
        }
        index(2).all {
          transform { it.cashoutOfferId }.isEqualTo("offer3")
          transform { it.iconUrl }.isEqualTo("http://somePathHere/game3.jpg")
          transform { it.applicationId }.isEqualTo(gameStub.applicationId)
          transform { it.activityName }.isEqualTo(gameStub.activityName)
          transform { it.installationLink }.isNull()
          transform { it.status }.isEqualTo(CashoutOfferApiDto.Status.UNCLAIMED)
          transform { it.activeUntilDate }.isNull()
        }
      }
    }
  }

  @Test
  fun `SHOULD apply giftBoxInsteadOfEarnings ON getUserCashoutConfig WHEN applicable`() {
    val cashoutStatus = cashoutStatusStub.copy(userHasCashouts = false)
    hideCashoutAmountExperimentService.mock({ shouldShowGiftBox(USER_ID, false, cashoutStatus.amountUsd) }, true)

    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatus)
    }.let { actual ->
      assertThat(actual).isEqualTo(
        cashoutApiDtoStub.copy(
          giftBoxInsteadOfEarnings = true,
          showRewards = false,
          //
          nextCashoutTimestamp = cashoutStatus.nextCashout,
          providersImageList = emptyList(),
          timestamp = now,
        )
      )
    }
  }

  @Test
  fun `SHOULD replace header text ON getUserCashoutConfig WHEN cashout available AND user participant`() {
    cashoutPersistenceService.mock({ hasCashouts(USER_ID) }, false)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(BigDecimal("0.99"), Currency.getInstance("USD"), BigDecimal("0.99"))
    )
    hideCashoutAmountExperimentService.mock({ shouldShowGiftBox(USER_ID, false, BigDecimal("0.99")) }, true)

    runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }.let { actual ->
      assertThat(actual.headerText).isEqualTo("Thanks for playing! Your cash reward is ready")
    }
  }

  @Test
  fun `SHOULD replace header text ON getUserCashoutConfig WHEN cashout available AND user participant AND second cashout period`() {
    val cashoutPeriodMock: CashoutPeriodDto = mock()
    whenever(cashoutPeriodMock.counter).thenReturn(2)
    whenever(cashoutPeriodMock.periodEnd).thenReturn(now)

    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodMock)
    cashoutPersistenceService.mock({ hasCashouts(USER_ID) }, false)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(BigDecimal("0.99"), Currency.getInstance("USD"), BigDecimal("0.99"))
    )
    hideCashoutAmountExperimentService.mock({ shouldShowGiftBox(USER_ID, false, BigDecimal("0.99")) }, true)

    runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }.let { actual ->
      assertThat(actual.headerText).isEqualTo("Thanks for playing! Your cash reward is ready")
    }
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 7, 11, 101, 180, 3000])
  fun `SHOULD respect first period duration for header value ON getUserCashoutConfig WHEN cashout available AND second cashout period`(firstCpDurationMinutes: Long) {
    val cashoutPeriodMock: CashoutPeriodDto = mock()
    whenever(cashoutPeriodMock.counter).thenReturn(2)
    whenever(cashoutPeriodMock.periodEnd).thenReturn(now)
    val duration = Duration.ofMinutes(firstCpDurationMinutes)
    val expectedDurationText =
      if (duration < Duration.ofHours(1)) "${firstCpDurationMinutes}m"
      else "${round(firstCpDurationMinutes / 60.0)}h"

    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodMock)
    cashoutPersistenceService.mock({ hasCashouts(USER_ID) }, false)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(BigDecimal("0.99"), Currency.getInstance("USD"), BigDecimal("0.99"))
    )
    cashoutPeriodsConfigService.mock({ firstPeriodDuration(USER_ID) }, duration)

    runBlocking { cashoutService.getCashoutStatus(USER_ID, EN_LOCALE) }.let { actual ->
      assertThat(actual.headerText)
        .isEqualTo(
          cashoutConfig.headerTextCashoutWithPeriod.replace("{{period_duration_description}}", expectedDurationText)
        )
    }
  }

  @Test
  fun `SHOULD cashoutButtonStyle ON getUserCashoutConfig WHEN experiment participant`() {
    abTestingService.mock({ assignedVariationValue(user.userId, ANDROID_HIDE_EARNINGS) }, ANDROID_HIDE_EARNINGS_GRAY_GREEN)

    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatusStub)
    }.let { actual ->
      assertThat(actual).isEqualTo(
        cashoutApiDtoStub.copy(
          cashoutButtonStyle = CashoutButtonStyle.GRAY_GREEN,
          //
          nextCashoutTimestamp = cashoutStatusStub.nextCashout,
          providersImageList = emptyList(),
          timestamp = now,
        )
      )
    }
  }

  @Test
  fun `SHOULD return address from ip service ON fillEmptyCashoutAddress even WHEN address is empty AND ios`() {
    ipService.mock({ getExtendedIpInfo(any()) }, ipData)

    runBlocking { cashoutService.fillEmptyCashoutAddress(cashoutDemandApiSample.copy(address = ""), "********") }
      .let { actual -> assertThat(actual).isEqualTo(cashoutDemandApiSample.copy(address = "United States, Illinois, Chicago")) }
  }

  @Test
  fun `SHOULD return address from ip service ON fillEmptyCashoutAddress`() {
    ipService.mock({ getExtendedIpInfo(any()) }, ipData)

    runBlocking { cashoutService.fillEmptyCashoutAddress(cashoutDemandApiSample, "********") }
      .let { actual -> assertThat(actual).isEqualTo(cashoutDemandApiSample.copy(address = "United States, Illinois, Chicago")) }
  }

  @Test
  fun `SHOULD return mocked address ON fillEmptyCashoutAddress WHEN no_address_on_cashout participant AND ip service returned null`() {
    ipService.mock({ getExtendedIpInfo("********") }, null)

    val expected = cashoutDemandApiSample.copy(address = "<failed to retrieve location for ip = ********>")

    runBlocking { cashoutService.fillEmptyCashoutAddress(cashoutDemandApiSample, "********") }.let { actual ->
      assertThat(actual).isEqualTo(expected)
    }
  }

  @Test
  fun `SHOULD emit sc_with_revenue event ON completeSuccessfulTransaction`() = runTest {
    cashoutPersistenceService.mock({ loadTransaction(CASHOUT_TRANSACTION_ID) }, cashoutTransactionStub)
    whenever(userEarningsPersistenceService.getCashoutRelatedRevenueSum(CASHOUT_TRANSACTION_ID)).thenReturn(BigDecimal("11.00"))
    whenever(userPersistenceService.fetchExternalIds(USER_ID)).thenReturn(
      UserExternalIds(USER_ID, "googleAdId", "idfa", null, "firebaseAppId", userDtoStub.trackingData)
    )
    whenever(adjustService.getUserIp(USER_ID)).thenReturn("***********")
    whenever(adjustService.getUserAgent(USER_ID)).thenReturn("Dalvik")

    cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
    advanceUntilIdle()

    verifyBlocking(googleAnalyticsService) {
      sendCashoutRevenueEvent(
        appInstanceId = "firebaseAppId",
        userId = USER_ID,
        appPlatform = ANDROID,
        revenueAmount = "11.00"
      )
    }
  }

  @Test
  fun `SHOULD not emit sc_with_revenue event ON completeSuccessfulTransaction WHEN there is incomplete tracking data`() = runTest {
    cashoutPersistenceService.mock({ loadTransaction(CASHOUT_TRANSACTION_ID) }, cashoutTransactionStub)

    whenever(userEarningsPersistenceService.getCashoutRelatedRevenueSum(CASHOUT_TRANSACTION_ID)).thenReturn(BigDecimal("11.00"))
    whenever(userPersistenceService.fetchExternalIds(USER_ID)).thenReturn(null)

    cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
    advanceUntilIdle()

    verifyNoInteractions(googleAnalyticsService)
  }

  @Test
  fun `SHOULD emit adjust sc_with_revenue_partner event ON completeSuccessfulTransaction`() = runTest {
    cashoutPersistenceService.mock({ loadTransaction(CASHOUT_TRANSACTION_ID) }, cashoutTransactionStub)
    whenever(userEarningsPersistenceService.getCashoutRelatedRevenueSum(CASHOUT_TRANSACTION_ID)).thenReturn(BigDecimal("11.00"))
    whenever(userPersistenceService.fetchExternalIds(USER_ID)).thenReturn(
      UserExternalIds(USER_ID, "googleAdId", "idfa", "adjustId", "firebaseAppId", userDtoStub.trackingData)
    )
    whenever(adjustService.getUserIp(USER_ID)).thenReturn("***********")
    whenever(adjustService.getUserAgent(USER_ID)).thenReturn("Dalvik/2.1.0 (Linux; U; Android 11; U696CL Build/UMX_U696CL_V11.01.01.07)")

    cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
    advanceUntilIdle()

    verifyBlocking(adjustApiClient) {
      sendCustomEvent(
        event = AdjustCustomEvent(
          eventType = AdMarketEvent.EventType.SC_WITH_REVENUE_PARTNER,
          googleAdId = "googleAdId",
          adjustId = "adjustId",
          idfa = "idfa",
          trackingId = "b19860d2-ccf9-472b-a0f6-c2e517bd841a",
          trackingType = "IDFA",
          appPlatform = user.appPlatform.name,
          createdAt = timeService.now(),
          userAgent = "Dalvik/2.1.0 (Linux; U; Android 11; U696CL Build/UMX_U696CL_V11.01.01.07)",
          ipAddress = "***********"
        ),
        partnerParams = mapOf("sc_currency" to "USD", "sc_revenue" to "11.00")
      )
    }
    verifyBlocking(messageBus) {
      publishAsync(
        effect = SendMolocoInAppEventEffect(
          event =
            MolocoInAppEvent(
              ipAddress = "***********",
              userAgent = "Dalvik/2.1.0 (Linux; U; Android 11; U696CL Build/UMX_U696CL_V11.01.01.07)",
              idfa = "b19860d2-ccf9-472b-a0f6-c2e517bd841a",
              idfv = null,
              platform = user.appPlatform,
              amount = BigDecimal("11.00"),
              timestamp = timeService.now().toEpochMilli()
            )
        )
      )
    }
  }

  @Test
  fun `SHOULD not emit adjust sc_with_revenue_partner event ON completeSuccessfulTransaction when no adjustId`() = runTest {
    cashoutPersistenceService.mock({ loadTransaction(CASHOUT_TRANSACTION_ID) }, cashoutTransactionStub)
    whenever(userEarningsPersistenceService.getCashoutRelatedRevenueSum(CASHOUT_TRANSACTION_ID)).thenReturn(BigDecimal("11.00"))
    whenever(userPersistenceService.fetchExternalIds(USER_ID)).thenReturn(
      UserExternalIds(USER_ID, "googleAdId", "idfa", null, "firebaseAppId", userDtoStub.trackingData)
    )
    whenever(adjustService.getUserIp(USER_ID)).thenReturn("***********")
    whenever(adjustService.getUserAgent(USER_ID)).thenReturn("Dalvik/2.1.0 (Linux; U; Android 11; U696CL Build/UMX_U696CL_V11.01.01.07)")

    cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
    advanceUntilIdle()

    verifyNoInteractions(adjustApiClient)
  }

  @Test
  fun `SHOULD not emit adjust sc_with_revenue_partner event ON completeSuccessfulTransaction when no revenue`() = runTest {
    cashoutPersistenceService.mock({ loadTransaction(CASHOUT_TRANSACTION_ID) }, cashoutTransactionStub)
    whenever(userEarningsPersistenceService.getCashoutRelatedRevenueSum(CASHOUT_TRANSACTION_ID)).thenReturn(BigDecimal.ZERO)
    whenever(userPersistenceService.fetchExternalIds(USER_ID)).thenReturn(
      UserExternalIds(USER_ID, "googleAdId", "idfa", null, "firebaseAppId", userDtoStub.trackingData)
    )
    whenever(adjustService.getUserIp(USER_ID)).thenReturn("***********")
    whenever(adjustService.getUserAgent(USER_ID)).thenReturn("Dalvik/2.1.0 (Linux; U; Android 11; U696CL Build/UMX_U696CL_V11.01.01.07)")

    cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
    advanceUntilIdle()

    verifyNoInteractions(adjustApiClient)
    verifyBlocking(messageBus) {
      messageBus.publish(
        message = XHoursPassedSinceNoEarningsCheckDto(userId = USER_ID, fromDate = now, counter = 1),
        delayUntil = now.plus(1, ChronoUnit.DAYS)
      )
    }
    verifyNoMoreInteractions(messageBus)
  }

  @ParameterizedTest
  @ValueSource(doubles = [0.5, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0])
  fun `SHOULD emit sc_reached_N and sc_reached_min_N events ON completeSuccessfulTransaction WEHN user is younger than 7 days`(revenue: Double) =
    testScope.runTest {
      cashoutPersistenceService.mock({ loadTransaction(CASHOUT_TRANSACTION_ID) }, cashoutTransactionStub)
      whenever(userEarningsPersistenceService.getCashoutRelatedRevenueSum(CASHOUT_TRANSACTION_ID)).thenReturn(BigDecimal.valueOf(revenue))
      rewardingFacade.mock({ getTotalUsdEarningsForUser(RevenueEventsServiceTest.USER_ID) }, BigDecimal.ZERO)

      cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
      testScope.advanceUntilIdle()

      val scReachedMinXEventRevenueThresholds = listOf(
        BigDecimal(10.0) to "sc_reached_min_10",
        BigDecimal(9.0) to "sc_reached_min_9",
        BigDecimal(8.0) to "sc_reached_min_8",
        BigDecimal(7.0) to "sc_reached_min_7",
        BigDecimal(6.0) to "sc_reached_min_6",
        BigDecimal(5.0) to "sc_reached_min_5",
        BigDecimal(4.0) to "sc_reached_min_4",
        BigDecimal(3.0) to "sc_reached_min_3",
        BigDecimal(2.0) to "sc_reached_min_2",
        BigDecimal(1.0) to "sc_reached_min_1",
        BigDecimal(0.5) to "sc_reached_min_05"
      )

      val expectedEvents = mutableListOf<String>()
      expectedEvents.add("sc_reached_${if (revenue % 1 == 0.0) revenue.toInt().toString() else revenue.toString().replace(".", "")}")
      scReachedMinXEventRevenueThresholds.forEach { (revenueReached, eventName) ->
        if (revenueReached <= BigDecimal.valueOf(revenue)) {
          expectedEvents.add(eventName)
        }
      }

      val actualEventsCaptor = argumentCaptor<List<String>>()
      verifyBlocking(adMarketService, times(2)) {
        sendMarketEvents(eq(USER_ID), actualEventsCaptor.capture())
      }

      val actualEvents = actualEventsCaptor.firstValue
      val actualEventsSecondCall = actualEventsCaptor.secondValue
      assertThat(actualEvents.plus(actualEventsSecondCall).toSet()).isEqualTo(expectedEvents.toSet())
      verifyNoMoreInteractions(adMarketService)
    }

  @Test
  fun `SHOULD emit only sc_reached_min_N ON completeSuccessfulTransaction WEHN user is older than 7 days`() =
    testScope.runTest {
      cashoutPersistenceService.mock({ loadTransaction(CASHOUT_TRANSACTION_ID) }, cashoutTransactionStub)
      whenever(userEarningsPersistenceService.getCashoutRelatedRevenueSum(CASHOUT_TRANSACTION_ID)).thenReturn(ONE)
      userService.mock({ getUser(USER_ID, includingDeleted = true) }, userDtoStub.copy(createdAt = now.minus(8, ChronoUnit.DAYS)))

      cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
      testScope.advanceUntilIdle()
      val actualEventsCaptor = argumentCaptor<List<String>>()
      verifyBlocking(adMarketService, times(1)) {
        sendMarketEvents(eq(USER_ID), actualEventsCaptor.capture())
      }

      val expectedEvents = listOf("sc_reached_min_05", "sc_reached_min_1")

      val actualEvents = actualEventsCaptor.firstValue
      assertThat(actualEvents.toSet()).isEqualTo(expectedEvents.toSet())
      verifyNoMoreInteractions(adMarketService)
    }

  @Test
  fun `SHOULD not emit any cashout events ON completeSuccessfulTransaction WEHN user is older than 30 days`() =
    testScope.runTest {
      cashoutPersistenceService.mock({ loadTransaction(CASHOUT_TRANSACTION_ID) }, cashoutTransactionStub)
      userService.mock({ getUser(USER_ID, includingDeleted = true) }, userDtoStub.copy(createdAt = now.minus(31, ChronoUnit.DAYS)))

      cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
      testScope.advanceUntilIdle()
      verifyNoInteractions(adMarketService)
    }

  @Test
  fun `SHOULD not emit any earnings_reached_min_x events ON completeSuccessfulTransaction WEHN user is on IOS`() =
    testScope.runTest {
      cashoutPersistenceService.mock({ loadTransaction(CASHOUT_TRANSACTION_ID) }, cashoutTransactionStub)
      rewardingFacade.mock({ getTotalUsdEarningsForUser(USER_ID) }, BigDecimal(10.0))
      whenever(userEarningsPersistenceService.getCashoutRelatedRevenueSum(CASHOUT_TRANSACTION_ID)).thenReturn(BigDecimal.ZERO)
      userService.mock(
        { getUser(USER_ID, includingDeleted = true) },
        userDtoStub.copy(createdAt = now.minus(8, ChronoUnit.DAYS), appPlatform = IOS)
      )

      cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
      testScope.advanceUntilIdle()
      val actualEventsCaptor = argumentCaptor<List<String>>()
      verifyBlocking(adMarketService, times(1)) {
        sendMarketEvents(eq(USER_ID), actualEventsCaptor.capture())
      }

      val actualEvents = actualEventsCaptor.firstValue
      assertThat(actualEvents.size).isEqualTo(0)
      verifyNoMoreInteractions(adMarketService)
    }

  @Test
  fun `SHOULD skip user check manager call on completeSuccessfulTransaction WHEN user handle non null and provider is Venmo`() {
    cashoutPersistenceService.mock(
      { loadTransaction(CASHOUT_TRANSACTION_ID) },
      cashoutTransactionStub.copy(provider = VENMO, userHandle = "venmoUserHandle")
    )

    runBlocking {
      cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
    }

    verifyBlocking(cashoutPersistenceService) {
      updateTransactionStatus(CASHOUT_TRANSACTION_ID, SUCCESSFUL)
    }
    verifyNoInteractions(userCheckManager)
  }

  @Test
  fun `SHOULD NOT skip user check manager call on completeSuccessfulTransaction WHEN user is assigned to enableVenmo experiment and provider is non Venmo`() {
    abTestingService.mock({ shouldEnableVenmo(userId = USER_ID) }, true)
    cashoutPersistenceService.mock(
      { loadTransaction(CASHOUT_TRANSACTION_ID) },
      cashoutTransactionStub.copy(provider = WALMART)
    )

    runBlocking {
      cashoutService.completeSuccessfulTransaction(CASHOUT_TRANSACTION_ID)
    }

    verifyBlocking(cashoutPersistenceService) {
      updateTransactionStatus(CASHOUT_TRANSACTION_ID, SUCCESSFUL)
    }
    verifyBlocking(userCheckManager) { onSuccessfulCashout(cashoutTransactionStub.copy(provider = WALMART)) }
  }

  @ParameterizedTest
  @MethodSource("paramsPaymentProviderSurvey")
  fun `SHOULD return paymentProviderSurvey WHEN user is exp participant`(
    platform: AppPlatform,
    experiment: ClientExperiment,
    variation: PaymentProviderSurveyVariation
  ) = runTest {
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(appPlatform = platform))
    abTestingService.mock({ assignedVariationValue(USER_ID, experiment) }, variation)
    cashoutPersistenceService.mock({ hasCashouts(USER_ID) }, false)
    paymentProviderSurveyService.mock({ shouldShowSurvey(USER_ID) }, true)
    val cashoutStatus = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }
    val result = cashoutService.getUserCashoutConfig(USER_ID, cashoutStatus)
    assertThat(result.paymentProviderSurvey).isNotNull().isEqualTo(PaymentProviderSurvey.fromVariation(variation))
  }

  @ParameterizedTest
  @MethodSource("paramsPaymentProviderSurvey")
  fun `SHOULD NOT return paymentProviderSurvey WHEN user is exp participant but has cashouts`(
    platform: AppPlatform,
    experiment: ClientExperiment,
    variation: PaymentProviderSurveyVariation
  ) = runTest {
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(appPlatform = platform))
    abTestingService.mock({ assignedVariationValue(USER_ID, experiment) }, variation)
    cashoutPersistenceService.mock({ hasCashouts(USER_ID) }, true)
    paymentProviderSurveyService.mock({ shouldShowSurvey(USER_ID) }, true)
    val cashoutStatus = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }
    val result = cashoutService.getUserCashoutConfig(USER_ID, cashoutStatus)
    assertThat(result.paymentProviderSurvey).isNull()
  }

  @ParameterizedTest
  @MethodSource("paramsPaymentProviderSurvey")
  fun `SHOULD NOT return paymentProviderSurvey WHEN user is exp participant but completed survey`(
    platform: AppPlatform,
    experiment: ClientExperiment,
    variation: PaymentProviderSurveyVariation
  ) = runTest {
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(appPlatform = platform))
    abTestingService.mock({ assignedVariationValue(USER_ID, experiment) }, variation)
    cashoutPersistenceService.mock({ hasCashouts(USER_ID) }, false)
    paymentProviderSurveyService.mock({ shouldShowSurvey(USER_ID) }, false)
    val cashoutStatus = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }
    val result = cashoutService.getUserCashoutConfig(USER_ID, cashoutStatus)
    assertThat(result.paymentProviderSurvey).isNull()
  }

  @ParameterizedTest
  @EnumSource(value = Variations::class, names = ["CHANGE_EMAIL", "CHANGE_EMAIL_AND_FOLLOWING_TEXT"])
  fun `SHOULD return new PP hints ON WHEN getUserCashoutConfig WHEN user is exp participant`(variation: Variations) = runTest {
    abTestingService.mock({ assignedVariationValue(USER_ID, ANDROID_PAYPAL_HINTS) }, variation)

    val cashoutStatus = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }
    val result = cashoutService.getUserCashoutConfig(USER_ID, cashoutStatus)

    assertThat(result.providers.filter { it.provider == AMAZON }[0].identifierHint).isEqualTo("Amazon account")
    assertThat(result.providers.filter { it.provider == AMAZON }[0].identifierExplanation).isNull()
    assertThat(result.providers.filter { it.provider == PAYPAL }[0].identifierHint).isEqualTo("Email associated with your Paypal")
    assertThat(result.providers.filter { it.provider == PAYPAL }[0].identifierExplanation).isEqualTo(
      if (variation == CHANGE_EMAIL) {
        null
      } else {
        "In case you do not have a PayPal account, you will receive an invitation"
      }
    )
  }

  @ParameterizedTest
  @ValueSource(booleans = [false, true])
  fun `SHOULD return userHasCashouts ON getCashoutStatus`(userHasCashouts: Boolean) = runTest {
    cashoutPersistenceService.mock({ hasCashouts(USER_ID) }, userHasCashouts)

    val cashoutStatus = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(cashoutStatus.userHasCashouts).isEqualTo(userHasCashouts)
  }

  @Test
  fun `SHOULD return amountBefore ON getCashoutStatus WHEN nonBoosted amounts available`() = runTest {
    val earnings = UserCurrencyEarnings(
      amountUsd = BigDecimal("1.00"),
      userCurrency = Currency.getInstance("CAD"),
      userCurrencyAmount = BigDecimal("1.10"),
      nonBoostedAmountUsd = BigDecimal("0.9"),
      nonBoostedUserCurrencyAmount = BigDecimal("0.95"),
    )

    rewardingFacade.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, earnings)

    val cashoutStatus = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(cashoutStatus.amountBefore).isNotNull().isEqualByComparingTo("0.95")
  }

  @Test
  fun `SHOULD apply non-resettable coins exp ON demandCashout WHEN user is Complete participant`() = testScope.runTest {
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(eq(USER_ID), any()) }, 1)
    userEarningsPersistenceService.mock(
      { getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) },
      UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    )
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(appPlatform = IOS))
    abTestingService.mock({ coinsDoNotResetVariation(USER_ID, IOS) }, CoinsDoNotResetVariation.Complete)

    withMockedUUID(CASHOUT_TRANSACTION_ID) {
      cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID)
    }
    testScope.advanceUntilIdle()
  }

  @Test
  fun `SHOULD invoke removeTrackedInitiatedCashouts ON demandCashout WHEN user is ANDROID_INCOMPLETE_CASHOUT_RESTORING participant`() {
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(eq(USER_ID), any()) }, 1)
    userEarningsPersistenceService.mock(
      { getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) },
      UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    )
    abTestingService.mock({ isUserExperimentParticipant(userId = USER_ID, ANDROID_INCOMPLETE_CASHOUT_RESTORING) }, true)

    runBlocking {
      withMockedUUID(CASHOUT_TRANSACTION_ID) {
        cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID)
      }
    }

    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, earningsCashoutDemand.emailHash, earningsCashoutDemand.normalizedEmailHash) }
    verifyBlocking(cashoutPersistenceService) { createTransaction(eq(USER_ID), any(), eq(earningsCashoutDemand)) }
    verifyBlocking(cashoutPersistenceService) { updateTransactionStatus(any(), eq(REQUESTED)) }
    verifyBlocking(cashoutStatusService) { disableCashout(USER_ID) }
    verifyBlocking(incompleteCashoutService) { removeTrackedInitiatedCashouts(USER_ID) }
  }

  @Test
  fun `SHOULD add Cashout2xOfferApiDto ON demandCashout WHEN user is ANDROID_CASHOUT_2X_OFFER participant`() {
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(eq(USER_ID), any()) }, 1)
    userEarningsPersistenceService.mock(
      { getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) },
      UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    )
    abTestingService.mock({ assignedVariationValue(userId = USER_ID, ANDROID_CASHOUT_2X_OFFER) }, AndroidCashout2xOfferVariation.AndroidCashout2xOfferOn)
    val expectedApiDto = CashoutDemandResponseApiDto(
      showRewardsIcon = true,
      providerIssuesMessage = null,
      cashout2xOffer = Cashout2xOfferApiDto(
        title = "Double Your Next Cash-Out?",
        description = "Claim your offer before it's gone and earn up to 200% for the next 3 hours!",
        buttonText = "Claim Offer",
        claimPeriodMs = 120000
      )
    )

    runBlocking {
      withMockedUUID(CASHOUT_TRANSACTION_ID) {
        cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID).let {
          assertEquals(expectedApiDto, it)
        }
      }
    }

    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, earningsCashoutDemand.emailHash, earningsCashoutDemand.normalizedEmailHash) }
    verifyBlocking(cashoutPersistenceService) { createTransaction(eq(USER_ID), any(), eq(earningsCashoutDemand)) }
    verifyBlocking(cashoutPersistenceService) { updateTransactionStatus(any(), eq(REQUESTED)) }
    verifyBlocking(cashoutStatusService) { disableCashout(USER_ID) }
    verifyBlocking(messageBus) { publishAsync(DeletePopupMessageEffect(USER_ID, EARNINGS_THRESHOLD_REACHED)) }
    verifyBlocking(surveyService) { sendCashoutSurveyIfApplicable(USER_ID, ANDROID) }
  }

  @Test
  fun `SHOULD add Cashout2xOfferApiDto ON demandCashout WHEN test env AND user cp less than 3h`() {
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(eq(USER_ID), any()) }, 1)
    userEarningsPersistenceService.mock(
      { getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) },
      UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    )
    abTestingService.mock({ assignedVariationValue(userId = USER_ID, ANDROID_CASHOUT_2X_OFFER) }, AndroidCashout2xOfferVariation.AndroidCashout2xOfferOn)
    buildVariantProvider.mock({ get() }, TEST)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodStub.copy(periodEnd = now.plus(5, MINUTES)))
    val expectedApiDto = CashoutDemandResponseApiDto(
      showRewardsIcon = true,
      providerIssuesMessage = null,
      cashout2xOffer = Cashout2xOfferApiDto(
        title = "Double Your Next Cash-Out?",
        description = "Claim your offer before it's gone and earn up to 200% for the next 3 hours!",
        buttonText = "Claim Offer",
        claimPeriodMs = 120000
      )
    )

    runBlocking {
      withMockedUUID(CASHOUT_TRANSACTION_ID) {
        cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID).let {
          assertEquals(expectedApiDto, it)
        }
      }
    }

    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, earningsCashoutDemand.emailHash, earningsCashoutDemand.normalizedEmailHash) }
    verifyBlocking(cashoutPersistenceService) { createTransaction(eq(USER_ID), any(), eq(earningsCashoutDemand)) }
    verifyBlocking(cashoutPersistenceService) { updateTransactionStatus(any(), eq(REQUESTED)) }
    verifyBlocking(cashoutStatusService) { disableCashout(USER_ID) }
    verifyBlocking(messageBus) { publishAsync(DeletePopupMessageEffect(USER_ID, EARNINGS_THRESHOLD_REACHED)) }
    verifyBlocking(surveyService) { sendCashoutSurveyIfApplicable(USER_ID, ANDROID) }
  }

  @Test
  fun `SHOULD add Cashout2xOfferApiDto ON demandCashout WHEN BM variation AND cp has more than 25 minutes remaining`() {
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(eq(USER_ID), any()) }, 1)
    userEarningsPersistenceService.mock(
      { getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) },
      UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    )
    abTestingService.mock({ assignedVariationValue(userId = USER_ID, ANDROID_CASHOUT_2X_OFFER) }, AndroidCashout2xOfferVariation.AndroidCashout2xOfferBM1)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodStub.copy(periodEnd = now.plus(30, MINUTES)))
    val expectedApiDto = CashoutDemandResponseApiDto(
      showRewardsIcon = true,
      providerIssuesMessage = null,
      cashout2xOffer = Cashout2xOfferApiDto(
        title = "Double Your Next Cash-Out?",
        description = "Claim your offer before it's gone and earn up to 200% for limited time!",
        buttonText = "Claim Offer",
        claimPeriodMs = 120000
      )
    )

    runBlocking {
      withMockedUUID(CASHOUT_TRANSACTION_ID) {
        cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID).let {
          assertEquals(expectedApiDto, it)
        }
      }
    }

    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, earningsCashoutDemand.emailHash, earningsCashoutDemand.normalizedEmailHash) }
    verifyBlocking(cashoutPersistenceService) { createTransaction(eq(USER_ID), any(), eq(earningsCashoutDemand)) }
    verifyBlocking(cashoutPersistenceService) { updateTransactionStatus(any(), eq(REQUESTED)) }
    verifyBlocking(cashoutStatusService) { disableCashout(USER_ID) }
    verifyBlocking(messageBus) { publishAsync(DeletePopupMessageEffect(USER_ID, EARNINGS_THRESHOLD_REACHED)) }
    verifyBlocking(surveyService) { sendCashoutSurveyIfApplicable(USER_ID, ANDROID) }
  }

  @Test
  fun `SHOULD NOT add Cashout2xOfferApiDto ON demandCashout WHEN BM variation AND cp has less than 25 minutes remaining`() {
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(eq(USER_ID), any()) }, 1)
    userEarningsPersistenceService.mock(
      { getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) },
      UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    )
    abTestingService.mock({ assignedVariationValue(userId = USER_ID, ANDROID_CASHOUT_2X_OFFER) }, AndroidCashout2xOfferVariation.AndroidCashout2xOfferBM1)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodStub.copy(periodEnd = now.plus(20, MINUTES)))
    val expectedApiDto = CashoutDemandResponseApiDto(
      showRewardsIcon = true,
      providerIssuesMessage = null,
      cashout2xOffer = null
    )

    runBlocking {
      withMockedUUID(CASHOUT_TRANSACTION_ID) {
        cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID).let {
          assertEquals(expectedApiDto, it)
        }
      }
    }

    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, earningsCashoutDemand.emailHash, earningsCashoutDemand.normalizedEmailHash) }
    verifyBlocking(cashoutPersistenceService) { createTransaction(eq(USER_ID), any(), eq(earningsCashoutDemand)) }
    verifyBlocking(cashoutPersistenceService) { updateTransactionStatus(any(), eq(REQUESTED)) }
    verifyBlocking(cashoutStatusService) { disableCashout(USER_ID) }
    verifyBlocking(messageBus) { publishAsync(DeletePopupMessageEffect(USER_ID, EARNINGS_THRESHOLD_REACHED)) }
    verifyBlocking(surveyService) { sendCashoutSurveyIfApplicable(USER_ID, ANDROID) }
  }

  @Test
  fun `SHOULD add Cashout2xOfferApiDto ON demandCashout WHEN BM variation AND cp has exactly 25 minutes remaining`() {
    userEarningsPersistenceService.mock({ updateUnpaidUserEarningsForDemand(eq(USER_ID), any()) }, 1)
    userEarningsPersistenceService.mock(
      { getEarningsSumForTransaction(CASHOUT_TRANSACTION_ID) },
      UserCurrencyEarnings(BigDecimal("4.0099"), Currency.getInstance("CAD"), BigDecimal("4.4099"))
    )
    abTestingService.mock({ assignedVariationValue(userId = USER_ID, ANDROID_CASHOUT_2X_OFFER) }, AndroidCashout2xOfferVariation.AndroidCashout2xOfferBM125)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodStub.copy(periodEnd = now.plus(25, MINUTES)))
    val expectedApiDto = CashoutDemandResponseApiDto(
      showRewardsIcon = true,
      providerIssuesMessage = null,
      cashout2xOffer = Cashout2xOfferApiDto(
        title = "Double Your Next Cash-Out?",
        description = "Claim your offer before it's gone and earn up to 200% for limited time!",
        buttonText = "Claim Offer",
        claimPeriodMs = 120000
      )
    )

    runBlocking {
      withMockedUUID(CASHOUT_TRANSACTION_ID) {
        cashoutService.demandCashout(USER_ID, earningsCashoutDemand, ANDROID).let {
          assertEquals(expectedApiDto, it)
        }
      }
    }

    verifyBlocking(fraudScoreService) { onNewUserEmail(USER_ID, earningsCashoutDemand.emailHash, earningsCashoutDemand.normalizedEmailHash) }
    verifyBlocking(cashoutPersistenceService) { createTransaction(eq(USER_ID), any(), eq(earningsCashoutDemand)) }
    verifyBlocking(cashoutPersistenceService) { updateTransactionStatus(any(), eq(REQUESTED)) }
    verifyBlocking(cashoutStatusService) { disableCashout(USER_ID) }
    verifyBlocking(messageBus) { publishAsync(DeletePopupMessageEffect(USER_ID, EARNINGS_THRESHOLD_REACHED)) }
    verifyBlocking(surveyService) { sendCashoutSurveyIfApplicable(USER_ID, ANDROID) }
  }

  @Test
  fun `SHOULD fill fullscreenCashout ON getUserCashoutConfig WHEN experiment participant`() {
    abTestingService.mock({ assignedVariationValue(user.userId, ANDROID_FULLSCREEN_CASHOUT_FORM_STYLE) }, AndroidFullscreenCashoutVariation.FullScreenCashout)

    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatusStub)
    }.let { actual ->
      assertThat(actual).isEqualTo(
        cashoutApiDtoStub.copy(
          cashoutFormStyle = CashoutFormStyle.FULLSCREEN,
          //
          nextCashoutTimestamp = cashoutStatusStub.nextCashout,
          providersImageList = emptyList(),
          timestamp = now,
        )
      )
    }
  }

  @Test
  fun `SHOULD return enabled highlightedGamesOnCashoutCancel ON getUserCashoutConfig WHEN user participant`() {
    val cashoutStatus = cashoutStatusStub.copy(isEnabled = true)
    androidHighlightedGamesService.mock({ shouldShowHighlightedGamesOnCashoutCancel(USER_ID) }, true)
    cashoutPersistenceService.mock({ getProviderImagesConfig(any(), any()) }, providerImagesCfg)

    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatus)
    }.let {
      assertThat(it).isEqualTo(
        cashoutApiDtoStub.copy(
          nextCashoutTimestamp = cashoutStatus.nextCashout,
          highlightedGamesOnCashoutCancel = true,
          timestamp = now,
        )
      )
    }
  }

  @Test
  fun `SHOULD add boosted mode info ON getUserCashoutConfig WHEN bm is active`() {
    val cashoutStatus = cashoutStatusStub.copy(isEnabled = true)
    cashoutPersistenceService.mock({ getProviderImagesConfig(any(), any()) }, providerImagesCfg)
    boostedModeService.mock({ findCurrentBoostedMode(USER_ID) }, boostedModeStub(USER_ID))
    translationService.mock({ tryTranslate("cashoutScreenHintTranslation", userDtoStub.locale, USER_ID) }, "cashoutScreenHintTranslated")

    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatus)
    }.let {
      assertThat(it).isEqualTo(
        cashoutApiDtoStub.copy(
          nextCashoutTimestamp = cashoutStatus.nextCashout,
          timestamp = now,
          boostedMode = CashoutBoostedModeApiDto(
            hintText = "cashoutScreenHintTranslated"
          )
        )
      )
    }
  }

  @Test
  fun `SHOULD add amountTextBefore ON getUserCashoutConfig WHEN amount before defined`() {
    val cashOutStatus = cashoutStatusStub.copy(
      amountBefore = BigDecimal("0.98765")
    )

    cashoutPersistenceService.mock({ getProviderImagesConfig(any(), any()) }, providerImagesCfg)

    runBlocking { cashoutService.getUserCashoutConfig(USER_ID, cashOutStatus) }.let { actual ->
      assertThat(actual).isEqualTo(
        cashoutApiDtoStub.copy(
          nextCashoutTimestamp = cashOutStatus.nextCashout,
          timestamp = now,
          cashoutAmountBefore = "$0.99"
        )
      )
    }
  }

  @Test
  fun `SHOULD return non paid earnings ON getNonCashedUserCurrencyEarnings`() = runTest {
    val expected = UserCurrencyEarnings(
      amountUsd = BigDecimal("1.00"),
      userCurrency = Currency.getInstance("CAD"),
      userCurrencyAmount = BigDecimal("1.10"),
      nonBoostedAmountUsd = BigDecimal("0.9"),
      nonBoostedUserCurrencyAmount = BigDecimal("0.95"),
    )
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      expected
    )

    val actual = cashoutService.getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID)

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD ignore non boosted amounts ON getNonCashedUserCurrencyEarnings WHEN difference with earnings is insignificant`() = runTest {
    val earnings = UserCurrencyEarnings(
      amountUsd = BigDecimal("1.00"),
      userCurrency = Currency.getInstance("CAD"),
      userCurrencyAmount = BigDecimal("1.10"),
      nonBoostedAmountUsd = BigDecimal("0.995"),
      nonBoostedUserCurrencyAmount = BigDecimal("0.95"),
    )
    val expected = UserCurrencyEarnings(
      amountUsd = BigDecimal("1.00"),
      userCurrency = Currency.getInstance("CAD"),
      userCurrencyAmount = BigDecimal("1.10"),
    )

    rewardingFacade.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, earnings)

    val actual = cashoutService.getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID)

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD ignore non boosted amounts ON getNonCashedUserCurrencyEarnings WHEN no difference with earnings`() = runTest {
    val earnings = UserCurrencyEarnings(
      amountUsd = BigDecimal("1.00"),
      userCurrency = Currency.getInstance("CAD"),
      userCurrencyAmount = BigDecimal("1.10"),
      nonBoostedAmountUsd = BigDecimal("1.00"),
      nonBoostedUserCurrencyAmount = BigDecimal("1.10"),
    )
    val expected = UserCurrencyEarnings(
      amountUsd = BigDecimal("1.00"),
      userCurrency = Currency.getInstance("CAD"),
      userCurrencyAmount = BigDecimal("1.10"),
    )

    rewardingFacade.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, earnings)

    val actual = cashoutService.getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID)

    assertThat(actual).isEqualTo(expected)
  }

  @ParameterizedTest
  @CsvSource("130.00, 169.00", "30.00, 39.00")
  fun `SHOULD return unpaid earnings threshold ON getNonCashedUserCurrencyEarnings when unpaid sum is equals or over the threshold`(
    sumUsd: String,
    sum: String
  ) = runTest {
    val thresholdUserCurrency = BigDecimal("39.00") // 30$ -> ~39 CAD
    val userEarnings = UserCurrencyEarnings(BigDecimal(sumUsd), Currency.getInstance("CAD"), BigDecimal(sum))
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      userEarnings
    )
    cashoutSettingsService.mock({ getUserMaxEarningsAmount(USER_ID) }, maxEarnings)
    currencyExchangeService.mock(
      { convert(maxEarnings, userEarnings.userCurrency) },
      CurrencyExchangeResultDto(BigDecimal(sumUsd), Currency.getInstance("CAD"), thresholdUserCurrency, thresholdUserCurrency)
    )

    val actual = cashoutService.getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID)

    assertThat(actual.userCurrencyAmount).isEqualByComparingTo(thresholdUserCurrency)
    assertThat(actual.amountUsd).isEqualByComparingTo(maxEarnings)
    assertThat(actual.userCurrency).isEqualTo(Currency.getInstance("CAD"))
    if (sumUsd != "30.00")
      verifyBlocking(currencyExchangeService) { convert(maxEarnings, userEarnings.userCurrency) }
  }

  @Test
  fun `SHOULD respect threshold ON hideUserCurrencyEarningsAboveThreshold WHEN non boosted earnings defined`() = runTest {
    val thresholdUserCurrency = BigDecimal("39.00") // 30$ -> ~39 CAD
    val userEarnings = UserCurrencyEarnings(
      amountUsd = BigDecimal("130"),
      userCurrency = Currency.getInstance("CAD"),
      userCurrencyAmount = BigDecimal("150"),
      nonBoostedAmountUsd = BigDecimal("100"),
      nonBoostedUserCurrencyAmount = BigDecimal("120")
    )

    cashoutSettingsService.mock({ getUserMaxEarningsAmount(USER_ID) }, maxEarnings)
    currencyExchangeService.mock(
      { convert(maxEarnings, userEarnings.userCurrency) },
      CurrencyExchangeResultDto(BigDecimal("55"), Currency.getInstance("CAD"), thresholdUserCurrency, thresholdUserCurrency)
    )

    val actual = cashoutService.hideUserCurrencyEarningsAboveThreshold(userEarnings, USER_ID)

    assertThat(actual.userCurrencyAmount).isEqualByComparingTo(thresholdUserCurrency)
    assertThat(actual.amountUsd).isEqualByComparingTo(maxEarnings)
    assertThat(actual.userCurrency).isEqualTo(Currency.getInstance("CAD"))
    assertThat(actual.nonBoostedUserCurrencyAmount).isEqualTo(thresholdUserCurrency)
    assertThat(actual.nonBoostedAmountUsd).isEqualTo(maxEarnings)
  }


  @ParameterizedTest
  @ValueSource(strings = ["10.00", "30.00"])
  fun `SHOULD return earnings ON hideUserCurrencyEarningsAboveThreshold WHEN it is less or equals than threshold`(sum: String) = runTest {
    val userEarnings = UserCurrencyEarnings(BigDecimal(sum), Currency.getInstance("CAD"), BigDecimal(sum))

    cashoutService.hideUserCurrencyEarningsAboveThreshold(userEarnings, USER_ID)
      .let { assertThat(it).isEqualTo(userEarnings) }
  }


  @Test
  fun `SHOULD return threshold ON hideUserCurrencyEarningsAboveThreshold WHEN incoming earnings over the threshold`() = runTest {
    cashoutSettingsService.mock({ getUserMaxEarningsAmount(USER_ID) }, maxEarnings)
    currencyExchangeService.mock(
      { convert(maxEarnings, Currency.getInstance("CAD")) },
      CurrencyExchangeResultDto(
        amount = BigDecimal("31"),
        userCurrency = Currency.getInstance("CAD"),
        usdAmount = BigDecimal("1"),
        amountNoRounding = BigDecimal("1")
      )
    )
    val userEarnings = UserCurrencyEarnings(BigDecimal("111"), Currency.getInstance("CAD"), BigDecimal("115"))
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      CurrencyExchangeResultDto(BigDecimal("111"), Currency.getInstance("CAD"), BigDecimal("1"), BigDecimal("1"))
    )

    cashoutService.hideUserCurrencyEarningsAboveThreshold(userEarnings, USER_ID)
      .let { assertThat(it).isEqualTo(userEarnings.copy(maxEarnings, userEarnings.userCurrency, BigDecimal("31"))) }
  }

  @Test
  fun `SHOULD return zero non paid earnings  ON getNonCashedUserCurrencyEarnings WHEN user has no earnings`() = runTest {
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      null
    )
    marketService.mock({ getUserCurrency(USER_ID) }, Currency.getInstance("CAD"))

    val actual = cashoutService.getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID)

    assertThat(actual).isEqualTo(
      UserCurrencyEarnings(BigDecimal.ZERO, Currency.getInstance("CAD"), BigDecimal.ZERO)
    )
  }

  @ParameterizedTest
  @CsvSource("30.00, 10.00, 10.00", "30.00, 40.00, 27.00")
  fun `SHOULD return bonus bank amount ON getCashoutStatus`(totalBalance: BigDecimal, bonusBankBalance: BigDecimal, expectedBonusBankEarnings: BigDecimal) {
    abTestingService.mock({ assignedVariationValue(USER_ID, BONUS_BANK) }, Variations.BONUS_CASH_BAR)
    rewardingFacade.mock({ getBonusBankEarnings(USER_ID) }, 42.toBigDecimal())
    currencyExchangeService.mock(
      { convert(42.toBigDecimal(), Currency.getInstance("USD")) },
      CurrencyExchangeResultDto(42.toBigDecimal(), Currency.getInstance("USD"), bonusBankBalance, 36.toBigDecimal())
    )
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(1.toBigDecimal(), Currency.getInstance("USD"), totalBalance)
    )

    val cashoutStatus = runBlocking {
      cashoutService.getCashoutStatus(USER_ID, EN_LOCALE)
    }

    assertThat(cashoutStatus.bonusAmount).isNotNull().isEqualByComparingTo(expectedBonusBankEarnings)
  }

  @Test
  fun `SHOULD show bonus bank amount in user currency on getUserCashoutConfig`() {
    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatusStub.copy(bonusAmount = 42.4235345.toBigDecimal()))
    }.let {
      assertThat(it.bonusAmountText).isEqualTo("$42.42")
    }
  }

  @Test
  fun `SHOULD show null bonus bank amount on getUserCashoutConfig IF balance is 0`() {
    runBlocking {
      cashoutService.getUserCashoutConfig(USER_ID, cashoutStatusStub.copy(bonusAmount = 0.00.toBigDecimal()))
    }.let {
      assertThat(it.bonusAmountText).isNull()
    }
  }
}
