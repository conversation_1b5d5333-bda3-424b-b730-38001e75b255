package com.moregames.playtime.user.promotion.event.manager

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.messaging.customnotification.ButtonAction
import com.moregames.base.messaging.customnotification.ButtonActionName
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.promotion.event.manager.PromotionEventService.Companion.EMPTY_PROMOTION_CONFIG
import com.moregames.playtime.user.promotion.event.manager.api.admin.*
import com.moregames.playtime.user.promotion.event.manager.api.client.*
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarContentApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarContentParametersApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarDynamicValueConfigurationApiDto
import com.moregames.playtime.user.promotion.event.manager.dto.AnnouncementDetailsDto
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEvent
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEventTranslationLink
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEventType
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEventUiConfiguration
import com.moregames.playtime.user.timezone.UserTimeZoneService
import com.moregames.playtime.util.defaultJsonConverter
import com.moregames.playtime.utils.DE_LOCALE
import com.moregames.playtime.utils.androidGameOfferListStub
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.encodeToString
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*

class PromotionEventServiceTest {
  private val promotionEventPersistenceService: PromotionEventPersistenceService = mock()
  private val userService: UserService = mock()
  private val timeService: TimeService = mock()
  private val translationService: UserTranslationService = mock()
  private val userTimeZoneService: UserTimeZoneService = mock()
  private val promotionTranslationService: PromotionTranslationService = mock()
  private val promotionEventConfigResolver: PromotionEventConfigResolver = mock()
  private val promotionEventValidator: PromotionEventValidator = mock()

  private val service = PromotionEventService(
    promotionEventPersistenceService = promotionEventPersistenceService,
    timeService = timeService,
    json = defaultJsonConverter,
    translationService = translationService,
    userTimeZoneService = userTimeZoneService,
    promotionEventConfigResolver = promotionEventConfigResolver,
    promotionTranslationService = promotionTranslationService,
    promotionEventValidator = promotionEventValidator,
  )

  companion object {
    const val USER_ID = "userId"
    val BUTTON_ACTION = ButtonAction(ButtonActionName.ROUTE_TO_MAIN)
    val promotionEventConfigDto = PromotionEventConfigApiDto(
      id = "TEST_2",
      startTime = 1732104000,
      endTime = 1732968000,
      timestamp = 1732276800,
      top = TopConfigApiDto(
        backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
        foregroundImage = "Autumn_fore.png",
        gradientTop = "#FF0000",
        gradientBottom = "#FF0033",
        cashoutButtonColor = "#FF0057",
        durationInMillis = 3000,
        order = 1
      ),
      mainTop = listOf(
        TopConfigApiDto(
          backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
          foregroundImage = "Autumn_fore.png",
          gradientTop = "#FF0000",
          gradientBottom = "#FF0033",
          cashoutButtonColor = "#FF0057",
          durationInMillis = 3000,
          order = 1
        ),
        TopConfigApiDto(
          backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background-1.png",
          foregroundImage = "Autumn_fore.png",
          gradientTop = "#FF0001",
          gradientBottom = "#FF0032",
          cashoutButtonColor = "#FF0053",
          durationInMillis = 4000,
          order = 5
        ),
      ),
      challengesTop = listOf(
        TopConfigApiDto(
          backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
          foregroundImage = "Autumn_fore.png",
          gradientTop = "#FF0000",
          gradientBottom = "#FF0033",
          cashoutButtonColor = "#FF0057",
          durationInMillis = 3000,
          order = 1
        ),
        TopConfigApiDto(
          backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background-1.png",
          foregroundImage = "Autumn_fore.png",
          gradientTop = "#FF0001",
          gradientBottom = "#FF0032",
          cashoutButtonColor = "#FF0053",
          durationInMillis = 4000,
          order = 5
        ),
      ),
      offerModifier = listOf(
        OfferModifierConfigApiDto(
          offerId = "2000089",
          offerImage = "treasure_master_autumn.jpg",
          badge = BadgeConfigApiDto(
            color = "#FFFF00",
            displaySpecialBadge = true,
            text = "<strikethrough>$5 täglich</strikethrough><br><font color=\"0x00FF00\">Jetzt</font><br>"
          ),
          highlight = null,
        ),
        OfferModifierConfigApiDto(
          offerId = "2000090",
          offerImage = "merge_blast_autumn.jpg",
          badge = BadgeConfigApiDto(
            color = "#FFFF00",
            displaySpecialBadge = true,
            text = "<strikethrough>$5 daily</strikethrough><br><font color=\"0x00FF00\">Now!</font><br>"
          ),
          highlight = null,
        )
      ),
      translations = mapOf(
        "\$_original_resource2" to "SCHWARZER FREITAG",
        "\$_original_resource1" to "Erhöhte Münzen",
      ),
      countDownBanners = CountDownBannersApiDto(
        startPosition = 1,
        step = 3,
        max = 2,
        title = "Banner title",
        backgroundImage = "background_image.jpg",
        infoImages = listOf("image1.jpg", "image2.jpg"),
        infoTitle = "Info title",
        infoSections = listOf(CountDownInfoSectionApiDto(subTitle = "SubTitle", subText = "SubText"))
      ),
      infoBar = InfoBarApiDto(
        content = listOf(
          InfoBarContentApiDto(
            text = "PLAYERS_ONLINE"
          ),
          InfoBarContentApiDto(
            text = "Text with {DYNAMIC_VALUE}$ amount in it!",
            parameters = InfoBarContentParametersApiDto(
              showDuringSec = 5,
              sliding = true,
              dynamicValueConfiguration = InfoBarDynamicValueConfigurationApiDto(
                baseValue = 1_000_000,
                updateEachSec = 5,
                updateValueBy = -200,
                randomnessPercentage = 5
              )
            )
          )
        )
      ),
      expectedAppVersion = 75,
      announcementDetails = AnnouncementDetailsDto(
        title = "AnnouncementTitle",
        description = "AnnouncementDescription",
        image = "image",
        buttonText = "buttonText",
        themeColor = "tc",
        buttonClickAction = BUTTON_ACTION,
      )
    )
    val promotionEventDto = PromotionEvent(
      dateFrom = Instant.parse("2024-11-20T12:00:00.000Z"),
      dateTo = Instant.parse("2024-11-30T12:00:00.000Z"),
      uiConfiguration = "{}",
      useUserTimeZone = true,
      id = "TEST_2",
      experimentKey = null,
      variationKey = null,
      priorityKey = 0,
      eventType = PromotionEventType.GLOBAL,
    )
  }

  @Test
  fun `SHOULD return reordered list ON applyPromotionOrder WHEN there is highlighted game`() = runTest{
    //given
    var startId = 1
    val uiConfiguration = javaClass.getResource("/user/promotion/promotionConfig-highlighted.json")!!.readText()
    promotionEventConfigResolver.mock({ resolveCurrentPromotion(USER_ID) },
      promotionEventDto.copy(uiConfiguration = uiConfiguration)
    )
    val defaultGamesList = androidGameOfferListStub.map { game -> game.copy(id = startId++) }
    //when
    val result = service.applyPromotionOrder(USER_ID, defaultGamesList)
    //then
    assertThat(result.map{ it.id }).isEqualTo(
      listOf(3, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35)
    )
  }

  @Test
  fun `SHOULD return untouched list ON applyPromotionOrder WHEN there are no highlighted games`() = runTest{
    //given
    var startId = 1
    val uiConfiguration = javaClass.getResource("/user/promotion/promotionConfig2.json")!!.readText()
    promotionEventConfigResolver.mock({ resolveCurrentPromotion(USER_ID) },
      promotionEventDto.copy(uiConfiguration = uiConfiguration)
    )
    val defaultGamesList = androidGameOfferListStub.map { game -> game.copy(id = startId++) }
    //when
    val result = service.applyPromotionOrder(USER_ID, defaultGamesList)
    //then
    assertThat(result).isEqualTo(defaultGamesList)
  }

  @Test
  fun `SHOUDL return untouched list ON applyPromotionOrder WHEN uiConfig is not parseable`() = runTest {
    //given
    var startId = 1
    promotionEventConfigResolver.mock({ resolveCurrentPromotion(USER_ID) },
      promotionEventDto.copy(uiConfiguration = "{"))
    val defaultGamesList = androidGameOfferListStub.map { game -> game.copy(id = startId++) }
    //when
    val result = service.applyPromotionOrder(USER_ID, defaultGamesList)
    //then
    assertThat(result).isEqualTo(defaultGamesList)
  }

  @Test
  fun `SHOUDL return untouched list ON applyPromotionOrder WHEN there is no promotion `() = runTest {
    //given
    var startId = 1
    promotionEventConfigResolver.mock({ resolveCurrentPromotion(USER_ID) }, null)
    val defaultGamesList = androidGameOfferListStub.map { game -> game.copy(id = startId++) }
    //when
    val result = service.applyPromotionOrder(USER_ID, defaultGamesList)
    //then
    assertThat(result).isEqualTo(defaultGamesList)
  }

  @Test
  fun `SHOULD return empty PromotionEventConfigApiDto WHEN event is in progress but config is not parseable`() {
    //given
    val now = Instant.parse("2024-11-22T12:00:00.000Z")
    prepareEvent(
      now = now,
      promotion2mock = PromotionEvent(
        dateFrom = Instant.parse("2024-11-20T12:00:00.000Z"),
        dateTo = Instant.parse("2024-11-30T12:00:00.000Z"),
        uiConfiguration = "{unparsable json",
        useUserTimeZone = false,
        id = "TEST_2",
        experimentKey = null,
        variationKey = null,
        priorityKey = 0,
        eventType = PromotionEventType.GLOBAL,
      )
    )
    //when
    val result = runBlocking { service.getPromotionEventConfig(USER_ID, DE_LOCALE) }
    //then
    assertThat(result).isEqualTo(EMPTY_PROMOTION_CONFIG)
  }

  @Test
  fun `SHOULD return empty PromotionEventConfigApiDto WHEN config resolver returns null`() {
    //given
    promotionEventConfigResolver.mock({ resolveCurrentPromotion(USER_ID) }, null)
    //when
    val result = runBlocking { service.getPromotionEventConfig(USER_ID, DE_LOCALE) }
    //then
    assertThat(result).isEqualTo(EMPTY_PROMOTION_CONFIG)
  }


  @Test
  fun `SHOULD return announcementDetails in PromotionEventConfigApiDto`() {
    //given
    val now = Instant.parse("2024-11-22T12:00:00.000Z")
    val uiConfiguration = PromotionEventUiConfiguration(
      announcementDetails = AnnouncementDetailsDto(
        title = "AnnouncementTitle",
        description = "AnnouncementDescription",
        image = "image",
        buttonText = "buttonText",
        themeColor = "tc",
        buttonClickAction = BUTTON_ACTION,
      ),
    )
    val uiConfigAsString = defaultJsonConverter.encodeToString(uiConfiguration)
    val dateFrom = Instant.parse("2024-11-20T12:00:00.000Z")
    val dateTo = Instant.parse("2024-11-30T12:00:00.000Z")
    prepareEvent(
      now, promotion2mock = PromotionEvent(
        dateFrom = dateFrom,
        dateTo = dateTo,
        uiConfiguration = uiConfigAsString,
        useUserTimeZone = true,
        id = "TEST_2",
        experimentKey = null,
        variationKey = null,
        priorityKey = 0,
        eventType = PromotionEventType.GLOBAL,
      )
    )
    val epochFrom = dateFrom.epochSecond + 36000
    val epochTo = dateTo.epochSecond + 36000
    userTimeZoneService.mock({ getEpochSecondRangeForUser(USER_ID, now, dateFrom, dateTo) }, epochFrom..epochTo)
    translationService.mock({ translateOrDefault(TranslationResource.PROMOTION_UPDATE_VERSION_BUTTON_TEXT, Locale.GERMANY, USER_ID) }, "Jetzt aktualisieren")
    //when
    val result = runBlocking { service.getPromotionEventConfig(USER_ID, Locale.GERMANY) }
    //then
    assertThat(result.id).isEqualTo("TEST_2")
    assertThat(result.announcementDetails).isEqualTo(
      AnnouncementDetailsDto(
        title = "AnnouncementTitle",
        description = "AnnouncementDescription",
        image = "image",
        buttonText = "buttonText",
        themeColor = "tc",
        buttonClickAction = BUTTON_ACTION,
      )
    )
  }

  @Test
  fun `SHOULD return PromotionEventConfigApiDto with userTimeZone WHEN event is in progress`() {
    //given
    val now = Instant.parse("2024-11-22T12:00:00.000Z")
    prepareEvent(now, useUserTimezone = true)
    val dateFrom = Instant.parse("2024-11-20T12:00:00.000Z")
    val dateTo = Instant.parse("2024-11-30T12:00:00.000Z")
    val epochFrom = dateFrom.epochSecond + 36000
    val epochTo = dateTo.epochSecond + 36000
    userTimeZoneService.mock({ getEpochSecondRangeForUser(USER_ID, now, dateFrom, dateTo) }, epochFrom..epochTo)
    //when
    val result = runBlocking { service.getPromotionEventConfig(USER_ID, Locale.GERMANY) }
    //then
    assertThat(result).isEqualTo(
      promotionEventConfigDto.copy(
        startTime = epochFrom,
        endTime = epochTo,
        countDownBanners = promotionEventConfigDto.countDownBanners?.copy(endTime = 1732278000)
      )
    )
  }

  @Test
  fun `SHOULD save promotion config`() {
    val id = "TEST_PROMOTION"
    val dateFrom = Instant.parse("2025-12-03T10:15:30.00Z")
    val dateTo = Instant.parse("2025-12-05T10:15:30.00Z")
    val uiConfiguration = PromotionEventUiConfigurationAdminApiDto(
      top = TopConfigApiDto(
        backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
        foregroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png",
        gradientTop = "#FFFF00",
        durationInMillis = 1000
      ),
      mainTop = listOf(
        TopConfigApiDto(
          backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
          foregroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png",
          gradientTop = "#FFFF00",
          gradientBottom = "#FFFF00",
          durationInMillis = 2000,
          order = 1
        )
      ),
      challengesTop = listOf(
        TopConfigApiDto(
          backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
          foregroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png",
          gradientTop = "#FFFF00",
          durationInMillis = 2000,
          order = 1
        )
      ),
      offerModifier = listOf(
        OfferModifierConfigApiDto(
          offerId = "2000039",
          offerImage = "treasure_master_override.jpg",
          badge = BadgeConfigApiDto(
            color = "#FFFF00",
            displaySpecialBadge = true,
            text = "<strikethrough>$5 daily</strikethrough><br><font color=\\\"0x00FF00\\\">Now!</font><br>"
          ),
          highlight = null,
        )
      ),
      expectedAppVersion = 75,
      announcementDetails = AnnouncementDetailsDto(
        title = "AnnouncementTitle",
        description = "AnnouncementDescription",
        image = "image",
        buttonText = "buttonText",
        themeColor = "tc",
        buttonClickAction = BUTTON_ACTION,
      )
    )
    val request = PromotionEventAdminApiDto(
      id = id,
      dateFrom = dateFrom,
      dateTo = dateTo,
      useUserTimeZone = true,
      uiConfiguration = uiConfiguration,
      experimentKey = "experiment",
      variationKey = "variation",
      priorityKey = 0,
      translationResources = listOf(
        PromotionEventTranslationAdminApiDto(
          resourceName = "resourceName",
          originalResourceName = "originalResourceName"
        )
      ),
      eventType = PromotionEventType.GLOBAL,
    )

    runBlocking { service.savePromotionConfig(request) }

    verifyBlocking(promotionEventPersistenceService) {
      savePromotion(
        PromotionEvent(
          id = id,
          dateFrom = dateFrom,
          dateTo = dateTo,
          useUserTimeZone = true,
          //language=json
          uiConfiguration = """
          {"top":{"backgroundImage":"https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png","foregroundImage":"https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png","gradientTop":"#FFFF00","durationInMillis":1000},"mainTop":[{"backgroundImage":"https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png","foregroundImage":"https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png","gradientTop":"#FFFF00","gradientBottom":"#FFFF00","durationInMillis":2000,"order":1}],"challengesTop":[{"backgroundImage":"https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png","foregroundImage":"https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png","gradientTop":"#FFFF00","durationInMillis":2000,"order":1}],"offerModifier":[{"offerId":"2000039","offerImage":"treasure_master_override.jpg","badge":{"color":"#FFFF00","displaySpecialBadge":true,"text":"<strikethrough>$5 daily</strikethrough><br><font color=\\\"0x00FF00\\\">Now!</font><br>"}}],"expectedAppVersion":75,"announcementDetails":{"title":"AnnouncementTitle","description":"AnnouncementDescription","image":"image","themeColor":"tc","buttonText":"buttonText","buttonClickAction":{"name":"ROUTE_TO_MAIN"}}}
          """.trimIndent(),
          experimentKey = "experiment",
          variationKey = "variation",
          priorityKey = 0,
          eventType = PromotionEventType.GLOBAL,
        )
      )
    }

    verifyBlocking(promotionTranslationService) {
      savePromotionEventTranslationLinks(
        listOf(
          PromotionEventTranslationLink(
            eventId = id,
            resourceName = "resourceName",
            originalResourceName = "originalResourceName",
          )
        )
      )
    }
  }

  @Test
  fun `SHOLUD get list of promotion config`() {
    val now = Instant.parse("2024-11-22T00:00:00Z")
    prepareEventList(now, experimentKey = "eKey", variationKey = "vKey", priorityKey = 42)

    val result = runBlocking {
      service.getPromotionConfigList(
        dateFrom = Instant.parse("2024-11-21T00:00:00Z"),
        dateTo = Instant.parse("2024-11-23T00:00:00Z"),
        enabled = true
      )
    }
    assertThat(result).isEqualTo(
      PromotionEventsAdminApiDto(
        events = listOf(
          PromotionEventAdminApiDto(
            id = "TEST_2",
            dateFrom = Instant.parse("2024-11-20T12:00:00Z"),
            dateTo = Instant.parse("2024-11-30T12:00:00Z"),
            useUserTimeZone = false,
            uiConfiguration = PromotionEventUiConfigurationAdminApiDto(
              top = TopConfigApiDto(
                backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
                foregroundImage = "Autumn_fore.png",
                gradientTop = "#FF0000",
                gradientBottom = "#FF0033",
                cashoutButtonColor = "#FF0057",
                durationInMillis = 3000,
                order = 1
              ),
              mainTop = listOf(
                TopConfigApiDto(
                  backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
                  foregroundImage = "Autumn_fore.png",
                  gradientTop = "#FF0000",
                  gradientBottom = "#FF0033",
                  cashoutButtonColor = "#FF0057",
                  durationInMillis = 3000,
                  order = 1
                ),
                TopConfigApiDto(
                  backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background-1.png",
                  foregroundImage = "Autumn_fore.png",
                  gradientTop = "#FF0001",
                  gradientBottom = "#FF0032",
                  cashoutButtonColor = "#FF0053",
                  durationInMillis = 4000,
                  order = 5
                ),
              ),
              challengesTop = listOf(
                TopConfigApiDto(
                  backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
                  foregroundImage = "Autumn_fore.png",
                  gradientTop = "#FF0000",
                  gradientBottom = "#FF0033",
                  cashoutButtonColor = "#FF0057",
                  durationInMillis = 3000,
                  order = 1
                ),
                TopConfigApiDto(
                  backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background-1.png",
                  foregroundImage = "Autumn_fore.png",
                  gradientTop = "#FF0001",
                  gradientBottom = "#FF0032",
                  cashoutButtonColor = "#FF0053",
                  durationInMillis = 4000,
                  order = 5
                ),
              ),
              offerModifier = listOf(
                OfferModifierConfigApiDto(
                  offerId = "2000089",
                  offerImage = "treasure_master_autumn.jpg",
                  badge = BadgeConfigApiDto(
                    color = "#FFFF00",
                    displaySpecialBadge = true,
                    text = "\$_promotion_event_autumn"
                  ),
                  highlight = null,
                ),
                OfferModifierConfigApiDto(
                  offerId = "2000090",
                  offerImage = "merge_blast_autumn.jpg",
                  badge = BadgeConfigApiDto(
                    color = "#FFFF00",
                    displaySpecialBadge = true,
                    text = "<strikethrough>$5 daily</strikethrough><br><font color=\"0x00FF00\">Now!</font><br>"
                  ),
                  highlight = null
                )
              ),
              countDownBanners = CountDownBannersAdminApiDto(
                startPosition = 1,
                step = 3,
                max = 2,
                title = "Banner title",
                backgroundImage = "background_image.jpg",
                infoImages = listOf("image1.jpg", "image2.jpg"),
                infoTitle = "Info title",
                infoSections = listOf(
                  CountDownInfoSectionApiDto(subTitle = "SubTitle", subText = "SubText")
                ),
                durationInSeconds = 3000
              ),
              infoBar = InfoBarApiDto(
                content = listOf(
                  InfoBarContentApiDto(
                    text = "PLAYERS_ONLINE"
                  ),
                  InfoBarContentApiDto(
                    text = "Text with {DYNAMIC_VALUE}$ amount in it!",
                    parameters = InfoBarContentParametersApiDto(
                      showDuringSec = 5,
                      sliding = true,
                      dynamicValueConfiguration = InfoBarDynamicValueConfigurationApiDto(
                        baseValue = 1_000_000,
                        updateEachSec = 5,
                        updateValueBy = -200,
                        randomnessPercentage = 5
                      )
                    )
                  )
                )
              ),
              expectedAppVersion = 75,
              announcementDetails = AnnouncementDetailsDto(
                title = "AnnouncementTitle",
                description = "AnnouncementDescription",
                image = "image",
                buttonText = "buttonText",
                themeColor = "tc",
                buttonClickAction = BUTTON_ACTION,
              )
            ),
            experimentKey = "eKey",
            variationKey = "vKey",
            priorityKey = 42,
            translationResources = listOf(
              PromotionEventTranslationAdminApiDto(resourceName = "\$_resource2", originalResourceName = "\$_original_resource2"),
              PromotionEventTranslationAdminApiDto(resourceName = "\$_resource1", originalResourceName = "\$_original_resource1"),
            ),
            eventType = PromotionEventType.GLOBAL,
          ),
          PromotionEventAdminApiDto(
            id = "TEST_1",
            dateFrom = Instant.parse("2024-11-21T12:00:00Z"),
            dateTo = Instant.parse("2024-11-25T12:00:00Z"),
            useUserTimeZone = false,
            uiConfiguration = PromotionEventUiConfigurationAdminApiDto(
              top = TopConfigApiDto(
                backgroundImage = "Christmas_back.jpg",
                foregroundImage = "Christmas_fore.png",
                gradientTop = "#FF0000",
                gradientBottom = "#FF0033",
                cashoutButtonColor = "#FF0057",
                durationInMillis = null,
                order = null
              ),
              mainTop = null,
              challengesTop = null,
              offerModifier = listOf(
                OfferModifierConfigApiDto(
                  offerId = "2000089",
                  offerImage = "treasure_master_override.jpg",
                  badge = BadgeConfigApiDto(
                    color = "#FFFF00",
                    displaySpecialBadge = true,
                    text = "\$_promotion_event_autumn"
                  ),
                  highlight = null
                ),
              ),
              expectedAppVersion = 74,
              announcementDetails = AnnouncementDetailsDto(
                title = "AnnouncementTitle",
                description = "AnnouncementDescription",
                image = "image",
                buttonText = "buttonText",
                themeColor = "tc",
                buttonClickAction = BUTTON_ACTION,
              )
            ),
            experimentKey = "eKey",
            variationKey = "vKey",
            priorityKey = 42,
            translationResources = listOf(
              PromotionEventTranslationAdminApiDto(
                resourceName = "\$_resource1", originalResourceName = "\$_original_resource1"
              )
            ),
            eventType = PromotionEventType.GLOBAL,
          ),
        )
      )
    )
  }

  private fun prepareEvent(
    now: Instant,
    useUserTimezone: Boolean = false,
    promotion2mock: PromotionEvent? = null,
    priorityKey: Int = 0,
  ) {
    timeService.mock({ now() }, now)
    val uiConfiguration2 = javaClass.getResource("/user/promotion/promotionConfig2.json")!!.readText()
    val promotion2 = promotion2mock ?: PromotionEvent(
      dateFrom = Instant.parse("2024-11-20T12:00:00.000Z"),
      dateTo = Instant.parse("2024-11-30T12:00:00.000Z"),
      uiConfiguration = uiConfiguration2,
      useUserTimeZone = useUserTimezone,
      id = "TEST_2",
      experimentKey = null,
      variationKey = null,
      priorityKey = priorityKey,
      eventType = PromotionEventType.GLOBAL,
    )
    userService.mock(
      { getUser(USER_ID) }, userDtoStub.copy(
        id = USER_ID,
        locale = Locale.GERMANY,
      )
    )
    whenever(runBlocking { translationService.tryTranslate(any(), any(), any()) }).then { it.getArgument(0) }
    translationService.mock(
      { tryTranslate(eq("\$_promotion_event_autumn"), eq(Locale.GERMANY), any()) },
      "<strikethrough>$5 täglich</strikethrough><br><font color=\"0x00FF00\">Jetzt</font><br>"
    )
    promotionTranslationService.mock(
      { getTranslationLinks(listOf("TEST_2", "TEST_1")) }, listOf(
        PromotionEventTranslationLink(
          eventId = "TEST_1",
          resourceName = "\$_resource1",
          originalResourceName = "\$_original_resource1",
        ),
        PromotionEventTranslationLink(
          eventId = "TEST_2",
          resourceName = "\$_resource2",
          originalResourceName = "\$_original_resource2",
        ),
        PromotionEventTranslationLink(
          eventId = "TEST_2",
          resourceName = "\$_resource1",
          originalResourceName = "\$_original_resource1",
        ),
      )
    )
    promotionTranslationService.mock(
      { getTranslationLinks(listOf("TEST_2")) }, listOf(
        PromotionEventTranslationLink(
          eventId = "TEST_2",
          resourceName = "\$_resource2",
          originalResourceName = "\$_original_resource2",
        ),
        PromotionEventTranslationLink(
          eventId = "TEST_2",
          resourceName = "\$_resource1",
          originalResourceName = "\$_original_resource1",
        ),
      )
    )
    promotionTranslationService.mock(
      { getTranslationsForPromoConfig("TEST_2", Locale.GERMANY) }, mapOf(
        "\$_original_resource2" to "SCHWARZER FREITAG",
        "\$_original_resource1" to "Erhöhte Münzen",
      )
    )
    translationService.mock({ tryTranslate(eq("\$_resource1"), eq(Locale.GERMANY), any()) }, "Erhöhte Münzen")
    translationService.mock({ tryTranslate(eq("\$_resource2"), eq(Locale.GERMANY), any()) }, "SCHWARZER FREITAG")
    promotionEventConfigResolver.mock({ resolveCurrentPromotion(USER_ID) }, promotion2)
  }

  private fun prepareEventList(
    now: Instant,
    useUserTimezone: Boolean = false,
    promotion2mock: PromotionEvent? = null,
    experimentKey: String? = null,
    variationKey: String? = null,
    priorityKey: Int = 0,
  ) {
    timeService.mock({ now() }, now)
    val dayAgo = now.minus(Duration.ofDays(1)).truncatedTo(ChronoUnit.DAYS)!!
    val dayAfter = now.plus(Duration.ofDays(1)).truncatedTo(ChronoUnit.DAYS)!!
    val uiConfiguration1 = javaClass.getResource("/user/promotion/promotionConfig1.json")!!.readText()
    val uiConfiguration2 = javaClass.getResource("/user/promotion/promotionConfig2.json")!!.readText()
    val promotion1 = PromotionEvent(
      dateFrom = Instant.parse("2024-11-21T12:00:00.000Z"),
      dateTo = Instant.parse("2024-11-25T12:00:00.000Z"),
      uiConfiguration = uiConfiguration1,
      useUserTimeZone = useUserTimezone,
      id = "TEST_1",
      experimentKey = experimentKey,
      variationKey = variationKey,
      priorityKey = priorityKey,
      eventType = PromotionEventType.GLOBAL,
    )
    val promotion2 = promotion2mock ?: PromotionEvent(
      dateFrom = Instant.parse("2024-11-20T12:00:00.000Z"),
      dateTo = Instant.parse("2024-11-30T12:00:00.000Z"),
      uiConfiguration = uiConfiguration2,
      useUserTimeZone = useUserTimezone,
      id = "TEST_2",
      experimentKey = experimentKey,
      variationKey = variationKey,
      priorityKey = priorityKey,
      eventType = PromotionEventType.GLOBAL,
    )
    promotionTranslationService.mock(
      { getTranslationLinks(listOf("TEST_2", "TEST_1")) }, listOf(
        PromotionEventTranslationLink(
          eventId = "TEST_1",
          resourceName = "\$_resource1",
          originalResourceName = "\$_original_resource1",
        ),
        PromotionEventTranslationLink(
          eventId = "TEST_2",
          resourceName = "\$_resource2",
          originalResourceName = "\$_original_resource2",
        ),
        PromotionEventTranslationLink(
          eventId = "TEST_2",
          resourceName = "\$_resource1",
          originalResourceName = "\$_original_resource1",
        ),
      )
    )
    promotionEventPersistenceService.mock(
      { getPromotionEventList(dayAgo, dayAfter, true) },
      listOf(promotion2, promotion1)
    )
  }
}