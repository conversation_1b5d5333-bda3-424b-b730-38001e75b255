package com.moregames.playtime.user.challenge.progress.calculator

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.ApplicationId
import com.moregames.playtime.user.challenge.dto.*
import com.moregames.playtime.user.challenge.progress.ChallengeProgressCalculatorType
import net.javacrumbs.jsonunit.JsonAssert
import org.junit.jupiter.api.Test
import java.time.Instant

class TreasureMaster20ProgressCalculatorTest {
  private val underTest = TreasureMaster20ProgressCalculator()

  companion object {
    const val USER_ID = "userId"

    //language=json
    const val ACHIEVEMENT = """{"levels": ["16"]}"""
  }

  private val now = Instant.now()
  private val eventId = ChallengeEventId("event-id")

  @Test
  fun `SHOULD return valid score WHEN progressMax is less 20`() {
    val progressDto = UserChallengeProgressDto.TmProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.TANGRAM_APP_ID,
      score = 25,
      isBoss = true,
      level = 15,
    )

    assertCalculation(progressDto, 17, 15, 15)
  }


  @Test
  fun `SHOULD return valid progress WHEN dto is TmProgress`() {
    val progressDto = UserChallengeProgressDto.TmProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.TANGRAM_APP_ID,
      score = 10,
      isBoss = true,
      level = 20,
    )

    assertCalculation(progressDto, 17, 20)
  }

  @Test
  fun `SHOULD return the same progress WHEN dto is invalid`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.TANGRAM_APP_ID,
      milestone = 15
    )

    assertCalculation(progressDto, 13, 13)
  }


  @Test
  fun `SHOULD return levelId as progress WHEN dto is LevelIdProgress`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.TANGRAM_APP_ID,
      levelId = "17"
    )

    assertCalculation(progressDto, 13, 17)
  }

  private fun assertCalculation(
    progressDto: UserChallengeProgressDto,
    currentProgress: Int,
    expectedProgress: Int,
    progressMax: Int = 20
  ) {
    val challenge = Challenge(
      id = ChallengeId("challengeId"),
      eventId = eventId,
      progressMax = progressMax,
      gameId = 42,
      title = "title",
      icon = "icon",
      calculator = ChallengeProgressCalculatorType.LEVEL_ID,
      applyEarningsCut = true,
      order = 0,
      goal = 0,
      challengeType = ChallengeType.REGULAR,
    )
    val userChallenge = UserChallenge(
      userId = USER_ID,
      challenge = challenge,
      progress = currentProgress,
      state = ChallengeState.IN_PROGRESS,
      coins = 0,
      achievement = ACHIEVEMENT,
      completedAt = null,
      updatedAt = now,
    )
    val result = underTest.calculateProgress(progressDto, userChallenge)
    assertThat(result.progress).isEqualTo(expectedProgress)
    JsonAssert.assertJsonEquals(ACHIEVEMENT, result.achievement)
  }
}