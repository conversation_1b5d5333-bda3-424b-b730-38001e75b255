package com.moregames.playtime.user.promotion.event.manager

import com.moregames.base.messaging.customnotification.ButtonAction
import com.moregames.base.messaging.customnotification.ButtonActionName
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.general.exception.InvalidParameterValueException
import com.moregames.playtime.translations.TranslationService
import com.moregames.playtime.user.promotion.event.manager.api.admin.CountDownBannersAdminApiDto
import com.moregames.playtime.user.promotion.event.manager.api.admin.PromotionEventAdminApiDto
import com.moregames.playtime.user.promotion.event.manager.api.admin.PromotionEventTranslationAdminApiDto
import com.moregames.playtime.user.promotion.event.manager.api.admin.PromotionEventUiConfigurationAdminApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.BadgeConfigApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.CountDownInfoSectionApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.OfferModifierConfigApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.TopConfigApiDto
import com.moregames.playtime.user.promotion.event.manager.dto.AnnouncementDetailsDto
import com.moregames.playtime.user.promotion.event.manager.dto.PromotionEventType
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito.mock
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Locale

class PromotionEventValidatorTest {

  private val translationService: TranslationService = mock()
  private val timeService: TimeService = mock()


  val underTest = PromotionEventValidator(translationService, timeService)

  companion object {
    val now: Instant = Instant.parse("2025-12-03T10:15:30.00Z")
    const val TEXT = "\$_text"
    const val ID = "TEST_PROMOTION"
    val dateFrom: Instant = Instant.parse("2025-12-03T10:15:30.00Z")
    val dateTo: Instant = Instant.parse("2025-12-05T10:15:30.00Z")
    val badge = BadgeConfigApiDto(
      color = "#FFFF00",
      displaySpecialBadge = true,
      text = "<strikethrough>$5 daily</strikethrough><br><font color=\\\"0x00FF00\\\">Now!</font><br>"
    )
    val buttonAction = ButtonAction(ButtonActionName.ROUTE_TO_MAIN)
    val uiConfiguration = PromotionEventUiConfigurationAdminApiDto(
      top = TopConfigApiDto(
        backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
        foregroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png",
        gradientTop = "#FFFF00",
        durationInMillis = 1000
      ),
      mainTop = listOf(
        TopConfigApiDto(
          backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
          foregroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png",
          gradientTop = "#FFFF00",
          gradientBottom = "#FFFF00",
          durationInMillis = 2000,
          order = 1
        )
      ),
      challengesTop = listOf(
        TopConfigApiDto(
          backgroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png",
          foregroundImage = "https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png",
          gradientTop = "#FFFF00",
          durationInMillis = 2000,
          order = 1
        )
      ),
      offerModifier = listOf(
        OfferModifierConfigApiDto(
          offerId = "2000039",
          offerImage = "treasure_master_override.jpg",
          badge = badge,
          highlight = null
        )
      ),
      expectedAppVersion = 75,
      announcementDetails = AnnouncementDetailsDto(
        title = "AnnouncementTitle",
        description = "AnnouncementDescription",
        image = "image",
        buttonText = "buttonText",
        themeColor = "tc",
        buttonClickAction = buttonAction
      ),
      countDownBanners = CountDownBannersAdminApiDto(
        title = "title",
        startPosition = 0,
        step = 0,
        max = 0,
        backgroundImage = "bgimage",
        infoImages = listOf("1"),
        infoTitle = "infoTitle",
        infoSections = listOf(CountDownInfoSectionApiDto(
          subTitle = "subTitle",
          subText = "subText",
        ))
      )
    )
    val requestStub = PromotionEventAdminApiDto(
      id = ID,
      dateFrom = dateFrom,
      dateTo = dateTo,
      useUserTimeZone = true,
      uiConfiguration = uiConfiguration,
      experimentKey = "experiment",
      variationKey = "variation",
      priorityKey = 0,
      translationResources = listOf(
        PromotionEventTranslationAdminApiDto(
          resourceName = "resourceName",
          originalResourceName = "originalResourceName"
        )
      ),
      eventType = PromotionEventType.GLOBAL,
    )
  }

  @BeforeEach
  fun init() {
    timeService.mock ({ now() }, now)
    translationService.mock ({ tryTranslate("\$_title1", Locale.ENGLISH) }, "Title")
    translationService.mock ({ tryTranslate(TEXT, Locale.ENGLISH) }, TEXT)
  }

  @Test
  fun `SHOULD not throw exception WHEN announcementDetails is null`() = runTest {
    val request = requestStub.copy(uiConfiguration = uiConfiguration.copy(announcementDetails = null))
    assertDoesNotThrow { underTest.validate(request) }
  }

  @Test
  fun `SHOULD throw exception WHEN there is no translation countdown banner infoSectionSubTitle`() = runTest {
    val infoSection = CountDownInfoSectionApiDto(
      subTitle = TEXT,
      subText = "subText",
    )
    val request = requestStub.copy(
      uiConfiguration = uiConfiguration.copy(
        countDownBanners = uiConfiguration.countDownBanners!!.copy(infoSections = listOf(infoSection))))
    assertThrows<InvalidParameterValueException> { underTest.validate(request) }
  }

  @Test
  fun `SHOULD throw exception WHEN there is no translation countdown banner infoSectionSubText`() = runTest {
    val infoSection = CountDownInfoSectionApiDto(
        subTitle = "subTitle",
        subText = TEXT,
    )
    val request = requestStub.copy(
      uiConfiguration = uiConfiguration.copy(
        countDownBanners = uiConfiguration.countDownBanners!!.copy(infoSections = listOf(infoSection))))
    assertThrows<InvalidParameterValueException> { underTest.validate(request) }
  }

  @Test
  fun `SHOULD throw exception WHEN there is no translation countdown banner infoButtonText`() = runTest {
    val request = requestStub.copy(
      uiConfiguration = uiConfiguration.copy(
        countDownBanners = uiConfiguration.countDownBanners!!.copy(infoButtonText = TEXT)
      ))
    assertThrows<InvalidParameterValueException> { underTest.validate(request) }
  }

  @Test
  fun `SHOULD throw exception WHEN there is no translation countdown banner title`() = runTest {
    val request = requestStub.copy(
      uiConfiguration = uiConfiguration.copy(
        countDownBanners = uiConfiguration.countDownBanners!!.copy(title = TEXT)
      ))
    assertThrows<InvalidParameterValueException> { underTest.validate(request) }
  }

  @Test
  fun `SHOULD throw exception WHEN there is no translation countdown banner infoTitle`() = runTest {
    val request = requestStub.copy(
      uiConfiguration = uiConfiguration.copy(
        countDownBanners = uiConfiguration.countDownBanners!!.copy(infoTitle = TEXT)
      ))
    assertThrows<InvalidParameterValueException> { underTest.validate(request) }
  }

  @Test
  fun `SHOULD throw exception WHEN there is no translation for badge text`() = runTest {
    val request = requestStub.copy(
      uiConfiguration = uiConfiguration.copy(
        offerModifier = listOf(uiConfiguration.offerModifier!![0].copy(badge = badge.copy(text = TEXT)))
      ))
    assertThrows<InvalidParameterValueException> { underTest.validate(request) }
  }


  @Test
  fun `SHOULD throw exception WHEN dateTo is equal dateFrom`() = runTest {
    val request = requestStub.copy( dateTo = requestStub.dateFrom)
    assertThrows<InvalidParameterValueException> { underTest.validate(request) }
  }

  @Test
  fun `SHOULD throw exception WHEN it is too late for the event`() = runTest {
    val request = requestStub.copy( dateTo = now.minus(2, ChronoUnit.DAYS))
    assertThrows<InvalidParameterValueException> { underTest.validate(request) }
  }

  @Test
  fun `SHOULD not throw exception WHEN fields are correct`() = runTest {
    assertDoesNotThrow { underTest.validate(requestStub) }
  }
}