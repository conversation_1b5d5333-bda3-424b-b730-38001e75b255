package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.moregames.base.dto.AppPlatform.*
import com.moregames.base.gameStub
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.mock
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.UserPersistenceService.GamePlayStatusDto
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.Test

@ExtendWith(MockExtension::class)
class WebAppGpsLocationCheckServiceTest(
  val userPersistenceService: UserPersistenceService,
  val gamesService: GamesService,
  val userService: UserService,
) {
  private val service = WebAppGpsLocationCheckService(userPersistenceService, gamesService, userService)

  companion object {
    const val USER_ID = "userIdX"
    private val now = Instant.now()
    val playedTime = mutableListOf<Instant>(
      now.minus(10, ChronoUnit.DAYS),
      now.minus(5, ChronoUnit.DAYS),
      now.minus(7, ChronoUnit.DAYS),
    )
    val gameDtoTest = GamePlayStatusDto(gameId = 1, coins = 2000, playedRecently = false, lastPlayedAt = now, firstPlayedAt = now)
    val playedGames = listOf(
      gameDtoTest.copy(gameId = 1, lastPlayedAt = playedTime.removeFirst()),
      gameDtoTest.copy(gameId = 2, lastPlayedAt = playedTime.removeFirst()),//last played
      gameDtoTest.copy(gameId = 3, lastPlayedAt = playedTime.removeFirst()),//next last played
    ).associateBy { it.gameId }
  }

  @BeforeEach
  fun init() {
    userService.mock({ loadUserGameCoins(USER_ID) }, playedGames)
    userService.mock({ getUser(USER_ID, includingDeleted = true) }, userDtoStub)
    gamesService.mock({ getGameById(ANDROID, 1) }, gameStub.copy(applicationId = "applicationId"))
    gamesService.mock({ getGameById(ANDROID, 2) }, gameStub.copy(applicationId = "applicationId2"))
    gamesService.mock({ getGameById(ANDROID, 3) }, gameStub.copy(applicationId = "applicationId3"))
  }

  @Test
  fun `SHOULD return null ON getApplicationId WHEN we have no info about played games`() {
    userService.mock({ loadUserGameCoins(USER_ID) }, emptyMap())

    runBlocking {
      service.getApplicationId(USER_ID)
    }.let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD return last played game ON getApplicationId WHEN we have not asked games yet`() {
    userPersistenceService.mock({ getGpsLocationCheckAskedGames(USER_ID) }, emptyList())

    runBlocking {
      service.getApplicationId(USER_ID)
    }.let { assertThat(it).isEqualTo("applicationId2") }
  }


  @Test
  fun `SHOULD return left played game ON getApplicationId WHEN we have asked the latest played game`() {
    userPersistenceService.mock({ getGpsLocationCheckAskedGames(USER_ID) }, listOf(2))

    runBlocking {
      service.getApplicationId(USER_ID)
    }.let { assertThat(it).isEqualTo("applicationId3") }
  }

  @Test
  fun `SHOULD return left played game ON getApplicationId WHEN we have asked all except one`() {
    userPersistenceService.mock({ getGpsLocationCheckAskedGames(USER_ID) }, listOf(2, 3))

    runBlocking {
      service.getApplicationId(USER_ID)
    }.let { assertThat(it).isEqualTo("applicationId") }
  }

  @Test
  fun `SHOULD return first played game ON getApplicationId WHEN we have asked all games`() {
    userPersistenceService.mock({ getGpsLocationCheckAskedGames(USER_ID) }, listOf(1, 2, 3))

    runBlocking {
      service.getApplicationId(USER_ID)
    }.let { assertThat(it).isEqualTo("applicationId2") }
  }

  @Test
  fun `SHOULD also work for ios web users ON getApplicationId`() {
    userService.mock({ getUser(USER_ID, includingDeleted = true) }, userDtoStub.copy(appPlatform = IOS_WEB))
    userPersistenceService.mock({ getGpsLocationCheckAskedGames(USER_ID) }, listOf(1, 3))
    gamesService.mock({ getGameById(IOS, 2) }, gameStub.copy(applicationId = "applicationId2x"))

    runBlocking {
      service.getApplicationId(USER_ID)
    }.let { assertThat(it).isEqualTo("applicationId2x") }
  }

  @Test
  fun `SHOULD return null ON getApplicationId WHEN we somehow failed to define game app id by id`() {
    userPersistenceService.mock({ getGpsLocationCheckAskedGames(USER_ID) }, listOf(1, 2, 3))
    gamesService.mock({ getGameById(ANDROID, 2) }, null)

    runBlocking {
      service.getApplicationId(USER_ID)
    }.let { assertThat(it).isNull() }
  }

}