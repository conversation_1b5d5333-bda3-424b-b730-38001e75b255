package com.moregames.playtime.user.offer.combine

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.app.OfferWallType
import com.moregames.base.app.OfferWallType.FYBER
import com.moregames.base.offers.dto.OfferAction
import com.moregames.base.util.answer
import com.moregames.base.util.mock
import com.moregames.playtime.user.offer.*
import com.moregames.playtime.user.offer.combine.AndroidOfferListCombineService.OfferwallRepresentation
import com.moregames.playtime.user.offer.combine.experiment.AdditionalOfferwallExperimentService
import com.moregames.playtime.utils.*
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.time.Instant

class AndroidOfferListCombineServiceTest {

  private val converter: OfferItemConverterService = mock()
  private val offerItemCustomizer: OfferItemApiDtoCustomizer = mock()
  private val bigOffersHelper: BigOffersHelper = mock()
  private val additionalOfferSplitter: AdditionalOfferSplitter = mock()
  private val offerwallCampaignService: OfferwallCampaignService = mock()
  private val hightlightOfwLowEcpmExperimentService: HightlightOfwLowEcpmExperimentService = mock()
  private val additionalOfferwallExperimentService: AdditionalOfferwallExperimentService = mock()
  private val disableMxOfferwallExperimentService: DisableMxOfferwallExperimentService = mock()

  val underTest = AndroidOfferListCombineService(
    converterService = converter,
    offerItemApiDtoCustomizer = offerItemCustomizer,
    bigOffersHelper = bigOffersHelper,
    additionalOfferSplitter = additionalOfferSplitter,
    offerwallCampaignService = offerwallCampaignService,
    hightlightOfwLowEcpmExperimentService = hightlightOfwLowEcpmExperimentService,
    additionalOfferwallExperimentService = additionalOfferwallExperimentService,
    disableMxOfferwallExperimentService = disableMxOfferwallExperimentService
  )

  private companion object {
    const val USER_ID = "user-id"
    val game = androidGameOfferStub.copy(id = 1)
    val games = listOf(
      game,
      game.copy(id = 2),
      game.copy(id = 3),
      game.copy(id = 4),
    )
    val playedGames = listOf(
      game.copy(id = 15, lastPlayedAt = Instant.parse("2007-12-03T10:15:30.00Z")),
      game.copy(id = 16, lastPlayedAt = Instant.parse("2007-12-03T10:15:30.00Z")),
    )
    val ao1 = additionalOfferStub.copy(id = 11)
    val ao2 = additionalOfferStub.copy(id = 12)
    val ao3 = additionalOfferStub.copy(id = 133)
    val additionalOffers = listOf(ao1, ao2, ao3)
    const val APP_VERSION = 68
    const val IS_NOT_CONSENSUAL_GDPR_USER = true
  }

  private val offerItemFromGameList = ArrayList<OfferItemGameApiDto>()

  @BeforeEach
  fun init() {
    additionalOfferSplitter.mock({ splitAdditionalOffers(additionalOffers) }, listOf(ao2) to listOf(ao1, ao3))
    bigOffersHelper.mock({ showBigOffers(USER_ID) }, true)
    games.forEach { game ->
      val result = mockGameApiDto.copy(id = "game_" + game.id.toString())
      converter.mock(
        { toOfferItemGameApiDto(eq(game), eq(offersConfigStub), eq(USER_ID), any()) },
        result
      )
      offerItemFromGameList.add(result)
    }
    playedGames.forEach { playedGame ->
      converter.mock(
        { toOfferItemGameApiDto(eq(playedGame), eq(offersConfigStub), eq(USER_ID), any()) },
        mockGameApiDto.copy(id = "game_" + playedGame.id.toString())
      )
    }
    offerItemCustomizer.answer({ customizeGameOfferItem(any(), eq(USER_ID), any()) }, { invocationOnMock -> invocationOnMock.getArgument(0) })
    additionalOffers.forEach { ao ->
      converter.answer(
        { toOfferItemApiDto(ao, offersConfigStub, APP_VERSION) },
        { invocationOnMock ->
          val arg = invocationOnMock.getArgument(0) as AdditionalOffer
          offerItemLargeApiDtoOfferwallStub.copy(id = "additionalOffer_" + arg.id)
        }
      )
    }
    converter.answer(
      { toOfferItemLargeApiDto(any(), eq(IS_NOT_CONSENSUAL_GDPR_USER), eq(APP_VERSION)) },
      { invocationOnMock ->
        val arg = invocationOnMock.getArgument(0) as OfferwallRepresentation.Big
        offerItemLargeApiDtoOfferwallStub.copy(id = "mainOfferWall" + arg.info.offerWallConfig.id)
      }
    )
    converter.mock(
      { toOfferItemLockedGamesInfoApiDto(any()) },
      offerItemLockedGamesInfoApiDto.copy(id = "lockedGames")
    )
    converter.mock(
      { toOfferItemUnlockedGameReminderApiDto(any()) },
      offerItemUnlockedGameReminderApiDtoStub.copy(id = "unlocked")
    )
    offerwallCampaignService.mock({ isEligibleForOfferwallCampaign(any()) }, false)
    hightlightOfwLowEcpmExperimentService.mock({ shouldHighlightOfw(any()) }, false)
    additionalOfferwallExperimentService.mock({ additionalBigOfferwalls(any(), any()) }, emptyList())
    disableMxOfferwallExperimentService.mock({ shouldSkipOfwHighlight(any()) }, false)
  }

  @Test
  fun `SHOULD do combine correct order WHEN we should show big offer and there is promotionPeriod`() {
    val offerwalls = listOf(
      mockOfferwallInfo.copy(
        offerWallConfig = mockOfferwallCfgPromo
      )
    )

    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = offerwalls,
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_12",
        "unlocked",
        "mainOfferWall1",
        "game_1",
        "game_2",
        "game_3",
        "game_4",
        "mainOfferWall1",
        "lockedGames",
      )
    )

    var gameIndex = 0
    offerItemFromGameList.forEach {
      verifyBlocking(offerItemCustomizer) { customizeGameOfferItem(it, USER_ID, gameIndex) }
      gameIndex++
    }
  }

  @Test
  fun `SHOULD do combine correct order WHEN user is from campaign`() {
    val offerwalls = listOf(
      mockOfferwallInfo.copy(
        offerWallConfig = mockOfferwallCfg
      )
    )
    offerwallCampaignService.mock({ isEligibleForOfferwallCampaign(USER_ID) }, true)

    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = offerwalls,
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_12",
        "unlocked",
        "mainOfferWall1",
        "game_1",
        "game_2",
        "game_3",
        "game_4",
        "mainOfferWall1",
        "lockedGames",
      )
    )

    var gameIndex = 0
    offerItemFromGameList.forEach {
      verifyBlocking(offerItemCustomizer) { customizeGameOfferItem(it, USER_ID, gameIndex) }
      gameIndex++
    }
  }

  @Test
  fun `SHOULD do combine correct order WHEN user has additional offerwalls`() {
    val offerwalls = listOf(
      mockOfferwallInfo.copy(
        offerWallConfig = mockOfferwallCfg
      )
    )
    offerwallCampaignService.mock({ isEligibleForOfferwallCampaign(USER_ID) }, true)
    additionalOfferwallExperimentService.mock(
      { additionalBigOfferwalls(USER_ID, offerwalls) }, listOf(
        mockOfferwallInfo.copy(
          offerWallType = FYBER,
          offerWallConfig = mockOfferwallCfg.copy(id = 2)
        )
      )
    )

    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = offerwalls,
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_12",
        "unlocked",
        "mainOfferWall1",
        "game_1",
        "game_2",
        "game_3",
        "game_4",
        "mainOfferWall1",
        "lockedGames",
        "mainOfferWall2",
      )
    )
  }

  @Test
  fun `SHOULD do combine correct order WHEN user is from low ecpm group`() {
    val offerwalls = listOf(
      mockOfferwallInfo.copy(
        offerWallConfig = mockOfferwallCfg
      )
    )
    hightlightOfwLowEcpmExperimentService.mock({ shouldHighlightOfw(USER_ID) }, true)

    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = offerwalls,
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_12",
        "unlocked",
        "mainOfferWall1",
        "game_1",
        "game_2",
        "game_3",
        "game_4",
        "mainOfferWall1",
        "lockedGames",
      )
    )

    var gameIndex = 0
    offerItemFromGameList.forEach {
      verifyBlocking(offerItemCustomizer) { customizeGameOfferItem(it, USER_ID, gameIndex) }
      gameIndex++
    }
  }

  @Test
  fun `SHOULD do combine correct order WHEN we should show big offer and there is promotionPeriod but isHighlighted is false`() {
    val offerwalls = listOf(
      mockOfferwallInfo.copy(
        offerWallConfig = mockOfferwallCfgPromo.copy(isHighlighted = false)
      )
    )

    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = offerwalls,
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_12",
        "unlocked",
        "game_1",
        "game_2",
        "game_3",
        "mainOfferWall1",
        "game_4",
        "lockedGames",
      )
    )

    var gameIndex = 0
    offerItemFromGameList.forEach {
      verifyBlocking(offerItemCustomizer) { customizeGameOfferItem(it, USER_ID, gameIndex) }
      gameIndex++
    }
  }

  @Test
  fun `SHOULD apply customizer for games`() {
    runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = listOf(mockOfferwallInfo),
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    var gameIndex = 0
    offerItemFromGameList.forEach {
      verifyBlocking(offerItemCustomizer) { customizeGameOfferItem(it, USER_ID, gameIndex) }
      gameIndex++
    }
  }

  @Test
  fun `SHOULD do combine correct order WHEN we should not show big offer and offerwalls defined`() {
    bigOffersHelper.mock({ showBigOffers(USER_ID) }, false)
    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = listOf(mockOfferwallInfo),
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_12",
        "unlocked",
        "game_1",
        "game_2",
        "game_3",
        "game_4",
        "lockedGames",
        "additionalOffer_11",
        "additionalOffer_133"
      )
    )
  }

  @Test
  fun `SHOULD do combine correct order WHEN we should show big offer and offerwalls defined`() {
    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = listOf(mockOfferwallInfo),
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_12",
        "unlocked",
        "game_1",
        "game_2",
        "game_3",
        "mainOfferWall1",
        "game_4",
        "lockedGames"
      )
    )
  }

  @Test
  fun `SHOULD do combine correct order WHEN we should show big offer and offerwalls defined BUT user is from MX`() {
    disableMxOfferwallExperimentService.mock({ shouldSkipOfwHighlight(USER_ID) }, true)
    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = listOf(mockOfferwallInfo),
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_12",
        "unlocked",
        "game_1",
        "game_2",
        "game_3",
        "game_4",
        "lockedGames",
        "additionalOffer_11",
        "additionalOffer_133",
      )
    )
  }

  @Test
  fun `SHOULD do combine correct order WHEN we should show big offer and there is promotionPeriod and several offerwalls`() {
    val offerwalls = listOf(
      mockOfferwallInfo.copy(
        offerWallType = FYBER,
        offerWallConfig = mockOfferwallCfgPromo.copy(
          id = 1,
        ) // any promoted offerwall enables promotion mode
      ),
      mockOfferwallInfo.copy(
        offerWallType = OfferWallType.TAPJOY,
        offerWallConfig = mockOfferwallCfgPromo.copy(
          id = 2,
        )
      )
    )
    val ao1 = additionalOfferStub.copy(
      id = 11,
      action = OfferAction.TAPJOY,
    )
    val ao2 = additionalOfferStub.copy(
      id = 12,
      action = OfferAction.VIDEO_AD,
    )
    val ao3 = additionalOfferStub.copy(
      id = 13,
      action = OfferAction.FYBER,
    )
    val ao4 = additionalOfferStub.copy(
      id = 14,
      action = OfferAction.WELCOME_COINS,
    )
    val additionalOffers = listOf(ao1, ao2, ao3, ao4)

    additionalOfferSplitter.mock({ splitAdditionalOffers(additionalOffers) }, listOf(ao4) to listOf(ao1, ao2, ao3))
    additionalOffers.forEach { ao ->
      converter.answer(
        { toOfferItemApiDto(ao, offersConfigStub, APP_VERSION) },
        { invocationOnMock ->
          val arg = invocationOnMock.getArgument(0) as AdditionalOffer
          offerItemLargeApiDtoOfferwallStub.copy(id = "additionalOffer_" + arg.id)
        }
      )
    }

    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = offerwalls,
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_14",
        "unlocked",
        "mainOfferWall1",
        "mainOfferWall2",
        "game_1",
        "game_2",
        "game_3",
        "game_4",
        "mainOfferWall1",
        "lockedGames",
        "additionalOffer_12",
      )
    )

    var gameIndex = 0
    offerItemFromGameList.forEach {
      verifyBlocking(offerItemCustomizer) { customizeGameOfferItem(it, USER_ID, gameIndex) }
      gameIndex++
    }
  }

  @Test
  fun `SHOULD do combine correct order WHEN we should not show big offer and offerwalls defined AND several offerwalls`() {
    val offerwalls = listOf(
      mockOfferwallInfo.copy(
        offerWallType = FYBER,
        offerWallConfig = mockOfferwallCfgPromo.copy(
          id = 1,
        ) // any promoted offerwall enables promotion mode
      ),
      mockOfferwallInfo.copy(
        offerWallType = OfferWallType.TAPJOY,
        offerWallConfig = mockOfferwallCfgPromo.copy(
          id = 2,
        )
      )
    )
    val ao1 = additionalOfferStub.copy(
      id = 11,
      action = OfferAction.TAPJOY,
    )
    val ao2 = additionalOfferStub.copy(
      id = 12,
      action = OfferAction.VIDEO_AD,
    )
    val ao3 = additionalOfferStub.copy(
      id = 13,
      action = OfferAction.FYBER,
    )
    val ao4 = additionalOfferStub.copy(
      id = 14,
      action = OfferAction.WELCOME_COINS,
    )
    val additionalOffers = listOf(ao1, ao2, ao3, ao4)

    additionalOfferSplitter.mock({ splitAdditionalOffers(additionalOffers) }, listOf(ao4) to listOf(ao1, ao2, ao3))
    additionalOffers.forEach { ao ->
      converter.answer(
        { toOfferItemApiDto(ao, offersConfigStub, APP_VERSION) },
        { invocationOnMock ->
          val arg = invocationOnMock.getArgument(0) as AdditionalOffer
          offerItemLargeApiDtoOfferwallStub.copy(id = "additionalOffer_" + arg.id)
        }
      )
    }

    bigOffersHelper.mock({ showBigOffers(USER_ID) }, false)

    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = offerwalls,
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_14",
        "unlocked",
        "game_1",
        "game_2",
        "game_3",
        "game_4",
        "lockedGames",
        "additionalOffer_13",
        "additionalOffer_11",
        "additionalOffer_12"
      )
    )
  }

  @Test
  fun `SHOULD do combine correct order WHEN empty offerwalls list`() {
    bigOffersHelper.mock({ showBigOffers(USER_ID) }, false)
    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = emptyList(),
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_12",
        "unlocked",
        "game_1",
        "game_2",
        "game_3",
        "game_4",
        "lockedGames",
        "additionalOffer_11",
        "additionalOffer_133"
      )
    )
  }


  @Test
  fun `SHOULD do combine correct order WHEN NOT offerwalls defined`() {
    bigOffersHelper.mock({ showBigOffers(USER_ID) }, false)
    val result = runBlocking {
      underTest.combineOffers(
        userId = USER_ID,
        games = games,
        unlockedAndLockedGamesWidgets = unlockedAndLockedGamesWidgetsStub,
        offerWalls = null,
        additionalOffers = additionalOffers,
        appVersion = APP_VERSION,
        isNonConsensualGdprUser = IS_NOT_CONSENSUAL_GDPR_USER,
        offersConfig = offersConfigStub
      )
    }
    val first = result.items.map { it.id }
    assertThat(first).isEqualTo(
      listOf(
        "additionalOffer_12",
        "unlocked",
        "game_1",
        "game_2",
        "game_3",
        "game_4",
        "lockedGames",
        "additionalOffer_11",
        "additionalOffer_133"
      )
    )
  }

}