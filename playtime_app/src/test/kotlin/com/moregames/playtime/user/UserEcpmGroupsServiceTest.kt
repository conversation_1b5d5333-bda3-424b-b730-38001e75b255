package com.moregames.playtime.user


import com.moregames.base.util.mock
import com.moregames.playtime.user.UserEcpmGroupsService.DefineUsersEcpmGroupEffect
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoMoreInteractions
import java.math.BigDecimal

class UserEcpmGroupsServiceTest {
  private val userPersistenceService: UserPersistenceService = mock()
  private val service = UserEcpmGroupsService(userPersistenceService)

  companion object {
    const val USER_ID = "userId"
    val effect = DefineUsersEcpmGroupEffect(USER_ID, BigDecimal("0.55"))
    val ecpmThresholds = listOf(
      0 to BigDecimal("0.70469806"),
      1 to BigDecimal("0.60469806"),
      2 to BigDecimal("0.47652368"),
      3 to BigDecimal("0.42652368"),
      4 to BigDecimal("0.35710806"),
      5 to BigDecimal("0.31710806"),
      6 to BigDecimal("0.27672577"),
      7 to BigDecimal("0.23672577"),
      8 to BigDecimal("0.20137254"),
      9 to BigDecimal("0.17137254"),
      10 to BigDecimal("0.1317917"),
      11 to BigDecimal("0.0917917"),
      12 to BigDecimal("0.07472855"),
      13 to BigDecimal("0.04472855"),
      14 to BigDecimal("0.03768203"),
      15 to BigDecimal("0.02768203"),
      16 to BigDecimal("0.01525086"),
      17 to BigDecimal("0.01025086"),
      18 to BigDecimal("0.00525086"),
      19 to BigDecimal("0"),
    )
  }

  @BeforeEach
  fun init() {
    userPersistenceService.mock({ getCurrentEcpmGroupsThresholds() }, ecpmThresholds)
  }

  @Test
  fun `SHOULD track 2 group ON defineUsersEcpmGroup WHEN not the best first reward`() {
    runBlocking {
      service.defineUsersEcpmGroup(effect)
    }

    verifyBlocking(userPersistenceService) { getCurrentEcpmGroupsThresholds() }
    verifyBlocking(userPersistenceService) { trackUserEcpmGroup(USER_ID, 2) }
  }

  @Test
  fun `SHOULD track 0 group ON defineUsersEcpmGroup WHEN the best first reward`() {
    runBlocking {
      service.defineUsersEcpmGroup(effect.copy(firstVideoAdRewardAmount = BigDecimal.ONE))
    }

    verifyBlocking(userPersistenceService) { getCurrentEcpmGroupsThresholds() }
    verifyBlocking(userPersistenceService) { trackUserEcpmGroup(USER_ID, 0) }
  }

  @Test
  fun `SHOULD track 10 group ON defineUsersEcpmGroup WHEN the the first reward is average`() {
    runBlocking {
      service.defineUsersEcpmGroup(effect.copy(firstVideoAdRewardAmount = BigDecimal("0.132")))
    }

    verifyBlocking(userPersistenceService) { getCurrentEcpmGroupsThresholds() }
    verifyBlocking(userPersistenceService) { trackUserEcpmGroup(USER_ID, 10) }
  }

  @Test
  fun `SHOULD track 18 group ON defineUsersEcpmGroup WHEN the the first reward is low`() {
    runBlocking {
      service.defineUsersEcpmGroup(effect.copy(firstVideoAdRewardAmount = BigDecimal("0.01")))
    }

    verifyBlocking(userPersistenceService) { getCurrentEcpmGroupsThresholds() }
    verifyBlocking(userPersistenceService) { trackUserEcpmGroup(USER_ID, 18) }
  }

  @Test
  fun `SHOULD track 19 group ON defineUsersEcpmGroup WHEN the the first reward is zero`() {
    runBlocking {
      service.defineUsersEcpmGroup(effect.copy(firstVideoAdRewardAmount = BigDecimal("0.0")))
    }

    verifyBlocking(userPersistenceService) { getCurrentEcpmGroupsThresholds() }
    verifyBlocking(userPersistenceService) { trackUserEcpmGroup(USER_ID, 19) }
  }

  @Test
  fun `SHOULD skip processing ON defineUsersEcpmGroup WHEN we have no calculated thresholds`() {
    userPersistenceService.mock({ getCurrentEcpmGroupsThresholds() }, null)

    runBlocking {
      service.defineUsersEcpmGroup(effect.copy(firstVideoAdRewardAmount = BigDecimal("0.33")))
    }

    verifyBlocking(userPersistenceService) { getCurrentEcpmGroupsThresholds() }
    verifyNoMoreInteractions(userPersistenceService)
  }
}