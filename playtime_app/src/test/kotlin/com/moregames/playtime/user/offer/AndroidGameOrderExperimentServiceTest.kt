package com.moregames.playtime.user.offer

import assertk.assertThat
import assertk.assertions.*
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.mock
import com.moregames.playtime.app.isAndroidBlockBuster3rdEnabled
import com.moregames.playtime.buseffects.ForceReassignVariationMessage
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.utils.androidGameOfferListStub
import com.moregames.playtime.utils.androidGameOfferStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import java.time.Instant

@ExtendWith(MockExtension::class)
class AndroidGameOrderExperimentServiceTest(
  private val abTestingService: AbTestingService,
  private val gamePersistenceService: GamePersistenceService,
  private val gamesService: GamesService,
  private val messageBus: MessageBus,
  private val featureFlagsFacade: FeatureFlagsFacade,
) {

  companion object {
    private const val USER_ID = "userId"
  }

  private val underTest = AndroidGameOrderExperimentService(
    abTestingService = abTestingService,
    gamePersistenceService = gamePersistenceService,
    gamesService = gamesService,
    featureFlagsFacade = featureFlagsFacade,
    messageBus = messageBus,
  )

  @Test
  fun `SHOULD replace WBP with BB on applyOrder WHEN user is on variation`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_BLOCK_INSTEAD_WBP)

    val listToSort = (androidGameOfferListStub + listOf(androidGameOfferStub.copy(id = 70, applicationId = ApplicationId.BLOCKBUSTER_APP_ID)))
    val zentilesIndex = androidGameOfferListStub.indexOfFirst { it.applicationId == ApplicationId.WOODEN_PUZZLE_APP_ID }

    val result = runBlocking {
      underTest.applyOrderExperiment(USER_ID, listToSort, mapOf())
    }

    assertThat(result).index(zentilesIndex).transform { it.applicationId }.isEqualTo(ApplicationId.BLOCKBUSTER_APP_ID)
    assertThat(result.last()).transform { it.applicationId }.isEqualTo(ApplicationId.WOODEN_PUZZLE_APP_ID)
    assertThat(result.filter { it.applicationId == ApplicationId.WOODEN_PUZZLE_APP_ID }).isNotNull().hasSize(1)

    assertThat(result.groupingBy { it.applicationId }.eachCount().filterValues { it > 1 }).isEmpty()
  }

  @Test
  fun `SHOULD move BLOCKBUSTER up WHEN user is on ANDROID_BLOCK_BUSTRER_1ST_ORDER variation`() {
    featureFlagsFacade.mock({ isAndroidBlockBuster3rdEnabled() }, false)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_BLOCK_BUSTER_1ST_ORDER)

    val listToSort = (androidGameOfferListStub + listOf(androidGameOfferStub.copy(id = 70, applicationId = ApplicationId.BLOCKBUSTER_APP_ID)))
      .shuffled()

    val result = runBlocking {
      underTest.applyOrderExperiment(USER_ID, listToSort, mapOf())
    }

    assertThat(result.first().applicationId).isEqualTo(ApplicationId.BLOCKBUSTER_APP_ID)
  }

  @Test
  fun `SHOULD return the same list WHEN user is not participant of AndroidGameOrder experiment`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, DEFAULT)

    val result = runBlocking {
      underTest.applyOrderExperiment(USER_ID, androidGameOfferListStub, mapOf())
    }
    assertThat(result).isEqualTo(androidGameOfferListStub)
  }

  @Test
  fun `SHOULD move 11 games to head of a list WHEN user has variation fixedFirst11GamesOrder`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_FIXED_FIRST_11_ORDER)

    val result = runBlocking {
      underTest.applyOrderExperiment(USER_ID, androidGameOfferListStub, mapOf())
    }

    val applicationIds = result.map { it.applicationId }

    assertThat(applicationIds).isEqualTo(
      listOf(
        "com.gimica.madsmash",
        "com.gimica.solitaireverse",
        "com.gimica.zentiles",
        "com.gimica.bubblepop",
        "com.gimica.treasuremaster",
        "com.gimica.ballbounce",
        "com.gimica.sugarmatch",
        "com.gimica.mergeblast",
        "com.gimica.wordseeker",
        "com.gimica.mixblox",
        "com.gimica.watersorter",
        "com.gimica.triviamadness",
        "com.gimica.emojiclickers",
        "com.relaxingbraintraining.grindmygears",
        "com.gimica.carsmerge",
        "com.gimica.blockholeclash",
        "com.gimica.idlemergefun",
        "com.gimica.blockslider",
        "com.gimica.sudoku",
        "com.gimica.puzzlepopblaster",
        "com.gimica.hexapuzzlefun",
        "com.gimica.brickdoku",
        "com.gimica.wordkitchen",
        "com.relaxingbraintraining.dunk",
        "com.relaxingbraintraining.rollthatball",
        "com.relaxingbraintraining.popslice",
        "com.relaxingbraintraining.six",
        "com.relaxingbraintraining.mousekeeper",
        "com.relaxingbraintraining.snakeclash",
        "com.relaxingbraintraining.ballrush",
        "com.relaxingbraintraining.onelineadvanced",
        "com.gimica.marblemadness",
        "com.gimica.hexmatch",
        "com.relaxingbraintraining.numbermerge",
        "com.pinmaster.screwpuzzle"
      )
    )
  }

  @Test
  fun `SHOULD move 11 games to head of a list according initial order WHEN user has variation coinsFirst11GamesOrder`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_COINS_FIRST_11_ORDER)

    val now: Instant = Instant.now()

    val gameCoins = listOf(
      UserPersistenceService.GamePlayStatusDto(1, 2000, false, now, now),
      UserPersistenceService.GamePlayStatusDto(2, 1500, false, now, now),
      UserPersistenceService.GamePlayStatusDto(3, 1000, false, now, now),
      UserPersistenceService.GamePlayStatusDto(29, 1000, false, now, now),
    ).associateBy { it.gameId }

    val result = runBlocking {
      underTest.applyOrderExperiment(USER_ID, androidGameOfferListStub, gameCoins)
    }

    val applicationIds = result.map { it.applicationId }

    assertThat(applicationIds).isEqualTo(
      listOf(
        "com.gimica.treasuremaster",
        "com.gimica.solitaireverse",
        "com.gimica.ballbounce",
        "com.relaxingbraintraining.onelineadvanced",
        "com.gimica.madsmash",
        "com.gimica.zentiles",
        "com.gimica.bubblepop",
        "com.gimica.sugarmatch",
        "com.gimica.mergeblast",
        "com.gimica.wordseeker",
        "com.gimica.mixblox",
        "com.gimica.watersorter",
        "com.gimica.triviamadness",
        "com.gimica.emojiclickers",
        "com.relaxingbraintraining.grindmygears",
        "com.gimica.carsmerge",
        "com.gimica.blockholeclash",
        "com.gimica.idlemergefun",
        "com.gimica.blockslider",
        "com.gimica.sudoku",
        "com.gimica.puzzlepopblaster",
        "com.gimica.hexapuzzlefun",
        "com.gimica.brickdoku",
        "com.gimica.wordkitchen",
        "com.relaxingbraintraining.dunk",
        "com.relaxingbraintraining.rollthatball",
        "com.relaxingbraintraining.popslice",
        "com.relaxingbraintraining.six",
        "com.relaxingbraintraining.mousekeeper",
        "com.relaxingbraintraining.snakeclash",
        "com.relaxingbraintraining.ballrush",
        "com.gimica.marblemadness",
        "com.gimica.hexmatch",
        "com.relaxingbraintraining.numbermerge",
        "com.pinmaster.screwpuzzle"
      )
    )
  }

  @Test
  fun `SHOULD move PinMaster up WHEN user is on ANDROID_PIN_MASTER_1ST_ORDER variation`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_PIN_MASTER_1ST_ORDER)

    val listToSort = androidGameOfferListStub.shuffled()

    val result = runBlocking {
      underTest.applyOrderExperiment(USER_ID, listToSort, mapOf())
    }

    assertThat(result[0].applicationId).isEqualTo(ApplicationId.PIN_MASTER_APP_ID)
    assertThat(result[1].applicationId).isEqualTo(ApplicationId.SOLITAIRE_VERSE_APP_ID)
    assertThat(result[2].applicationId).isEqualTo(ApplicationId.TREASURE_MASTER_APP_ID)
  }

  @Test
  fun `SHOULD add PinMaster ON addGameToTheTop WHEN user is ON ANDROID_PIN_MASTER_1ST_ORDER variation`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_PIN_MASTER_1ST_ORDER)
    val pinMasterId = 2000565
    gamesService.mock({ getGameId(ApplicationId.PIN_MASTER_APP_ID, AppPlatform.ANDROID) }, pinMasterId)
    val pinMaster = androidGameOfferStub.copy(id = pinMasterId, applicationId = ApplicationId.PIN_MASTER_APP_ID)
    gamePersistenceService.mock({ loadAndroidGamesByIds(setOf(pinMasterId)) }, listOf(pinMaster))
    val offersWithoutPinMaster = androidGameOfferListStub.filter {
      it.applicationId !in arrayOf(ApplicationId.PIN_MASTER_APP_ID)
    }

    val result = runBlocking {
      underTest.addGameToTheTop(USER_ID, offersWithoutPinMaster)
    }
    assertThat(result).isEqualTo(listOf(pinMaster) + offersWithoutPinMaster)
  }

  @Test
  fun `SHOULD not add PinMaster ON addPinMaster WHEN PinMaster already in list`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_PIN_MASTER_1ST_ORDER)
    val result = runBlocking {
      underTest.addGameToTheTop(USER_ID, androidGameOfferListStub)
    }
    assertThat(result).isEqualTo(androidGameOfferListStub)
    verifyNoInteractions(gamesService)
    verifyNoInteractions(gamePersistenceService)
  }

  @Test
  fun `SHOULD move BLOCKBUSTER up WHEN user is on ANDROID_BLOCK_BUSTRER_3RD_ORDER variation`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_BLOCK_BUSTER_3RD_ORDER)

    val listToSort = (androidGameOfferListStub + listOf(androidGameOfferStub.copy(id = 70, applicationId = ApplicationId.BLOCKBUSTER_APP_ID)))
      .shuffled()

    val result = runBlocking {
      underTest.applyOrderExperiment(USER_ID, listToSort, mapOf())
    }

    assertThat(result[2].applicationId).isEqualTo(ApplicationId.BLOCKBUSTER_APP_ID)
  }

  @Test
  fun `SHOULD move BLOCKBUSTER up to 3rd WHEN user is on ANDROID_BLOCK_BUSTRER_1ST_ORDER variation AND FF is enabled`() {
    featureFlagsFacade.mock({ isAndroidBlockBuster3rdEnabled() }, true)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_BLOCK_BUSTER_1ST_ORDER)

    val listToSort = (androidGameOfferListStub + listOf(androidGameOfferStub.copy(id = 70, applicationId = ApplicationId.BLOCKBUSTER_APP_ID)))
      .shuffled()

    val result = runBlocking {
      underTest.applyOrderExperiment(USER_ID, listToSort, mapOf())
    }

    assertThat(result[2].applicationId).isEqualTo(ApplicationId.BLOCKBUSTER_APP_ID)
    verifyBlocking(messageBus) {
      publish(
        ForceReassignVariationMessage(
          USER_ID,
          ClientExperiment.ANDROID_GAMES_ORDER,
          Variations.ANDROID_BLOCK_BUSTER_3RD_ORDER
        )
      )
    }
  }

  @Test
  fun `SHOULD add BubbleChef ON addGameToTheTop WHEN user is ON ANDROID_BUBBLE_CHEF_1ST_ORDER variation`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_BUBBLE_CHEF_1ST_ORDER)
    val bubbleChefId = 2000566
    gamesService.mock({ getGameId(ApplicationId.BUBBLE_CHIEF_APP_ID, AppPlatform.ANDROID) }, bubbleChefId)
    val bubbleChef = androidGameOfferStub.copy(id = bubbleChefId, applicationId = ApplicationId.BUBBLE_CHIEF_APP_ID)
    gamePersistenceService.mock({ loadAndroidGamesByIds(setOf(bubbleChefId)) }, listOf(bubbleChef))
    val offersWithoutBubbleChef = androidGameOfferListStub.filter {
      it.applicationId != ApplicationId.BUBBLE_CHIEF_APP_ID
    }

    val result = runBlocking {
      underTest.addGameToTheTop(USER_ID, offersWithoutBubbleChef)
    }
    assertThat(result).isEqualTo(listOf(bubbleChef) + offersWithoutBubbleChef)
  }

  @Test
  fun `SHOULD do nothing on addGameToTheTop WHEN user is not participant ANDROID_GAME_ORDER`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, DEFAULT)

    val result = runBlocking {
      underTest.addGameToTheTop(USER_ID, androidGameOfferListStub)
    }
    assertThat(result).isEqualTo(androidGameOfferListStub)

    verifyNoInteractions(gamePersistenceService)
  }

  @Test
  fun `SHOULD not add BubbleChef ON addGameToTheTop WHEN BubbleChef already in list`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_BUBBLE_CHEF_1ST_ORDER)
    val offersWithBubbleChef = androidGameOfferListStub + listOf(androidGameOfferStub.copy(id = 70, applicationId = ApplicationId.BUBBLE_CHIEF_APP_ID))

    val result = runBlocking {
      underTest.addGameToTheTop(USER_ID, offersWithBubbleChef)
    }
    assertThat(result).isEqualTo(offersWithBubbleChef)
    verifyNoInteractions(gamesService)
    verifyNoInteractions(gamePersistenceService)
  }

  @Test
  fun `SHOULD return original offers ON addGameToTheTop WHEN gameId not found`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_GAMES_ORDER) }, Variations.ANDROID_BUBBLE_CHEF_1ST_ORDER)
    gamesService.mock({ getGameId(ApplicationId.BUBBLE_CHIEF_APP_ID, AppPlatform.ANDROID) }, null)
    val offersWithoutBubbleChef = androidGameOfferListStub.filter {
      it.applicationId != ApplicationId.BUBBLE_CHIEF_APP_ID
    }

    val result = runBlocking {
      underTest.addGameToTheTop(USER_ID, offersWithoutBubbleChef)
    }
    assertThat(result).isEqualTo(offersWithoutBubbleChef)
    verifyNoInteractions(gamePersistenceService)
  }
}