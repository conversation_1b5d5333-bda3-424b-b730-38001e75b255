package com.moregames.playtime.user.bonuscashbar

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.justplayapps.base.Common
import com.justplayapps.playtime.rewarding.bonusbank.bonusBankClaim
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.commands.CommandIdResponse
import com.moregames.base.messaging.commands.CommandsClient
import com.moregames.base.util.ClientVersionsSupport.getUserCreationMinAppVersion
import com.moregames.base.util.Constants.ANDROID_APP_VERSION_HEADER
import com.moregames.base.util.mock
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.bonuscashbar.dto.BonusCashBar
import com.moregames.playtime.util.installDefaultContentNegotiation
import com.moregames.playtime.utils.Json
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.server.testing.*
import kotlinx.serialization.decodeFromString
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal

@ExtendWith(MockExtension::class)
class BonusCashBarControllerTest(
  private val rewardingFacade: RewardingFacade,
  private val commandsClient: CommandsClient,
  private val messageBus: MessageBus,
) {

  private fun controller(): Application.() -> Unit = {
    install(IgnoreTrailingSlash)
    installDefaultContentNegotiation()
    routing {
      BonusCashBarController(
        rewardingFacade = rewardingFacade,
        commandsClient = commandsClient,
        messageBus = messageBus
      ).startRouting(this)
    }
  }

  companion object {
    private const val USER_ID = "user-id"
  }

  @Test
  fun `SHOULD return not-found ON widget call WHEN NOT bcb defined`() = withTestApplication(controller()) {
    rewardingFacade.mock({ getBonusCashBarState(USER_ID, ANDROID) }, null)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/bonus-cash-bar/widget?userId=$USER_ID"
    ) {
      addHeader(ANDROID_APP_VERSION_HEADER, getUserCreationMinAppVersion(ANDROID).toString())
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.NotFound)

    verifyBlocking(rewardingFacade) { getBonusCashBarState(USER_ID, ANDROID) }
  }

  @Test
  fun `SHOULD return widget ON widget call WHEN bcb defined`() = withTestApplication(controller()) {
    val bcbWidget = BonusCashBar(
      valueToReach = BigDecimal("1"),
      currentValue = BigDecimal("2"),
      reward = BigDecimal("3"),
      readyToClaim = false,
      milestones = listOf()
    )

    rewardingFacade.mock({ getBonusCashBarState(USER_ID, ANDROID) }, bcbWidget)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/bonus-cash-bar/widget?userId=$USER_ID"
    ) {
      addHeader(ANDROID_APP_VERSION_HEADER, getUserCreationMinAppVersion(ANDROID).toString())
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(Json.defaultJsonConverter.decodeFromString<BonusCashBar>(response.response.content!!)).isEqualTo(bcbWidget)

    verifyBlocking(rewardingFacade) { getBonusCashBarState(USER_ID, ANDROID) }
  }

  @Test
  fun `SHOULD emit claim processing ON claim call WHEN bcb defined`() = withTestApplication(controller()) {
    val expected = CommandIdResponse(commandId = "command-id")

    commandsClient.mock({ createCommand() }, "command-id")

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/bonus-cash-bar/claim?userId=$USER_ID"
    ) {
      addHeader(ANDROID_APP_VERSION_HEADER, getUserCreationMinAppVersion(ANDROID).toString())
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(Json.defaultJsonConverter.decodeFromString<CommandIdResponse>(response.response.content!!)).isEqualTo(expected)

    verifyBlocking(commandsClient) { createCommand() }
    verifyBlocking(messageBus) {
      publish(
        bonusBankClaim {
          this.userId = USER_ID
          this.commandId = "command-id"
          this.platform = Common.AppPlatformProto.ANDROID
        }
      )
    }
  }
}