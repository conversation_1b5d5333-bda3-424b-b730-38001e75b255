package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import assertk.assertions.isFalse
import assertk.assertions.isTrue
import com.justplayapps.service.rewarding.bonus.UserBonusBalancePersistenceService
import com.justplayapps.service.rewarding.bonus.UserBonusBalanceService
import com.justplayapps.service.rewarding.earnings.EmExperimentBaseService
import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalancePersistenceService
import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalanceService
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.bonusCoins
import com.justplayapps.service.rewarding.earnings.proto.userCoinsAddedEvent
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.justplayapps.service.rewarding.revenue.RevenuePersistenceService
import com.moregames.base.bus.MessageBus
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.NonGameCoinsAddedEventDto
import com.moregames.base.user.UserBonusBalanceType
import com.moregames.base.util.mock
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.test.assertEquals

class UserBonusBalanceServiceTest {
  private val userBonusBalancePersistenceService: UserBonusBalancePersistenceService = mock()
  private val abTestingFacade: AbTestingFacade = mock()
  private val messageBus: MessageBus = mock()
  private val userCurrentCoinsBalancePersistenceService: UserCurrentCoinsBalancePersistenceService = mock()
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService = mock()
  private val revenuePersistenceService: RevenuePersistenceService = mock()
  private val emExperimentBaseService: EmExperimentBaseService = mock {
    onBlocking { inflatingCoinsMultiplier(USER_ID) } doReturn 200
  }

  private val service = UserBonusBalanceService(
    userBonusBalancePersistenceService = userBonusBalancePersistenceService,
    userCurrentCoinsBalancePersistenceService = userCurrentCoinsBalancePersistenceService,
    userCurrentCoinsBalanceService = userCurrentCoinsBalanceService,
    revenuePersistenceService = revenuePersistenceService,
    messageBus = messageBus,
    abTestingFacade = abTestingFacade,
    emExperimentBaseService = emExperimentBaseService,
  )

  init {
    abTestingFacade.mock({ isEm2Participant(USER_ID) }, false)
    abTestingFacade.mock({ isCoinsDoNotResetParticipant(eq(USER_ID), any()) }, false)
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, AppPlatform.ANDROID) }, UserCurrentCoinsGoalBalance(228, 0, 0))
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, AppPlatform.IOS) }, UserCurrentCoinsGoalBalance(228, 0, 0))
  }

  private companion object {
    const val USER_ID = "user1"
  }

  @Test
  fun `SHOULD add bonus coins ON addBonusCoins`() {
    userBonusBalancePersistenceService.mock(
      { addBonusCoins(USER_ID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS, "bonusKey") }, true
    )

    runBlocking { service.addBonusCoins(USER_ID, AppPlatform.ANDROID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS, "bonusKey") }

    verifyBlocking(userBonusBalancePersistenceService) {
      addBonusCoins(USER_ID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS, "bonusKey")
    }
    verifyBlocking(userCurrentCoinsBalancePersistenceService, never()) {
      userCurrentCoinsBalancePersistenceService.updateEm2CurrentBonusBalance(USER_ID, 42.toBigDecimal(), false)
    }
    verifyBlocking(userCurrentCoinsBalancePersistenceService) {
      userCurrentCoinsBalancePersistenceService.updateCurrentBonusBalance(USER_ID, 42.toBigDecimal(), false)
    }
    verifyBlocking(messageBus) { publish(NonGameCoinsAddedEventDto(USER_ID)) }
    verifyBlocking(messageBus) {
      publish(
        userCoinsAddedEvent {
          this.userId = USER_ID
          this.coins = 228
          this.coinsAdded = 8400
          this.bonusCoinsData = bonusCoins {}
        }
      )
    }
  }

  @Test
  fun `SHOULD add bonus coins ON addBonusCoins WHEN em2 participant`() {
    userBonusBalancePersistenceService.mock(
      { addBonusCoins(USER_ID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS, "bonusKey") }, true
    )
    abTestingFacade.mock({ isEm2Participant(USER_ID) }, true)

    runBlocking { service.addBonusCoins(USER_ID, AppPlatform.ANDROID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS, "bonusKey") }

    verifyBlocking(userBonusBalancePersistenceService) {
      addBonusCoins(USER_ID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS, "bonusKey")
    }
    verifyBlocking(userCurrentCoinsBalancePersistenceService) {
      userCurrentCoinsBalancePersistenceService.updateEm2CurrentBonusBalance(USER_ID, 42.toBigDecimal(), false)
    }
    verifyBlocking(userCurrentCoinsBalancePersistenceService, never()) {
      userCurrentCoinsBalancePersistenceService.updateCurrentBonusBalance(USER_ID, 42.toBigDecimal(), false)
    }
    verifyBlocking(messageBus) { publish(NonGameCoinsAddedEventDto(USER_ID)) }
    verifyBlocking(messageBus) {
      publish(
        userCoinsAddedEvent {
          this.userId = USER_ID
          this.coins = 228
          this.coinsAdded = 8400
          this.bonusCoinsData = bonusCoins {}
        }
      )
    }
  }

  @Test
  fun `SHOULD NOT add bonus coins ON addBonusCoins WHEN bonus already acquired`() {
    userBonusBalancePersistenceService.mock(
      { addBonusCoins(USER_ID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS) }, false
    )

    runBlocking { service.addBonusCoins(USER_ID, AppPlatform.ANDROID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS) }

    verifyBlocking(userBonusBalancePersistenceService) {
      addBonusCoins(USER_ID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS)
    }
    verifyNoMoreInteractions(userBonusBalancePersistenceService)
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD NOT add bonus coins ON addBonusCoins WHEN em2 participant AND bonus already acquired`() {
    userBonusBalancePersistenceService.mock(
      { addBonusCoins(USER_ID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS) }, false
    )
    abTestingFacade.mock({ isEm2Participant(USER_ID) }, true)

    runBlocking { service.addBonusCoins(USER_ID, AppPlatform.ANDROID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS) }

    verifyBlocking(userBonusBalancePersistenceService) {
      addBonusCoins(USER_ID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS)
    }
    verifyBlocking(userCurrentCoinsBalancePersistenceService, never()) {
      userCurrentCoinsBalancePersistenceService.updateEm2CurrentBonusBalance(USER_ID, 42.toBigDecimal(), false)
    }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD track visible coins ON addBonusCoins WHEN em1 AND coinsDoNotReset participant`() {
    userBonusBalancePersistenceService.mock(
      { addBonusCoins(USER_ID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS, "bonusKey") }, true
    )
    abTestingFacade.mock({ isCoinsDoNotResetParticipant(USER_ID, AppPlatform.ANDROID) }, true)

    runBlocking { service.addBonusCoins(USER_ID, AppPlatform.ANDROID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS, "bonusKey") }

    verifyBlocking(userCurrentCoinsBalancePersistenceService) {
      userCurrentCoinsBalancePersistenceService.updateCurrentBonusBalance(USER_ID, 42.toBigDecimal(), true)
    }
    verifyBlocking(messageBus) {
      publish(
        userCoinsAddedEvent {
          this.userId = USER_ID
          this.coins = 228
          this.coinsAdded = 8400
          this.bonusCoinsData = bonusCoins {}
        }
      )
    }
  }

  @Test
  fun `SHOULD track visible coins ON addBonusCoins WHEN em2 AND coinsDoNotReset participant`() {
    userBonusBalancePersistenceService.mock(
      { addBonusCoins(USER_ID, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS, "bonusKey") }, true
    )
    abTestingFacade.mock({ isEm2Participant(USER_ID) }, true)
    abTestingFacade.mock({ isCoinsDoNotResetParticipant(USER_ID, AppPlatform.IOS) }, true)

    runBlocking { service.addBonusCoins(USER_ID, AppPlatform.IOS, 42.toBigDecimal(), UserBonusBalanceType.WELCOME_COINS, "bonusKey") }

    verifyBlocking(userCurrentCoinsBalancePersistenceService) {
      userCurrentCoinsBalancePersistenceService.updateEm2CurrentBonusBalance(USER_ID, 42.toBigDecimal(), true)
    }
    verifyBlocking(messageBus) {
      publish(
        userCoinsAddedEvent {
          this.userId = USER_ID
          this.coins = 228
          this.coinsAdded = 8400
          this.bonusCoinsData = bonusCoins {}
        }
      )
    }
  }

  @Test
  fun `SHOULD make properly calls ON findLastWelcomeCoinsDate`() {
    val userId = UUID.randomUUID().toString()
    val welcomeCoinsReceivedAt = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    userBonusBalancePersistenceService.mock({ findLastWelcomeCoinsDate(userId) }, welcomeCoinsReceivedAt)

    runBlocking {
      service.findLastWelcomeCoinsDate(userId)
    }.let {
      assertEquals(welcomeCoinsReceivedAt, it)
    }
  }

  @Test
  fun `SHOULD call persistence and propagate the answer ON hasBonusFor`() {
    userBonusBalancePersistenceService.mock({ hasBonusFor(USER_ID, UserBonusBalanceType.WELCOME_COINS) }, true)

    runBlocking { service.hasBonusFor(USER_ID, UserBonusBalanceType.WELCOME_COINS) }
      .let { assertThat(it).isTrue() }
    verifyBlocking(userBonusBalancePersistenceService) { hasBonusFor(USER_ID, UserBonusBalanceType.WELCOME_COINS) }
  }

  @Test
  fun `SHOULD call persistence and propagate the answer ON hasBonusFor v2`() {
    userBonusBalancePersistenceService.mock({ hasBonusFor(USER_ID, UserBonusBalanceType.WELCOME_COINS, USER_ID) }, false)

    runBlocking { service.hasBonusFor(USER_ID, UserBonusBalanceType.WELCOME_COINS, USER_ID) }
      .let { assertThat(it).isFalse() }
    verifyBlocking(userBonusBalancePersistenceService) { hasBonusFor(USER_ID, UserBonusBalanceType.WELCOME_COINS, USER_ID) }
  }

  @Test
  fun `SHOULD convert bonus bank coins to earnings`() {
    userBonusBalancePersistenceService.mock({ getBonusBankUnpaidCoins(USER_ID) }, 127.5.toBigDecimal())
    runBlocking {
      service.getBonusBankEarnings(USER_ID).let {
        assertThat(it).isEqualByComparingTo(0.29.toBigDecimal())
      }
    }
  }
}