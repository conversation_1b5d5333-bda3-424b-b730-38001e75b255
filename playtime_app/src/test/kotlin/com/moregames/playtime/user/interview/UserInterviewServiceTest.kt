package com.moregames.playtime.user.interview

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.abtesting.*
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.messaging.DelayedMessagePublisher
import com.moregames.base.messaging.dto.CheckAndInviteToInterviewTaskDto
import com.moregames.base.util.mock
import com.moregames.playtime.app.PlaytimeFeatureFlags
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.buseffects.SendInvitedToInterviewEventEffect
import com.moregames.playtime.ios.dto.user.IosInterviewDecisionApiDto
import com.moregames.playtime.notifications.PushNotification.IosPushNotification.ShareYourExperienceNotification
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPersistenceService
import com.moregames.playtime.user.cashout.dto.CashoutStatus
import com.moregames.playtime.user.fraudscore.HighlyTrustedUsersService
import com.moregames.playtime.utils.USD
import com.moregames.playtime.utils.cashoutProvidersStub
import com.moregames.playtime.utils.iosInterviewApiDtoStub
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.Mockito.verifyNoMoreInteractions
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal
import java.time.Instant
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.time.Duration.Companion.days

class UserInterviewServiceTest {

  private val abTestingService: AbTestingService = mock()
  private val interviewPersistenceService: UserInterviewPersistenceService = mock()
  private val cashoutPersistenceService: CashoutPersistenceService = mock()
  private val highlyTrustedUsersService: HighlyTrustedUsersService = mock()
  private val delayedMessagePublisher: DelayedMessagePublisher = mock()
  private val userService: UserService = mock()
  private val featureFlagsFacade: FeatureFlagsFacade = mock()
  private val messageBus: MessageBus = mock()

  private val service = UserInterviewService(
    abTestingService = abTestingService,
    interviewPersistenceService = interviewPersistenceService,
    cashoutPersistenceService = cashoutPersistenceService,
    highlyTrustedUsersService = highlyTrustedUsersService,
    delayedMessagePublisher = delayedMessagePublisher,
    userService = userService,
    messageBus = messageBus,
    featureFlagsFacade = featureFlagsFacade,
  )

  companion object {
    const val USER_ID = "userId"
    private val task = CheckAndInviteToInterviewTaskDto(USER_ID, IOS)
    private val cashoutStatus = CashoutStatus(
      isEnabled = true,
      headerText = "headerText",
      iconFilename = "iconFilename.jpg",
      nextCashout = Instant.now().plusSeconds(10500L),
      currency = USD,
      amount = BigDecimal.TEN,
      bonusAmount = null,
      amountBefore = null,
      amountUsd = BigDecimal.TEN,
      providers = cashoutProvidersStub,
      disclaimer = "some disclaimer",
      userHasCashouts = true,
    )
  }

  @BeforeEach
  fun init() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.IOS_USERS_INTERVIEW) }, Variations.INVITED_TO_INTERVIEW)
    interviewPersistenceService.mock({ trackUserForInterview(USER_ID) }, true)
    interviewPersistenceService.mock({ isUserInvitedToInterview(USER_ID) }, false)
    cashoutPersistenceService.mock({ userHasSuccessfulCashout(USER_ID) }, false)
    highlyTrustedUsersService.mock({ isHighlyTrustedUser(USER_ID) }, true)
    userService.mock({ isUserUnique(USER_ID) }, true)
    interviewPersistenceService.mock({ updateInterviewStatus(USER_ID, UserInterviewStatus.INVITED) }, true)
    userService.mock({ userExists(USER_ID) }, true)
    featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.IOS_USER_INTERVIEW_DISABLED) }, false)
    featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.IOS_USER_SURVEY_DISABLED) }, false)
  }

  @ParameterizedTest
  @EnumSource(Variations::class, names = ["INVITED_TO_INTERVIEW", "INVITED_TO_SURVEY"])
  fun `SHOULD not publish an invitation a user to an interview WHEN feature flag is set to true`(
    interviewVariation: BaseVariation
  ) = runTest {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.IOS_USERS_INTERVIEW) }, interviewVariation)
    featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.IOS_USER_INTERVIEW_DISABLED) }, true)
    featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.IOS_USER_SURVEY_DISABLED) }, true)
    //when
    service.onCashoutStatusGetCall(USER_ID, IOS, cashoutStatus)
    //then
    verifyBlocking(interviewPersistenceService) {
      updateInterviewStatus(USER_ID, UserInterviewStatus.FF_SKIPPED)
    }
    verifyNoInteractions(delayedMessagePublisher)
  }

  @ParameterizedTest
  @EnumSource(Variations::class, names = ["INVITED_TO_INTERVIEW", "INVITED_TO_SURVEY"])
  fun `SHOULD not invite a user to an interview WHEN feature flag is set to true`(
    interviewVariation: BaseVariation
  ) = runTest {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.IOS_USERS_INTERVIEW) }, interviewVariation)
    featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.IOS_USER_INTERVIEW_DISABLED) }, true)
    featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.IOS_USER_SURVEY_DISABLED) }, true)
    //when
    service.checkAndInviteToInterview(USER_ID, IOS)
    //then
    verifyBlocking(interviewPersistenceService) {
      updateInterviewStatus(USER_ID, UserInterviewStatus.FF_SKIPPED)
    }
    verifyNoMoreInteractions(interviewPersistenceService)
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD return null ON isUserInvitedToInterview WHEN user is not invited`() {
    runBlocking {
      service.getInterviewInvitationInfo(USER_ID)
    }.let { assertNull(it) }
  }

  @Test
  fun `SHOULD return invitation model ON isUserInvitedToInterview WHEN user is invited AND on invitedToInterview variation`() {
    interviewPersistenceService.mock({ isUserInvitedToInterview(USER_ID) }, true)

    runBlocking {
      service.getInterviewInvitationInfo(USER_ID)
    }.let {
      assertEquals(iosInterviewApiDtoStub, it)
    }
  }

  @Test
  fun `SHOULD return invitation model ON isUserInvitedToInterview WHEN user is invited AND on invitedToSurvey variation`() {
    interviewPersistenceService.mock({ isUserInvitedToInterview(USER_ID) }, true)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.IOS_USERS_INTERVIEW) }, Variations.INVITED_TO_SURVEY)

    runBlocking {
      service.getInterviewInvitationInfo(USER_ID)
    }.let {
      assertEquals(
        expected = iosInterviewApiDtoStub.copy(
          url = "https://forms.gle/eojg7QmWcmJC6d9v9",
          popup = iosInterviewApiDtoStub.popup?.copy(
            description = """As one of JustPlay's top gamers, click "yes" to fill out a questionnaire and help us improve!""",
            confirmButtonText = null,
            cancelButtonText = null
          )
        ),
        actual = it
      )
    }
  }

  @Test
  fun `SHOULD push a delayed task on onCashoutStatusGetCall WHEN all conditions match`() {
    runBlocking {
      service.onCashoutStatusGetCall(USER_ID, IOS, cashoutStatus)
    }

    verifyBlocking(delayedMessagePublisher) { publish(task, 2.days) }
  }

  @Test
  fun `SHOULD not push a delayed task on onCashoutStatusGetCall WHEN cashout is not available`() {
    runBlocking {
      service.onCashoutStatusGetCall(USER_ID, IOS, cashoutStatus.copy(isEnabled = false))
    }

    verifyNoInteractions(delayedMessagePublisher)
  }

  @Test
  fun `SHOULD not push a delayed task on onCashoutStatusGetCall WHEN it is android`() {
    runBlocking {
      service.onCashoutStatusGetCall(USER_ID, ANDROID, cashoutStatus)
    }

    verifyNoInteractions(delayedMessagePublisher)
  }

  @Test
  fun `SHOULD not push a delayed task on onCashoutStatusGetCall WHEN amount is below the threshold`() {
    runBlocking {
      service.onCashoutStatusGetCall(USER_ID, IOS, cashoutStatus.copy(amountUsd = BigDecimal("0.5")))
    }

    verifyNoInteractions(delayedMessagePublisher)
  }

  @Test
  fun `SHOULD not push a delayed task on onCashoutStatusGetCall WHEN the user is not a participant`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.IOS_USERS_INTERVIEW) }, DEFAULT)
    runBlocking {
      service.onCashoutStatusGetCall(USER_ID, IOS, cashoutStatus)
    }

    verifyNoInteractions(delayedMessagePublisher)
  }

  @Test
  fun `SHOULD not push a delayed task on onCashoutStatusGetCall WHEN user was already tracked before`() {
    interviewPersistenceService.mock({ trackUserForInterview(USER_ID) }, false)

    runBlocking {
      service.onCashoutStatusGetCall(USER_ID, IOS, cashoutStatus)
    }

    verifyNoInteractions(delayedMessagePublisher)
  }

  @Test
  fun `SHOULD not push a delayed task on onCashoutStatusGetCall WHEN user has a successful cashout already`() {
    cashoutPersistenceService.mock({ userHasSuccessfulCashout(USER_ID) }, true)

    runBlocking {
      service.onCashoutStatusGetCall(USER_ID, IOS, cashoutStatus)
    }

    verifyNoInteractions(delayedMessagePublisher)
  }

  @Test
  fun `SHOULD not push a delayed task on onCashoutStatusGetCall WHEN user is not HT`() {
    highlyTrustedUsersService.mock({ isHighlyTrustedUser(USER_ID) }, false)

    runBlocking {
      service.onCashoutStatusGetCall(USER_ID, IOS, cashoutStatus)
    }

    verifyNoInteractions(delayedMessagePublisher)
  }

  @Test
  fun `SHOULD not push a delayed task on onCashoutStatusGetCall WHEN user is not unique`() {
    userService.mock({ isUserUnique(USER_ID) }, false)

    runBlocking {
      service.onCashoutStatusGetCall(USER_ID, IOS, cashoutStatus)
    }

    verifyNoInteractions(delayedMessagePublisher)
  }


  @Test
  fun `SHOULD set interview status invited and push some notifications ON checkAndInviteToInterview WHEN all checks have passed`() {
    runBlocking {
      service.checkAndInviteToInterview(USER_ID, IOS)
    }

    verifyBlocking(interviewPersistenceService) { updateInterviewStatus(USER_ID, UserInterviewStatus.INVITED) }
    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(ShareYourExperienceNotification(USER_ID))) }
    verifyBlocking(messageBus) { publishAsync(SendInvitedToInterviewEventEffect(USER_ID, IOS)) }
  }

  @Test
  fun `SHOULD not set interview status invited and not push any notifications ON checkAndInviteToInterview WHEN it is not IOS`() {
    runBlocking {
      service.checkAndInviteToInterview(USER_ID, ANDROID)
    }

    verifyNoInteractions(interviewPersistenceService, messageBus)
  }

  @Test
  fun `SHOULD not set interview status invited and not push any notifications ON checkAndInviteToInterview WHEN the experiment was stopped`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.IOS_USERS_INTERVIEW) }, DEFAULT)
    runBlocking {
      service.checkAndInviteToInterview(USER_ID, IOS)
    }

    verifyNoInteractions(interviewPersistenceService, messageBus)
  }

  @Test
  fun `SHOULD not set interview status invited and not push any notifications ON checkAndInviteToInterview WHEN the user was deleted`() {
    userService.mock({ userExists(USER_ID) }, false)

    runBlocking {
      service.checkAndInviteToInterview(USER_ID, IOS)
    }

    verifyNoInteractions(interviewPersistenceService, messageBus)
  }

  @Test
  fun `SHOULD not set interview status invited and not push any notifications ON checkAndInviteToInterview WHEN user made a successful cashout`() {
    cashoutPersistenceService.mock({ userHasSuccessfulCashout(USER_ID) }, true)

    runBlocking {
      service.checkAndInviteToInterview(USER_ID, IOS)
    }

    verifyNoInteractions(interviewPersistenceService, messageBus)
  }

  @Test
  fun `SHOULD not set interview status invited and not push any notifications ON checkAndInviteToInterview WHEN user is not HT anymore`() {
    highlyTrustedUsersService.mock({ isHighlyTrustedUser(USER_ID) }, false)

    runBlocking {
      service.checkAndInviteToInterview(USER_ID, IOS)
    }

    verifyNoInteractions(interviewPersistenceService, messageBus)
  }

  @Test
  fun `SHOULD try to set interview status invited and not push any notifications ON checkAndInviteToInterview WHEN it is the second delayed task run case`() {
    interviewPersistenceService.mock({ updateInterviewStatus(USER_ID, UserInterviewStatus.INVITED) }, false)

    runBlocking {
      service.checkAndInviteToInterview(USER_ID, IOS)
    }

    verifyBlocking(interviewPersistenceService) { updateInterviewStatus(USER_ID, UserInterviewStatus.INVITED) }
    verifyNoInteractions(messageBus)
  }

  @ParameterizedTest
  @EnumSource(IosInterviewDecisionApiDto.Decision::class)
  fun `SHOULD save interview decision ON saveInterviewDecision`(decision: IosInterviewDecisionApiDto.Decision) {
    runBlocking {
      service.saveInterviewDecision(USER_ID, decision)
    }

    val captor = argumentCaptor<UserInterviewStatus>()
    verifyBlocking(interviewPersistenceService) { updateInterviewStatus(eq(USER_ID), captor.capture()) }
    assertThat(captor.firstValue).transform { it.name }.isEqualTo(decision.name)
  }
}