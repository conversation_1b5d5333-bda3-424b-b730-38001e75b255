package com.moregames.playtime.boost

import assertk.assertThat
import assertk.assertions.isEmpty
import assertk.assertions.isEqualTo
import com.moregames.playtime.boost.model.BoostedMode
import com.moregames.playtime.translations.TranslationService
import com.moregames.playtime.util.DEFAULT_USER_LOCALE
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock

class BoostedModeTranslationServiceTest {

  private val translationService: TranslationService = mock {
    onBlocking { tryTranslate("translationKey", DEFAULT_USER_LOCALE) } doReturn "translated"
    onBlocking { tryTranslate("earnPlayingGamesKey", DEFAULT_USER_LOCALE) } doReturn "earn playing games translated"
  }
  private val underTest = BoostedModeTranslationService(translationService)

  @Test
  fun `SHOULD return balance_title translation WHEN topLeftCoinsReplacementTranslation is provided`() {
    val uiConfig = mock<BoostedMode.UiConfig> {
      on { topLeftCoinsReplacementTranslation } doReturn "translationKey"
      on { earnPlayingGamesReplacementTranslation } doReturn null
    }
    val boostedMode = mock<BoostedMode> {
      on { this.uiConfig } doReturn uiConfig
    }
    val actual = runBlocking { underTest.getAppTranslations(DEFAULT_USER_LOCALE, boostedMode) }

    assertThat(actual).isEqualTo(mapOf("boosted_mode_coins_header" to "translated"))
  }

  @Test
  fun `SHOULD return earn_playing_games translation WHEN earnPlayingGamesReplacementTranslation is provided`() {
    val uiConfig = mock<BoostedMode.UiConfig> {
      on { topLeftCoinsReplacementTranslation } doReturn null
      on { earnPlayingGamesReplacementTranslation } doReturn "earnPlayingGamesKey"
    }
    val boostedMode = mock<BoostedMode> {
      on { this.uiConfig } doReturn uiConfig
    }
    val actual = runBlocking { underTest.getAppTranslations(DEFAULT_USER_LOCALE, boostedMode) }

    assertThat(actual).isEqualTo(mapOf("earn_playing_games" to "earn playing games translated"))
  }

  @Test
  fun `SHOULD return both translations WHEN both translation keys are provided`() {
    val uiConfig = mock<BoostedMode.UiConfig> {
      on { topLeftCoinsReplacementTranslation } doReturn "translationKey"
      on { earnPlayingGamesReplacementTranslation } doReturn "earnPlayingGamesKey"
    }
    val boostedMode = mock<BoostedMode> {
      on { this.uiConfig } doReturn uiConfig
    }
    val actual = runBlocking { underTest.getAppTranslations(DEFAULT_USER_LOCALE, boostedMode) }

    assertThat(actual).isEqualTo(
      mapOf(
        "boosted_mode_coins_header" to "translated",
        "earn_playing_games" to "earn playing games translated"
      )
    )
  }

  @Test
  fun `SHOULD return empty map WHEN no translation keys are provided`() {
    val uiConfig = mock<BoostedMode.UiConfig> {
      on { topLeftCoinsReplacementTranslation } doReturn null
      on { earnPlayingGamesReplacementTranslation } doReturn null
    }
    val boostedMode = mock<BoostedMode> {
      on { this.uiConfig } doReturn uiConfig
    }
    val actual = runBlocking { underTest.getAppTranslations(DEFAULT_USER_LOCALE, boostedMode) }

    assertThat(actual).isEmpty()
  }

  @Test
  fun `SHOULD skip translation WHEN translation service returns null for balance_title`() {
    val translationServiceWithNull: TranslationService = mock {
      onBlocking { tryTranslate("translationKey", DEFAULT_USER_LOCALE) }.thenReturn(null)
      onBlocking { tryTranslate("earnPlayingGamesKey", DEFAULT_USER_LOCALE) } doReturn "earn playing games translated"
    }
    val underTestWithNull = BoostedModeTranslationService(translationServiceWithNull)

    val uiConfig = mock<BoostedMode.UiConfig> {
      on { topLeftCoinsReplacementTranslation } doReturn "translationKey"
      on { earnPlayingGamesReplacementTranslation } doReturn "earnPlayingGamesKey"
    }
    val boostedMode = mock<BoostedMode> {
      on { this.uiConfig } doReturn uiConfig
    }
    val actual = runBlocking { underTestWithNull.getAppTranslations(DEFAULT_USER_LOCALE, boostedMode) }

    assertThat(actual).isEqualTo(mapOf("earn_playing_games" to "earn playing games translated"))
  }

  @Test
  fun `SHOULD skip translation WHEN translation service returns null for earn_playing_games`() {
    val translationServiceWithNull: TranslationService = mock {
      onBlocking { tryTranslate("translationKey", DEFAULT_USER_LOCALE) } doReturn "translated"
      onBlocking { tryTranslate("earnPlayingGamesKey", DEFAULT_USER_LOCALE) }.thenReturn(null)
    }
    val underTestWithNull = BoostedModeTranslationService(translationServiceWithNull)

    val uiConfig = mock<BoostedMode.UiConfig> {
      on { topLeftCoinsReplacementTranslation } doReturn "translationKey"
      on { earnPlayingGamesReplacementTranslation } doReturn "earnPlayingGamesKey"
    }
    val boostedMode = mock<BoostedMode> {
      on { this.uiConfig } doReturn uiConfig
    }
    val actual = runBlocking { underTestWithNull.getAppTranslations(DEFAULT_USER_LOCALE, boostedMode) }

    assertThat(actual).isEqualTo(mapOf("boosted_mode_coins_header" to "translated"))
  }

  @Test
  fun `SHOULD return empty map WHEN translation service returns null for both translations`() {
    val translationServiceWithNull: TranslationService = mock {
      onBlocking { tryTranslate("translationKey", DEFAULT_USER_LOCALE) }.thenReturn(null)
      onBlocking { tryTranslate("earnPlayingGamesKey", DEFAULT_USER_LOCALE) }.thenReturn(null)
    }
    val underTestWithNull = BoostedModeTranslationService(translationServiceWithNull)

    val uiConfig = mock<BoostedMode.UiConfig> {
      on { topLeftCoinsReplacementTranslation } doReturn "translationKey"
      on { earnPlayingGamesReplacementTranslation } doReturn "earnPlayingGamesKey"
    }
    val boostedMode = mock<BoostedMode> {
      on { this.uiConfig } doReturn uiConfig
    }
    val actual = runBlocking { underTestWithNull.getAppTranslations(DEFAULT_USER_LOCALE, boostedMode) }

    assertThat(actual).isEmpty()
  }
}