package com.moregames.playtime.subscribers

import com.moregames.base.messaging.dto.WebAppUserRegistrationEventDto
import com.moregames.playtime.web.WebUserService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking

class WebAppUserRegistrationPushSubscriberTest {

  private val userService: WebUserService = mock()
  private val service = WebAppUserRegistrationPushSubscriber(userService = userService)

  companion object {
    val testMessage = WebAppUserRegistrationEventDto(
      projectName = "projectName",
      userId = "userId",
      idfv = "idfv",
      deviceToken = "deviceToken",
      notificationEnabled = false
    )
  }

  @Test
  fun `SHOULD trigger user service ON incoming message`() {
    runBlocking {
      service.handle(testMessage)
    }
    verifyBlocking(userService) { registerUser(testMessage) }
  }
}