package com.moregames.playtime.subscribers

import com.justplayapps.playtime.games.progress.proto.GameProgress
import com.justplayapps.playtime.games.progress.proto.calculateTargetEarningsCoefficientCommand
import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalance
import com.justplayapps.service.rewarding.earnings.dto.Earnings
import com.justplayapps.service.rewarding.earnings.dto.EarningsCalculationResult
import com.justplayapps.service.rewarding.earnings.dto.toProto
import com.justplayapps.service.rewarding.earnings.proto.revenueToEarningsConvertedEvent
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.messaging.dto.XHoursPassedSinceNoEarningsCheckDto
import com.moregames.base.messaging.dto.XHoursPassedSinceUnclaimedEarningsCheckDto
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.base.util.toProto
import com.moregames.playtime.buseffects.CashoutPeriodEndedEffect
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.earnings.dto.UsedQuota
import com.moregames.playtime.earnings.dto.UserEarningsQuotasDto
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.MissedEarningsNotification
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPeriodCounters
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import com.moregames.playtime.user.cashout.CashoutStatusService
import com.moregames.playtime.util.plus
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.time.Duration.Companion.minutes

@OptIn(ExperimentalCoroutinesApi::class)
class RevenueToEarningsConvertedEventSubscriberTest {
  private val cashoutPeriodsService: CashoutPeriodsService = mock()
  private val cashoutStatusService: CashoutStatusService = mock()
  private val userService: UserService = mock()
  private val timeService: TimeService = mock()
  private val abTestingService: AbTestingService = mock()
  private val messageBus: MessageBus = mock()

  private val subscriber = RevenueToEarningsConvertedEventSubscriber(
    cashoutPeriodsService = cashoutPeriodsService,
    cashoutStatusService = cashoutStatusService,
    abTestingService = abTestingService,
    userService = userService,
    timeService = timeService,
    messageBus = messageBus,
  )

  private companion object {
    val now: Instant = Instant.now()
    const val META_ID: Int = 123

    val em2EarningsComputationData = EarningsCalculationResult.Em2(
      simpleCalculationResult = EarningsCalculationResult.Simple(
        metaId = META_ID,
        amount = BigDecimal.ONE,
        amountNoRounding = BigDecimal.TEN
      ),
      noEarnings = false,
      realRevenue = BigDecimal("3.0"),
      realGameRevenue = BigDecimal("4.0"),
      em2CoinsBalance = UserCurrentCoinsBalance(
        gameCoins = BigDecimal("600.0"),
        offerCoins = BigDecimal("700.0"),
        bonusCoins = BigDecimal("800.0")
      ),
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal("5.77"),
      em2GameRevenue = BigDecimal("5.0"),
      earnings =
        Earnings(
          userId = "userId",
          earningsSum = BigDecimal("1.0"),
          quotasDto = UserEarningsQuotasDto(
            userId = "userId",
            quotas = listOf("quotas"),
            periodEnd = now,
            values = listOf(BigDecimal("0.5"))
          ),
          usedQuota = UsedQuota(BigDecimal("0.5"))
        ),
    )

    val event = revenueToEarningsConvertedEvent {
      userId = "userId"
      result = em2EarningsComputationData.toProto()
    }
  }

  init {
    Dispatchers.setMain(StandardTestDispatcher())
  }

  @BeforeEach
  fun before() {
    userService.mock({ userExists(any()) }, true)
    userService.mock({ shouldNotifyOnEmptyCPNotification(event.userId) }, false)
    timeService.mock({ now() }, now)
    abTestingService.mock({ isEm2Participant(event.userId) }, false)
    abTestingService.mock({ isTargetEarningsParticipant(event.userId) }, false)
    userService.mock({ getUser(eq(event.userId), any()) }, userDtoStub)
  }

  @Test
  fun `SHOULD convertRevenueToEarnings + enable cashout + create next cashout period ON handle`() {
    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(cashoutStatusService) { enableCashout(event.userId) }
    verifyBlocking(cashoutPeriodsService) { createNextCashoutPeriod(event.userId, true) }
  }

  @Test
  fun `SHOULD NOT enable cashout AND NOT create next cashout period ON handle WHEN earnings are zero AND previous period was empty`() {
    val event = revenueToEarningsConvertedEvent {
      userId = "userId"
      result = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO).toProto()
    }
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(2, 1, 0))

    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(cashoutStatusService, never()) { enableCashout(event.userId) }
    verifyBlocking(cashoutPeriodsService, never()) { createNextCashoutPeriod(eq(event.userId), any()) }
  }

  @Test
  fun `SHOULD stop creating cashout periods for user WHEN has no earnings 2 CPs in a row`() {
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(3, 1, 0))

    val event = revenueToEarningsConvertedEvent {
      userId = "userId"
      result = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO).toProto()
    }
    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(cashoutStatusService, never()) { enableCashout(event.userId) }
    verifyBlocking(cashoutPeriodsService) { getCashoutPeriodCounters(event.userId) }
    verifyNoMoreInteractions(cashoutPeriodsService)
  }

  @ParameterizedTest
  @ValueSource(ints = [0, 1])
  fun `SHOULD publish a delayed task for notifying WHEN user has savings and earned nothing and he is a notify unclaimed participant`(counter: Int) {
    val earnings = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO)
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(50, counter, 0))

    val event = revenueToEarningsConvertedEvent {
      userId = "userId"
      result = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO).toProto()
    }
    runTest {
      subscriber.handle(event)
    }

    if (counter == 0) {
      verifyBlocking(messageBus) {
        publish(XHoursPassedSinceUnclaimedEarningsCheckDto(event.userId, now, 1), now.plus(1, ChronoUnit.MINUTES))
      }
      verifyBlocking(cashoutPeriodsService) { createNextCashoutPeriod(event.userId, false) }
    } else {
      verifyBlocking(messageBus) {
        publish(CashoutPeriodEndedEffect(event.userId, earnings))
      }
      verifyNoMoreInteractions(messageBus)
      verifyBlocking(cashoutPeriodsService, never()) { createNextCashoutPeriod(event.userId, false) }
    }
  }

  @ParameterizedTest
  @ValueSource(ints = [0, 1])
  fun `SHOULD publish a delayed notification task WHEN user earned nothing and has no savings`(counter: Int) {
    val earnings = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO)
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(1, counter, 0))

    val event = revenueToEarningsConvertedEvent {
      userId = "userId"
      result = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO).toProto()
    }
    runTest {
      subscriber.handle(event)
    }

    if (counter == 0) {
      verifyBlocking(messageBus) {
        publish(XHoursPassedSinceNoEarningsCheckDto(event.userId, now, 1), now.plus(1, ChronoUnit.MINUTES))
      }
      verifyBlocking(cashoutPeriodsService) { createNextCashoutPeriod(event.userId, false) }
    } else {
      verifyBlocking(messageBus) {
        publish(CashoutPeriodEndedEffect(event.userId, earnings))
      }
      verifyNoMoreInteractions(messageBus)
      verifyBlocking(cashoutPeriodsService, never()) { createNextCashoutPeriod(event.userId, false) }
    }
  }

  @Test
  fun `SHOULD not publish a delayed notification task WHEN user is em2 participant`() {
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(1, 0, 0))

    runTest { subscriber.handle(event) }

    verifyBlocking(messageBus, never()) {
      publish(XHoursPassedSinceNoEarningsCheckDto(event.userId, now, 1), now.plus(1, ChronoUnit.MINUTES))
    }
    verifyBlocking(cashoutPeriodsService) { createNextCashoutPeriod(event.userId, true) }
  }

  @ParameterizedTest
  @CsvSource("1,0", "2,1")
  fun `SHOULD publish oh no you are missing your earnings notification ON first x empty cashout periods`(
    periodCounter: Int, noEarningsCounter: Int
  ) {
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(periodCounter, noEarningsCounter, 0))

    val event = revenueToEarningsConvertedEvent {
      userId = "userId"
      result = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO).toProto()
    }
    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(MissedEarningsNotification(event.userId))) }
  }


  @ParameterizedTest
  @CsvSource("1,0", "2,0", "2,1", "3,1")
  fun `SHOULD NOT publish oh no you are missing your earnings notification ON first x empty cashout periods WHEN pushes not allowed`(
    periodCounter: Int, noEarningsCounter: Int
  ) {
    val earnings = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO)
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(periodCounter, noEarningsCounter, 0))
    userService.mock({ getUser(event.userId, includingDeleted = true) }, userDtoStub.copy(appPlatform = IOS))

    val event = revenueToEarningsConvertedEvent {
      userId = "userId"
      result = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO).toProto()
    }
    runTest { subscriber.handle(event) }

    verifyBlocking(messageBus, atMost(1)) {
      publish(
        XHoursPassedSinceUnclaimedEarningsCheckDto(event.userId, now, 1),
        now + 1.minutes
      )
    }
    verifyBlocking(messageBus, atMost(1)) {
      publish(
        XHoursPassedSinceNoEarningsCheckDto(event.userId, now, 1),
        now + 1.minutes
      )
    }
    verifyBlocking(messageBus) {
      publish(CashoutPeriodEndedEffect(event.userId, earnings))
    }
    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD publish oh no you are missing your earnings translated notification ON first x empty cashout periods`() {
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(2, 1, 0))

    val event = revenueToEarningsConvertedEvent {
      userId = "userId"
      result = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO).toProto()
    }
    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(MissedEarningsNotification(event.userId))) }
  }

  @ParameterizedTest
  @CsvSource("2,0", "3,0", "3,1", "3,2")
  fun `SHOULD NOT publish oh no you are missing your earnings notification ON non-first x empty cashout periods`(
    periodCounter: Int, noEarningsCounter: Int
  ) {
    val earnings = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO)
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(periodCounter, noEarningsCounter, 0))

    val event = revenueToEarningsConvertedEvent {
      userId = "userId"
      result = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO).toProto()
    }
    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(messageBus, atMost(1)) {
      publish(
        XHoursPassedSinceUnclaimedEarningsCheckDto(event.userId, now, 1),
        now + 1.minutes
      )
    }
    verifyBlocking(messageBus, atMost(1)) {
      publish(
        XHoursPassedSinceNoEarningsCheckDto(event.userId, now, 1),
        now + 1.minutes
      )
    }
    verifyBlocking(messageBus) {
      publish(CashoutPeriodEndedEffect(event.userId, earnings))
    }
    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD create push notification on empty cashout period end if there are some attempts left`() {
    userService.mock({ shouldNotifyOnEmptyCPNotification(event.userId) }, true)
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(150, 1, 0))

    val event = revenueToEarningsConvertedEvent {
      userId = "userId"
      result = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO).toProto()
    }
    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(MissedEarningsNotification(event.userId))) }
  }

  @Test
  fun `SHOULD publish a translated push notification on empty cashout period end if there are some attempts left`() {
    userService.mock({ shouldNotifyOnEmptyCPNotification(event.userId) }, true)
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(150, 1, 0))

    val event = revenueToEarningsConvertedEvent {
      userId = "userId"
      result = EarningsCalculationResult.Simple(META_ID, BigDecimal.ZERO, BigDecimal.ZERO).toProto()
    }
    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(MissedEarningsNotification(event.userId))) }
  }

  @Test
  fun `SHOULD emit CalculateTargetEarningsCoefficient ON handle WHEN target earnings participant`() {
    val expectedMessageProto = calculateTargetEarningsCoefficientCommand {
      userId = event.userId
      gameCoins = BigDecimal("600.0").toProto()
      applovinRevenue = BigDecimal("4.0").toProto()
      coinsForOneDollar = BigDecimal("100.0").toProto()
      cutFactor = BigDecimal("0.5").toProto()
    }

    abTestingService.mock({ isTargetEarningsParticipant(event.userId) }, true)
    userService.mock({ shouldNotifyOnEmptyCPNotification(event.userId) }, true)
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(150, 1, 0))

    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(messageBus) { publish(expectedMessageProto) }
  }

  @Test
  fun `SHOULD NOT emit CalculateTargetEarningsCoefficient ON handle WHEN NOT target earnings participant`() {
    userService.mock({ shouldNotifyOnEmptyCPNotification(event.userId) }, true)
    cashoutPeriodsService.mock({ getCashoutPeriodCounters(event.userId) }, CashoutPeriodCounters(150, 1, 0))

    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(messageBus, never()) {
      publish(
        argThat<com.google.protobuf.Message> { this is GameProgress.CalculateTargetEarningsCoefficientCommand }
      )
    }
  }

  @Test
  fun `SHOULD NOT emit CalculateTargetEarningsCoefficient ON handle WHEN NOT em2 calculation result`() {
    userService.mock({ userExists(any()) }, false)

    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(messageBus, never()) {
      publish(
        argThat<com.google.protobuf.Message> { this is GameProgress.CalculateTargetEarningsCoefficientCommand }
      )
    }
  }
}