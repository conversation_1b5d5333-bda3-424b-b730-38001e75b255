package com.moregames.playtime.revenue

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.justplayapps.service.rewarding.revenue.RevenueAggregationService
import com.justplayapps.service.rewarding.revenue.RevenuePersistenceService
import com.moregames.base.util.mock
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking

class RevenueAggregationServiceTest {
  private val revenuePersistenceService: RevenuePersistenceService = mock()

  private val service: RevenueAggregationService = RevenueAggregationService(revenuePersistenceService)

  @Test
  fun `SHOULD trigger persistence method ON removeDailyRevenueOldTotalsBatch`() {
    revenuePersistenceService.mock({ removeDailyRevenueOldTotalsBatch(529) }, 517)

    runBlocking {
      service.removeDailyRevenueOldTotalsBatch(529)
    }.let { assertThat(it).isEqualTo(517) }

    verifyBlocking(revenuePersistenceService) { removeDailyRevenueOldTotalsBatch(529) }
  }
}