package com.moregames.playtime.utils

import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService
import com.moregames.base.app.OfferWallType.TAPJOY
import com.moregames.base.app.PaymentProviderType.AMAZON
import com.moregames.base.app.PaymentProviderType.PAYPAL
import com.moregames.base.app.UserIdentifierType.EMAIL
import com.moregames.base.detailedPaymentStub
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.dto.TrackingDataType.IDFA
import com.moregames.base.ipregistry.IpRegistryResponseDto
import com.moregames.base.messaging.customnotification.BackgroundAction.REFRESH_OFFERS
import com.moregames.base.messaging.customnotification.BackgroundAction.REFRESH_USER
import com.moregames.base.messaging.customnotification.ButtonAction
import com.moregames.base.messaging.customnotification.ButtonApiDto
import com.moregames.base.messaging.customnotification.CustomNotificationDto
import com.moregames.base.messaging.customnotification.CustomNotificationType.CUSTOM_NOTIFICATION
import com.moregames.base.messaging.customnotification.OnClickActionApiDto
import com.moregames.base.messaging.dto.EmailNotificationEventDto
import com.moregames.base.offers.dto.OfferAction
import com.moregames.base.offers.dto.OffersConfig
import com.moregames.base.table.UserCashoutTransactionsTable
import com.moregames.base.user.dto.*
import com.moregames.base.util.ClientVersionsSupport.getUserCreationMinAppVersion
import com.moregames.base.util.getUtcDateTimeFormatter
import com.moregames.base.util.parseToInstant
import com.moregames.base.util.trimToOneLineString
import com.moregames.playtime.boost.model.BoostedMode
import com.moregames.playtime.cashstreak.api.CashStreakRewardApiDto
import com.moregames.playtime.cashstreak.api.CashStreakRewardTypeApiDto
import com.moregames.playtime.cashstreak.api.CashStreakStatusApiDto
import com.moregames.playtime.cashstreak.api.CashStreakWidgetApiDto
import com.moregames.playtime.cashstreak.model.CashStreakReward
import com.moregames.playtime.cashstreak.model.CashStreakRewardType
import com.moregames.playtime.cashstreak.model.CashStreakShortStatus
import com.moregames.playtime.cashstreak.model.CashStreakStatus
import com.moregames.playtime.checks.dto.AttestationStatementDto
import com.moregames.playtime.checks.dto.IpQualityScoreResponseDto
import com.moregames.playtime.checks.ipregistry.IpRegistryInfo
import com.moregames.playtime.games.IosGameOffer
import com.moregames.playtime.games.fallback.dto.DefaultTextDto
import com.moregames.playtime.games.fallback.dto.android.AndroidFallbackGameApiDto
import com.moregames.playtime.general.GamesSetupService.AdUnitIds
import com.moregames.playtime.general.GamesSetupService.AdUnitType.*
import com.moregames.playtime.ios.dto.user.IosInterviewApiDto
import com.moregames.playtime.ios.dto.user.IosInterviewPopupApiDto
import com.moregames.playtime.ios.dto.user.PreGameScreenApiDto
import com.moregames.playtime.ios.examination.dto.IosAttestationStatementDto
import com.moregames.playtime.ios.news.NewsApiDto
import com.moregames.playtime.ios.news.NewsListApiDto
import com.moregames.playtime.ios.news.NewsRelatedGameApiDto
import com.moregames.playtime.ios.pregamescreen.IosPreGameScreen
import com.moregames.playtime.revenue.adjoe.AdjoeCoinsReportDto
import com.moregames.playtime.translations.TranslationsApiDto
import com.moregames.playtime.user.LimitedTrackingInfo
import com.moregames.playtime.user.NotificationsPersistenceService.PromotionsEmailNotificationDto
import com.moregames.playtime.user.UserDto
import com.moregames.playtime.user.UserPersistenceService.GamePlayStatusDto
import com.moregames.playtime.user.cashout.dto.*
import com.moregames.playtime.user.dto.AdUnitIdsApiDto
import com.moregames.playtime.user.dto.User
import com.moregames.playtime.user.gamescelebration.GamesCelebrationConfigApiDto
import com.moregames.playtime.user.gamescoinsbooster.GameCoinsBoosterConfigApiDto
import com.moregames.playtime.user.gamestories.AndroidGameStoriesApiDto
import com.moregames.playtime.user.gamestories.AndroidGameStoryApiDto
import com.moregames.playtime.user.highlightedgames.AndroidHighlightedGameApiDto
import com.moregames.playtime.user.highlightedgames.AndroidHighlightedGamesApiDto
import com.moregames.playtime.user.iosgameattconsent.IosGameAttConsentConfigApiDto
import com.moregames.playtime.user.iosgameattconsent.IosGameAttConsentConfiguration.CONSENT_VARIANT_1
import com.moregames.playtime.user.offer.*
import com.moregames.playtime.user.offer.lock.AndroidLockedGamesInfo
import com.moregames.playtime.user.offer.lock.AndroidLockedGamesService
import com.moregames.playtime.user.offer.lock.AndroidLockedGamesService.GamesAvailableAndLocked
import com.moregames.playtime.user.offer.lock.AndroidUnlockedGameInfo
import com.moregames.playtime.user.survey.SurveyService
import com.moregames.playtime.user.survey.dto.*
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.util.plus
import com.moregames.playtime.webhook.adjust.dto.AdjustInstallation
import io.ktor.util.*
import org.apache.commons.lang3.RandomStringUtils
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.time.temporal.ChronoUnit.MINUTES
import java.util.*
import kotlin.time.Duration.Companion.hours

val paymentApiDtoStub = PaymentApiDto(
  cashoutTransactionId = detailedPaymentStub.cashoutTransactionId,
  createdAt = detailedPaymentStub.createdAt,
  userId = detailedPaymentStub.userId,
  provider = detailedPaymentStub.provider,
  providerIconUrl = "",
  amount = detailedPaymentStub.amountString,
  recipientName = detailedPaymentStub.encryptedRecipientName,
  recipientEmail = detailedPaymentStub.encryptedRecipientEmail,
  status = detailedPaymentStub.status,
  statusText = "",
  paymentExternalTransactionId = detailedPaymentStub.paymentExternalTransactionId,
  claim = detailedPaymentStub.claim,
  claimLabel = detailedPaymentStub.claimLabel,
  claimCaption = "",
  redeemInstructions = detailedPaymentStub.redeemInstructions
)

val appTranslationsStub = mapOf(
  "en" to mapOf("support" to "en_support", "having_issues" to "en_having_issues", "claim_code" to "en_claim_code"),
  "fr" to mapOf("support" to "fr_support", "having_issues" to "fr_having_issues", "claim_code" to "fr_claim_code"),
  "fr-ca" to mapOf("support" to "fr-CA_support", "having_issues" to "fr-CA_having_issues", "claim_code" to "fr-CA_claim_code"),
  "zh-hk" to mapOf("support" to "zh-HK_support", "having_issues" to "zh-HK_having_issues", "claim_code" to "zh-HK_claim_code"),
  "zh-tw" to mapOf("support" to "zh-TW_support", "having_issues" to "zh-TW_having_issues", "claim_code" to "zh-TW_claim_code")
)

val backendTranslationsStub = mapOf(
  "en" to mapOf("\$_support" to "en_support", "\$_having_issues" to "en_having_issues", "\$_claim_code" to "en_claim_code"),
  "fr" to mapOf("\$_support" to "fr_support", "\$_having_issues" to "fr_having_issues", "\$_claim_code" to "fr_claim_code"),
  "fr-ca" to mapOf("\$_support" to "fr-CA_support", "\$_having_issues" to "fr-CA_having_issues", "\$_claim_code" to "fr-CA_claim_code")
)

val noWithhold = WithholdsAndBonusesAmounts.ZERO

val adjustInstallEventsQueryParametersStub =
  valuesOf(
    "user_id" to listOf("user-id"),
    "campaign_name" to listOf("campaignName"),
    "tracker" to listOf("ABCDE12345"),
    "tracker_name" to listOf("ChristmasSpecialOffer"),
    "network_name" to listOf("amplifier"),
    "gps_adid" to listOf("8cdede78-0412-4e4f-bb68-b074a0021698"),
    "adid" to listOf("7777de7804124e4fbb68b074a0027777"),
    "ip_address" to listOf("*************"),
    "country" to listOf("US"),
    "installed_at" to listOf("1614516453"),
    "app_name" to listOf("my.very.package"),
    "os_version" to listOf("hercules-11"),
    "device_name" to listOf("green-dragon"),
    "user_agent" to listOf("Dalvik(nixos)-11.22.1963"),
    "tracking_limited" to listOf("0"),
    "is_organic" to listOf("1"),
    "adgroup_name" to listOf("someAdGroupName"),
    "creative_name" to listOf("someCreativeName"),
    "referrer" to listOf("utm_source=my.apps.com&utm_campaign"),
    "idfa" to listOf("8C6CBCOD-5F43-4765-A6E6-84DFF3D24707"),
    "idfv" to listOf("CCB300A0-DE1B-4D48-BC7E-599E453B8DD4"),
  )

val adjustInstallationStub = AdjustInstallation.fromQueryParameters("user-id", adjustInstallEventsQueryParametersStub)

val adjustUpdatedAttributionEventQueryParametersStub =
  valuesOf(
    "user_id" to listOf("user-id"),
    "campaign_name" to listOf("campaignName"),
    "tracker" to listOf("ABCDE12345"),
    "tracker_name" to listOf("ChristmasSpecialOffer"),
    "network_name" to listOf("amplifier"),
    "gps_adid" to listOf("8cdede78-0412-4e4f-bb68-b074a0021698"),
    "adid" to listOf("7777de7804124e4fbb68b074a0027777"),
    "ip_address" to listOf("*************"),
    "country" to listOf("US"),
    "installed_at" to listOf("1614516453"),
    "app_name" to listOf("my.very.package"),
    "os_version" to listOf("hercules-11"),
    "device_name" to listOf("green-dragon"),
    "user_agent" to listOf("Dalvik(nixos)-11.22.1963"),
    "tracking_limited" to listOf("0"),
    "is_organic" to listOf("1"),
    "adgroup_name" to listOf("someAdGroupName"),
    "creative_name" to listOf("someCreativeName"),
    "referrer" to listOf("utm_source=my.apps.com&utm_campaign"),
    "idfa" to listOf("8C6CBCOD-5F43-4765-A6E6-84DFF3D24707"),
    "idfv" to listOf("CCB300A0-DE1B-4D48-BC7E-599E453B8DD4"),
    "google_app_set_id" to listOf("8710a8a2-f633-4ad1-ab0b-b01db7b0d275"),
    "outdated_tracker" to listOf("outdatedTracker1"),
    "outdated_tracker_name" to listOf("outdatedTracker1Name1"),
    "attribution_updated_at" to listOf("1614516454"),
    "activity_kind" to listOf("activityKind1"),
    "created_at" to listOf("1614516455"),
  )

val adjustUpdatedAttributionStub = AdjustInstallation.fromQueryParameters("user-id", adjustUpdatedAttributionEventQueryParametersStub)

val user = User(
  userId = "userId",
  googleAdId = null,
  deviceToken = "deviceToken",
  coinsGoal = 142,
  createdAt = Instant.now().minus(1, ChronoUnit.DAYS),
//  coinGoalLabel = "coinLabel",
  appPlatform = ANDROID,
  appVersion = 0,
  countryCode = "US",
  isConsentedToAnalytics = false,
)

val userDtoStub = UserDto(
  id = "userId",
  googleAdId = "b19860d2-ccf9-472b-a0f6-c2e517bd841a",
  createdAt = getUtcDateTimeFormatter("yyyy-MM-dd HH:mm:ss").parseToInstant("2024-11-25 21:00:00"),
  countryCode = "US",
  limitedTrackingInfo = LimitedTrackingInfo(false, "US", false),
  appVersion = getUserCreationMinAppVersion(ANDROID),
  isDeleted = false,
  trackingData = TrackingData("b19860d2-ccf9-472b-a0f6-c2e517bd841a", IDFA, ANDROID),
  appPlatform = ANDROID,
  lastActiveAtDay = LocalDate.parse("20241126", DateTimeFormatter.BASIC_ISO_DATE),
  timeZone = "America/Chicago",
)

val userWithPlatform = UserDto(
  id = "userId",
  googleAdId = "b19860d2-ccf9-472b-a0f6-c2e517bd841a",
  createdAt = Instant.now(),
  countryCode = "US",
  limitedTrackingInfo = LimitedTrackingInfo(
    isLimitedTracking = false,
    countryCode = "US",
    isConsentedToAnalytics = false,
  ),
  trackingData = TrackingData(
    id = "42",
    type = IDFA,
    platform = ANDROID
  ),
  isDeleted = false,
  appPlatform = ANDROID
)

val cashoutTransactionStub = CashoutTransactionDto(
  cashoutTransactionId = "cashoutTransactionId",
  userId = "userId",
  encryptedEmail = "<EMAIL>",
  emailHash = "emailHash",
  userIp = "***********",
  provider = PAYPAL,
  amountUsd = BigDecimal("2.50"),
  userCurrency = Currency.getInstance("USD"),
  userCurrencyAmount = BigDecimal("2.50"),
  status = UserCashoutTransactionsTable.Status.SUCCESSFUL,
  encryptedUserName = "Encrypted Barbara",
  encryptedAddress = "Encrypted Bart Apart",
)

val attestationStatementStub = AttestationStatementDto(
  timestampMs = 1636897470147,
  nonce = "bm9uY2U=",
  apkPackageName = "apkPackageName",
  apkCertificateDigestSha256 = arrayOf("dGVzdA==", "dGVzdA=="),
  apkDigestSha256 = "dGVzdA==",
  ctsProfileMatch = true,
  basicIntegrity = true,
  evaluationType = "HARDWARE_BACKED",
  advice = "no advice",
  error = "no error",
  versionCode = 42,
  appLicensingVerdict = "licensed",
  appRecognitionVerdict = "recognized",
  deviceRecognitionVerdict = listOf("MEETS_BASIC_INTEGRITY", "MEETS_STRONG_INTEGRITY")
)

val iosAttestationStub = IosAttestationStatementDto(
  creationTime = Instant.parse("2023-06-29T18:22:00.33Z"),
  challenge = "bm9uY2U=",
  keyIdBase64 = "key id",
  error = null,
  iosVersion = "14.0.4",
  appId = "com.my.app",
  attestationCertificate = RandomStringUtils.randomAlphanumeric(2000),
  clientHash = "Y2xpZW50IGhhc2g=",
  token = "token",
  receiptType = "ATTEST",
  environment = "PRODUCTION",
  riskMetric = 2,
  notBefore = Instant.parse("2023-06-29T18:20:00.22Z"),
  expirationTime = Instant.parse("2023-06-29T18:24:00.44Z")
)

val ipqsResponseStub = IpQualityScoreResponseDto(
  success = true,
  message = "Success.",
  proxy = true,
  isp = "Mediacom Cable",
  organization = "Mediacom Cable",
  asn = 30036,
  countryCode = "US",
  city = "Houston",
  region = "Texas",
  connectionType = "Residential",
  recentAbuse = true,
  abuseVelocity = "medium",
  botStatus = true,
  vpn = true,
  tor = true,
  activeVpn = true,
  activeTor = true,
  fraudScore = 25
)

val providerImagesCfg = ProviderImagesCfg(
  countryCode = "US", imageFileNames = listOf("walmart.png", "google.png", "reward.png")
)

val cashoutStatusStub = CashoutStatus(
  isEnabled = true,
  headerText = "some text",
  iconFilename = "a.ico",
  nextCashout = Instant.now(),
  currency = Currency.getInstance("USD"),
  amount = BigDecimal.ONE,
  bonusAmount = null,
  amountBefore = null,
  amountUsd = BigDecimal.ONE,
  providers = emptyList(),
  disclaimer = "some disclaimer",
  userHasCashouts = true,
)

val cashoutApiDtoStub = CashoutApiDto(
  isEnabled = true,
  headerText = "some text",
  iconUrl = "http://someUrlHere/a.ico",
  nextCashoutTimestamp = Instant.now().plus(1, MINUTES),
  amountText = "$1.00",
  providers = emptyList(),
  bonusStatus = CashoutBonusStatusApiDto.BACK_COMPATIBILITY_MOCK,
  disclaimer = "some disclaimer",
  showRewards = true,
  providersImageList = listOf("http://somePathHerewalmart.png", "http://somePathHeregoogle.png", "http://somePathHerereward.png"),
  timestamp = Instant.now(),
  cashoutFormType = "HIDE_ADDRESS",
  consentedToAnalytics = true,
  oneClickType = "ONE_CLICK_ONLY",
  giftBoxInsteadOfEarnings = false,
  cashoutButtonStyle = null,
  cashoutFormStyle = null,
  cashoutOffers = null,
)

val USD: Currency = Currency.getInstance("USD")
val CAD: Currency = Currency.getInstance("CAD")

val someEarningsStub = UserEarningsPersistenceService.UserCurrencyEarnings(BigDecimal.ONE, USD, BigDecimal.ONE)
val noEarningsStub = UserEarningsPersistenceService.UserCurrencyEarnings(BigDecimal.ZERO, USD, BigDecimal.ZERO)

val adUnitIdsStub = AdUnitIds(mapOf(BANNER to "bannerAdUnitId", REWARDED to "rewardedAdUnitId", INTERSTITIAL to "interstitialAdUnitId"))

val adUnitIdsApiDtoStub = AdUnitIdsApiDto(
  banner = "bannerAdUnitId", rewarded = "rewardedAdUnitId", interstitial = "interstitialAdUnitId"
)

val mockGameApiDto = OfferItemGameApiDto(
  id = "42",
  style = GameStyle.LARGE_WIDGET,
  activityName = "offerwall",
  isEnabled = true,
  title = "name",
  subtitle = "description",
  subtext = "buttonText",
  imageUrl = "http://example.com/",
  iconUrl = "http://example.com/",
  applicationId = "offerwall",
  showInstallImage = false,
  installImageUrl = "",
  videoPreviewUrl = null,
  showVideoPreview = false,
  infoTextInstallTop = "",
  infoTextInstallBottom = "",
  backGroundColor = "",
  installationLink = null,
  lastPlayedAt = null,
)

val offerItemLargeApiDtoOfferwallStub = OfferItemLargeApiDto(
  id = "1",
  activityName = "offerwall",
  isEnabled = true,
  title = "name",
  subtitle = "description",
  subtext = "buttonText",
  imageUrl = "http://example.com/",
  iconUrl = "http://example.com/",
  applicationId = "offerwall",
  showInstallImage = false,
  installImageUrl = "",
  videoPreviewUrl = null,
  showVideoPreview = false,
  infoTextInstallTop = "",
  infoTextInstallBottom = "",
  backGroundColor = "",
  installationLink = null,
  lastPlayedAt = null,
  widgets = GameAdditionalWidgets(
    moneyPaidOut = 42,
    moneyBadgeUrl = "moneybadge.jpg"
  ),
)

var locationDto = IpRegistryResponseDto.LocationDto(
  IpRegistryResponseDto.LocationDto.CountryDto(code = "US", name = "United States"),
  region = IpRegistryResponseDto.LocationDto.RegionDto(name = "Illinois", code = "US-IL"),
  city = "Chicago"
)

var securityDto = IpRegistryResponseDto.SecurityDto(
  isAbuser = false,
  isAnonymous = false,
  isAttacker = false,
  isBogon = false,
  isCloudProvider = false,
  isProxy = false,
  isRelay = false,
  isThreat = false,
  isTor = false,
  isTorExit = false,
  isVpn = false
)

val timeZoneDto = IpRegistryResponseDto.TimeZoneDto("America/Chicago")

val ipRegistryResponse = IpRegistryResponseDto(
  ip = "*************",
  location = locationDto,
  security = securityDto,
  timeZone = timeZoneDto
)

val ipRegistryInfoStub = IpRegistryInfo(
  ip = "*******",
  isAbuser = null,
  isAttacker = null,
  isBogon = null,
  isCloudProvider = null,
  isProxy = null,
  isRelay = null,
  isTor = null,
  isTorExit = null,
  isVpn = null,
  isAnonymous = null,
  isThreat = null,
  regionCode = "IL",
  countryCode = "US",
  countryName = "USA",
  regionName = "Illinois",
  cityName = "Chicago",
  timeZone = "America/Chicago"
)

val appVersionStub = AppVersionDto(ANDROID, 99)

val createUserDataStub = CreateUserData(
  appVersion = AppVersionDto(ANDROID, getUserCreationMinAppVersion(ANDROID)),
  userRequestMetadata = UserRequestMetadata(
    ip = "userIp",
    forwardedIp = null,
    countryCode = "US",
    loadBalancerCountryData = "US",
    forwardedIpRaw = null
  ),
  userRequestDto = CreateUserRequestDto(
    networkCountry = "us",
    networkOperatorName = "verizon",
    simCountry = "ca",
    simOperatorName = "Verizon Canada",
    deviceLocale = "en",
    deviceLanguageTag = "en-US",
    installedFromStore = true,
    userPublicKey = "some user public key",
    timeZone = "Europe/Berlin",
    jailBreak = false,
    deviceSpecification = UserDeviceSpecificationApiDto(
      osVersion = "42",
      modelName = "Pixel 4 XL",
      ramSize = 6144,
      fontScale = 12.121,
      density = 10,
      densityScaleFactor = 15.151
    )
  ),
  userRequestDtoSignature = "some signature here",
  definedExperimentVariations = mapOf("exp1" to setOf("var1", "var2"), "exp2" to setOf("varA", "varB")),
)

val defaultUserCreationResponse = CreateUserResponseApiDto(
  userId = "userId",
  market = "test-market",
  deviceAttestationAtUserCreation = false,
  showPointingArrow = false,
  welcomeBonusPopupAvailable = false,
  welcomeBonusCoins = null,
  useAnimationForTimer = false,
  showTopRunningBar = false,
  useGdpr = false,
  useAmplitudeAnalytics = false,
  privacyRegulation = null,
)

val customNotificationStub = CustomNotificationDto(
  id = 7,
  notificationId = "2a6e4376-49f9-45b8-ac0b-cd9a6d7c4dab",
  notificationType = CUSTOM_NOTIFICATION.value,
  title = "some title",
  icon = "https://storage.googleapis.com/public-playtime/images/block_puzzle_offer_icon.jpg",
  backgroundColor = "#A090B0",
  size = "medium",
  image = null,
  shortDescription = "some short description",
  longDescription = "some long description",
  countdownTimerTarget = "60000",
  vibrationEnabled = true,
  soundEnabled = false,
  collapseKey = CUSTOM_NOTIFICATION.value,
  onClickAction = OnClickActionApiDto.routeToCashout(),
  backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
  textColor = "#5262FB",
  actionButtons = listOf(
    ButtonApiDto("Play", "#5262FB", "#FFFFFF", ButtonAction.scrollToOfferAndOpenPreGameScreen(200032)),
    ButtonApiDto("Discard", "#5262FB", "#FFFFFF", ButtonAction.discardNotification())
  ),
  label = "label"
)

val emailNotificationEventStub = EmailNotificationEventDto(
  userId = "userId",
  encryptedEmail = "encryptedEmail",
  encryptedName = "encryptedName",
  sendGridTemplateId = "id1"
)

val EN_LOCALE = Locale.forLanguageTag("en")!!
val EN_US_LOCALE = Locale.forLanguageTag("en-US")!!
val ES_LOCALE = Locale.forLanguageTag("es")!!
val DE_LOCALE = Locale.forLanguageTag("de")!!
val FR_LOCALE = Locale.forLanguageTag("fr")!!

val androidGameOfferStub = AndroidGameOffer(
  id = 1,
  applicationId = "applicationId",
  activityName = "activityName",
  name = "name",
  description = "description",
  iconFilename = "iconFilename",
  imageFilename = "imageFilename",
  orderKey = 1,
  applovinApiKey = "applovinApiKey",
  installImageFilename = "installImageFilename",
  videoPreviewFilename = "videoPreviewFilename",
  showVideoPreview = false,
  infoTextInstallTop = "infoTextInstallTop",
  infoTextInstallBottom = "infoTextInstallBottom",
  expImageFilename = "expImageFilename",
  backGroundColor = "",
  publisherId = 1,
  showForLat = false,
  doNotShow = false,
)

val androidGameOfferListStub = listOf(
  androidGameOfferStub.copy(id = 1, applicationId = "com.gimica.treasuremaster", orderKey = 10),
  androidGameOfferStub.copy(id = 2, applicationId = "com.gimica.solitaireverse", orderKey = 20),
  androidGameOfferStub.copy(id = 3, applicationId = "com.gimica.ballbounce", orderKey = 30),
  androidGameOfferStub.copy(id = 4, applicationId = "com.gimica.sugarmatch", orderKey = 40),
  androidGameOfferStub.copy(id = 5, applicationId = "com.gimica.madsmash", orderKey = 50),
  androidGameOfferStub.copy(id = 6, applicationId = "com.gimica.mixblox", orderKey = 60),
  androidGameOfferStub.copy(id = 7, applicationId = "com.gimica.mergeblast", orderKey = 70),
  androidGameOfferStub.copy(id = 8, applicationId = "com.gimica.bubblepop", orderKey = 80),
  androidGameOfferStub.copy(id = 9, applicationId = "com.gimica.zentiles", orderKey = 90),
  androidGameOfferStub.copy(id = 10, applicationId = "com.gimica.triviamadness", orderKey = 100),
  androidGameOfferStub.copy(id = 12, applicationId = "com.gimica.emojiclickers", orderKey = 110),
  androidGameOfferStub.copy(id = 13, applicationId = "com.relaxingbraintraining.grindmygears", orderKey = 120),
  androidGameOfferStub.copy(id = 14, applicationId = "com.gimica.carsmerge", orderKey = 130),
  androidGameOfferStub.copy(id = 15, applicationId = "com.gimica.blockholeclash", orderKey = 140),
  androidGameOfferStub.copy(id = 15, applicationId = "com.gimica.idlemergefun", orderKey = 150),
  androidGameOfferStub.copy(id = 15, applicationId = "com.gimica.blockslider", orderKey = 160),
  androidGameOfferStub.copy(id = 16, applicationId = "com.gimica.sudoku", orderKey = 170),
  androidGameOfferStub.copy(id = 11, applicationId = "com.gimica.wordseeker", orderKey = 180),
  androidGameOfferStub.copy(id = 17, applicationId = "com.gimica.puzzlepopblaster", orderKey = 190),
  androidGameOfferStub.copy(id = 18, applicationId = "com.gimica.hexapuzzlefun", orderKey = 200),
  androidGameOfferStub.copy(id = 19, applicationId = "com.gimica.brickdoku", orderKey = 210),
  androidGameOfferStub.copy(id = 20, applicationId = "com.gimica.wordkitchen", orderKey = 220),
  androidGameOfferStub.copy(id = 22, applicationId = "com.relaxingbraintraining.dunk", orderKey = 230),
  androidGameOfferStub.copy(id = 23, applicationId = "com.relaxingbraintraining.rollthatball", orderKey = 240),
  androidGameOfferStub.copy(id = 24, applicationId = "com.relaxingbraintraining.popslice", orderKey = 250),
  androidGameOfferStub.copy(id = 25, applicationId = "com.relaxingbraintraining.six", orderKey = 260),
  androidGameOfferStub.copy(id = 26, applicationId = "com.relaxingbraintraining.mousekeeper", orderKey = 270),
  androidGameOfferStub.copy(id = 27, applicationId = "com.relaxingbraintraining.snakeclash", orderKey = 280),
  androidGameOfferStub.copy(id = 28, applicationId = "com.relaxingbraintraining.ballrush", orderKey = 290),
  androidGameOfferStub.copy(id = 29, applicationId = "com.relaxingbraintraining.onelineadvanced", orderKey = 300),
  androidGameOfferStub.copy(id = 21, applicationId = "com.gimica.marblemadness", orderKey = 310),
  androidGameOfferStub.copy(id = 31, applicationId = "com.gimica.hexmatch", orderKey = 320),
  androidGameOfferStub.copy(id = 30, applicationId = "com.gimica.watersorter", orderKey = 330),
  androidGameOfferStub.copy(id = 32, applicationId = "com.relaxingbraintraining.numbermerge", orderKey = 340),
  androidGameOfferStub.copy(id = 33, applicationId = "com.pinmaster.screwpuzzle", orderKey = 350),
)

val androidGamesAvailableAndLockedStub =
  GamesAvailableAndLocked(availableGames = androidGameOfferListStub, yetLockedGames = emptyList(), recentUnlockedGame = androidGameOfferStub)

val iosGameOfferStub = IosGameOffer(
  id = 1,
  applicationId = "applicationId",
  name = "name",
  description = "description",
  iconFilename = "iconFilename",
  imageFilename = "imageFilename",
  orderKey = 1,
  infoTextInstall = "infoTextInstallTop",
  iosApplicationId = "iosApplicationIdValue",
  iosGameUrl = "iosGameUrlValue",
)

val iosNewsStub =
  NewsListApiDto(
    listOf(
      NewsApiDto(
        title = "New monsters are added to Treasure Master!",
        text = """New monsters have been added to one of our best games!
            | Welcome “The Mummy”, “The Dragon”, and “The Skeleton”!
            | To enjoy the new monsters, make sure your version of Treasure Master is up to date.""".trimToOneLineString(),
        imageUrl = "https://storage.googleapis.com/public-playtime/images/iOS_news_tm_new_monsters.png",
      ),
      NewsApiDto(
        title = "Epic Boss Battles Await: Unleash Your Archery Skills!",
        text = """Engage in adrenaline-pumping boss battles where the precise aim is crucial.
            | Test your archery skills and take down formidable foes with well-placed arrow shots.
            | Can you defeat the toughest bosses and claim legendary rewards?""".trimToOneLineString(),
        imageUrl = "https://storage.googleapis.com/public-playtime/images/iOS_news_tm_boss_battles.png",
        detailedDescription = "some detailed description",
        detailedImageUrl = "https://storage.googleapis.com/public-playtime/images/some_image_url.png",
        link = "some link",
        featureSurveyId = "survey id",
        game = NewsRelatedGameApiDto(
          appstoreId = "id6478022829",
          bundleId = "applicationId",
          installButtonText = "infoTextInstallTop",
          appScheme = "LaunchFairytaleMansion:",
        )
      ),
    )
  )

val offerItemGameApiDtoStub = OfferItemGameApiDto(
  id = "id",
  activityName = "activityName",
  isEnabled = true,
  title = "title",
  subtitle = "subtitle",
  subtext = "subtext",
  imageUrl = "imageUrl",
  iconUrl = "iconUrl",
  applicationId = "applicationId",
  showInstallImage = true,
  installImageUrl = "installImageUrl",
  videoPreviewUrl = "videoPreviewUrl",
  showVideoPreview = true,
  infoTextInstallTop = "infoTextInstallTop",
  infoTextInstallBottom = "infoTextInstallBottom",
  backGroundColor = "backGroundColor",
  installationLink = "installationLink",
  lastPlayedAt = 109,
  style = GameStyle.LARGE_WIDGET
)

val cashoutProvidersStub = listOf(
  CashoutProvider(
    displayName = "Amazon",
    url = "https://www.amazon.com",
    videoUrl = "https://www.youtube.com",
    iconFilename = "icon",
    largeIconFilename = "largeIcon",
    smallIconFilename = "smallIcon",
    text = "text",
    providerType = AMAZON,
    disclaimer = "disclaimer",
    emailHint = "Amazon account",
    minimumAmount = BigDecimal("0.01"),
    maximumAmount = BigDecimal("2000"),
    orderKey = 0,
    identifierType = EMAIL,
    identifierHint = "Amazon account"
  ),
  CashoutProvider(
    displayName = "PayPal",
    url = "https://www.paypal.com",
    videoUrl = "https://www.youtube.com",
    iconFilename = "icon2",
    largeIconFilename = "largeIcon2",
    smallIconFilename = "smallIcon2",
    text = "text2",
    providerType = PAYPAL,
    disclaimer = null,
    emailHint = "PayPal account",
    minimumAmount = null,
    maximumAmount = null,
    orderKey = 0,
    identifierType = EMAIL,
    identifierHint = "PayPal account"
  )
)

val offerItemUnlockedGameReminderApiDtoStub = OfferItemUnlockedGameReminderApiDto(
  id = "1",
  imageUrl = "imageUrl",
  text = "text",
  unlockedGameId = "unlockedGameId"
)

val offerItemLockedGamesInfoApiDto = OfferItemLockedGamesInfoApiDto(
  id = "1",
  lockedCount = 77,
  gameIcons = listOf("1.png", "2.png")
)


val offersListStub = listOf(
  OfferItemSmallApiDto(
    id = "id1",
    imageUrl = "imageUrl1",
    rightImageUrl = "rightIconUrl1",
    action = OfferAction.VIDEO_AD,
    title = "title1",
    subtext = "subtext1",
  ),
  OfferItemLargeApiDto(
    id = "id2",
    activityName = "activityName2",
    isEnabled = true,
    title = "title2",
    subtitle = "subtitle2",
    subtext = "subtext2",
    imageUrl = "imageUrl2",
    iconUrl = "iconUrl2",
    applicationId = "applicationId2",
    showInstallImage = false,
    installImageUrl = "installImageUrl2",
    videoPreviewUrl = "videoPreviewUrl2",
    showVideoPreview = false,
    infoTextInstallTop = "infoTextInstallTop2",
    infoTextInstallBottom = "infoTextInstallBottom2",
    backGroundColor = "",
    installationLink = """https://play.google.com/store/apps/details?id=applicationId2&referrer={"user_id":"userId"}""",
    lastPlayedAt = null,
  ),
  OfferItemLargeApiDto(
    id = "1",
    activityName = "offerwall",
    isEnabled = true,
    title = "name",
    subtitle = "description",
    subtext = "buttonText",
    imageUrl = "http://example.com/",
    iconUrl = "http://example.com/",
    applicationId = "offerwall",
    showInstallImage = false,
    installImageUrl = "",
    videoPreviewUrl = null,
    showVideoPreview = false,
    infoTextInstallTop = "",
    infoTextInstallBottom = "",
    backGroundColor = "",
    installationLink = null,
    lastPlayedAt = null,
  )
)

val additionalOfferStub = AdditionalOffer(
  id = 1,
  action = OfferAction.TAPJOY,
  imageFilename = "Zzz_Zzz.jpg",
  rightIcon = "right_icon.svg",
  coinsToEarn = 100500,
  isOneTimeUseOnly = false,
  offerCompletionNotificationDelaySeconds = 0,
  orderKey = 1,
  isDisabled = false,
  title = "title",
  subtext = "substring",
  bodyText = "offer body text",
  rewardText = "reward: {coins} coins",
)

val offerListConfigStub = OfferListConfigApiDto(
  offerWallPlacementId = "offerWallPlacementId",
  offerWallType = TAPJOY,
  offerWallAppId = "d1111111",
  offerWallSecurityToken = null
)

val iosTranslationsStub = TranslationsApiDto(
  language = "en",
  translations = mapOf(
    "Main.MyBalance" to "😇Loyalty Points:",
    "Main.CashOut" to "😇Cash out!",
    "Main.Claim" to "😇Claim!",
    "Main.Reach" to "😇Reach the Loyalty Points goal to maximize rewards!",
    "Main.Goal" to "😇Goal",
    "Main.EarnPlayingGames" to "😇Earn playing games:",
    "Main.EarnCompletingOffers" to "😇Earn completing offers:",
    "Main.PlayAndEarn" to "😇Play & Earn",
    "Main.DoAndEarn" to "😇Do and earn",
    "Main.ByGimicaGames" to "😇By Gimica Games",
    "AboutUs.MainText" to "<b>IT IS TEST TEXT</b>\nJustPlay is the <b>Gimica Games Loyalty Program</b>, an app made <b>exclusively</b> for <b>Gimica Games</b> players...\n\nBy having JustPlay installed you’ll be able to accumulate <b>Loyalty Points</b> for playing your favorites games from <b>Gimica Games\n\nEvery 3 hours</b>, you’ll be able to convert loyalty points accumulated by playing to <b>real rewards</b>!"
  )
)

val cashoutTransactionInfoApiDtoStub = CashoutTransactionInfoApiDto(
  name = "Barbara",
  address = "Bart Apart",
  email = "<EMAIL>",
  provider = CashoutProviderApiDto(
    displayName = "PayPal",
    url = "https://www.paypal.com",
    videoUrl = "https://www.youtube.com",
    iconUrl = "http://someUrlHere/icon2.ico",
    text = "text2",
    provider = PAYPAL,
    donation = false,
    disclaimer = null,
    emailHint = "PayPal account",
    minimumAmount = null,
    maximumAmount = null,
    enabled = true,
    bonusEnabled = false,
    identifierType = EMAIL,
    identifierHint = "PayPal account",
  )
)

val iosPreGameScreenStub = mapOf(
  500001 to IosPreGameScreen(
    gameId = 500001,
    title = "Treasure Master",
    description = "Indulge in an endless and fast-paced gameplay! Customize your spears and time your shots to defeat the monsters and earn coins!",
    tags = "Action,Fast-Paced,Thrilling",
    images = "ios_pgs_tm_1.png,ios_pgs_tm_2.png,ios_pgs_tm_3.png,ios_pgs_tm_4.png,ios_pgs_tm_5.png,ios_pgs_tm_6.png"
  ),
  500002 to IosPreGameScreen(
    gameId = 500002,
    title = "Solitaire Verse",
    description = "Earn coins while enjoying your favorite classic solitaire, now with additional game modes and exciting daily challenges!",
    tags = "Cards,Solitaire,Classic",
    images = "ios_pgs_solitaireverse_1.png,ios_pgs_solitaireverse_2.png,ios_pgs_solitaireverse_3.png,ios_pgs_solitaireverse_4.png"
  ),
  500006 to IosPreGameScreen(
    gameId = 500006,
    title = "Chef's Quest: Match Sensation",
    description = "Embark on a culinary adventure in Chef's Quest: Match Sensation, the delicious match-3 game that will tantalize your taste buds and challenge your mind!",
    tags = "Matching,Casual,Fun",
    images = "ios_pgs_sugarmatch_1.png,ios_pgs_sugarmatch_2.png,ios_pgs_sugarmatch_2_1.jpg,ios_pgs_sugarmatch_2_2.jpg,ios_pgs_sugarmatch_3.png,ios_pgs_sugarmatch_4.png,ios_pgs_sugarmatch_5.png,ios_pgs_sugarmatch_7.png"
  ),
  500012 to IosPreGameScreen(
    gameId = 500012,
    title = "Water Sorter",
    description = "Dive into a world of calming colors and satisfying splashes in Water Sorter! Strategically arrange colorful liquids, one by one, until each tube has only one color!",
    tags = "Challenging,Puzzle,Relaxing",
    images = "ios_pgs_watersorter_1.png,ios_pgs_watersorter_2.png,ios_pgs_watersorter_3.png,ios_pgs_watersorter_4.png,ios_pgs_watersorter_5.png,ios_pgs_watersorter_6.png"
  ),
  500013 to IosPreGameScreen(
    gameId = 500013,
    title = "Fairytale Mansion",
    description = "Unveil the enchanting secrets of Fairytale Mansion! Embark on a magical match-3 adventure and restore a once-grand mansion to its former glory!",
    tags = "Matching,Casual,Fun",
    images = "ios_pgs_fairytalematch_1.png,ios_pgs_fairytalematch_2.png,ios_pgs_fairytalematch_3.png,ios_pgs_fairytalematch_4.png,ios_pgs_fairytalematch_5.png,ios_pgs_fairytalematch_6.png,ios_pgs_fairytalematch_7.png'"
  ),
  500014 to IosPreGameScreen(
    gameId = 500014,
    title = "Puzzle Pop Blaster",
    description = "Blast through vibrant color blocks to collect coins, progress through exciting levels, and unlock special rewards and bonuses with Puzzle Pop Blaster!",
    tags = "Matching,Casual,Puzzle",
    images = "ios_pgs_puzzlepopblaster_1.png,ios_pgs_puzzlepopblaster_2.png,ios_pgs_puzzlepopblaster_3.png,ios_pgs_puzzlepopblaster_4.png,ios_pgs_puzzlepopblaster_5.png,ios_pgs_puzzlepopblaster_6.png,ios_pgs_puzzlepopblaster_7.png,ios_pgs_puzzlepopblaster_8.png"
  ),
  500015 to IosPreGameScreen(
    gameId = 500015,
    title = "Tile Match Ultimate",
    description = "Clear tiles from the board to unlock rewards, spin the fortune wheel for special abilities, and advance through challenging levels!",
    tags = "Matching,Casual,Puzzle",
    images = "ios_pgs_tilematchpro_1.png,ios_pgs_tilematchpro_2.png,ios_pgs_tilematchpro_3.png,ios_pgs_tilematchpro_4.png"
  ),
)

val iosPreGameScreenApiDtoStub = PreGameScreenApiDto(
  title = "Treasure Master",
  description = "Indulge in an endless and fast-paced gameplay! Customize your spears and time your shots to defeat the monsters and earn coins!",
  tags = listOf("Action", "Fast-Paced", "Thrilling"),
  images = listOf(
    "https://storage.googleapis.com/public-playtime/images/ios_pgs_tm_1.png",
    "https://storage.googleapis.com/public-playtime/images/ios_pgs_tm_2.png",
    "https://storage.googleapis.com/public-playtime/images/ios_pgs_tm_3.png",
    "https://storage.googleapis.com/public-playtime/images/ios_pgs_tm_4.png",
    "https://storage.googleapis.com/public-playtime/images/ios_pgs_tm_5.png",
    "https://storage.googleapis.com/public-playtime/images/ios_pgs_tm_6.png"
  )
)

val unlockedAndLockedGamesWidgetsStub = AndroidLockedGamesService.UnlockedAndLockedGamesWidgets(
  unlockedGameInfo = AndroidUnlockedGameInfo(
    unlockedGame = androidGameOfferStub.copy(id = 19),
    text = "someText",
    orderKey = 19
  ),
  lockedGamesInfo = AndroidLockedGamesInfo(
    lockedCount = 23,
    gameIcons = listOf("1.png", "2.png"),
    orderKey = 25
  )
)

val offersConfigStub = OffersConfig(
  "button_text",
  "unlock_text",
  "additional_subtext"
)

val mockOfferwallCfg = OfferwallConfig.Regular(
  id = 1,
  name = "name",
  description = "description",
  buttonText = "buttonText",
  imageFilename = "",
  iconFilename = "",
  orderKey = 1,
  backGroundColor = "",
)

val mockOfferwallCfgPromo = OfferwallConfig.Promotion(
  id = 1,
  name = "name",
  description = "description",
  buttonText = "buttonText",
  imageFilename = "",
  iconFilename = "",
  orderKey = 1,
  backGroundColor = "",
  isHighlighted = true
)

val mockOfferwallInfo = AndroidOfferwallService.OfferwallInfo(
  offerWallType = TAPJOY,
  offerWallPlacementId = "OfferwallDefault",
  offerWallConfig = mockOfferwallCfg,
  orderKey = 1
)

val androidGameApiStub = AndroidFallbackGameApiDto(
  id = 1,
  activityName = "activityName",
  title = DefaultTextDto("name"),
  subtitle = DefaultTextDto("description"),
  applicationId = "applicationId",
  iconUrl = "iconFilename",
  showInstallImage = true,
  installImageUrl = "installImageFilename",
  infoTextInstallTop = DefaultTextDto("infoTextInstallTop"),
  infoTextInstallBottom = DefaultTextDto("infoTextInstallBottom"),
  installationLink = "installationLink"
)

val androidApiGameListStub = listOf(
  androidGameApiStub.copy(
    id = 1,
    applicationId = "com.gimica.treasuremaster",
    iconUrl = "http://example.com/iconFilename",
    installImageUrl = "http://example.com/installImageFilename",
    installationLink = "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"
  ),
  androidGameApiStub.copy(
    id = 2,
    applicationId = "com.gimica.solitaireverse",
    iconUrl = "http://example.com/iconFilename",
    installImageUrl = "http://example.com/installImageFilename",
    installationLink = "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"
  ),
  androidGameApiStub.copy(
    id = 3,
    applicationId = "com.gimica.ballbounce",
    iconUrl = "http://example.com/iconFilename",
    installImageUrl = "http://example.com/installImageFilename",
    installationLink = "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"
  ),
  androidGameApiStub.copy(
    id = 4,
    applicationId = "com.gimica.sugarmatch",
    iconUrl = "http://example.com/iconFilename",
    installImageUrl = "http://example.com/installImageFilename",
    installationLink = "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"
  )
)

val surveyStub = SurveyConfig(
  id = SurveyService.CASHOUT_SURVEY_ID,
  type = SurveyDialogType.CASHOUT,
  title = "Hey!",
  subTitle = "How was your experience with the recent cashout?",
  imageFilename = "survey_cashout.png",
  happyTitle = "",
  happySubOptions = "",
  neutralTitle = "What could be improved?",
  neutralSubOptions = "cashout_amount,cashout_process,payout_options,verification_process",
  unhappyTitle = "What did we do wrong?",
  unhappySubOptions = "payment_delay,low_cash_amount,payout_options_shortage,unclear_cashout_instructions,long_cashout_process,data_privacy_concerns,other",
)

val surveyApiStub = SurveyConfigApiDto(
  dialogType = SurveyDialogType.CASHOUT,
  title = "Hey!",
  subtitle = "How was your experience with the recent cashout?",
  image = "https://storage.googleapis.com/public-playtime/images/survey_cashout.png",
  options = listOf(
    SurveyOptionApiDto(
      type = SurveyOptionType.HAPPY,
      title = "",
      subOptions = emptyList(),
    ),
    SurveyOptionApiDto(
      type = SurveyOptionType.NEUTRAL,
      title = "What could be improved?",
      subOptions = listOf(
        SurveySubOptionApiDto(
          id = "cashout_amount",
          text = "Cash-out amounts",
        ),
        SurveySubOptionApiDto(
          id = "cashout_process",
          text = "Cash-out process",
        ),
        SurveySubOptionApiDto(
          id = "payout_options",
          text = "Payout options",
        ),
        SurveySubOptionApiDto(
          id = "verification_process",
          text = "Verification process",
        ),
      ),
    ),
    SurveyOptionApiDto(
      type = SurveyOptionType.UNHAPPY,
      title = "What did we do wrong?",
      subOptions = listOf(
        SurveySubOptionApiDto(
          id = "payment_delay",
          text = "Delayed payment",
        ),
        SurveySubOptionApiDto(
          id = "low_cash_amount",
          text = "Low cash-out amounts",
        ),
        SurveySubOptionApiDto(
          id = "payout_options_shortage",
          text = "Not enough payout options",
        ),
        SurveySubOptionApiDto(
          id = "unclear_cashout_instructions",
          text = "Unclear cash-out instructions",
        ),
        SurveySubOptionApiDto(
          id = "long_cashout_process",
          text = "Long cash-out process",
        ),
        SurveySubOptionApiDto(
          id = "data_privacy_concerns",
          text = "Data privacy concerns",
        ),
        SurveySubOptionApiDto(
          id = "other",
          text = "Others",
        ),
      ),
    ),
  ),
)

val iosGameAttConsentConfigApiDtoStub = IosGameAttConsentConfigApiDto("userIdValue", CONSENT_VARIANT_1)

val gameCoinsBoosterConfigApiDtoStub = GameCoinsBoosterConfigApiDto("30,120")

val adjoeReportStub = AdjoeCoinsReportDto(
  id = "Id",
  userId = "userId",
  coins = 10500,
  signature = "someSignature",
  currencyName = "LoyaltyPoints",
  sdkAppId = "sdkAppId",
  deviceId = "deviceId",
  appId = "appId",
  appName = "appName",
  rewardLevel = 101,
  rewardType = "rewardType",
  uaNetwork = "uaNetwork",
  uaChannel = "uaChannel",
  uaSubPublisherEncrypted = "uaSubPublisherEncrypted",
  uaSubPublisherClearText = "uaSubPublisherClearText",
  placement = "placement",
  publisherSubId1 = "publisherSubId1",
  publisherSubId2 = "publisherSubId2",
  publisherSubId3 = "publisherSubId3",
  publisherSubId4 = "publisherSubId4",
  publisherSubId5 = "publisherSubId5",
)

val iosInterviewApiDtoStub = IosInterviewApiDto(
  url = "https://calendly.com/userfeedback-justplayapps/user-interview",
  IosInterviewPopupApiDto(
    title = "Interested in sharing your gaming experience?",
    description = """Click “Yes” to schedule 30 minutes interview to talk about JustPlay games and as a gratitude for your time, JustPlay would be happy to offer you a <b>$50</b> Amazon gift card!""",
  )
)

val emailNotificationDto = PromotionsEmailNotificationDto(
  userId = "u",
  encryptedName = "encryptedName",
  encryptedEmail = "encryptedEmail",
  sendGridTemplateId = "s1",
  promotionId = 1
)

val userGameCoinsStub = listOf(
  GamePlayStatusDto(gameId = 1, coins = 2000, playedRecently = false, firstPlayedAt = Instant.now(), lastPlayedAt = Instant.now()),
  GamePlayStatusDto(gameId = 2, coins = 2000, playedRecently = false, firstPlayedAt = Instant.now(), lastPlayedAt = Instant.now()),
).associateBy { it.gameId }

val androidHighlightedGamesApiDtoStub = AndroidHighlightedGamesApiDto(
  items = listOf(
    AndroidHighlightedGameApiDto(
      id = 2,
      applicationId = "com.gimica.solitaireverse",
      title = "name",
      subtitle = "Play for loyalty points!",
      subtext = "Play & Earn",
      iconUrl = "https://storage.googleapis.com/public-playtime/images/iconFilename",
      activityName = "activityName",
      installationLink = "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse&referrer=user_id="
    ),
    AndroidHighlightedGameApiDto(
      id = 3,
      applicationId = "com.gimica.ballbounce",
      title = "name",
      subtitle = "Play for loyalty points!",
      subtext = "Play & Earn",
      iconUrl = "https://storage.googleapis.com/public-playtime/images/iconFilename",
      activityName = "activityName",
      installationLink = "https://play.google.com/store/apps/details?id=com.gimica.ballbounce&referrer=user_id="
    ),
  )
)

val cashStreakShortStatusStub = CashStreakShortStatus(
  daysOnStreak = 7,
  currentDayGoalAchieved = true,
  currentDayGoalAchievementDeadline = Instant.parse("2025-03-10T00:00:00Z"),
  unclaimedRewards = listOf(
    CashStreakReward(
      achievementDay = 5,
      type = CashStreakRewardType.COINS,
      value = BigDecimal("175000"),
      bigReward = false,
    )
  )
)

val cashStreakWidgetApiDtoStub = CashStreakWidgetApiDto(
  title = "Cash streak",
  image = "https://storage.googleapis.com/public-playtime/images/cash_streak_widget_daily_goal_achieved.png",
  daysOnStreak = 7,
  currentDayGoalAchieved = true,
  readyToClaim = true,
  unclaimedRewards = listOf(
    CashStreakRewardApiDto(
      achievementDay = 5,
      type = CashStreakRewardTypeApiDto.COINS,
      title = "Coins",
      value = "175,000",
    )
  )
)

val cashStreakStatusStub = CashStreakStatus(
  daysOnStreak = 7,
  currentDayGoalAchieved = true,
  currentDayGoalAchievementDeadline = Instant.parse("2025-03-10T00:00:00Z"),
  currentBoosters = listOf(
    CashStreakReward(
      achievementDay = 6,
      type = CashStreakRewardType.EARNING_POWER,
      value = BigDecimal("2.000000"),
      bigReward = false,
    )
  ),
  unclaimedRewards = listOf(
    CashStreakReward(
      achievementDay = 7,
      type = CashStreakRewardType.COINS,
      value = BigDecimal("175000"),
      bigReward = false,
    )
  ),
  nextRewards = listOf(
    CashStreakReward(
      achievementDay = 8,
      type = CashStreakRewardType.EARNING_POWER,
      value = BigDecimal("2.500000"),
      bigReward = false,
    ),
    CashStreakReward(
      achievementDay = 9,
      type = CashStreakRewardType.COINS,
      value = BigDecimal("150000"),
      bigReward = false,
    ),
    CashStreakReward(
      achievementDay = 10,
      type = CashStreakRewardType.EARNING_POWER,
      value = BigDecimal("3.000000"),
      bigReward = false,
    ),
    CashStreakReward(
      achievementDay = 11,
      type = CashStreakRewardType.EARNING_POWER,
      value = BigDecimal("50.000000"),
      bigReward = true,
    )
  ),
)

val cashStreakStatusApiDtoStub = CashStreakStatusApiDto(
  title = "7-Days Streak!",
  image = "https://storage.googleapis.com/public-playtime/images/cash_streak_status_daily_goal_achieved.png",
  text = "Complete coin goal to extend the streak!",
  daysOnStreak = 7,
  currentBoosters = listOf(
    CashStreakRewardApiDto(
      achievementDay = 6,
      type = CashStreakRewardTypeApiDto.EARNING_POWER,
      title = "Earning power",
      value = "+2%",
    )
  ),
  nextRewards = listOf(
    CashStreakRewardApiDto(
      achievementDay = 8,
      type = CashStreakRewardTypeApiDto.EARNING_POWER,
      title = "Earnings",
      image = "https://storage.googleapis.com/public-playtime/images/cash_streak_next_reward.png",
      value = "+2.5%",
    ),
    CashStreakRewardApiDto(
      achievementDay = 9,
      type = CashStreakRewardTypeApiDto.COINS,
      title = "Coins",
      image = "https://storage.googleapis.com/public-playtime/images/cash_streak_next_reward.png",
      value = "150,000",
    ),
    CashStreakRewardApiDto(
      achievementDay = 10,
      type = CashStreakRewardTypeApiDto.EARNING_POWER,
      title = "Earnings",
      image = "https://storage.googleapis.com/public-playtime/images/cash_streak_next_reward.png",
      value = "+3%",
    ),
    CashStreakRewardApiDto(
      achievementDay = 11,
      type = CashStreakRewardTypeApiDto.EARNING_POWER,
      title = "Earnings",
      image = "https://storage.googleapis.com/public-playtime/images/cash_streak_next_big_reward.png",
      value = "+50%",
      bigReward = true,
    ),
  )
)

val gamesCelebrationConfigApiDtoStub = GamesCelebrationConfigApiDto("100,150,4,8,18,40")

val androidGameStoriesApiDtoStub = AndroidGameStoriesApiDto(
  stories = listOf(
    AndroidGameStoryApiDto(
      isActive = true,
      title = "Treasure Master",
      iconUrl = "https://storage.googleapis.com/public-playtime/images/android_stories_tm_icon.png",
      imageUrl = "https://storage.googleapis.com/public-playtime/images/android_stories_tm_image.png",
      backgroundImage = "https://storage.googleapis.com/public-playtime/images/android_stories_tm_b_image.png",
      amountText = "\$76+",
      amountTextGradientColors = listOf("#FFFFFF", "#FFC345", "#DF9D13"),
      amountTextBorderGradientColors = listOf("#E944BD", "#77118A"),
      amountTextBackgroundColor = "#B2008DC5",
      buttonText = "Earn <span style=\"color: #F3A700;\">\$\$\$</span> Now",
      subtext = "YOUR ADVENTURE WAITS!",
      installationLink = "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster&referrer=user_id=",
      applicationId = "com.gimica.treasuremaster",
      activityName = "activityName"
    ),
    AndroidGameStoryApiDto(
      isActive = true,
      title = "BallBounce",
      iconUrl = "https://storage.googleapis.com/public-playtime/images/android_stories_ballbounce_icon.png",
      imageUrl = "https://storage.googleapis.com/public-playtime/images/android_stories_ballbounce_image.png",
      backgroundImage = "https://storage.googleapis.com/public-playtime/images/android_stories_ballbounce_b_image.png",
      amountText = "\$76+",
      amountTextGradientColors = listOf("#FFFFFF", "#FFC345", "#DF9D13"),
      amountTextBorderGradientColors = listOf("#E944BD", "#77118A"),
      amountTextBackgroundColor = "#CC1D1E30",
      buttonText = "Earn <span style=\"color: #F3A700;\">\$\$\$</span> Now",
      subtext = "YOUR ADVENTURE WAITS!",
      installationLink = "https://play.google.com/store/apps/details?id=com.gimica.ballbounce&referrer=user_id=",
      applicationId = "com.gimica.ballbounce",
      activityName = "activityName"
    ),
    AndroidGameStoryApiDto(
      isActive = false,
      title = "Solitaire Verse",
      iconUrl = "https://storage.googleapis.com/public-playtime/images/android_stories_solitaireverse_icon.png",
      imageUrl = "https://storage.googleapis.com/public-playtime/images/android_stories_solitaireverse_image.png",
      backgroundImage = "https://storage.googleapis.com/public-playtime/images/android_stories_solitaireverse_b_image.png",
      amountText = "\$76+",
      amountTextGradientColors = listOf("#FFFFFF"),
      amountTextBorderGradientColors = listOf("#44E965", "#118A23"),
      amountTextBackgroundColor = "#99001845",
      buttonText = "Earn <span style=\"color: #F3A700;\">\$\$\$</span> Now",
      subtext = "YOUR ADVENTURE WAITS!",
      installationLink = "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse&referrer=user_id=",
      applicationId = "com.gimica.solitaireverse",
      activityName = "activityName"
    ),
  )
)

fun boostedModeStub(userId: String, now: Instant = Instant.now()) = BoostedMode(
  userId = userId,
  presetId = "presetId",
  sessionId = "sessionId",
  uiConfig = BoostedMode.UiConfig(
    mainScreenHintTranslation = "mainScreenHintTranslation",
    cashoutScreenHintTranslation = "cashoutScreenHintTranslation",
    topLeftCoinsReplacementTranslation = "topLeftCoinsReplacementTranslation",
    colorBadge = "colorBadge",
    colorBadgeEnd = "colorBadgeEnd",
    colorTextBadge = "colorTextBadge",
    colorBack = "colorBack",
    colorBackEnd = "colorBackEnd",
    colorTextCoinsBefore = "colorTextCoinsBefore",
    colorTextCoinsNow = "colorTextCoinsNow",
    topImage = null,
    balanceUpdate = null,
    readyToCashout = null,
  ),
  coinsCoefficient = 2.0,
  earningsCoefficient = 1.5,
  startTime = now,
  visibleEndTime = now + 3.hours,
  effectiveEndTime = now + 2.hours,
)
