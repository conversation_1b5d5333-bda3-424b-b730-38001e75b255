package com.moregames.playtime.buseffects

import com.moregames.base.dto.AppPlatform
import com.moregames.playtime.user.verification.VerificationService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking

class VerificationsEffectHandlerTest {
  private val verificationService: VerificationService = mock()

  private val underTest = VerificationsEffectHandler(
    verificationService
  )

  @Test
  fun `SHOULD trigger gps location verification ON handleWebAppGpsLocationCheckEventEffect`() {
    val effect = WebAppGpsLocationCheckEventEffect(
      userId = "testUserId",
      sessionId = "sessionId",
      location = "US",
      isMocked = false
    )

    runBlocking {
      underTest.handleWebAppGpsLocationCheckEventEffect(effect)
    }

    verifyBlocking(verificationService) {
      verifyGpsLocation(
        sessionId = "sessionId",
        location = "US",
        isMocked = false,
        appPlatform = AppPlatform.IOS_WEB
      )
    }
  }
}