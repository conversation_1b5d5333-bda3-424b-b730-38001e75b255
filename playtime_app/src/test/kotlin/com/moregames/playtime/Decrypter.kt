package com.moregames.playtime

import com.moregames.base.encryption.EncryptionService
import com.moregames.base.secret.GcpApiSecretService
import kotlinx.coroutines.runBlocking
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.csv.CSVPrinter
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable
import java.io.FileReader
import java.io.FileWriter

class Decrypter {

  @Test
  @EnabledIfEnvironmentVariable(named = "RUN_DECRYPTER", matches = "true")
  fun decrypt() = runBlocking {
    val encryptionService = EncryptionService(GcpApiSecretService())

    val parser = CSVParser.parse(FileReader("/Users/<USER>/Downloads/US Android user experiment info - US experiment user info encrypted.csv"), CSVFormat.DEFAULT)
    val writer = CSVPrinter(FileWriter("/Users/<USER>/Downloads/US Android user experiment info - US experiment user info decrypted.csv"), CSVFormat.DEFAULT)

    parser.asSequence()
      .forEach {
        val email = it[1]
        val name = it[2]

        val decryptedEmail = encryptionService.decryptOrEmpty(email)
        val decryptedName = encryptionService.decryptOrEmpty(name)

        writer.printRecord(it[0], decryptedEmail, decryptedName, *it.drop(3).toTypedArray())
      }

    writer.flush()
  }
}