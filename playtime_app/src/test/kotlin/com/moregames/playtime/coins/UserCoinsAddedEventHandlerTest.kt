package com.moregames.playtime.coins

import com.google.protobuf.StringValue
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.additionalOfferCoins
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.bonusCoins
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.em3UserGameCoins
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.offerWallCoins
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.userGameCoins
import com.justplayapps.service.rewarding.earnings.proto.userCoinsAddedEvent
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.junit.MockExtension
import com.moregames.base.offers.dto.OfferAction
import com.moregames.base.util.ClientVersionsSupport.IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.SendCommandBasedBalanceUpdatedNotification
import com.moregames.playtime.coins.UserCoinsAddedEventHandler.Em3UserGameProgressUpdatedEffect
import com.moregames.playtime.games.Em2IosBoostedGameService
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.CashoutOfferBalanceUpdate
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.BalanceUpdatedNotification
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.offers.CashoutOffersService
import com.moregames.playtime.user.offer.OfferPersistenceService
import com.moregames.playtime.user.onboarding.progressbar.buseffects.OnboardingProgressBarOfferwallCoinsReceivedEffect
import com.moregames.playtime.utils.additionalOfferStub
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions

@ExtendWith(MockExtension::class)
class UserCoinsAddedEventHandlerTest(
  private val offerPersistenceService: OfferPersistenceService,
  private val abTestingService: AbTestingService,
  private val userService: UserService,
  private val cashoutOffersService: CashoutOffersService,
  private val messageBus: MessageBus,
  private val em2IosBoostedGameService: Em2IosBoostedGameService,
) {

  private val handler = UserCoinsAddedEventHandler(
    offerPersistenceService = offerPersistenceService,
    abTestingService = abTestingService,
    messageBus = messageBus,
    cashoutOffersService = cashoutOffersService,
    userService = userService,
    em2IosBoostedGameService = em2IosBoostedGameService
  )

  private companion object {
    const val USER_ID = "userId"
    const val COINS = 42L
    const val COINS_ADDED = 12L
    const val GAME_ID = 50001
  }

  @BeforeEach
  fun init() {
    userService.mock({ getUser(USER_ID) }, userDtoStub)
    em2IosBoostedGameService.mock({ isIosBoostedGame(any()) }, false)
    cashoutOffersService.mock({ hasActiveOfferForGame(eq(USER_ID), any()) }, false)
  }

  @Test
  fun `SHOULD send BalanceUpdatedNotification IF game coins added`() {
    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = COINS
          coinsAdded = COINS_ADDED
          gameCoinsData = userGameCoins {
            gameId = 1
          }
        }
      )
    }

    verifyBlocking(messageBus) {
      publishAsync(
        BalanceUpdatedNotification(USER_ID, COINS, COINS_ADDED).toEffect()
      )
    }
  }

  @Test
  fun `SHOULD send BalanceUpdatedNotification with isIosBoostedGame WHEN boosted game coins added`() {
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(appPlatform = IOS, appVersion = IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE))
    em2IosBoostedGameService.mock({ isIosBoostedGame(GAME_ID) }, true)

    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = COINS
          coinsAdded = COINS_ADDED
          gameCoinsData = userGameCoins {
            gameId = GAME_ID
          }
        }
      )
    }

    verifyBlocking(messageBus) {
      publishAsync(
        BalanceUpdatedNotification(USER_ID, COINS, COINS_ADDED, isIosBoostedGameCoins = true).toEffect()
      )
    }
  }

  @Test
  fun `SHOULD send BalanceUpdatedNotification without isIosBoostedGame WHEN boosted game coins added but new version`() {
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(appPlatform = IOS, appVersion = IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE + 1))
    em2IosBoostedGameService.mock({ isIosBoostedGame(GAME_ID) }, true)

    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = COINS
          coinsAdded = COINS_ADDED
          gameCoinsData = userGameCoins {
            gameId = GAME_ID
          }
        }
      )
    }

    verifyBlocking(messageBus) {
      publishAsync(
        BalanceUpdatedNotification(USER_ID, COINS, COINS_ADDED).toEffect()
      )
    }
  }

  @Test
  fun `Do nothing IF game coins added and coins = 0`() {
    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = 0
          gameCoinsData = userGameCoins {}
        }
      )
    }

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD send CashoutOfferBalanceUpdate IF game coins added and has active offers = 0`() {
    cashoutOffersService.mock({ hasActiveOfferForGame(USER_ID, 1) }, true)

    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = COINS
          gameCoinsData = userGameCoins {
            gameId = 1
          }
        }
      )
    }

    verifyBlocking(messageBus) {
      publishAsync(
        CashoutOfferBalanceUpdate(USER_ID, COINS, false).toEffect()
      )
    }
  }

  @Test
  fun `SHOULD send SendCommandBasedBalanceUpdatedNotification IF game coins added for games`() {
    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = COINS
          gameCoinsData = userGameCoins {
            gameId = 1
            forceCommandNotification = true
            commandId = StringValue.of("commandId")
          }
        }
      )
    }

    verifyBlocking(messageBus) {
      publish(
        SendCommandBasedBalanceUpdatedNotification("commandId", COINS)
      )
    }
  }

  @Test
  fun `SHOULD send BalanceUpdatedNotification IF EM3 game coins added`() {
    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = COINS
          coinsAdded = COINS_ADDED
          em3GameCoinsData = em3UserGameCoins {  }
        }
      )
    }

    verifyBlocking(messageBus) {
      publishAsync(
        BalanceUpdatedNotification(USER_ID, COINS, COINS_ADDED).toEffect()
      )
    }
  }

  @Test
  fun `SHOULD send BalanceUpdatedNotification IF Bonus coins added`() {
    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = COINS
          coinsAdded = COINS_ADDED
          bonusCoinsData = bonusCoins {}
        }
      )
    }

    verifyBlocking(messageBus) {
      publishAsync(
        BalanceUpdatedNotification(USER_ID, COINS, COINS_ADDED).toEffect()
      )
    }
  }

  @Test
  fun `SHOULD send BalanceUpdatedNotification IF Additional offers coins added`() {
    val offerId = 146
    offerPersistenceService.mock( { loadAdditionalOffer(offerId) }, additionalOfferStub.copy(action = OfferAction.VIDEO_AD))
    abTestingService.mock({ isEm3Participant(USER_ID) }, false)
    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = COINS
          coinsAdded = COINS_ADDED
          additionalOfferCoinsData = additionalOfferCoins {
            this.offerId = offerId
          }
        }
      )
    }

    verifyBlocking(messageBus) {
      publishAsync(
        BalanceUpdatedNotification(USER_ID, COINS, COINS_ADDED).toEffect()
      )
    }
  }

  @Test
  fun `SHOULD send BalanceUpdatedNotification but hide coins IF Additional offers coins added for EM3`() {
    val offerId = 146
    offerPersistenceService.mock( { loadAdditionalOffer(offerId) }, additionalOfferStub.copy(action = OfferAction.VIDEO_AD))
    abTestingService.mock({ isEm3Participant(USER_ID) }, true)
    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = COINS
          coinsAdded = COINS_ADDED
          additionalOfferCoinsData = additionalOfferCoins {
            this.offerId = offerId
          }
        }
      )
    }

    verifyBlocking(messageBus) {
      publishAsync(
        BalanceUpdatedNotification(USER_ID, COINS, COINS_ADDED, true).toEffect()
      )
    }
  }

  @Test
  fun `SHOULD send BalanceUpdatedNotification IF OfferWall coins added`() {
    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = COINS
          coinsAdded = COINS_ADDED
          offerWallCoinsData = offerWallCoins {}
        }
      )
    }

    verifyBlocking(messageBus) {
      publishAsync(
        BalanceUpdatedNotification(USER_ID, COINS, COINS_ADDED).toEffect()
      )
    }
  }

  @Test
  fun `SHOULD publish effects WHEN OfferWall coins added`() {
    runBlocking {
      handler.handle(
        userCoinsAddedEvent {
          userId = USER_ID
          coins = COINS
          coinsAdded = COINS_ADDED
          offerWallCoinsData = offerWallCoins {}
        }
      )
    }

    verifyBlocking(messageBus) {
      publishAsync(OnboardingProgressBarOfferwallCoinsReceivedEffect(USER_ID))
      publishAsync(BalanceUpdatedNotification(USER_ID, COINS, COINS_ADDED).toEffect())
    }
  }

  @Test
  fun `handle em3UserGameProgressUpdated effect`() {
    runBlocking {
      handler.em3UserGameProgressUpdated(
        Em3UserGameProgressUpdatedEffect(USER_ID, null, false, null)
      )
    }
    verifyBlocking(messageBus) {
      publishAsync(
        BalanceUpdatedNotification(USER_ID, 0, 0,true).toEffect()
      )
    }
  }

  @Test
  fun `handle em3UserGameProgressUpdated effect if it is InGameBalance`() {
    cashoutOffersService.mock({ hasActiveOfferForGame(USER_ID, 42) }, true)

    runBlocking {
      handler.em3UserGameProgressUpdated(
        Em3UserGameProgressUpdatedEffect(USER_ID, "commandId", true, 42)
      )
    }
    verifyBlocking(messageBus) {
      publish(
        SendCommandBasedBalanceUpdatedNotification("commandId", 0)
      )
    }
  }

  @Test
  fun `handle em3UserGameProgressUpdated effect if use has active offers`() {
    cashoutOffersService.mock({ hasActiveOfferForGame(USER_ID, 42) }, true)
    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, userDtoStub.appPlatform, true) }, false)

    runBlocking {
      handler.em3UserGameProgressUpdated(
        Em3UserGameProgressUpdatedEffect(USER_ID, "commandId", false, 42)
      )
    }
    verifyBlocking(messageBus) {
      publishAsync(
        CashoutOfferBalanceUpdate(USER_ID, 0, true).toEffect()
      )
    }
  }
}