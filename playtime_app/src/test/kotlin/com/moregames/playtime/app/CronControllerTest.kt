package com.moregames.playtime.app

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.justplayapps.service.rewarding.earnings.UserEarningsService
import com.justplayapps.service.rewarding.revenue.RevenueAggregationService
import com.moregames.base.app.OfferWallType
import com.moregames.base.util.mock
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.REMOVE_COINS_BY_PERIODS_CRON_JOB
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.REMOVE_EMPTY_GAME_COINS_TOTALS_CRON_JOB
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.REMOVE_REVENUE_BY_PERIODS_CRON_JOB
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.SCHEDULE_PROMOTIONS_NOTIFICATIONS
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.SCHEDULE_PROMOTIONS_NOTIFICATIONS_FYBER
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.SEND_PROMOTIONS_EMAILS
import com.moregames.playtime.cashstreak.CashStreakEngine
import com.moregames.playtime.earnings.currency.CurrencyExchangeService
import com.moregames.playtime.notifications.MassEmailNotificationService
import com.moregames.playtime.notifications.MassPushNotificationService
import com.moregames.playtime.notifications.PromotionsNotificationService
import com.moregames.playtime.revenue.applovin.DataImportManager
import com.moregames.playtime.user.ActiveUsersService
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.abmigration.AbTestingMigrationService
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import com.moregames.playtime.user.verification.VerificationService
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.server.testing.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.time.Instant

class CronControllerTest {
  private val dataImportManager: DataImportManager = mock()
  private val userEarningsService: UserEarningsService = mock()
  private val userPersistenceService: UserPersistenceService = mock()
  private val revenueAggregationService: RevenueAggregationService = mock()
  private val cashoutPeriodsService: CashoutPeriodsService = mock()
  private val currencyExchangeService: CurrencyExchangeService = mock()
  private val cronTempDataPersistenceService: CronTempDataPersistenceService = mock()
  private val verificationService: VerificationService = mock()
  private val promotionsNotificationService: PromotionsNotificationService = mock()
  private val massPushNotificationService: MassPushNotificationService = mock()
  private val massEmailNotificationService: MassEmailNotificationService = mock()
  private val activeUsersService: ActiveUsersService = mock()
  private val abTestingMigrationService: AbTestingMigrationService = mock()
  private val cashStreakEngine: CashStreakEngine = mock()
  private val userService: UserService = mock()

  private fun controller(): Application.() -> Unit = {
    routing {
      CronController(
        dataImportManager = dataImportManager,
        userEarningsService = userEarningsService,
        userPersistenceService = userPersistenceService,
        revenueAggregationService = revenueAggregationService,
        cashoutPeriodsService = cashoutPeriodsService,
        currencyExchangeService = currencyExchangeService,
        cronTempDataPersistenceService = cronTempDataPersistenceService,
        verificationService = verificationService,
        promotionsNotificationService = promotionsNotificationService,
        massPushNotificationService = massPushNotificationService,
        massEmailNotificationService = massEmailNotificationService,
        activeUsersService = activeUsersService,
        abTestingMigrationService = abTestingMigrationService,
        cashStreakEngine = cashStreakEngine,
        userService = userService
      ).startRouting(this)
    }
  }

  @Test
  fun `SHOULD remove old data from current generic revenue ON removeOldDataFromCurrentGenericRevenue call`() = withTestApplication(controller()) {
    userEarningsService.mock({ removeOldDataFromCurrentGenericRevenue() }, 123)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/removeOldDataFromCurrentGenericRevenue"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userEarningsService) { removeOldDataFromCurrentGenericRevenue() }
  }

  @ParameterizedTest
  @ValueSource(doubles = [1.0, 55.*********])
  fun `SHOULD remove old unconverted revenue by deleted users from current generic revenue ON  call`(removedRevenue: Double) =
    withTestApplication(controller()) {
      userEarningsService.mock({ removeUnconvertedOldRevenueForDeletedUsers() }, 10 to removedRevenue.toBigDecimal())

      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/removeUnconvertedOldRevenueForDeletedUsers"
      )

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      verifyBlocking(userEarningsService) { removeUnconvertedOldRevenueForDeletedUsers() }
    }

  @Test
  fun `SHOULD trigger checkUsersWithoutOpenCashoutPeriods ON checkUsersWithoutOpenCashoutPeriods call`() = withTestApplication(controller()) {
    cashoutPeriodsService.mock({ checkUsersWithoutOpenCashoutPeriods() }, 123)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/checkUsersWithoutOpenCashoutPeriods"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(cashoutPeriodsService) { checkUsersWithoutOpenCashoutPeriods() }
  }

  @Test
  fun `SHOULD trigger exchange rate sync ON syncCurrencyExchangeRates call`() = withTestApplication(controller()) {
    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/syncCurrencyExchangeRates"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(currencyExchangeService) { syncCurrencyExchangeRates() }
  }

  @Test
  fun `SHOULD trigger removeBatchOfZeroGameTotals ON removeEmptyGameCoinsTotals call`() = withTestApplication(controller()) {
    cronTempDataPersistenceService.mock(
      { getCronJobLastRunData(REMOVE_EMPTY_GAME_COINS_TOTALS_CRON_JOB) },
      CronJobLastRunData("a", Instant.now())
    )
    userPersistenceService.mock({ removeBatchOfZeroGameTotals(10000, "a") }, 10000)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/removeEmptyGameCoinsTotals?batchSize=10000"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userPersistenceService) { removeBatchOfZeroGameTotals(10000, "a") }
    verifyBlocking(cronTempDataPersistenceService) {
      saveCronJobLastRunData(eq(REMOVE_EMPTY_GAME_COINS_TOTALS_CRON_JOB), argThat { this.lastChunkUserIdFirstLetter == "a" })
    }
  }

  @Test
  fun `SHOULD trigger removeBatchOfZeroGameTotals and shift letter for the next run ON removeEmptyGameCoinsTotals call`() = withTestApplication(controller()) {
    cronTempDataPersistenceService.mock(
      { getCronJobLastRunData(REMOVE_EMPTY_GAME_COINS_TOTALS_CRON_JOB) },
      CronJobLastRunData("a", Instant.now())
    )
    userPersistenceService.mock({ removeBatchOfZeroGameTotals(10000, "a") }, 123) // the last matching rows for "a" like users

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/removeEmptyGameCoinsTotals?batchSize=10000"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userPersistenceService) { removeBatchOfZeroGameTotals(10000, "a") }
    verifyBlocking(cronTempDataPersistenceService) {
      saveCronJobLastRunData(eq(REMOVE_EMPTY_GAME_COINS_TOTALS_CRON_JOB), argThat { this.lastChunkUserIdFirstLetter == "b" })
    }
  }

  @Test
  fun `SHOULD trigger removeBatchOfCoinsTrackedByPeriods and shift letter for the next run ON removeCoinsByPeriods call`() = withTestApplication(controller()) {
    cronTempDataPersistenceService.mock(
      { getCronJobLastRunData(REMOVE_COINS_BY_PERIODS_CRON_JOB) },
      CronJobLastRunData("a", Instant.now())
    )
    userPersistenceService.mock({ removeBatchOfCoinsTrackedByPeriods(10000, "a") }, 123) // the last matching rows for "a" like users
    userPersistenceService.mock({ removeBatchOfFractionalCoinsTrackedByPeriods(10000, "a") }, 17)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/removeCoinsByPeriods?batchSize=10000"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userPersistenceService) { removeBatchOfCoinsTrackedByPeriods(10000, "a") }
    verifyBlocking(cronTempDataPersistenceService) {
      saveCronJobLastRunData(eq(REMOVE_COINS_BY_PERIODS_CRON_JOB), argThat { this.lastChunkUserIdFirstLetter == "b" })
    }
  }

  @ParameterizedTest
  @CsvSource("10000,17", "17,10000", "10000,10000")
  fun `SHOULD NOT shift letter for the next run ON removeCoinsByPeriods call WHEN we still have some data to remove`(
    coinsAmount: Int, fractionalCoinsAmount: Int
  ) = withTestApplication(controller()) {
    cronTempDataPersistenceService.mock(
      { getCronJobLastRunData(REMOVE_COINS_BY_PERIODS_CRON_JOB) },
      CronJobLastRunData("a", Instant.now())
    )
    userPersistenceService.mock({ removeBatchOfCoinsTrackedByPeriods(10000, "a") }, coinsAmount)
    userPersistenceService.mock({ removeBatchOfFractionalCoinsTrackedByPeriods(10000, "a") }, fractionalCoinsAmount)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/removeCoinsByPeriods?batchSize=10000"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userPersistenceService) { removeBatchOfCoinsTrackedByPeriods(10000, "a") }
    verifyBlocking(cronTempDataPersistenceService) {
      saveCronJobLastRunData(eq(REMOVE_COINS_BY_PERIODS_CRON_JOB), argThat { this.lastChunkUserIdFirstLetter == "a" })
    }
  }

  @Test
  fun `SHOULD trigger removeBatchOfRevenueTrackedByPeriods and shift letter for the next run ON removeRevenueByPeriods call`() =
    withTestApplication(controller()) {
      cronTempDataPersistenceService.mock(
        { getCronJobLastRunData(REMOVE_REVENUE_BY_PERIODS_CRON_JOB) },
        CronJobLastRunData("a", Instant.now())
      )
      userEarningsService.mock({ removeBatchOfRevenueTrackedByPeriods(10000, "a") }, 123) // the last matching rows for "a" like users

      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/removeRevenueByPeriods?batchSize=10000"
      )

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      verifyBlocking(userEarningsService) { removeBatchOfRevenueTrackedByPeriods(10000, "a") }
      verifyBlocking(cronTempDataPersistenceService) {
        saveCronJobLastRunData(eq(REMOVE_REVENUE_BY_PERIODS_CRON_JOB), argThat { this.lastChunkUserIdFirstLetter == "b" })
      }
    }

  @Test
  fun `SHOULD trigger removal of old FaceMaps on removeOldFaceMaps`() = withTestApplication(controller()) {
    verificationService.mock({ removeOldFaceMaps(120, 10000, true) }, 0)
    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/removeOldFaceMaps?olderThanDays=120"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(verificationService) { removeOldFaceMaps(120, 10000, true) }
    verifyNoMoreInteractions(verificationService)
  }

  @Test
  fun `SHOULD trigger push notification sending on sendScheduledGenericPushNotifications`() = withTestApplication(controller()) {
    massPushNotificationService.mock({ sendScheduledGenericPushNotificationsBatch(500) }, 450)
    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/sendScheduledGenericPushNotifications?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(massPushNotificationService) { sendScheduledGenericPushNotificationsBatch(500) }
  }

  @Test
  fun `SHOULD trigger email notification sending on sendScheduledEmailNotifications`() = withTestApplication(controller()) {
    massEmailNotificationService.mock({ sendScheduledEmailNotificationsBatch(500) }, 450)
    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/sendScheduledEmailNotifications?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(massEmailNotificationService) { sendScheduledEmailNotificationsBatch(500) }
  }

  @Test
  fun `SHOULD trigger adding scheduled notifications for promotions on schedulePromotionsNotifications`() = withTestApplication(controller()) {
    val runData = CronJobLastRunData("f", Instant.now())
    cronTempDataPersistenceService.mock({ getCronJobLastRunData(SCHEDULE_PROMOTIONS_NOTIFICATIONS) }, runData)
    promotionsNotificationService.mock({ scheduleNotifications(500, "f", OfferWallType.TAPJOY) }, 450)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/schedulePromotionsNotifications?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(promotionsNotificationService) { scheduleNotifications(500, "f", OfferWallType.TAPJOY) }
    verifyBlocking(cronTempDataPersistenceService) {
      saveCronJobLastRunData(eq(SCHEDULE_PROMOTIONS_NOTIFICATIONS), argThat { this.lastChunkUserIdFirstLetter == "0" })
    }
  }

  @Test
  fun `SHOULD trigger adding scheduled notifications for fyber promotions on sendScheduledGenericPushNotificationsFyber`() = withTestApplication(controller()) {
    val runData = CronJobLastRunData("f", Instant.now())
    cronTempDataPersistenceService.mock({ getCronJobLastRunData(SCHEDULE_PROMOTIONS_NOTIFICATIONS_FYBER) }, runData)
    promotionsNotificationService.mock({ scheduleNotifications(500, "f", OfferWallType.FYBER) }, 450)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/schedulePromotionsNotificationsFyber?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(promotionsNotificationService) { scheduleNotifications(500, "f", OfferWallType.FYBER) }
    verifyBlocking(cronTempDataPersistenceService) {
      saveCronJobLastRunData(eq(SCHEDULE_PROMOTIONS_NOTIFICATIONS_FYBER), argThat { this.lastChunkUserIdFirstLetter == "0" })
    }
  }

  @Test
  fun `SHOULD trigger sending promotions email notifications on sendScheduledGenericPushNotificationsFyber`() = withTestApplication(controller()) {
    val runData = CronJobLastRunData("f", Instant.now())
    cronTempDataPersistenceService.mock({ getCronJobLastRunData(SEND_PROMOTIONS_EMAILS) }, runData)
    promotionsNotificationService.mock({ sendPromotionsEmailsBatch(500, "f") }, 450)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/sendPromotionsEmailNotifications?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(promotionsNotificationService) { sendPromotionsEmailsBatch(500, "f") }
    verifyBlocking(cronTempDataPersistenceService) {
      saveCronJobLastRunData(eq(SEND_PROMOTIONS_EMAILS), argThat { this.lastChunkUserIdFirstLetter == "0" })
    }
  }

  @Test
  fun `SHOULD removing a batch of processed notifications ON cleanUpProcessedScheduledNotifications`() = withTestApplication(controller()) {
    massPushNotificationService.mock({ cleanUpProcessedScheduledNotifications(500) }, 450)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/cleanUpProcessedScheduledNotifications?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(massPushNotificationService) { cleanUpProcessedScheduledNotifications(500) }
  }

  @Test
  fun `SHOULD removing a batch of processed notifications ON cleanUpProcessedEmailNotifications`() = withTestApplication(controller()) {
    massEmailNotificationService.mock({ cleanUpProcessedEmailNotifications(500) }, 420)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/cleanUpProcessedEmailNotifications?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)

    verifyBlocking(massEmailNotificationService) { cleanUpProcessedEmailNotifications(500) }
  }

  @Test
  fun `SHOULD removing a batch of processed notifications ON cleanUpProcessedPromotionsEmails`() = withTestApplication(controller()) {
    promotionsNotificationService.mock({ cleanUpProcessedPromotionsEmails(500) }, 380)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/cleanUpProcessedPromotionsEmails?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(promotionsNotificationService) { cleanUpProcessedPromotionsEmails(500) }
  }

  @Test
  fun `SHOULD remove batch of old revenue totals ON cleanUpGenericRevenueDailyTotals`() = withTestApplication(controller()) {
    revenueAggregationService.mock({ removeDailyRevenueOldTotalsBatch(500) }, 450)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/cleanUpGenericRevenueDailyTotals?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(revenueAggregationService) { removeDailyRevenueOldTotalsBatch(500) }
  }

  @Test
  fun `SHOULD trigger non matching users removal ON tidyUpActiveUsersList`() = withTestApplication(controller()) {
    cronTempDataPersistenceService.mock({ getCronJobLastRunData("tidyUpActiveUsers") }, CronJobLastRunData("f", Instant.now()))
    activeUsersService.mock({ removeNonActiveUsers("f", 500) }, 5)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/tidyUpActiveUsersList?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(activeUsersService) { removeNonActiveUsers("f", 500) }
    verifyBlocking(cronTempDataPersistenceService) { getCronJobLastRunData("tidyUpActiveUsers") }
    verifyBlocking(cronTempDataPersistenceService) { saveCronJobLastRunData(eq("tidyUpActiveUsers"), argThat { this.lastChunkUserIdFirstLetter == "0" }) }
  }

  @Test
  fun `SHOULD process a batch of users force assignment ON abForceAssignmentProcessing`() = withTestApplication(controller()) {
    abTestingMigrationService.mock({ processBatchOfForceAssigningUsers(500) }, 450)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/abForceAssignmentProcessing?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(abTestingMigrationService) { processBatchOfForceAssigningUsers(500) }
  }

  @Test
  fun `SHOULD remove a batch of obsolete force assignments tracks ON cleanAbForceAssignmentProcessed`() = withTestApplication(controller()) {
    abTestingMigrationService.mock({ cleanAbForceAssignmentProcessed(500) }, 450)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/cleanAbForceAssignmentProcessed?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(abTestingMigrationService) { cleanAbForceAssignmentProcessed(500) }
  }

  @Test
  fun `SHOULD notify a batch of users on cash streak x hours before deadline ON `() = withTestApplication(controller()) {
    cashStreakEngine.mock({ notifyUsersCloseToStreakDeadline(500) }, 450)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/notifyUsersCloseToStreakDeadline?batchSize=500"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(cashStreakEngine) { notifyUsersCloseToStreakDeadline(500) }
  }

  @Test
  fun `SHOULD trigger related method ON calculateMinThresholdsForEcpmGroups`() = withTestApplication(controller()) {
    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/calculateMinThresholdsForEcpmGroups"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) { calculateMinThresholdsForEcpmGroups() }
  }
}