{"events": [{"id": "testId", "dateFrom": "2088-12-01T10:15:30Z", "dateTo": "2088-12-03T10:15:30Z", "useUserTimeZone": true, "uiConfiguration": {"top": {"gradientTop": "#FF1E072B"}, "mainTop": [{"imageClickAction": {"name": "ROUTE_TO_MAIN", "parameters": ["p1", "p2"]}}], "countDownBanners": {"startPosition": 1, "step": 3, "max": 2, "title": "Banner title", "backgroundImage": "background_image.jpg", "infoImages": ["image1.jpg", "image2.jpg"], "infoTitle": "Info title", "infoSections": [{"subTitle": "SubTitle", "subText": "SubText"}], "infoButtonClickAction": {"name": "OPEN_FIRST_FOUND_OFFERWALL", "parameters": ["parameter1", "parameter2"]}, "infoButtonText": "Info button text", "durationInSeconds": 3600}, "infoBar": {"content": [{"text": "PLAYERS_ONLINE"}, {"text": "Text with {DYNAMIC_VALUE}$ amount in it!", "parameters": {"showDuringSec": 5, "sliding": true, "dynamicValueConfiguration": {"baseValue": 1000000, "updateEachSec": 5, "updateValueBy": -200, "randomnessPercentage": 5}}}]}, "expectedAppVersion": 75}, "experimentKey": "<PERSON><PERSON><PERSON>", "variationKey": "<PERSON><PERSON><PERSON>", "priorityKey": 0, "translationResources": [{"resourceName": "$_resource2", "originalResourceName": "$_original_resource2"}, {"resourceName": "$_resource1", "originalResourceName": "$_original_resource1"}], "eventType": "GLOBAL"}]}