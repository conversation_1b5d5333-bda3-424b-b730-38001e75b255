{"id": "TestPromotion", "dateFrom": "2007-12-03T10:15:30.00Z", "dateTo": "2007-12-05T10:15:30.00Z", "useUserTimeZone": false, "uiConfiguration": {"mainTop": [{"imageClickAction": {"name": "ROUTE_TO_MAIN", "parameters": ["p1", "p2"]}}], "top": {"gradientTop": "#FF1E072B", "gradientBottom": "#FF171524", "backgroundImage": "https://storage.googleapis.com/public-playtime/images/offer_haloween_background.png", "foregroundImage": "https://storage.googleapis.com/public-playtime/images/offer_haloween_foreground.png", "cashoutButtonColor": "#FFF19D00"}, "offerModifier": [{"offerId": "200039", "badge": {"text": "<strikethrough>$5 daily</strikethrough><br><font color=\"0x00FF00\">Now!</font><br>", "color": "#FFFF00", "displaySpecialBadge": true}, "offerImage": "treasure_master_override.jpg", "highlight": true}], "countDownBanners": {"startPosition": 1, "step": 3, "max": 2, "title": "Banner title", "backgroundImage": "background_image.jpg", "infoImages": ["image1.jpg", "image2.jpg"], "infoTitle": "Info title", "infoSections": [{"subTitle": "SubTitle", "subText": "SubText"}], "infoButtonClickAction": {"name": "OPEN_CHALLENGES", "parameters": ["parameter1", "parameter2"]}, "infoButtonText": "Info button text", "durationInSeconds": 3600}, "infoBar": {"content": [{"text": "PLAYERS_ONLINE"}, {"text": "Text with {DYNAMIC_VALUE}$ amount in it!", "parameters": {"showDuringSec": 5, "sliding": true, "dynamicValueConfiguration": {"baseValue": 1000000, "updateEachSec": 5, "updateValueBy": -200, "randomnessPercentage": 5}}}]}, "expectedAppVersion": 75}, "experimentKey": "experiment", "variationKey": "variation", "priorityKey": 17, "translationResources": [{"resourceName": "resource_name", "originalResourceName": "originalResourceName"}], "eventType": "GLOBAL"}