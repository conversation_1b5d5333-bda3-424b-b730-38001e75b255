dependencies {
  implementation project(':base')
  implementation(project(":playtime_app:proto"))
  implementation(project(":orchestrator:proto"))

  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:$kotlinx_coroutines_version"
  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:$kotlinx_coroutines_version"
  implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"
  implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
  implementation "io.ktor:ktor-serialization:$ktor_version"

  implementation "io.ktor:ktor-server-servlet:$ktor_version"
  implementation "io.ktor:ktor-client-apache:$ktor_version"
  implementation "io.ktor:ktor-server-netty:$ktor_version"

  implementation "javax.servlet:javax.servlet-api:4.0.1"

  implementation "org.jetbrains.exposed:exposed-core:$exposed_version"
  implementation "org.jetbrains.exposed:exposed-jdbc:$exposed_version"
  implementation "org.jetbrains.exposed:exposed-java-time:$exposed_version"
  implementation "com.google.cloud.sql:mysql-socket-factory-connector-j-8:$mysql_socket_factory_version"
  implementation "mysql:mysql-connector-java:$mysql_connector_version"

  implementation "com.zaxxer:HikariCP:$hikari_version"

  implementation "ch.qos.logback:logback-classic:$logback_version"

  implementation "org.apache.commons:commons-csv:1.8"

  // fix for inconsistent grpc version https://github.com/grpc/grpc-java/issues/7002
  implementation "com.google.api:gax-grpc:1.57.0"

  implementation "redis.clients:jedis:3.7.0"

  testImplementation(testFixtures(project(":base")))
  testImplementation "net.bytebuddy:byte-buddy:1.10.18"
  testImplementation "net.bytebuddy:byte-buddy-agent:1.10.18"
  testImplementation 'org.mockito.kotlin:mockito-kotlin:5.2.0'
  testImplementation "org.junit.jupiter:junit-jupiter:5.6.1"
  testImplementation "com.willowtreeapps.assertk:assertk-jvm:0.22"
  testImplementation "org.mockito:mockito-inline:5.2.0"
  testImplementation "org.mockito:mockito-junit-jupiter:5.12.0"
  testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$kotlinx_coroutines_version"
  testImplementation "org.jetbrains.kotlin:kotlin-test-junit5:$kotlin_version"
  testImplementation "io.ktor:ktor-server-test-host:$ktor_version"
  testImplementation "io.ktor:ktor-client-mock:$ktor_version"
  testImplementation "com.redis:testcontainers-redis:2.0.1"
  testImplementation "org.testcontainers:junit-jupiter:$testcontainers_version"
  testImplementation "org.liquibase:liquibase-core:$liquibase_version"
}
