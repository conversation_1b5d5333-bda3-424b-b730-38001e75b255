package com.moregames.games.progress

import com.justplayapps.playtime.games.progress.proto.userOverallProgressReachedEvent
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoInteractions
import java.time.Instant

@ExtendWith(MockExtension::class)
class GameOverallProgressServiceTest(
  private val messageBus: MessageBus,
  private val userService: UserService,
  private val timeService: TimeService,
) {

  private val underTest = GameOverallProgressService(
    messageBus = messageBus,
    userService = userService,
    timeService = timeService,
  )

  @BeforeEach
  fun setUp() {
    timeService.mock({ now() }, now)
    userService.mock({ getUserCreationDate(USER_ID) }, now)
  }

  @Test
  fun `SHOULD track handleGameProgressEvent with game win for applicable game`() = runTest {
    underTest.handleGameWin(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      totalGamesCompleted = 10,
    )

    verify(messageBus).publish(
      userOverallProgressReachedEvent {
        this.userId = USER_ID
        this.applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID
        this.progress = 10
      }
    )
  }

  @Test
  fun `SHOULD NOT track handleGameProgressEvent with game win for not applicable game`() = runTest {
    underTest.handleGameWin(
      userId = USER_ID,
      applicationId = ApplicationId.WATER_SORTER_APP_ID,
      totalGamesCompleted = 10,
    )

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD track handleGameProgressEvent with level completion for applicable game`() = runTest {
    underTest.handleLevelComplete(
      userId = USER_ID,
      applicationId = ApplicationId.WATER_SORTER_APP_ID,
      level = 10,
    )

    verify(messageBus).publish(
      userOverallProgressReachedEvent {
        this.userId = USER_ID
        this.applicationId = ApplicationId.WATER_SORTER_APP_ID
        this.progress = 10
      }
    )
  }

  @Test
  fun `SHOULD NOT track handleGameProgressEvent with level completion for not applicable game`() = runTest {
    underTest.handleLevelComplete(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      level = 10,
    )

    verifyNoInteractions(messageBus)
  }

  @ParameterizedTest
  @CsvSource(
    "${ApplicationId.SOLITAIRE_VERSE_APP_ID},0,0,10,10",
    "${ApplicationId.BLOCKBUSTER_APP_ID},0,10,0,10",
    "${ApplicationId.TREASURE_MASTER_APP_ID},5,0,0,9",
    "${ApplicationId.WATER_SORTER_APP_ID},0,0,10,10",
    "${ApplicationId.SPACE_CONNECT_APP_ID},0,0,10,10",
    "${ApplicationId.MAD_SMASH_APP_ID},10,0,0,10",
    "${ApplicationId.PUZZLE_POP_BLASTER_APP_ID},10,0,0,10",
    "${ApplicationId.WORD_SEEKER_APP_ID},0,0,10,10",
    "${ApplicationId.TANGRAM_APP_ID},0,0,10,10",
    "${ApplicationId.TILE_MATCH_PRO_APP_ID},0,0,10,10",
    "${ApplicationId.DICE_LOGIC_APP_ID},10,10,10,0",
  )
  fun `SHOULD track handleUserProgress properly`(applicationId: String, score: Int, amount: Int, milestone: Int, progress: Int) = runTest {
    underTest.handleUserProgress(
      UserGameProgressEvent(
        userId = USER_ID,
        applicationId = applicationId,
        platform = AppPlatform.ANDROID,
        score = score,
        amount = amount,
        parameters = mapOf(
          "score" to score.toString(),
          "amount" to amount.toString(),
          "milestone" to milestone.toString(),
        )
      )
    )

    if (progress > 0) {
      verify(messageBus).publish(
        userOverallProgressReachedEvent {
          this.userId = USER_ID
          this.applicationId = applicationId
          this.progress = progress
        }
      )
    } else {
      verifyNoInteractions(messageBus)
    }
  }

  private companion object {
    private const val USER_ID = "userId"
    private val now = Instant.now()
  }
}