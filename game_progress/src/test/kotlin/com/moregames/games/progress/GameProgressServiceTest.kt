package com.moregames.games.progress

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.google.common.util.concurrent.UncheckedExecutionException
import com.moregames.base.app.BuildVariant
import com.moregames.base.dto.AppPlatform
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.commands.commandId
import com.moregames.base.util.Constants.JP_PLATFORM
import com.moregames.base.util.Constants.JP_USER_ID
import com.moregames.base.util.SignatureVerificationResult
import com.moregames.base.util.mock
import com.moregames.base.util.throwException
import com.moregames.games.progress.GameProgressService.Companion.COOL_DOWN_BETWEEN_REQUESTS_SECONDS
import com.moregames.games.security.UserGameKeysService
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import kotlin.test.assertFailsWith

@ExtendWith(MockExtension::class)
class GameProgressServiceTest(
  private val gameProgressBalanceUpdateManager: GameProgressBalanceUpdateManager,
  private val userGameKeysService: UserGameKeysService,
  private val buildVariant: BuildVariant,
  private val gameChallengeProgressService: GameChallengeProgressService,
  private val gameOverallProgressService: GameOverallProgressService,
) {

  private val service = GameProgressService(
    gameProgressBalanceUpdateManager = gameProgressBalanceUpdateManager,
    userGameKeysService = userGameKeysService,
    buildVariant = buildVariant,
    gameChallengeProgressService = gameChallengeProgressService,
    gameOverallProgressService = gameOverallProgressService,
  )

  private companion object {
    const val USER_ID = "userId"
    const val GAME_ID = "actually.application.id.in.lower.case"
    val platform = AppPlatform.ANDROID
    const val SIGNATURE = "123signature123"
    const val SCORE = 10
    const val AMOUNT = 20
    const val BASE_QUERY_STRING = "score=10&amount=20&game_id=$GAME_ID&signature=$SIGNATURE"
    const val BASE_QUERY_WITHOUT_SIGNATURE = "score=10&amount=20&game_id=$GAME_ID"
    val QUERY_STRING = "$BASE_QUERY_STRING&$JP_USER_ID=$USER_ID&$JP_PLATFORM=$platform"
    val gameProgressEvent = UserGameProgressEvent(
      userId = USER_ID,
      applicationId = GAME_ID,
      platform = platform,
      score = 10,
      amount = 20,
      parameters = mapOf(
        "score" to "10",
        "amount" to "20",
        "game_id" to GAME_ID,
        "JP_USER_ID" to USER_ID,
        "JP_PLATFORM" to platform.name,
        "signature" to SIGNATURE
      )
    )
  }

  @Test
  fun `SHOULD trigger balance update ON progressSigned endpoint call WHEN we have a valid signature`() {
    userGameKeysService.mock({ verifySignature(USER_ID, GAME_ID, platform, BASE_QUERY_WITHOUT_SIGNATURE, SIGNATURE) }, SignatureVerificationResult.valid())
    val gameProgressEventWithSignature = gameProgressEvent.copy(parameters = gameProgressEvent.parameters + ("signature" to SIGNATURE))
    runBlocking {
      service.processUserGameScore(
        queryString = QUERY_STRING,
        userId = USER_ID,
        applicationId = GAME_ID,
        signature = SIGNATURE,
        score = SCORE,
        amount = AMOUNT,
        platform = platform,
        isSigned = true,
        commandId = null,
        forceCommandNotification = false,
      )
    }

    verifyBlocking(userGameKeysService) { verifySignature(USER_ID, GAME_ID, platform, BASE_QUERY_WITHOUT_SIGNATURE, SIGNATURE) }
    verifyBlocking(gameProgressBalanceUpdateManager) { onNewUserProgressEvent(gameProgressEventWithSignature) }
    verifyBlocking(gameChallengeProgressService) { handleUserProgress(gameProgressEventWithSignature) }
    verifyBlocking(gameOverallProgressService) { handleUserProgress(gameProgressEventWithSignature) }
  }

  @Test
  fun `SHOULD fail with error ON progressSigned endpoint call WHEN there is no signature in query string`() {
    assertFailsWith(IllegalStateException::class) {
      runBlocking {
        service.processUserGameScore(
          queryString = QUERY_STRING,
          userId = USER_ID,
          applicationId = GAME_ID,
          signature = null,
          score = SCORE,
          amount = AMOUNT,
          platform = platform,
          isSigned = true,
          commandId = null,
          forceCommandNotification = false,
        )
      }
    }
    Unit
  }

  @Test
  fun `SHOULD trigger balance update for now ON progressSigned endpoint call WHEN signature is not valid`() {
    userGameKeysService.mock({ verifySignature(USER_ID, GAME_ID, platform, BASE_QUERY_STRING, SIGNATURE) }, SignatureVerificationResult.invalid())
    val gameProgressEventWithSignature = gameProgressEvent.copy(parameters = gameProgressEvent.parameters + ("signature" to SIGNATURE))
    runBlocking {
      service.processUserGameScore(
        queryString = QUERY_STRING,
        userId = USER_ID,
        applicationId = GAME_ID,
        signature = SIGNATURE,
        score = SCORE,
        amount = AMOUNT,
        platform = platform,
        isSigned = true,
        commandId = null,
        forceCommandNotification = false,
      )
    }

    verifyBlocking(userGameKeysService) { verifySignature(USER_ID, GAME_ID, platform, BASE_QUERY_WITHOUT_SIGNATURE, SIGNATURE) }
    verifyBlocking(gameProgressBalanceUpdateManager) { onNewUserProgressEvent(gameProgressEventWithSignature) }
    verifyBlocking(gameChallengeProgressService) { handleUserProgress(gameProgressEventWithSignature) }
    verifyBlocking(gameOverallProgressService) { handleUserProgress(gameProgressEventWithSignature) }
  }

  @Test
  fun `SHOULD trigger balance update ON progress endpoint call`() {
    runBlocking {
      service.processUserGameScore(
        queryString = QUERY_STRING,
        userId = USER_ID,
        applicationId = GAME_ID,
        signature = null,
        score = SCORE,
        amount = AMOUNT,
        platform = platform,
        isSigned = false,
        commandId = null,
        forceCommandNotification = false,
      )
    }

    verifyNoInteractions(userGameKeysService)
    verifyBlocking(gameProgressBalanceUpdateManager) { onNewUserProgressEvent(gameProgressEvent) }
    verifyBlocking(gameChallengeProgressService) { handleUserProgress(gameProgressEvent) }
    verifyBlocking(gameOverallProgressService) { handleUserProgress(gameProgressEvent) }
  }

  @Test
  fun `SHOULD fail with error on ON progress endpoint call if user id was not provided`() {
    assertFailsWith(IllegalStateException::class) {
      runBlocking {
        service.processUserGameScore(
          queryString = QUERY_STRING,
          userId = "",
          applicationId = GAME_ID,
          signature = null,
          score = SCORE,
          amount = AMOUNT,
          platform = platform,
          isSigned = false,
          commandId = null,
          forceCommandNotification = false,
        )
      }
    }
    Unit
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD propagate command id to coroutine context WHEN game progress have this argument`(forceCommandNotification: Boolean) {
    val gameProgressEventWithSignature = gameProgressEvent.copy(
      parameters = gameProgressEvent.parameters + ("signature" to SIGNATURE) + ("commandId" to "commandId"),
      forceCommandNotification = forceCommandNotification
    )
    userGameKeysService.mock({ verifySignature(USER_ID, GAME_ID, platform, BASE_QUERY_WITHOUT_SIGNATURE, SIGNATURE) }, SignatureVerificationResult.valid())
    gameProgressBalanceUpdateManager.stub {
      onBlocking { onNewUserProgressEvent(gameProgressEventWithSignature) } doSuspendableAnswer {
        assertThat(currentCoroutineContext().commandId()).isEqualTo("commandId")
      }
    }
    runBlocking {
      service.processUserGameScore(
        queryString = QUERY_STRING,
        userId = USER_ID,
        applicationId = GAME_ID,
        signature = SIGNATURE,
        score = SCORE,
        amount = AMOUNT,
        platform = platform,
        isSigned = true,
        commandId = "commandId",
        forceCommandNotification = forceCommandNotification,
      )
    }
    verifyBlocking(userGameKeysService) { verifySignature(USER_ID, GAME_ID, platform, BASE_QUERY_WITHOUT_SIGNATURE, SIGNATURE) }
    verifyBlocking(gameProgressBalanceUpdateManager) { onNewUserProgressEvent(gameProgressEventWithSignature) }
    verifyBlocking(gameChallengeProgressService) { handleUserProgress(gameProgressEventWithSignature) }
    verifyBlocking(gameOverallProgressService) { handleUserProgress(gameProgressEventWithSignature) }
  }

  @Test
  fun `SHOULD NOT skip requests ON progress endpoint call WHEN requests for different userId are sent too frequently`() {
    runBlocking {
      service.processUserGameScore(
        queryString = QUERY_STRING,
        userId = USER_ID,
        applicationId = GAME_ID,
        signature = null,
        score = SCORE,
        amount = AMOUNT,
        platform = platform,
        isSigned = false,
        commandId = null,
        forceCommandNotification = false,
      )
      service.processUserGameScore(
        queryString = QUERY_STRING,
        userId = "userId2",
        applicationId = GAME_ID,
        signature = null,
        score = SCORE,
        amount = AMOUNT,
        platform = platform,
        isSigned = false,
        commandId = null,
        forceCommandNotification = false,
      )
    }

    verifyBlocking(gameProgressBalanceUpdateManager, times(2)) { onNewUserProgressEvent(any()) }
  }

  @Test
  fun `SHOULD NOT skip requests ON progress endpoint call WHEN requests are for the same userId but are sent not too frequently`() {
    runBlocking {
      service.processUserGameScore(
        queryString = QUERY_STRING,
        userId = USER_ID,
        applicationId = GAME_ID,
        signature = null,
        score = SCORE,
        amount = AMOUNT,
        platform = platform,
        isSigned = false,
        commandId = null,
        forceCommandNotification = false,
      )
      Thread.sleep(COOL_DOWN_BETWEEN_REQUESTS_SECONDS * 1000 + 500)
      service.processUserGameScore(
        queryString = QUERY_STRING,
        userId = USER_ID,
        applicationId = GAME_ID,
        signature = null,
        score = SCORE,
        amount = AMOUNT,
        platform = platform,
        isSigned = false,
        commandId = null,
        forceCommandNotification = false,
      )
    }
    verifyBlocking(gameProgressBalanceUpdateManager, times(2)) { onNewUserProgressEvent(any()) }
  }

  @Test
  fun `SHOULD NOT mute exception WHEN unexpected UncheckedExecutionException occurs`() {
    gameProgressBalanceUpdateManager.throwException({ onNewUserProgressEvent(any()) }, UncheckedExecutionException(ArrayIndexOutOfBoundsException()))

    assertFailsWith<UncheckedExecutionException> {
      runBlocking {
        service.processUserGameScore(
          queryString = QUERY_STRING,
          userId = USER_ID,
          applicationId = GAME_ID,
          signature = SIGNATURE,
          score = SCORE,
          amount = AMOUNT,
          platform = platform,
          isSigned = true,
          commandId = null,
          forceCommandNotification = false,
        )
      }
    }
    Unit
  }
}