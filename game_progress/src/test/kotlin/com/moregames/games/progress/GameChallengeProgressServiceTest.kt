package com.moregames.games.progress

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import java.time.Instant
import java.time.temporal.ChronoUnit

@ExtendWith(MockExtension::class)
class GameChallengeProgressServiceTest(
  private val messageBus: MessageBus,
  private val abTestingService: AbTestingService,
  private val randomGenerator: RandomGenerator,
  private val userService: UserService,
  private val timeService: TimeService,
) {

  private val underTest = GameChallengeProgressService(
    messageBus = messageBus,
    abTestingService = abTestingService,
    randomGenerator = randomGenerator,
    userService = userService,
    timeService = timeService,
  )

  companion object {
    private const val USER_ID = "userId"
    private val progressEvent = UserGameProgressEvent(
      userId = USER_ID,
      platform = AppPlatform.ANDROID,
      applicationId = ApplicationId.TREASURE_MASTER_APP_ID,
      score = 7,
      amount = 101,
      parameters = mapOf(
        "milestone" to "2",
        "is_boss" to "true",
      ),
    )
  }

  @Test
  fun `SHOULD propagate TreasureMasterProgress WHEN user is a participant and game is TM`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, Variations.SHOW_CHALLENGES)
    runBlocking { underTest.handleUserProgress(progressEvent.copy(applicationId = ApplicationId.TREASURE_MASTER_APP_ID)) }
    verifyBlocking(abTestingService) { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }
    verifyBlocking(messageBus) {
      publish(
        UserChallengeProgressDto.TmProgressDto(
          userId = USER_ID,
          applicationId = ApplicationId.TREASURE_MASTER_APP_ID,
          score = 7,
          isBoss = true,
          level = 15,
        )
      )
    }
  }

  @Test
  fun `SHOULD propagate AmountMilestoneProgress WHEN user is a participant and game is Tangram`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, Variations.SHOW_CHALLENGES)
    runBlocking { underTest.handleUserProgress(progressEvent.copy(applicationId = ApplicationId.TANGRAM_APP_ID)) }
    verifyBlocking(abTestingService) { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }
    verifyBlocking(messageBus) {
      publish(
        UserChallengeProgressDto.AmountMilestoneProgressDto(
          userId = USER_ID,
          applicationId = ApplicationId.TANGRAM_APP_ID,
          milestone = 2,
          amount = 101,
        )
      )
    }
  }


  @ParameterizedTest
  @ValueSource(
    strings = [
      ApplicationId.TILE_MATCH_PRO_APP_ID,
      ApplicationId.PUZZLE_POP_BLASTER_APP_ID,
      ApplicationId.SOLITAIRE_VERSE_APP_ID,
      ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID,
    ]
  )
  fun `SHOULD propagate MilestoneProgress WHEN user is a participant and game is from the list`(
    applicationId: String,
  ) {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, Variations.SHOW_CHALLENGES)
    runBlocking { underTest.handleUserProgress(progressEvent.copy(applicationId = applicationId)) }
    verifyBlocking(abTestingService) { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }
    verifyBlocking(messageBus) {
      publish(
        UserChallengeProgressDto.MilestoneProgressDto(
          userId = USER_ID,
          applicationId = applicationId,
          milestone = 2
        )
      )
    }
  }

  @ParameterizedTest
  @ValueSource(
    strings = [
      ApplicationId.SPACE_CONNECT_APP_ID,
      ApplicationId.WATER_SORTER_APP_ID,
      ApplicationId.MAD_SMASH_APP_ID,
      ApplicationId.BALL_BOUNCE_APP_ID,
      ApplicationId.SPIRAL_DROP_APP_ID,
      ApplicationId.WORD_KITCHEN_APP_ID,
      ApplicationId.BLOCK_HOLE_CLASH_APP_ID,
      ApplicationId.BUBBLE_POP_APP_ID,
      ApplicationId.SUGAR_RUSH_APP_ID,
      ApplicationId.PIN_MASTER_APP_ID,
      ApplicationId.HEX_MATCH_APP_ID,
      ApplicationId.AERO_ESCAPE_APP_ID,
      ApplicationId.BUBBLE_CHIEF_APP_ID,
      ApplicationId.ATLANTIS_BOUNCE_APP_ID,
    ]
  )
  fun `SHOULD propagate unique id WHEN game is from the list`(applicationId: String) {
    randomGenerator.mock({ nextUUID() }, "uniqueId")
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, Variations.SHOW_CHALLENGES)
    runBlocking { underTest.handleUserProgress(progressEvent.copy(applicationId = applicationId)) }
    verifyBlocking(abTestingService) { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }
    verifyBlocking(messageBus) {
      publish(
        UserChallengeProgressDto.LevelIdProgressDto(
          userId = USER_ID,
          applicationId = applicationId,
          levelId = "uniqueId",
        )
      )
    }
  }

  @Test
  fun `SHOULD propagate word seeker unique id WHEN amount is 4`() {
    randomGenerator.mock({ nextUUID() }, "uniqueId")
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, Variations.SHOW_CHALLENGES)
    runBlocking { underTest.handleUserProgress(progressEvent.copy(applicationId = ApplicationId.WORD_SEEKER_APP_ID, amount = 4)) }
    verifyBlocking(abTestingService) { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }
    verifyBlocking(messageBus) {
      publish(
        UserChallengeProgressDto.LevelIdProgressDto(
          userId = USER_ID,
          applicationId = ApplicationId.WORD_SEEKER_APP_ID,
          levelId = "uniqueId",
        )
      )
    }
  }

  @Test
  fun `SHOULD not propagate word seeker unique id WHEN amount is not 4`() {
    randomGenerator.mock({ nextUUID() }, "uniqueId")
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, Variations.SHOW_CHALLENGES)
    runBlocking { underTest.handleUserProgress(progressEvent.copy(applicationId = ApplicationId.WORD_SEEKER_APP_ID, amount = 3)) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD not propagate message WHEN user is on default variation`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, DEFAULT)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.FTUE) }, DEFAULT)
    val now = Instant.parse("2025-12-03T10:15:30.00Z")
    timeService.mock( { now()}, now)
    userService.mock( { getUserCreationDate(USER_ID)}, now.minus(3, ChronoUnit.DAYS))
    runBlocking { underTest.handleUserProgress(progressEvent) }
    verifyNoInteractions(messageBus)
    verifyBlocking(abTestingService) { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }
  }

  @Test
  fun `SHOULD not propagate message WHEN user is on FTUE_PROMOS_D0D5_CHALLNGES_D1D3 variation but user is  too old`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, DEFAULT)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.FTUE) }, Variations.FTUE_PROMOS_D0D5_CHALLENGES_D1D3)
    val now = Instant.parse("2025-12-03T10:15:30.00Z")
    timeService.mock( { now()}, now)
    userService.mock( { getUserCreationDate(USER_ID)}, now.minus(15, ChronoUnit.DAYS))
    runBlocking { underTest.handleUserProgress(progressEvent) }
    verifyNoInteractions(messageBus)
    verifyBlocking(abTestingService) { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }
  }

  @Test
  fun `SHOULD propagate message WHEN user is on FTUE_PROMOS_D0D5_CHALLNGES_D1D3 variation`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_CHALLENGES) }, DEFAULT)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.FTUE) }, Variations.FTUE_PROMOS_D0D5_CHALLENGES_D1D3)
    val now = Instant.parse("2025-12-03T10:15:30.00Z")
    timeService.mock({ now() }, now)
    userService.mock({ getUserCreationDate(USER_ID) }, now.minus(1, ChronoUnit.DAYS))
    runBlocking { underTest.handleUserProgress(progressEvent) }
    verifyBlocking(messageBus) {
      publish(
        UserChallengeProgressDto.TmProgressDto(
          userId = USER_ID,
          applicationId = ApplicationId.TREASURE_MASTER_APP_ID,
          score = 7,
          isBoss = true,
          level = 15,
        )
      )
    }
  }

  @Test
  fun `SHOULD not propagate message WHEN applicationId is not for propagating`() {
    runBlocking { underTest.handleUserProgress(progressEvent.copy(applicationId = ApplicationId.SUDOKU_APP_ID)) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD not propagate message WHEN platform IOS`() {
    runBlocking { underTest.handleUserProgress(progressEvent.copy(platform = AppPlatform.IOS)) }
    verifyNoInteractions(messageBus)
    verifyNoInteractions(abTestingService)
  }
}