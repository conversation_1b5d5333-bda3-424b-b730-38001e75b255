package com.moregames.games

import assertk.assertThat
import com.google.cloud.tasks.v2.CloudTasksClient
import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.util.Modules
import com.justplayapps.service.rewarding.earnings.proto.EmApiGrpc
import com.moregames.base.GuiceExtension
import com.moregames.base.app.BuildVariant
import com.moregames.base.bus.MessageBusModule
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.config.ServicesRegistry
import com.moregames.base.grpc.client.GrpcClientModule
import com.moregames.base.messaging.auth.AuthService
import com.moregames.base.redis.RedisExtension
import com.moregames.base.secret.SecretService
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.table.TestDataSourceProvider
import com.moregames.games.app.ApiManager
import com.moregames.games.app.CoreModule
import com.redis.testcontainers.RedisContainer
import io.grpc.inprocess.InProcessChannelBuilder
import io.ktor.application.*
import io.ktor.server.testing.*
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.condition.DisabledIfSystemProperty
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.RegisterExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock

@ExtendWith(DatabaseExtension::class)
@ExtendWith(RedisExtension::class)
@DisabledIfSystemProperty(named = "noDocker", matches = "1")
class ComponentTest(
  redisContainer: RedisContainer,
) {

  private val application: Application = withTestApplication { application }

  @JvmField
  @RegisterExtension
  val guiceExtension = GuiceExtension(
    Modules.override(
      CoreModule(
        application = application,
        buildVariant = BuildVariant.LOCAL,
        dataSourceProvider = TestDataSourceProvider()
      ),
      MessageBusModule {
        rootPackages("com.moregames.games.progress.buseffects")
      },
      GrpcClientModule(
        ServicesRegistry.PLAYTIME,
        EmApiGrpc.EmApiStub::class, EmApiGrpc::newStub,
        { InProcessChannelBuilder.forName("local").build() })
    ).with(TestModule(redisContainer))
  ) {
    runBlocking { getInstance(ApiManager::class.java).initApi() }
  }

  @Test
  fun `context starts up`() {
    val injector = guiceExtension.getInjector()
    println("Context starts up")
    val database = injector.getInstance(Database::class.java)
    assertThat { database != null }
  }

  class TestModule(private val redisContainer: RedisContainer) : AbstractModule() {
    @Provides
    @Singleton
    fun secretService() = mock<SecretService> {
      onBlocking { secretValue(any()) } doReturn "**********"
    }

    @Provides
    @Singleton
    fun cloudTasksClient() = mock<CloudTasksClient>()

    @Provides
    @Singleton
    fun applicationConfig(): ApplicationConfig {
      return mock {
        on { redisHost } doReturn redisContainer.host
        on { redisPort } doReturn redisContainer.firstMappedPort
        on { justplayMarket } doReturn "justplay-test"
      }
    }

    @Provides
    @Singleton
    fun authService() = mock<AuthService>()
  }
}
