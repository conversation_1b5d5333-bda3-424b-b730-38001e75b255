package com.moregames.games.progress

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.dto.AppPlatform
import com.moregames.base.table.*
import com.moregames.base.util.TimeService
import com.moregames.base.util.insertOrAdd
import com.moregames.base.util.toBeginningOf5MinInterval
import org.jetbrains.exposed.sql.*
import java.lang.Integer.max
import java.math.BigDecimal
import java.sql.Timestamp
import java.time.Instant

@Singleton
class UserGameBalancePersistenceService @Inject constructor(database: Database, private val timeService: TimeService) : BasePersistenceService(database) {

  suspend fun getGameIdByApplicationId(applicationId: String, platform: AppPlatform): GameId? =
    dbQuery {
      GamesTable
        .slice(GamesTable.id, GamesTable.isDisabled)
        .select { (GamesTable.applicationId eq applicationId) and (GamesTable.platform eq platform.name) }
        .firstOrNull()
        ?.let {
          GameId(it[GamesTable.id], it[GamesTable.isDisabled])
        }
    }

  suspend fun getApplicationIdByGameIdOrNull(gameId: Int): String? = dbQuery {
    GamesTable
      .slice(GamesTable.applicationId)
      .select { (GamesTable.id eq gameId) and (GamesTable.isDisabled eq false) }
      .firstOrNull()
      ?.get(GamesTable.applicationId)
  }

  suspend fun addGameCoinsToUser(userId: String, gameId: Int, coinsEarned: Int, isFirstCoins: Boolean) {
    val now = timeService.now()
    dbQuery {
      UserGameBalanceTotalsTable.insertOrAdd(UserGameBalanceTotalsTable.coins) {
        it[UserGameBalanceTotalsTable.userId] = userId
        it[UserGameBalanceTotalsTable.gameId] = gameId
        it[coins] = coinsEarned
        it[lastPlayedAt] = now
      }
      // TODO: switch to UserGameBalanceTotalsTable.firstPlayedAt, but backfill needed
      if (isFirstCoins) {
        UserGameFirstPlayedTable.insertIgnore {
          it[UserGameFirstPlayedTable.userId] = userId
          it[UserGameFirstPlayedTable.gameId] = gameId
          it[firstPlayedAt] = now
        }
      }
    }
    dbQuery {
      UserGameCoinsBalanceByPeriodsTable.insertOrAdd(
        UserGameCoinsBalanceByPeriodsTable.coins,
        UserGameCoinsBalanceByPeriodsTable.coinsTransactionsCount
      ) {
        it[periodStart] = now.toBeginningOf5MinInterval()
        it[UserGameCoinsBalanceByPeriodsTable.userId] = userId
        it[UserGameCoinsBalanceByPeriodsTable.gameId] = gameId
        it[coins] = coinsEarned
        it[coinsTransactionsCount] = 1
      }
    }
  }

  suspend fun addFractionalGameCoinsToUser(
    userId: String,
    gameId: Int,
    coinsEarned: BigDecimal,
    calculatedCoins: Int,
    blockedCoins: Int,
  ) {
    val now = timeService.now()
    dbQuery {
      UserGameBalanceTotalsEm2Table.insertOrAdd(
        UserGameBalanceTotalsEm2Table.coins, UserGameBalanceTotalsEm2Table.calculatedCoins, UserGameBalanceTotalsEm2Table.blockedCoins
      ) {
        it[UserGameBalanceTotalsEm2Table.userId] = userId
        it[UserGameBalanceTotalsEm2Table.gameId] = gameId
        it[coins] = coinsEarned
        it[UserGameBalanceTotalsEm2Table.calculatedCoins] = calculatedCoins
        it[UserGameBalanceTotalsEm2Table.blockedCoins] = blockedCoins
      }
    }
    dbQuery {
      UserGameCoinsBalanceByPeriodsEm2Table.insertOrAdd(
        UserGameCoinsBalanceByPeriodsEm2Table.blockedCoins,
        UserGameCoinsBalanceByPeriodsEm2Table.calculatedCoins,
        UserGameCoinsBalanceByPeriodsEm2Table.coins,
        UserGameCoinsBalanceByPeriodsEm2Table.coinsTransactionsCount
      ) {
        it[periodStart] = now.toBeginningOf5MinInterval()
        it[UserGameCoinsBalanceByPeriodsEm2Table.userId] = userId
        it[UserGameCoinsBalanceByPeriodsEm2Table.gameId] = gameId
        it[coins] = coinsEarned
        it[UserGameCoinsBalanceByPeriodsEm2Table.calculatedCoins] = calculatedCoins
        it[UserGameCoinsBalanceByPeriodsEm2Table.blockedCoins] = blockedCoins
        it[coinsTransactionsCount] = 1
      }
    }
  }

  suspend fun getGameCoinsDataByUser(userId: String, gameId: Int): UserGameCoinsData =
    dbQuery {
      UserGameBalanceTotalsTable
        .slice(UserGameBalanceTotalsTable.coins, UserGameBalanceTotalsTable.lastPlayedAt)
        .select { (UserGameBalanceTotalsTable.userId eq userId) and (UserGameBalanceTotalsTable.gameId eq gameId) }
        .firstOrNull()?.let {
          UserGameCoinsData(
            coins = it[UserGameBalanceTotalsTable.coins],
            lastPlayedAt = it[UserGameBalanceTotalsTable.lastPlayedAt],
          )
        } ?: UserGameCoinsData(coins = 0)
    }

  suspend fun hasCoinsForAnyGame(userId: String): Boolean =
    dbQuery {
      UserGameBalanceTotalsTable
        .slice(UserGameBalanceTotalsTable.coins)
        .select { (UserGameBalanceTotalsTable.userId eq userId) and (UserGameBalanceTotalsTable.coins greater 0) }
        .any()
    }

  suspend fun hasCoinsForAnyGameEm2(userId: String): Boolean =
    dbQuery {
      UserGameBalanceTotalsEm2Table
        .slice(UserGameBalanceTotalsEm2Table.coins)
        .select { (UserGameBalanceTotalsEm2Table.userId eq userId) and (UserGameBalanceTotalsEm2Table.coins greater BigDecimal.ZERO) }
        .any()
    }

  suspend fun getGameCoinsForUsers(userIds: List<String>): List<UserGameCoins> =
    dbQuery {
      val decimalCoins = UserGameBalanceTotalsTable.coins.castTo<BigDecimal>(DecimalColumnType(18, 6)).alias("decimalCoins")
      UserGameBalanceTotalsTable
        .slice(
          UserGameBalanceTotalsTable.userId, UserGameBalanceTotalsTable.gameId,
          UserGameBalanceTotalsTable.lastPlayedAt, decimalCoins
        )
        .select { UserGameBalanceTotalsTable.userId inList userIds }
        .unionAll(
          UserGameBalanceTotalsEm2Table
            .slice(
              UserGameBalanceTotalsEm2Table.userId, UserGameBalanceTotalsEm2Table.gameId,
              UserGameBalanceTotalsEm2Table.lastPlayedAt, UserGameBalanceTotalsEm2Table.coins
            )
            .select { UserGameBalanceTotalsEm2Table.userId inList userIds }
        )
        .map {
          UserGameCoins(
            userId = it[UserGameBalanceTotalsTable.userId],
            gameId = it[UserGameBalanceTotalsTable.gameId],
            lastPlayedAt = it[UserGameBalanceTotalsTable.lastPlayedAt],
            coins = it[decimalCoins]
          )
        }
    }

  suspend fun getGameFractionalCoinsDataByUser(userId: String, gameId: Int, beginningOf5MinInterval: Instant): UserGameCoinsDataEm2 =
    dbQuery {
      val otherGameLastPlayedAt = UserGameBalanceTotalsEm2Table
        .slice(UserGameBalanceTotalsEm2Table.lastPlayedAt.max())
        .select {
          (UserGameBalanceTotalsEm2Table.userId eq userId) and
            (UserGameBalanceTotalsEm2Table.gameId neq gameId)
        }
        .let {
          wrapAsExpression<Timestamp>(it)
        }

      UserGameBalanceTotalsEm2Table
        .leftJoin(
          otherTable = UserGameCoinsBalanceByPeriodsEm2Table,
          onColumn = { UserGameBalanceTotalsEm2Table.userId },
          otherColumn = { UserGameCoinsBalanceByPeriodsEm2Table.userId },
          additionalConstraint = {
            (UserGameBalanceTotalsEm2Table.gameId eq UserGameCoinsBalanceByPeriodsEm2Table.gameId) and
              (UserGameCoinsBalanceByPeriodsEm2Table.periodStart eq beginningOf5MinInterval)
          })
        .slice(
          UserGameBalanceTotalsEm2Table.coins, UserGameBalanceTotalsEm2Table.lastPlayedAt, UserGameBalanceTotalsEm2Table.calculatedCoins,
          UserGameCoinsBalanceByPeriodsEm2Table.calculatedCoins, UserGameCoinsBalanceByPeriodsEm2Table.coinsTransactionsCount,
          UserGameCoinsBalanceByPeriodsEm2Table.blockedCoins, UserGameBalanceTotalsEm2Table.firstPlayedAt,
          otherGameLastPlayedAt
        )
        .select { (UserGameBalanceTotalsEm2Table.userId eq userId) and (UserGameBalanceTotalsEm2Table.gameId eq gameId) }
        .firstOrNull()
        ?.let { row ->
          UserGameCoinsDataEm2(
            coins = row[UserGameBalanceTotalsEm2Table.coins],
            calculatedCoins = row[UserGameBalanceTotalsEm2Table.calculatedCoins],
            fiveMinIntervalCoins = max(
              (row.getOrNull(UserGameCoinsBalanceByPeriodsEm2Table.calculatedCoins) ?: 0) -
                (row.getOrNull(UserGameCoinsBalanceByPeriodsEm2Table.blockedCoins) ?: 0), 0
            ),
            fiveMinIntervalCoinsTransactionsCount = row.getOrNull(UserGameCoinsBalanceByPeriodsEm2Table.coinsTransactionsCount) ?: 0,
            lastPlayedAt = row[UserGameBalanceTotalsEm2Table.lastPlayedAt],
            firstPlayedAt = row[UserGameBalanceTotalsEm2Table.firstPlayedAt],
            otherGameLastPlayedAt = row[otherGameLastPlayedAt]?.toInstant()
          )
        }
        ?: UserGameCoinsDataEm2(coins = BigDecimal.ZERO, calculatedCoins = 0, fiveMinIntervalCoins = 0, fiveMinIntervalCoinsTransactionsCount = 0)
    }

  data class UserGameCoinsDataEm2(
    val coins: BigDecimal,
    val calculatedCoins: Int,
    val fiveMinIntervalCoins: Int,
    val fiveMinIntervalCoinsTransactionsCount: Int,
    val lastPlayedAt: Instant? = null,
    val firstPlayedAt: Instant? = null,
    val otherGameLastPlayedAt: Instant? = null
  )

  data class UserGameCoinsData(
    val coins: Int,
    val lastPlayedAt: Instant? = null
  )

  data class UserGameCoins(
    val userId: String,
    val gameId: Int,
    val coins: BigDecimal,
    val lastPlayedAt: Instant? = null
  )

  data class GameId(
    val id: Int,
    val isDisabled: Boolean
  )
}