package com.moregames.games.progress

import com.google.inject.Inject
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.messaging.dto.UserChallengeProgressDto.AmountMilestoneProgressDto
import com.moregames.base.messaging.dto.UserChallengeProgressDto.MilestoneProgressDto
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.TimeService
import java.time.temporal.ChronoUnit
import javax.inject.Singleton

@Singleton
class GameChallengeProgressService @Inject constructor(
  private val messageBus: MessageBus,
  private val abTestingService: AbTestingService,
  private val randomGenerator: RandomGenerator,
  private val userService: UserService,
  private val timeService: TimeService,
) {

  companion object {
    const val FTUE_PERIOD = 7L
  }

  suspend fun handleUserProgress(userProgressEvent: UserGameProgressEvent) {
    val userChallengeProgressDto = createUserProgressDto(userProgressEvent) ?: return
    doHandleChallengeProgress(userChallengeProgressDto, platform = userProgressEvent.platform)
  }

  suspend fun handleChallengeProgress(userChallengeProgressDto: UserChallengeProgressDto) {
    doHandleChallengeProgress(userChallengeProgressDto, platform = AppPlatform.ANDROID)
  }

  private suspend fun doHandleChallengeProgress(userChallengeProgressDto: UserChallengeProgressDto, platform: AppPlatform) {
    if (!userChallengeProgressDto.isApplicableForTracking(platform)) return
    messageBus.publish(userChallengeProgressDto)
  }

  private fun createUserProgressDto(event: UserGameProgressEvent): UserChallengeProgressDto? =
    when (event.applicationId) {
      //score
      ApplicationId.TREASURE_MASTER_APP_ID ->
        createTmDto(event)
      //milestone
      ApplicationId.PUZZLE_POP_BLASTER_APP_ID,
      ApplicationId.SOLITAIRE_VERSE_APP_ID,
      ApplicationId.TILE_MATCH_PRO_APP_ID,
      ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID ->
        createMilestoneDto(event)
      //milestone+amount
      ApplicationId.TANGRAM_APP_ID ->
        createAmountMilestoneDto(event)
      //unique level
      ApplicationId.SPACE_CONNECT_APP_ID,
      ApplicationId.WATER_SORTER_APP_ID,
      ApplicationId.WORD_SEEKER_APP_ID,
      ApplicationId.MAD_SMASH_APP_ID,
      ApplicationId.BALL_BOUNCE_APP_ID,
      ApplicationId.SPIRAL_DROP_APP_ID,
      ApplicationId.WORD_KITCHEN_APP_ID,
      ApplicationId.BLOCK_HOLE_CLASH_APP_ID,
      ApplicationId.BUBBLE_POP_APP_ID,
      ApplicationId.SUGAR_RUSH_APP_ID,
      ApplicationId.PIN_MASTER_APP_ID,
      ApplicationId.HEX_MATCH_APP_ID,
      ApplicationId.AERO_ESCAPE_APP_ID,
      ApplicationId.BUBBLE_CHIEF_APP_ID,
      ApplicationId.ATLANTIS_BOUNCE_APP_ID,
         -> createLevelIdDto(event)
      else -> null
    }

  private fun createLevelIdDto(event: UserGameProgressEvent): UserChallengeProgressDto? {
    if (event.applicationId == ApplicationId.WORD_SEEKER_APP_ID && event.amount != 4) return null
    return UserChallengeProgressDto.LevelIdProgressDto(
      userId = event.userId,
      applicationId = event.applicationId,
      levelId = randomGenerator.nextUUID()
    )
  }

  private fun createAmountMilestoneDto(event: UserGameProgressEvent): UserChallengeProgressDto? {
    val milestone = event.milestone ?: return null
    val amount = event.amount ?: return null
    return AmountMilestoneProgressDto(
      userId = event.userId,
      applicationId = event.applicationId,
      milestone = milestone,
      amount = amount
    )
  }

  private fun createMilestoneDto(event: UserGameProgressEvent): MilestoneProgressDto? {
    val milestone = event.milestone ?: return null
    return MilestoneProgressDto(
      userId = event.userId,
      applicationId = event.applicationId,
      milestone = milestone,
    )
  }

  private fun createTmDto(event: UserGameProgressEvent): UserChallengeProgressDto? {
    if (event.score == null) return null
    return UserChallengeProgressDto.TmProgressDto(
      userId = event.userId,
      applicationId = event.applicationId,
      score = event.score,
      isBoss = event.isBoss,
      level = event.tmLevel,
    )
  }

  private suspend fun UserChallengeProgressDto.isApplicableForTracking(platform: AppPlatform): Boolean = with(this) {
    platform == AppPlatform.ANDROID && userIsParticipantOfChallengeExperiment(userId)
  }

  private suspend fun userIsParticipantOfChallengeExperiment(userId: String): Boolean {
    return abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_CHALLENGES) != DEFAULT
      || (ChronoUnit.DAYS.between(userService.getUserCreationDate(userId), timeService.now()) <= FTUE_PERIOD &&
      abTestingService.assignedVariationValue(userId, ClientExperiment.FTUE) == Variations.FTUE_PROMOS_D0D5_CHALLENGES_D1D3)
  }
}