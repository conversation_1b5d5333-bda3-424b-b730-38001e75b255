package com.moregames.games.progress

import com.google.inject.Inject
import com.justplayapps.playtime.games.progress.proto.userOverallProgressReachedEvent
import com.moregames.base.bus.MessageBus
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.TimeService
import java.time.temporal.ChronoUnit
import javax.inject.Singleton

@Singleton
class GameOverallProgressService @Inject constructor(
  private val messageBus: MessageBus,
  private val userService: UserService,
  private val timeService: TimeService,
) {

  companion object {
    const val FTUE_PERIOD = 30
    val gamesWithWinsToTrack = setOf(
      ApplicationId.SOLITAIRE_VERSE_APP_ID,
      ApplicationId.BLOCKBUSTER_APP_ID,
    )
    val gameWithLevelCompletionsToTrack = setOf(
      ApplicationId.TREASURE_MASTER_APP_ID,
      ApplicationId.WATER_SORTER_APP_ID,
      ApplicationId.SPACE_CONNECT_APP_ID,
      ApplicationId.MAD_SMASH_APP_ID,
      ApplicationId.PUZZLE_POP_BLASTER_APP_ID,
      ApplicationId.WORD_SEEKER_APP_ID,
      ApplicationId.TANGRAM_APP_ID,
      ApplicationId.TILE_MATCH_PRO_APP_ID,
    )
  }

  suspend fun handleUserProgress(event: UserGameProgressEvent) {
    if (event.applicationId !in (gamesWithWinsToTrack + gameWithLevelCompletionsToTrack)) return
    if (!isApplicableForTracking(event.userId)) return
    val progress = event.extractOverallProgress() ?: return

    emitEvent(userId = event.userId, applicationId = event.applicationId, progress = progress)
  }

  suspend fun handleLevelComplete(userId: String, applicationId: String, level: Int) {
    if (applicationId !in gameWithLevelCompletionsToTrack) return
    if (!isApplicableForTracking(userId)) return

    emitEvent(userId = userId, applicationId = applicationId, progress = level)
  }

  suspend fun handleGameWin(userId: String, applicationId: String, totalGamesCompleted: Int) {
    if (applicationId !in gamesWithWinsToTrack) return
    if (!isApplicableForTracking(userId)) return

    emitEvent(userId = userId, applicationId = applicationId, progress = totalGamesCompleted)
  }

  private fun UserGameProgressEvent.extractOverallProgress(): Int? = when (applicationId) {
    // game wins
    ApplicationId.SOLITAIRE_VERSE_APP_ID -> milestone
    ApplicationId.BLOCKBUSTER_APP_ID -> amount

    // level completions
    ApplicationId.TREASURE_MASTER_APP_ID -> tmLevel
    ApplicationId.WATER_SORTER_APP_ID -> milestone
    ApplicationId.SPACE_CONNECT_APP_ID -> milestone ?: amount
    ApplicationId.MAD_SMASH_APP_ID -> score
    ApplicationId.PUZZLE_POP_BLASTER_APP_ID -> score
    ApplicationId.WORD_SEEKER_APP_ID -> milestone ?: amount
    ApplicationId.TANGRAM_APP_ID -> milestone
    ApplicationId.TILE_MATCH_PRO_APP_ID -> milestone

    else -> null
  }

  private suspend fun emitEvent(userId: String, applicationId: String, progress: Int) {
    messageBus.publish(
      userOverallProgressReachedEvent {
        this.userId = userId
        this.applicationId = applicationId
        this.progress = progress
      }
    )
  }

  private suspend fun isApplicableForTracking(userId: String) =
    ChronoUnit.DAYS.between(userService.getUserCreationDate(userId), timeService.now()) <= FTUE_PERIOD
}
