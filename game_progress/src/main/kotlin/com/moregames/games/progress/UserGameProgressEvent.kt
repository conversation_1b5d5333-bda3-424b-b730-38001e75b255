package com.moregames.games.progress

import com.moregames.base.dto.AppPlatform

data class UserGameProgressEvent(
  val userId: String,
  val applicationId: String,
  val platform: AppPlatform,
  val score: Int?,
  val amount: Int?,
  val parameters: Map<String, String>,
  val forceCommandNotification: Boolean = false,
)

val UserGameProgressEvent.milestone: Int?
  get() = parameters["milestone"]?.toIntOrNull()

val UserGameProgressEvent.isBoss: Boolean
  get() = parameters["is_boss"]?.lowercase()?.toBooleanStrictOrNull() == true

val UserGameProgressEvent.tmLevel: Int?
  get() = when {
    score == null -> null
    score == 1 -> 2
    score == 2 -> 4
    score == 3 && isBoss -> 5
    score == 3 && !isBoss -> 6
    score == 4 -> 8
    score == 5 && !isBoss -> 9
    score == 5 && isBoss -> 10
    score == 6 -> 13
    score == 7 && !isBoss -> 14
    score == 7 && isBoss -> 15
    score == 8 -> 18
    score == 9 && !isBoss -> 19
    score == 9 && isBoss -> 20
    score > 9 -> 20
    else -> null
  }
