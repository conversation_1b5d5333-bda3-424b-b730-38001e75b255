package com.moregames.games.progress

import com.google.inject.Inject
import com.moregames.base.app.BuildVariant
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.commands.ExtendedCommandIdContextElement
import com.moregames.base.util.SignatureVerificationResult
import com.moregames.base.util.logger
import com.moregames.games.security.UserGameKeysService
import io.ktor.http.*
import io.ktor.util.*
import kotlinx.coroutines.withContext
import java.util.*
import javax.inject.Singleton

@Singleton
class GameProgressService @Inject constructor(
  private val gameProgressBalanceUpdateManager: GameProgressBalanceUpdateManager,
  private val userGameKeysService: UserGameKeysService,
  private val buildVariant: BuildVariant,
  private val gameChallengeProgressService: GameChallengeProgressService,
  private val gameOverallProgressService: GameOverallProgressService,
) {
  companion object {
    const val COOL_DOWN_BETWEEN_REQUESTS_SECONDS = 1L
    const val SIGNATURE_PARAM_NAME = "signature"
  }

  suspend fun processUserGameScore(
    queryString: String,
    userId: String,
    applicationId: String,
    signature: String?,
    score: Int?,
    amount: Int?,
    platform: AppPlatform,
    isSigned: Boolean,
    commandId: String?,
    forceCommandNotification: Boolean
  ) {
    if (userId.isEmpty()) {
      throw IllegalStateException("There is no JP_USER_ID in request came from orchestrator")
    }
    if (isSigned && signature.isNullOrEmpty()) {
      throw IllegalStateException("There is no SIGNATURE in signed request came from orchestrator")
    }
    if (isSigned) {
      queryString.substringBefore("&${SIGNATURE_PARAM_NAME}")
        .let { signedQuery ->
          val verificationResult = userGameKeysService.verifySignature(
            userId = userId,
            applicationId = applicationId,
            platform = platform,
            signedText = signedQuery,
            signatureText = signature.orEmpty()
          )
          if (verificationResult?.isSignatureValid == false) {
            logger().info("Invalid signature for $queryString")
          }
          if (buildVariant == BuildVariant.TEST && verificationResult == SignatureVerificationResult.valid()) {
            logger().info("Valid signature for user: $userId signedQuery: $signedQuery signature: $signature")
          }
        }
    }

    val userProgressEvent = createUserProgressEvent(queryString, commandId, userId, applicationId, platform, score, amount, forceCommandNotification)
      ?: return

    updateUserBalance(userProgressEvent, commandId)
    val apiLevel = userProgressEvent.parameters["apiLevel"]?.toInt()
    if (apiLevel == null || apiLevel < 2) { // if game supports apiLevel 2 - then it already uses dedicated challenges endpoint
      gameChallengeProgressService.handleUserProgress(userProgressEvent)
    }
    gameOverallProgressService.handleUserProgress(userProgressEvent)
  }

  private fun createUserProgressEvent(
    queryString: String,
    commandId: String?,
    userId: String,
    applicationId: String,
    platform: AppPlatform,
    score: Int?,
    amount: Int?,
    forceCommandNotification: Boolean,
  ): UserGameProgressEvent? {
    return try {
      val parameters = parseQueryString(queryString).flattenEntries().toMap().toMutableMap()
      if (commandId != null) parameters += ("commandId" to commandId)
      UserGameProgressEvent(
        userId = userId,
        applicationId = applicationId.lowercase(Locale.US),
        platform = platform,
        score = score,
        amount = amount,
        parameters = parameters,
        forceCommandNotification = forceCommandNotification,
      )
    } catch (e: Exception) {
      logger().error(e.message, e)
      null
    }
  }

  private suspend fun updateUserBalance(userProgressEvent: UserGameProgressEvent, commandId: String?) {
    try {
      withContext(ExtendedCommandIdContextElement(commandId)) {
        gameProgressBalanceUpdateManager.onNewUserProgressEvent(userProgressEvent)
      }
    } catch (_: GameNotFoundException) {
      logger().warn("Game with application ID '${userProgressEvent.applicationId}' not found")
    } catch (e: CoinCalculationMissingException) {
      logger().warn(e.message)
    }
  }
}