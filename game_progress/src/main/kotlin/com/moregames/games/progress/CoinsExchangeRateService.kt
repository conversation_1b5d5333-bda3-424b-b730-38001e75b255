package com.moregames.games.progress

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.app.BuildVariant
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.buildCache
import kotlinx.coroutines.async
import java.math.BigDecimal
import javax.inject.Singleton

// TODO: move to game-progress when all EM2 experiments are finished ( == when no usage in "default" module )

@Singleton
class CoinsExchangeRateService @Inject constructor(
  private val coinsExchangeRatePersistenceService: CoinsExchangeRatePersistenceService,
  buildVariant: BuildVariant,
  private val coroutineScope: Provider<IoCoroutineScope>,
) {

  companion object {
    const val CACHE_KEY_STUB = "stub"
  }

  private val exchangeRateCache = buildCache(buildVariant, expireAfter = 5L) { _: String ->
    coroutineScope.get().async {
      coinsExchangeRatePersistenceService.getExchangeRate()
    }
  }

  suspend fun getExchangeRate(): BigDecimal =
    exchangeRateCache.get(CACHE_KEY_STUB).await()
}