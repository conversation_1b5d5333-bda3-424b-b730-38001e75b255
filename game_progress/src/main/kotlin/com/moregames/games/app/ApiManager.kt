package com.moregames.games.app

import com.google.inject.Inject
import com.moregames.base.app.BuildVariant
import com.moregames.base.exceptions.ParameterRequiredException
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.ktor.cronInterceptor
import com.moregames.base.messaging.auth.HttpClientAuthInterceptor
import com.moregames.base.util.cronLogger
import com.moregames.base.util.installLogging
import com.moregames.base.util.logger
import com.moregames.games.TaskController
import com.moregames.games.cron.CronController
import com.moregames.games.progress.GameParameterRequiredException
import com.moregames.games.progress.calculation.SwitchGameCoinCalculationModeController
import com.moregames.games.security.UserGameKeysController
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import org.slf4j.Logger

class ApiManager @Inject constructor(
  private val userGameKeysController: UserGameKeysController,
  private val cronController: CronController,
  private val taskController: TaskController,
  private val buildVariant: BuildVariant,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val switchGameCoinCalculationModeController: SwitchGameCoinCalculationModeController,
  private val httpClientAuthInterceptor: HttpClientAuthInterceptor,
  private val application: Application
) {

  fun initApi() {
    application.installLogging(buildVariant, featureFlagsFacade)
    initRouting()
  }

  private fun initRouting() {
    application.routing {
      initGamesRoute()
      initWarmup()
      initCronRoute()
      initServiceRoute()
      taskController.initRouting(this)
    }
  }

  private fun Routing.initWarmup() {
    get("/_ah/warmup") {
      call.respond(HttpStatusCode.OK)
    }
  }

  private fun Routing.initServiceRoute() {
    route("/service") {
      with(httpClientAuthInterceptor) { installInterceptor() }
      if (buildVariant != BuildVariant.PRODUCTION) {
        switchGameCoinCalculationModeController.initRouting(this)
      }
    }
  }

  private fun Routing.initGamesRoute() {
    route("/games/") {
      install(StatusPages, statusPageConfig())

      userGameKeysController.startRouting(this)
    }
  }

  private fun Routing.initCronRoute() {
    route("/cron/") {
      install(StatusPages, statusPageConfig(cronLogger()))
      cronInterceptor(buildVariant)
      cronController.startRouting(this)
    }
  }

  private fun statusPageConfig(logger: Logger = logger()): StatusPages.Configuration.() -> Unit = {
    exception<UserRecordNotFoundException> { e ->
      logger.error("User record not found", e)
      call.respondText(e.message ?: "", ContentType.Text.Plain, HttpStatusCode.NotFound)
    }
    exception<ParameterRequiredException> { e ->
      logger.debug("Invalid request", e)
      call.respondText(e.message ?: "", ContentType.Text.Plain, HttpStatusCode.BadRequest)
    }
    exception<GameParameterRequiredException> { e ->
      logger.debug("Parameter ${e.parameterName} missing for game progress of ${e.applicationId}", e)
      call.respondText(e.message ?: "", ContentType.Text.Plain, HttpStatusCode.BadRequest)
    }
    exception<Throwable> { e ->
      logger.error("Error", e)
      call.respondText(e.message ?: "Unknown server error", ContentType.Text.Plain, HttpStatusCode.InternalServerError)
    }
  }
}
