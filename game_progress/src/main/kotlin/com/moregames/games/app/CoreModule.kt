package com.moregames.games.app

import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.moregames.base.app.BaseModule
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.DataSourceProvider
import com.moregames.base.app.DefaultDataSourceProvider
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.messaging.auth.AuthService
import com.moregames.base.messaging.commands.CommandsClient
import com.moregames.base.secret.Secret
import com.moregames.base.secret.SecretService
import com.moregames.base.util.RandomGenerator
import com.moregames.games.progress.calculation.GameCoinsCalculationService
import com.moregames.games.progress.calculation.ProdGameCoinsCalculationService
import com.moregames.games.progress.calculation.TestGameCoinCalculationService
import com.moregames.games.util.getDefaultJsonConverter
import io.ktor.application.*
import kotlinx.serialization.json.Json
import redis.clients.jedis.JedisPool
import redis.clients.jedis.JedisPoolConfig

@Suppress("unused")
class CoreModule(
  application: Application,
  buildVariant: BuildVariant,
  dataSourceProvider: DataSourceProvider = DefaultDataSourceProvider(),
) : BaseModule(application, buildVariant, dataSourceProvider) {

  @Provides
  @Singleton
  fun gameCoinsCalculatorService(): GameCoinsCalculationService {
    return when (buildVariant()) {
      BuildVariant.PRODUCTION -> ProdGameCoinsCalculationService()
      BuildVariant.TEST, BuildVariant.LOCAL -> TestGameCoinCalculationService(ProdGameCoinsCalculationService())
    }
  }

  @Provides
  @Singleton
  fun database(secretService: SecretService) = connectToDatabase(DbConfigs.GAMES, secretService, alias = "old")

  @Provides
  @Singleton
  @Named("game-progress")
  fun gameProgressDatabase(secretService: SecretService) = connectToDatabase(DbConfigs.GAME_PROGRESS, secretService)

  @Provides
  fun jsonConverter() = getDefaultJsonConverter()

  @Provides
  @Singleton
  fun jedisPool(applicationConfig: ApplicationConfig): JedisPool {
    val jedisPoolConfig = JedisPoolConfig()
    jedisPoolConfig.maxTotal = 200
    return JedisPool(jedisPoolConfig, applicationConfig.redisHost, applicationConfig.redisPort)
  }

  @Provides
  @Singleton
  fun commandsClient(json: Json, authService: AuthService, randomGenerator: RandomGenerator): CommandsClient {
    return CommandsClient(json, authService, randomGenerator)
  }

  private enum class DbConfigs(override val username: String, override val password: Secret, override val maximumPoolSize: Int) : DbConfig {
    GAMES("games", GameProgressSecrets.DATABASE_USER_GAMES, 20),
    GAME_PROGRESS("game-progress", GameProgressSecrets.DATABASE_USER_GAME_PROGRESS, 20)
  }
}
